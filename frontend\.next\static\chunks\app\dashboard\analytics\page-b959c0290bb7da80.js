(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[754],{3033:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(95155);r(12115);var a=r(17313),i=r(35695),l=r(91462),o=r(3638),n=r(58527);let d=[{value:"orders",label:"Orders",href:"/dashboard",icon:(0,s.jsx)(l.A,{})},{value:"history",label:"History",href:"/dashboard/history",icon:(0,s.jsx)(o.A,{})},{value:"analytics",label:"Analytics",href:"/dashboard/analytics",icon:(0,s.jsx)(n.A,{})}];function c(){let e=(0,i.useRouter)(),t=(0,i.usePathname)(),r="orders";return"/dashboard/history"===t?r="history":"/dashboard/analytics"===t&&(r="analytics"),(0,s.jsx)(a.tU,{value:r,onValueChange:t=>{let r=d.find(e=>e.value===t);r&&e.push(r.href)},className:"w-full mb-6",children:(0,s.jsx)(a.j7,{className:"grid w-full grid-cols-3 bg-card border-2 border-border",children:d.map(e=>(0,s.jsx)(a.Xi,{value:e.value,className:"text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-bold",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,e.label]})},e.value))})})}},17313:(e,t,r)=>{"use strict";r.d(t,{Xi:()=>d,av:()=>c,j7:()=>n,tU:()=>o});var s=r(95155),a=r(12115),i=r(30064),l=r(59434);let o=i.bL,n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.B8,{ref:t,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...a})});n.displayName=i.B8.displayName;let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.l9,{ref:t,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...a})});d.displayName=i.l9.displayName;let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.UC,{ref:t,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...a})});c.displayName=i.UC.displayName},22346:(e,t,r)=>{"use strict";r.d(t,{w:()=>o});var s=r(95155),a=r(12115),i=r(87489),l=r(59434);let o=a.forwardRef((e,t)=>{let{className:r,orientation:a="horizontal",decorative:o=!0,...n}=e;return(0,s.jsx)(i.b,{ref:t,decorative:o,orientation:a,className:(0,l.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",r),...n})});o.displayName=i.b.displayName},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var s=r(95155);r(12115);var a=r(74466),i=r(59434);let l=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)(l({variant:r}),t),...a})}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(95155),a=r(12115),i=r(99708),l=r(74466),o=r(59434);let n=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:d=!1,...c}=e,u=d?i.DX:"button";return(0,s.jsx)(u,{className:(0,o.cn)(n({variant:a,size:l,className:r})),ref:t,...c})});d.displayName="Button"},52669:(e,t,r)=>{Promise.resolve().then(r.bind(r,70709))},54530:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var s=r(95155),a=r(12115),i=r(77213),l=r(66695),o=r(62523),n=r(30285),d=r(10518),c=r(25318),u=r(15222),m=r(16392),f=r(45219),p=r(38988);function x(){let{crypto1Balance:e,crypto2Balance:t,stablecoinBalance:r,config:x,dispatch:h}=(0,i.U)(),[g,y]=(0,a.useState)(null),[b,v]=(0,a.useState)({crypto1:e.toString(),crypto2:t.toString(),stablecoin:r.toString()}),j=e=>e.toFixed(x.numDigits),N=s=>{y(s),v({crypto1:e.toString(),crypto2:t.toString(),stablecoin:r.toString()})},w=r=>{let s=parseFloat(b[r]);!isNaN(s)&&s>=0&&("crypto1"===r?h({type:"UPDATE_BALANCES",payload:{crypto1:s,crypto2:t}}):"crypto2"===r?h({type:"UPDATE_BALANCES",payload:{crypto1:e,crypto2:s}}):"stablecoin"===r&&h({type:"UPDATE_STABLECOIN_BALANCE",payload:s})),y(null)},C=()=>{y(null),v({crypto1:e.toString(),crypto2:t.toString(),stablecoin:r.toString()})},P=(e,t,r,a,i)=>(0,s.jsxs)(l.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(l.ZB,{className:"text-sm font-medium text-muted-foreground",children:e}),a]}),(0,s.jsx)(l.Wu,{children:g===r?(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.p,{type:"number",value:b[r],onChange:e=>v(t=>({...t,[r]:e.target.value})),className:"text-lg font-bold",step:"any",min:"0"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(n.$,{size:"sm",onClick:()=>w(r),className:"flex-1",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-1"}),"Save"]}),(0,s.jsxs)(n.$,{size:"sm",variant:"outline",onClick:C,className:"flex-1",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-1"}),"Cancel"]})]})]}):(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-foreground",children:j(t)}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Available ",i]})]}),(0,s.jsx)(n.$,{size:"sm",variant:"ghost",onClick:()=>N(r),className:"ml-2",children:(0,s.jsx)(u.A,{className:"h-4 w-4"})})]})})]});return(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-3 mb-6",children:[P("".concat(x.crypto1," Balance"),e,"crypto1",(0,s.jsx)(m.A,{className:"h-5 w-5 text-primary"}),x.crypto1),P("".concat(x.crypto2," Balance"),t,"crypto2",(0,s.jsx)(f.A,{className:"h-5 w-5 text-primary"}),x.crypto2),P("Stablecoin Balance (".concat(x.preferredStablecoin||"N/A",")"),r,"stablecoin",(0,s.jsx)(p.A,{className:"h-5 w-5 text-primary"}),"Stablecoins")]})}},59409:(e,t,r)=>{"use strict";r.d(t,{bq:()=>m,eb:()=>h,gC:()=>x,l6:()=>c,yv:()=>u});var s=r(95155),a=r(12115),i=r(50663),l=r(79556),o=r(77381),n=r(10518),d=r(59434);let c=i.bL;i.YJ;let u=i.WT,m=a.forwardRef((e,t)=>{let{className:r,children:a,...o}=e;return(0,s.jsxs)(i.l9,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...o,children:[a,(0,s.jsx)(i.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=i.l9.displayName;let f=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})});f.displayName=i.PP.displayName;let p=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})});p.displayName=i.wn.displayName;let x=a.forwardRef((e,t)=>{let{className:r,children:a,position:l="popper",...o}=e;return(0,s.jsx)(i.ZL,{children:(0,s.jsxs)(i.UC,{ref:t,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:l,...o,children:[(0,s.jsx)(f,{}),(0,s.jsx)(i.LM,{className:(0,d.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,s.jsx)(p,{})]})})});x.displayName=i.UC.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...a})}).displayName=i.JU.displayName;let h=a.forwardRef((e,t)=>{let{className:r,children:a,...l}=e;return(0,s.jsxs)(i.q7,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...l,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(n.A,{className:"h-4 w-4"})})}),(0,s.jsx)(i.p4,{children:a})]})});h.displayName=i.q7.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",r),...a})}).displayName=i.wv.displayName},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var s=r(95155),a=r(12115),i=r(59434);let l=a.forwardRef((e,t)=>{let{className:r,type:a,...l}=e;return(0,s.jsx)("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...l})});l.displayName="Input"},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>n,Zp:()=>l,aR:()=>o});var s=r(95155),a=r(12115),i=r(59434);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",r),...a})});l.displayName="Card";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-4 md:p-6",r),...a})});o.displayName="CardHeader";let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-xl font-semibold leading-none tracking-tight",r),...a})});n.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-4 md:p-6 pt-0",r),...a})});c.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-4 md:p-6 pt-0",r),...a})}).displayName="CardFooter"},70709:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>L});var s=r(95155),a=r(12115),i=r(3033),l=r(54530),o=r(66695),n=r(59409),d=r(26126),c=r(22346),u=r(77213),m=r(84553),f=r(80659),p=r(97723),x=r(5263),h=r(81203),g=r(37648),y=r(83540),b=r(56965),v=r(94754),j=r(96025),N=r(16238),w=r(94517),C=r(21374),P=r(28184);let A=(e,t)=>{let r=e.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2),s=r.reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0),a=r.reduce((e,t)=>e+(t.realizedProfitLossCrypto1||0),0),i=r.filter(e=>(e.realizedProfitLossCrypto2||0)>0).length,l=r.length>0?i/r.length*100:0,o=e.length,n=e.filter(e=>"BUY"===e.orderType).length,d=r.length>0?s/r.length:0,c=r.length>0?a/r.length:0;return{totalProfitLossCrypto1:parseFloat(a.toFixed(t.numDigits)),totalProfitLossCrypto2:parseFloat(s.toFixed(t.numDigits)),winRate:parseFloat(l.toFixed(2)),totalTradesExecuted:o,buyTrades:n,sellTrades:r.length,avgProfitPerTradeCrypto2:parseFloat(d.toFixed(t.numDigits)),avgProfitPerTradeCrypto1:parseFloat(c.toFixed(t.numDigits))}},S=(e,t)=>{let r=e.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2),s=0;return r.map((e,t)=>(s+=e.realizedProfitLossCrypto2||0,{date:(0,P.GP)(new Date(e.timestamp),"MMM dd HH:mm"),pnl:parseFloat(s.toFixed(4)),trade:t+1}))};function k(){let{orderHistory:e,config:t,getDisplayOrders:r}=(0,u.U)(),[i,l]=(0,a.useState)([]),[P,k]=(0,a.useState)("current"),[L,R]=(0,a.useState)([]),[T,F]=(0,a.useState)(t),z=m.C.getInstance();(0,a.useEffect)(()=>{B()},[]),(0,a.useEffect)(()=>{if("current"===P)R(e),F(t);else{let e=z.loadSession(P);e&&(R(e.orderHistory),F(e.config))}},[P,e,t]);let B=()=>{l(z.getAllSessions().sort((e,t)=>t.lastModified-e.lastModified))},E=(0,a.useMemo)(()=>A(L,T),[L,T]),D=(0,a.useMemo)(()=>S(L,T.crypto2),[L,T.crypto2]),U=(0,a.useMemo)(()=>"current"!==P?"0.0000":r().reduce((e,t)=>"Full"===t.status&&void 0!==t.incomeCrypto2?e+t.incomeCrypto2:e,0).toFixed(T.numDigits),[r,T.numDigits,P]),_="current"===P?{name:"Current Session",pair:"".concat(t.crypto1,"/").concat(t.crypto2),isActive:!0}:i.find(e=>e.id===P),W=(0,a.useMemo)(()=>[{title:"Total Realized P/L (".concat(T.crypto1,")"),value:E.totalProfitLossCrypto1,icon:(0,s.jsx)(f.A,{className:"h-6 w-6 text-primary"}),description:"Sum of profits from sell trades in Crypto1",isProfit:E.totalProfitLossCrypto1>=0},{title:"Total Realized P/L (".concat(T.crypto2,")"),value:E.totalProfitLossCrypto2,icon:(0,s.jsx)(f.A,{className:"h-6 w-6 text-primary"}),description:"Sum of profits from sell trades in Crypto2",isProfit:E.totalProfitLossCrypto2>=0},{title:"Win Rate",value:"".concat(E.winRate,"%"),icon:(0,s.jsx)(p.A,{className:"h-6 w-6 text-primary"}),description:"Profitable sell trades / Total sell trades",isProfit:E.winRate>=50},{title:"Total Trades",value:E.totalTradesExecuted,icon:(0,s.jsx)(x.A,{className:"h-6 w-6 text-primary"}),description:"".concat(E.buyTrades," buys, ").concat(E.sellTrades," sells"),isProfit:!0},{title:"Avg Profit/Trade (".concat(T.crypto2,")"),value:E.avgProfitPerTradeCrypto2,icon:(0,s.jsx)(h.A,{className:"h-6 w-6 text-primary"}),description:"Average profit per sell trade",isProfit:E.avgProfitPerTradeCrypto2>=0},{title:"Current Unrealized P/L (".concat(T.crypto2,")"),value:U,icon:(0,s.jsx)(g.A,{className:"h-6 w-6 text-primary"}),description:"Unrealized profit/loss from active positions",isProfit:parseFloat(U)>=0,isCurrentOnly:!0}],[E,T,U]);return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{className:"text-xl font-bold text-primary",children:"Session Analytics"}),(0,s.jsx)(o.BT,{children:"View trading analytics for current and past sessions."})]}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsx)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center",children:(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Select Session:"}),(0,s.jsxs)(n.l6,{value:P,onValueChange:k,children:[(0,s.jsx)(n.bq,{className:"w-full sm:w-[300px]",children:(0,s.jsx)(n.yv,{placeholder:"Select a session"})}),(0,s.jsxs)(n.gC,{children:[(0,s.jsx)(n.eb,{value:"current",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.E,{variant:"default",className:"text-xs",children:"Current"}),(0,s.jsxs)("span",{children:["Current Session (",t.crypto1,"/",t.crypto2,")"]})]})}),i.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.w,{className:"my-1"}),i.map(e=>(0,s.jsx)(n.eb,{value:e.id,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.E,{variant:"secondary",className:"text-xs",children:"Past"}),(0,s.jsxs)("span",{children:[e.name," (",e.pair,")"]})]})},e.id))]})]})]})]})}),_&&(0,s.jsx)("div",{className:"bg-muted/50 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium",children:_.name}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:_.pair})]}),_.isActive&&(0,s.jsx)(d.E,{variant:"default",className:"text-xs",children:"Active"})]})})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:W.map((e,t)=>e.isCurrentOnly&&"current"!==P?null:(0,s.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(o.ZB,{className:"text-sm font-medium",children:e.title}),e.icon]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold ".concat("number"==typeof e.value?e.isProfit?"text-green-600":"text-red-600":"text-foreground"),children:"number"==typeof e.value?e.value.toFixed(T.numDigits):e.value}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},t))}),(0,s.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsxs)(o.ZB,{className:"text-xl font-bold text-primary",children:["Cumulative Profit/Loss Over Time (",T.crypto2,")"]}),(0,s.jsxs)(o.BT,{children:["Chart visualization of trading performance for ",(null==_?void 0:_.name)||"selected session","."]})]}),(0,s.jsx)(o.Wu,{className:"h-80",children:D.length>0?(0,s.jsx)(y.u,{width:"100%",height:"100%",children:(0,s.jsxs)(b.b,{data:D,margin:{top:5,right:20,left:-25,bottom:5},children:[(0,s.jsx)(v.d,{strokeDasharray:"3 3",stroke:"hsl(var(--border))"}),(0,s.jsx)(j.W,{dataKey:"date",stroke:"hsl(var(--muted-foreground))",fontSize:12,tickLine:!1,axisLine:!1}),(0,s.jsx)(N.h,{stroke:"hsl(var(--muted-foreground))",fontSize:12,tickLine:!1,axisLine:!1,tickFormatter:e=>"".concat(e.toFixed(2))}),(0,s.jsx)(w.m,{content:e=>{let{active:t,payload:r,label:a}=e;if(t&&r&&r.length&&r[0]){var i,l,o,n;return(0,s.jsxs)("div",{className:"bg-card border border-border rounded-lg p-3 shadow-lg",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Date: ".concat(a)}),(0,s.jsxs)("p",{className:"text-sm",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"P/L: "}),(0,s.jsxs)("span",{className:(null===(i=r[0])||void 0===i?void 0:i.value)>=0?"text-green-600":"text-red-600",children:[null===(l=r[0])||void 0===l?void 0:l.value," ",null==T?void 0:T.crypto2]})]}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Trade #",null===(n=r[0])||void 0===n?void 0:null===(o=n.payload)||void 0===o?void 0:o.trade]})]})}return null}}),(0,s.jsx)(C.N,{type:"monotone",dataKey:"pnl",stroke:"hsl(var(--primary))",strokeWidth:2,dot:{fill:"hsl(var(--primary))",strokeWidth:2,r:4},activeDot:{r:6,stroke:"hsl(var(--primary))",strokeWidth:2}})]})}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(x.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,s.jsx)("p",{children:"No sell trades recorded yet for this session."}),(0,s.jsx)("p",{className:"text-xs",children:"Chart will appear after first profitable trade."})]})})})]})]})}function L(){return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(i.A,{}),(0,s.jsx)(l.A,{}),(0,s.jsx)(k,{})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[823,569,892,425,251,318,441,684,358],()=>t(52669)),_N_E=e.O()}]);