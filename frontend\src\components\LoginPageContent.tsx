'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Logo from '@/components/shared/Logo';
import { Loader2, Eye, EyeOff, LogIn, UserPlus, TrendingUp, ShieldCheck, BotMessageSquare, Users, Wifi, WifiOff } from 'lucide-react';
import Link from 'next/link';
import { authApi } from '@/lib/api';

export default function LoginPageContent() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [email, setEmail] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const { login, isLoading, isAuthenticated } = useAuth();
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [showLoginForm, setShowLoginForm] = useState(false);
  const [showRegisterForm, setShowRegisterForm] = useState(false);
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [backendStatus, setBackendStatus] = useState<'checking' | 'online' | 'offline'>('checking');

  useEffect(() => {
    // Check backend connectivity on load
    const checkBackendStatus = async () => {
      try {
        const response = await fetch('http://localhost:5000/health/', {
          method: 'GET'
        });
        setBackendStatus(response.ok ? 'online' : 'offline');
      } catch (error) {
        console.error('Backend connectivity check failed:', error);
        setBackendStatus('offline');
      }
    };
    
    // Initial check
    checkBackendStatus();
    
    // Set up periodic checks
    const intervalId = setInterval(checkBackendStatus, 10000);
    
    // Set current year
    setCurrentYear(new Date().getFullYear());
    
    // Cleanup interval on unmount
    return () => clearInterval(intervalId);
  }, []);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    try {
      const success = await login(username, password);
      if (!success) {
        setError('Invalid credentials. Try using testuser/password123');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('Login failed. Please try again.');
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccessMessage('');
    
    if (!username || !password || !email) {
      setError('All fields are required');
      return;
    }
    
    try {
      const response = await authApi.register(username, password, email);
      
      setSuccessMessage('Registration successful! You can now log in.');
      setTimeout(() => {
        setShowRegisterForm(false);
        setShowLoginForm(true);
        setUsername('');
        setPassword('');
        setEmail('');
        setSuccessMessage('');
      }, 2000);
    } catch (error: any) {
      console.error('Registration error:', error);
      setError(error.message || 'Could not connect to server. Please try again later.');
    }
  };
  
  const openLoginForm = () => {
    setShowRegisterForm(false);
    setShowLoginForm(true);
    setError('');
    setSuccessMessage('');
  };
  
  const openRegisterForm = () => {
    setShowLoginForm(false);
    setShowRegisterForm(true);
    setError('');
    setSuccessMessage('');
  };
  
  if (isLoading && !isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background p-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  if (isAuthenticated) return null; // AuthContext handles redirect

  return (
    <div className="min-h-screen bg-background text-foreground flex flex-col">
      {/* Header */}
      <header className="py-4 px-6 md:px-10 flex justify-between items-center border-b border-border">
        <Logo className="text-2xl" />
        <nav className="hidden md:flex items-center space-x-6 text-sm">
          <Link href="#" className="hover:text-primary">Home</Link>
          <Link href="#" className="hover:text-primary">Features</Link>
          <Link href="#" className="hover:text-primary">Pricing</Link>
          <Link href="#" className="hover:text-primary">Contact</Link>
        </nav>
        <div className="space-x-2">
          <Button variant="outline" className="btn-outline-neo" onClick={() => openLoginForm()}>Login</Button>
          <Button className="btn-neo" onClick={() => openRegisterForm()}>Register</Button>
        </div>
      </header>

      {/* Hero Section */}
      <main className="flex-grow flex flex-col items-center justify-center text-center px-4 py-10 md:py-20">
        <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6">
          <span className="text-primary">Pluto</span> Trading Bot Platform
        </h1>
        <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mb-10">
          Automate your trading strategy with our powerful Simple Spot and Stablecoin Swap bots. Maximize your profits with minimal effort.
        </p>
        
        {!showLoginForm && !showRegisterForm && (
          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            <Button size="lg" className="btn-outline-neo text-lg px-8 py-4" onClick={() => openLoginForm()}>
              <LogIn className="mr-2 h-5 w-5" /> Login to Trading Platform
            </Button>
            <Button size="lg" className="btn-neo text-lg px-8 py-4" onClick={() => openRegisterForm()}>
              <UserPlus className="mr-2 h-5 w-5" /> Create Free Account
            </Button>
          </div>
        )}

        {/* Login Form Card - Revealed on click */}
        {showLoginForm && (
          <Card className="w-full max-w-md border-2 border-border shadow-2xl hard-shadow mt-10">
            <CardHeader className="text-center">
              <CardTitle className="text-3xl font-bold">Account Login</CardTitle>
              <CardDescription>Access your Pluto Trading Bot dashboard.</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="username" className="text-lg sr-only">Username</Label>
                  <Input
                    id="username"
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    required
                    className="text-base"
                    placeholder="Username"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-lg sr-only">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      className="text-base pr-10" 
                      placeholder="Password (try: password123)"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 px-3 flex items-center text-muted-foreground hover:text-foreground"
                      aria-label={showPassword ? "Hide password" : "Show password"}
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>
                {error && <p className="text-destructive text-sm">{error}</p>}
                {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                <Button type="submit" className="w-full btn-neo text-lg py-3" disabled={isLoading || backendStatus === 'offline'}>
                  {isLoading ? <Loader2 className="mr-2 h-5 w-5 animate-spin" /> : "Login"}
                </Button>
                
                {/* Backend status indicator */}
                <div className="flex items-center justify-center mt-2 text-sm">
                  {backendStatus === 'checking' && (
                    <div className="flex items-center text-orange-500">
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                      <span>Checking server connection...</span>
                    </div>
                  )}
                  {backendStatus === 'online' && (
                    <div className="flex items-center text-green-500">
                      <Wifi className="h-3 w-3 mr-1" />
                      <span>Server connected</span>
                    </div>
                  )}
                  {backendStatus === 'offline' && (
                    <div className="flex items-center text-destructive">
                      <WifiOff className="h-3 w-3 mr-1" />
                      <span>Server offline - Please start the backend</span>
                    </div>
                  )}
                </div>
                
                <div className="text-center text-sm text-muted-foreground space-y-2 pt-2">
                  <p>
                    <a href="#" className="hover:text-primary underline">Forgot password?</a> (Simulated)
                  </p>
                  <p>
                    Don't have an account? <button type="button" onClick={openRegisterForm} className="hover:text-primary underline">Create Account</button>
                  </p>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Registration Form Card */}
        {showRegisterForm && (
          <Card className="w-full max-w-md border-2 border-border shadow-2xl hard-shadow mt-10">
            <CardHeader className="text-center">
              <CardTitle className="text-3xl font-bold">Create Account</CardTitle>
              <CardDescription>Join Pluto Trading Bot platform.</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleRegister} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="reg-username" className="text-lg">Username</Label>
                  <Input
                    id="reg-username"
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    required
                    className="text-base"
                    placeholder="Choose a username"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reg-email" className="text-lg">Email</Label>
                  <Input
                    id="reg-email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="text-base"
                    placeholder="Your email address"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reg-password" className="text-lg">Password</Label>
                  <div className="relative">
                    <Input
                      id="reg-password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      className="text-base pr-10" 
                      placeholder="Create a password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 px-3 flex items-center text-muted-foreground hover:text-foreground"
                      aria-label={showPassword ? "Hide password" : "Show password"}
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>
                {error && <p className="text-destructive text-sm">{error}</p>}
                {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                <Button type="submit" className="w-full btn-neo text-lg py-3" disabled={isLoading}>
                  {isLoading ? <Loader2 className="mr-2 h-5 w-5 animate-spin" /> : "Register"}
                </Button>
                <div className="text-center text-sm text-muted-foreground pt-2">
                  <p>
                    Already have an account? <button type="button" onClick={openLoginForm} className="hover:text-primary underline">Login</button>
                  </p>
                </div>
              </form>
            </CardContent>
          </Card>
        )}
      </main>

      <footer className="py-6 text-center text-sm text-muted-foreground border-t border-border">
        © {currentYear} Pluto Trading. All Rights Reserved (Simulation).
      </footer>
    </div>
  );
}
