(()=>{var e={};e.id=105,e.ids=[105],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},6638:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=t(65239),o=t(48088),a=t(88170),i=t.n(a),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let c={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80559)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7252:e=>{"use strict";e.exports=require("express")},8751:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(82614).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var s=t(60687),o=t(43210),a=t(78895),i=t(4780);let n=o.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:t,className:(0,i.cn)("w-full caption-bottom text-sm",e),...r})}));n.displayName="Table";let l=o.forwardRef(({className:e,...r},t)=>(0,s.jsx)("thead",{ref:t,className:(0,i.cn)("[&_tr]:border-b",e),...r}));l.displayName="TableHeader";let c=o.forwardRef(({className:e,...r},t)=>(0,s.jsx)("tbody",{ref:t,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...r}));c.displayName="TableBody",o.forwardRef(({className:e,...r},t)=>(0,s.jsx)("tfoot",{ref:t,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...r})).displayName="TableFooter";let d=o.forwardRef(({className:e,...r},t)=>(0,s.jsx)("tr",{ref:t,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...r}));d.displayName="TableRow";let p=o.forwardRef(({className:e,...r},t)=>(0,s.jsx)("th",{ref:t,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...r}));p.displayName="TableHead";let u=o.forwardRef(({className:e,...r},t)=>(0,s.jsx)("td",{ref:t,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...r}));u.displayName="TableCell",o.forwardRef(({className:e,...r},t)=>(0,s.jsx)("caption",{ref:t,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...r})).displayName="TableCaption";var x=t(42692),m=t(96834);function b(){let{getDisplayOrders:e,config:r,currentMarketPrice:t}=(0,a.U)(),o=e(),b=(e,t=!1)=>{if(null==e||isNaN(e))return"-";let s=e.toFixed(r.numDigits);return t&&e>0?`+${s}`:s},h=e=>null==e||isNaN(e)?"-":`${e.toFixed(2)}%`,f=[{key:"#",label:"#"},{key:"status",label:"Status"},{key:"orderLevel",label:"Level"},{key:"valueLevel",label:"Value"},{key:"crypto2Var",label:`${r.crypto2} Var.`},{key:"crypto1Var",label:`${r.crypto1} Var.`},{key:"targetPrice",label:"Target Price"},{key:"percentFromActualPrice",label:"% from Actual"},{key:"incomeCrypto1",label:`Income ${r.crypto1}`},{key:"incomeCrypto2",label:`Income ${r.crypto2}`},{key:"originalCostCrypto2",label:`Original Cost ${r.crypto2}`}];return(0,s.jsx)("div",{className:"border-2 border-border rounded-sm",children:(0,s.jsxs)(x.F,{className:"w-full whitespace-nowrap",children:[(0,s.jsxs)(n,{className:"min-w-full",children:[(0,s.jsx)(l,{children:(0,s.jsx)(d,{className:"bg-card hover:bg-card",children:f.map(e=>(0,s.jsx)(p,{className:"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm",children:e.label},e.key))})}),(0,s.jsx)(c,{children:0===o.length?(0,s.jsx)(d,{children:(0,s.jsx)(u,{colSpan:f.length,className:"h-24 text-center text-muted-foreground",children:'No target prices set. Use "Set Target Prices" in the sidebar.'})}):o.map(e=>(0,s.jsxs)(d,{className:"hover:bg-card/80",children:[(0,s.jsx)(u,{className:"px-3 py-2 text-xs",children:e.counter}),(0,s.jsx)(u,{className:"px-3 py-2 text-xs",children:(0,s.jsx)(m.E,{variant:"Full"===e.status?"default":"secondary",className:(0,i.cn)("Full"===e.status?"bg-green-600 text-white":"bg-yellow-500 text-black","font-bold"),children:e.status})}),(0,s.jsx)(u,{className:"px-3 py-2 text-xs",children:e.orderLevel}),(0,s.jsx)(u,{className:"px-3 py-2 text-xs",children:b(e.valueLevel)}),(0,s.jsx)(u,{className:(0,i.cn)("px-3 py-2 text-xs",e.crypto2Var&&e.crypto2Var<0?"text-destructive":"text-green-400"),children:b(e.crypto2Var,!0)}),(0,s.jsx)(u,{className:(0,i.cn)("px-3 py-2 text-xs",e.crypto1Var&&e.crypto1Var<0?"text-destructive":"text-green-400"),children:b(e.crypto1Var,!0)}),(0,s.jsx)(u,{className:"px-3 py-2 text-xs font-semibold text-primary",children:b(e.targetPrice)}),(0,s.jsx)(u,{className:(0,i.cn)("px-3 py-2 text-xs",e.percentFromActualPrice<0?"text-destructive":"text-green-400"),children:h(e.percentFromActualPrice)}),(0,s.jsx)(u,{className:(0,i.cn)("px-3 py-2 text-xs",e.incomeCrypto1&&e.incomeCrypto1<0?"text-destructive":"text-green-400"),children:b(e.incomeCrypto1)}),(0,s.jsx)(u,{className:(0,i.cn)("px-3 py-2 text-xs",e.incomeCrypto2&&e.incomeCrypto2<0?"text-destructive":"text-green-400"),children:b(e.incomeCrypto2)}),(0,s.jsx)(u,{className:"px-3 py-2 text-xs",children:b(e.originalCostCrypto2)})]},e.id))})]}),(0,s.jsx)(x.$,{orientation:"horizontal"})]})})}var h=t(37079),f=t(44493),y=t(70428),g=t(8751);function j(){let{config:e,currentMarketPrice:r}=(0,a.U)();return(0,s.jsx)("div",{className:"mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"h-5 w-5 text-green-500"}),(0,s.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Current Market Price"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("span",{className:"text-lg font-semibold text-foreground",children:[e.crypto1,"/",e.crypto2,":"]}),(0,s.jsxs)("span",{className:"text-2xl font-bold text-primary",children:["$",r.toFixed(e.numDigits)]})]})]})})}function v(){let{config:e}=(0,a.U)();return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(h.A,{}),(0,s.jsx)(y.A,{}),(0,s.jsxs)(f.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(f.aR,{children:[(0,s.jsxs)(f.ZB,{className:"text-2xl font-bold text-primary",children:["Active Orders (",e.crypto1,"/",e.crypto2,")"]}),(0,s.jsx)(f.BT,{children:"Current state of your target price levels. Prices update in real-time."})]}),(0,s.jsxs)(f.Wu,{children:[(0,s.jsx)(j,{}),(0,s.jsx)(b,{})]})]})]})}},14985:e=>{"use strict";e.exports=require("dns")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19185:e=>{"use strict";e.exports=require("dgram")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37830:e=>{"use strict";e.exports=require("node:stream/web")},44708:e=>{"use strict";e.exports=require("node:https")},47729:(e,r,t)=>{Promise.resolve().then(t.bind(t,13014))},54379:e=>{"use strict";e.exports=require("node:path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57457:(e,r,t)=>{Promise.resolve().then(t.bind(t,80559))},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80559:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[585,118,759,124,241],()=>t(6638));module.exports=s})();