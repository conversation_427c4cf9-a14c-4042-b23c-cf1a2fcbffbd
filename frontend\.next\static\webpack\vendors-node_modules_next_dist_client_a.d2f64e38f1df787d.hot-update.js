"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("vendors-node_modules_next_dist_client_a",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"attachHydrationErrorState\", ({\n    enumerable: true,\n    get: function() {\n        return attachHydrationErrorState;\n    }\n}));\nconst _ishydrationerror = __webpack_require__(/*! ../is-hydration-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\");\nconst _hydrationerrorinfo = __webpack_require__(/*! ./hydration-error-info */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js\");\nfunction attachHydrationErrorState(error) {\n    let parsedHydrationErrorState = {};\n    const isHydrationWarning = (0, _ishydrationerror.testReactHydrationWarning)(error.message);\n    const isHydrationRuntimeError = (0, _ishydrationerror.isHydrationError)(error);\n    // If it's not hydration warnings or errors, skip\n    if (!(isHydrationRuntimeError || isHydrationWarning)) {\n        return;\n    }\n    const reactHydrationDiffSegments = (0, _hydrationerrorinfo.getReactHydrationDiffSegments)(error.message);\n    // If the reactHydrationDiffSegments exists\n    // and the diff (reactHydrationDiffSegments[1]) exists\n    // e.g. the hydration diff log error.\n    if (reactHydrationDiffSegments) {\n        const diff = reactHydrationDiffSegments[1];\n        parsedHydrationErrorState = {\n            ...error.details,\n            ..._hydrationerrorinfo.hydrationErrorState,\n            // If diff is present in error, we don't need to pick up the console logged warning.\n            // - if hydration error has diff, and is not hydration diff log, then it's a normal hydration error.\n            // - if hydration error no diff, then leverage the one from the hydration diff log.\n            warning: (diff && !isHydrationWarning ? null : _hydrationerrorinfo.hydrationErrorState.warning) || [\n                (0, _ishydrationerror.getDefaultHydrationErrorMessage)()\n            ],\n            // When it's hydration diff log, do not show notes section.\n            // This condition is only for the 1st squashed error.\n            notes: isHydrationWarning ? '' : reactHydrationDiffSegments[0],\n            reactOutputComponentDiff: diff\n        };\n        // Cache the `reactOutputComponentDiff` into hydrationErrorState.\n        // This is only required for now when we still squashed the hydration diff log into hydration error.\n        // Once the all error is logged to dev overlay in order, this will go away.\n        if (!_hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff && diff) {\n            _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff = diff;\n        }\n        // If it's hydration runtime error that doesn't contain the diff, combine the diff from the cached hydration diff.\n        if (!diff && isHydrationRuntimeError && _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff) {\n            parsedHydrationErrorState.reactOutputComponentDiff = _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff;\n        }\n    } else {\n        // Normal runtime error, where it doesn't contain the hydration diff.\n        // If there's any extra information in the error message to display,\n        // append it to the error message details property\n        if (_hydrationerrorinfo.hydrationErrorState.warning) {\n            // The patched console.error found hydration errors logged by React\n            // Append the logged warning to the error message\n            parsedHydrationErrorState = {\n                ...error.details,\n                // It contains the warning, component stack, server and client tag names\n                ..._hydrationerrorinfo.hydrationErrorState\n            };\n        }\n        // Consume the cached hydration diff.\n        // This is only required for now when we still squashed the hydration diff log into hydration error.\n        // Once the all error is logged to dev overlay in order, this will go away.\n        if (_hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff) {\n            parsedHydrationErrorState.reactOutputComponentDiff = _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff;\n        }\n    }\n    // If it's a hydration error, store the hydration error state into the error object\n    ;\n    error.details = parsedHydrationErrorState;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=attach-hydration-error-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/errors/console-error.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/console-error.js ***!
  \**************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// To distinguish from React error.digest, we use a different symbol here to determine if the error is from console.error or unhandled promise rejection.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createUnhandledError: function() {\n        return createUnhandledError;\n    },\n    getUnhandledErrorType: function() {\n        return getUnhandledErrorType;\n    },\n    isUnhandledConsoleOrRejection: function() {\n        return isUnhandledConsoleOrRejection;\n    }\n});\nconst digestSym = Symbol.for('next.console.error.digest');\nconst consoleTypeSym = Symbol.for('next.console.error.type');\nfunction createUnhandledError(message, environmentName) {\n    const error = typeof message === 'string' ? Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    }) : message;\n    error[digestSym] = 'NEXT_UNHANDLED_ERROR';\n    error[consoleTypeSym] = typeof message === 'string' ? 'string' : 'error';\n    if (environmentName && !error.environmentName) {\n        error.environmentName = environmentName;\n    }\n    return error;\n}\nconst isUnhandledConsoleOrRejection = (error)=>{\n    return error && error[digestSym] === 'NEXT_UNHANDLED_ERROR';\n};\nconst getUnhandledErrorType = (error)=>{\n    return error[consoleTypeSym];\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=console-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/errors/console-error.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/hydration-error-info.js ***!
  \*********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getHydrationWarningType: function() {\n        return getHydrationWarningType;\n    },\n    getReactHydrationDiffSegments: function() {\n        return getReactHydrationDiffSegments;\n    },\n    hydrationErrorState: function() {\n        return hydrationErrorState;\n    },\n    storeHydrationErrorStateFromConsoleArgs: function() {\n        return storeHydrationErrorStateFromConsoleArgs;\n    }\n});\nconst _ishydrationerror = __webpack_require__(/*! ../is-hydration-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\");\nconst hydrationErrorState = {};\n// https://github.com/facebook/react/blob/main/packages/react-dom/src/__tests__/ReactDOMHydrationDiff-test.js used as a reference\nconst htmlTagsWarnings = new Set([\n    'Warning: In HTML, %s cannot be a child of <%s>.%s\\nThis will cause a hydration error.%s',\n    'Warning: In HTML, %s cannot be a descendant of <%s>.\\nThis will cause a hydration error.%s',\n    'Warning: In HTML, text nodes cannot be a child of <%s>.\\nThis will cause a hydration error.',\n    \"Warning: In HTML, whitespace text nodes cannot be a child of <%s>. Make sure you don't have any extra whitespace between tags on each line of your source code.\\nThis will cause a hydration error.\",\n    'Warning: Expected server HTML to contain a matching <%s> in <%s>.%s',\n    'Warning: Did not expect server HTML to contain a <%s> in <%s>.%s'\n]);\nconst textAndTagsMismatchWarnings = new Set([\n    'Warning: Expected server HTML to contain a matching text node for \"%s\" in <%s>.%s',\n    'Warning: Did not expect server HTML to contain the text node \"%s\" in <%s>.%s'\n]);\nconst getHydrationWarningType = (message)=>{\n    if (typeof message !== 'string') {\n        // TODO: Doesn't make sense to treat no message as a hydration error message.\n        // We should bail out somewhere earlier.\n        return 'text';\n    }\n    const normalizedMessage = message.startsWith('Warning: ') ? message : \"Warning: \" + message;\n    if (isHtmlTagsWarning(normalizedMessage)) return 'tag';\n    if (isTextInTagsMismatchWarning(normalizedMessage)) return 'text-in-tag';\n    return 'text';\n};\nconst isHtmlTagsWarning = (message)=>htmlTagsWarnings.has(message);\nconst isTextInTagsMismatchWarning = (msg)=>textAndTagsMismatchWarnings.has(msg);\nconst getReactHydrationDiffSegments = (msg)=>{\n    if (msg) {\n        const { message, diff } = (0, _ishydrationerror.getHydrationErrorStackInfo)(msg);\n        if (message) return [\n            message,\n            diff\n        ];\n    }\n    return undefined;\n};\nfunction storeHydrationErrorStateFromConsoleArgs() {\n    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n        args[_key] = arguments[_key];\n    }\n    let [msg, firstContent, secondContent, ...rest] = args;\n    if ((0, _ishydrationerror.testReactHydrationWarning)(msg)) {\n        // Some hydration warnings has 4 arguments, some has 3, fallback to the last argument\n        // when the 3rd argument is not the component stack but an empty string\n        const isReact18 = msg.startsWith('Warning: ');\n        // For some warnings, there's only 1 argument for template.\n        // The second argument is the diff or component stack.\n        if (args.length === 3) {\n            secondContent = '';\n        }\n        const warning = [\n            // remove the last %s from the message\n            msg,\n            firstContent,\n            secondContent\n        ];\n        const lastArg = (rest[rest.length - 1] || '').trim();\n        if (!isReact18) {\n            hydrationErrorState.reactOutputComponentDiff = lastArg;\n        } else {\n            hydrationErrorState.reactOutputComponentDiff = generateHydrationDiffReact18(msg, firstContent, secondContent, lastArg);\n        }\n        hydrationErrorState.warning = warning;\n        hydrationErrorState.serverContent = firstContent;\n        hydrationErrorState.clientContent = secondContent;\n    }\n}\n/*\n * Some hydration errors in React 18 does not have the diff in the error message.\n * Instead it has the error stack trace which is component stack that we can leverage.\n * Will parse the diff from the error stack trace\n *  e.g.\n *  Warning: Expected server HTML to contain a matching <div> in <p>.\n *    at div\n *    at p\n *    at div\n *    at div\n *    at Page\n *  output:\n *    <Page>\n *      <div>\n *        <p>\n *  >       <div>\n *\n */ function generateHydrationDiffReact18(message, firstContent, secondContent, lastArg) {\n    const componentStack = lastArg;\n    let firstIndex = -1;\n    let secondIndex = -1;\n    const hydrationWarningType = getHydrationWarningType(message);\n    // at div\\n at Foo\\n at Bar (....)\\n -> [div, Foo]\n    const components = componentStack.split('\\n') // .reverse()\n    .map((line, index)=>{\n        // `<space>at <component> (<location>)` -> `at <component> (<location>)`\n        line = line.trim();\n        // extract `<space>at <component>` to `<<component>>`\n        // e.g. `  at Foo` -> `<Foo>`\n        const [, component, location] = /at (\\w+)( \\((.*)\\))?/.exec(line) || [];\n        // If there's no location then it's user-land stack frame\n        if (!location) {\n            if (component === firstContent && firstIndex === -1) {\n                firstIndex = index;\n            } else if (component === secondContent && secondIndex === -1) {\n                secondIndex = index;\n            }\n        }\n        return location ? '' : component;\n    }).filter(Boolean).reverse();\n    let diff = '';\n    for(let i = 0; i < components.length; i++){\n        const component = components[i];\n        const matchFirstContent = hydrationWarningType === 'tag' && i === components.length - firstIndex - 1;\n        const matchSecondContent = hydrationWarningType === 'tag' && i === components.length - secondIndex - 1;\n        if (matchFirstContent || matchSecondContent) {\n            const spaces = ' '.repeat(Math.max(i * 2 - 2, 0) + 2);\n            diff += \"> \" + spaces + \"<\" + component + \">\\n\";\n        } else {\n            const spaces = ' '.repeat(i * 2 + 2);\n            diff += spaces + \"<\" + component + \">\\n\";\n        }\n    }\n    if (hydrationWarningType === 'text') {\n        const spaces = ' '.repeat(components.length * 2);\n        diff += \"+ \" + spaces + '\"' + firstContent + '\"\\n';\n        diff += \"- \" + spaces + '\"' + secondContent + '\"\\n';\n    } else if (hydrationWarningType === 'text-in-tag') {\n        const spaces = ' '.repeat(components.length * 2);\n        diff += \"> \" + spaces + \"<\" + secondContent + \">\\n\";\n        diff += \">   \" + spaces + '\"' + firstContent + '\"\\n';\n    }\n    return diff;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hydration-error-info.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/errors/runtime-error-handler.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/runtime-error-handler.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RuntimeErrorHandler\", ({\n    enumerable: true,\n    get: function() {\n        return RuntimeErrorHandler;\n    }\n}));\nconst RuntimeErrorHandler = {\n    hadRuntimeError: false\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=runtime-error-handler.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3JzL3J1bnRpbWUtZXJyb3ItaGFuZGxlci5qcyIsIm1hcHBpbmdzIjoiOzs7O3VEQUFhQTs7O2VBQUFBOzs7QUFBTixNQUFNQSxzQkFBc0I7SUFDakNDLGlCQUFpQjtBQUNuQiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxlcnJvcnNcXHJ1bnRpbWUtZXJyb3ItaGFuZGxlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgUnVudGltZUVycm9ySGFuZGxlciA9IHtcbiAgaGFkUnVudGltZUVycm9yOiBmYWxzZSxcbn1cbiJdLCJuYW1lcyI6WyJSdW50aW1lRXJyb3JIYW5kbGVyIiwiaGFkUnVudGltZUVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/errors/runtime-error-handler.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/stitched-error.js ***!
  \***************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getReactStitchedError\", ({\n    enumerable: true,\n    get: function() {\n        return getReactStitchedError;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(pages-dir-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _errortelemetryutils = __webpack_require__(/*! ../../../lib/error-telemetry-utils */ \"(pages-dir-browser)/./node_modules/next/dist/lib/error-telemetry-utils.js\");\nconst REACT_ERROR_STACK_BOTTOM_FRAME = 'react-stack-bottom-frame';\nconst REACT_ERROR_STACK_BOTTOM_FRAME_REGEX = new RegExp(\"(at \" + REACT_ERROR_STACK_BOTTOM_FRAME + \" )|(\" + REACT_ERROR_STACK_BOTTOM_FRAME + \"\\\\@)\");\nfunction getReactStitchedError(err) {\n    const isErrorInstance = (0, _iserror.default)(err);\n    const originStack = isErrorInstance ? err.stack || '' : '';\n    const originMessage = isErrorInstance ? err.message : '';\n    const stackLines = originStack.split('\\n');\n    const indexOfSplit = stackLines.findIndex((line)=>REACT_ERROR_STACK_BOTTOM_FRAME_REGEX.test(line));\n    const isOriginalReactError = indexOfSplit >= 0 // has the react-stack-bottom-frame\n    ;\n    let newStack = isOriginalReactError ? stackLines.slice(0, indexOfSplit).join('\\n') : originStack;\n    const newError = Object.defineProperty(new Error(originMessage), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    // Copy all enumerable properties, e.g. digest\n    Object.assign(newError, err);\n    (0, _errortelemetryutils.copyNextErrorCode)(err, newError);\n    newError.stack = newStack;\n    // Avoid duplicate overriding stack frames\n    appendOwnerStack(newError);\n    return newError;\n}\nfunction appendOwnerStack(error) {\n    if (!_react.default.captureOwnerStack) {\n        return;\n    }\n    let stack = error.stack || '';\n    // This module is only bundled in development mode so this is safe.\n    const ownerStack = _react.default.captureOwnerStack();\n    // Avoid duplicate overriding stack frames\n    if (ownerStack && stack.endsWith(ownerStack) === false) {\n        stack += ownerStack;\n        // Override stack\n        error.stack = stack;\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=stitched-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\n"));

/***/ })

});