"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/editor-link.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/editor-link.js ***!
  \**********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    EDITOR_LINK_STYLES: function() {\n        return EDITOR_LINK_STYLES;\n    },\n    EditorLink: function() {\n        return EditorLink;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _useopenineditor = __webpack_require__(/*! ../../utils/use-open-in-editor */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/use-open-in-editor.js\");\nfunction EditorLink(param) {\n    let { file, location } = param;\n    var _location_line, _location_column;\n    const open = (0, _useopenineditor.useOpenInEditor)({\n        file,\n        lineNumber: (_location_line = location == null ? void 0 : location.line) != null ? _location_line : 1,\n        column: (_location_column = location == null ? void 0 : location.column) != null ? _location_column : 0\n    });\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-with-open-in-editor-link\": true,\n        \"data-with-open-in-editor-link-import-trace\": true,\n        tabIndex: 10,\n        role: 'link',\n        onClick: open,\n        title: 'Click to open in your editor',\n        children: [\n            file,\n            location ? \":\" + location.line + \":\" + location.column : null,\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        d: \"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"polyline\", {\n                        points: \"15 3 21 3 21 9\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"line\", {\n                        x1: \"10\",\n                        y1: \"14\",\n                        x2: \"21\",\n                        y2: \"3\"\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c = EditorLink;\nconst EDITOR_LINK_STYLES = \"\\n  [data-with-open-in-editor-link] svg {\\n    width: auto;\\n    height: var(--size-14);\\n    margin-left: 8px;\\n  }\\n  [data-with-open-in-editor-link] {\\n    cursor: pointer;\\n  }\\n  [data-with-open-in-editor-link]:hover {\\n    text-decoration: underline dotted;\\n  }\\n  [data-with-open-in-editor-link-import-trace] {\\n    margin-left: 16px;\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=editor-link.js.map\nvar _c;\n$RefreshReg$(_c, \"EditorLink\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/editor-link.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/index.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/index.js ***!
  \****************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Terminal\", ({\n    enumerable: true,\n    get: function() {\n        return _terminal.Terminal;\n    }\n}));\nconst _terminal = __webpack_require__(/*! ./terminal */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/terminal.js\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy90ZXJtaW5hbC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OzRDQUFTQTs7O2VBQUFBLFVBQUFBLFFBQVE7OztzQ0FBUSIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXGNvbXBvbmVudHNcXHRlcm1pbmFsXFxpbmRleC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgVGVybWluYWwgfSBmcm9tICcuL3Rlcm1pbmFsJ1xuIl0sIm5hbWVzIjpbIlRlcm1pbmFsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/terminal.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/terminal.js ***!
  \*******************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    TERMINAL_STYLES: function() {\n        return TERMINAL_STYLES;\n    },\n    Terminal: function() {\n        return Terminal;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _anser = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/anser */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/anser/index.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _hotlinkedtext = __webpack_require__(/*! ../hot-linked-text */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js\");\nconst _editorlink = __webpack_require__(/*! ./editor-link */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/editor-link.js\");\nconst _external = __webpack_require__(/*! ../../icons/external */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/external.js\");\nconst _stackframe = __webpack_require__(/*! ../../../utils/stack-frame */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/stack-frame.js\");\nconst _useopenineditor = __webpack_require__(/*! ../../utils/use-open-in-editor */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/use-open-in-editor.js\");\nconst _file = __webpack_require__(/*! ../../icons/file */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/file.js\");\nfunction getFile(lines) {\n    const contentFileName = lines.shift();\n    if (!contentFileName) return null;\n    const [fileName, line, column] = contentFileName.split(':', 3);\n    const parsedLine = Number(line);\n    const parsedColumn = Number(column);\n    const hasLocation = !Number.isNaN(parsedLine) && !Number.isNaN(parsedColumn);\n    return {\n        fileName: hasLocation ? fileName : contentFileName,\n        location: hasLocation ? {\n            line: parsedLine,\n            column: parsedColumn\n        } : undefined\n    };\n}\nfunction getImportTraceFiles(lines) {\n    if (lines.some((line)=>/ReactServerComponentsError:/.test(line)) || lines.some((line)=>/Import trace for requested module:/.test(line))) {\n        // Grab the lines at the end containing the files\n        const files = [];\n        while(/.+\\..+/.test(lines[lines.length - 1]) && !lines[lines.length - 1].includes(':')){\n            const file = lines.pop().trim();\n            files.unshift(file);\n        }\n        return files;\n    }\n    return [];\n}\nfunction getEditorLinks(content) {\n    const lines = content.split('\\n');\n    const file = getFile(lines);\n    const importTraceFiles = getImportTraceFiles(lines);\n    return {\n        file,\n        source: lines.join('\\n'),\n        importTraceFiles\n    };\n}\nconst Terminal = function Terminal(param) {\n    _s();\n    let { content } = param;\n    var _file_location, _file_location1, _file_location2, _file_location3, _stackFrame_file;\n    const { file, source, importTraceFiles } = _react.useMemo({\n        \"Terminal.useMemo\": ()=>getEditorLinks(content)\n    }[\"Terminal.useMemo\"], [\n        content\n    ]);\n    const decoded = _react.useMemo({\n        \"Terminal.useMemo[decoded]\": ()=>{\n            return _anser.default.ansiToJson(source, {\n                json: true,\n                use_classes: true,\n                remove_empty: true\n            });\n        }\n    }[\"Terminal.useMemo[decoded]\"], [\n        source\n    ]);\n    var _file_location_line, _file_location_column;\n    const open = (0, _useopenineditor.useOpenInEditor)({\n        file: file == null ? void 0 : file.fileName,\n        lineNumber: (_file_location_line = file == null ? void 0 : (_file_location = file.location) == null ? void 0 : _file_location.line) != null ? _file_location_line : 1,\n        column: (_file_location_column = file == null ? void 0 : (_file_location1 = file.location) == null ? void 0 : _file_location1.column) != null ? _file_location_column : 0\n    });\n    var _file_fileName, _file_location_line1, _file_location_column1;\n    const stackFrame = {\n        file: (_file_fileName = file == null ? void 0 : file.fileName) != null ? _file_fileName : null,\n        methodName: '',\n        arguments: [],\n        lineNumber: (_file_location_line1 = file == null ? void 0 : (_file_location2 = file.location) == null ? void 0 : _file_location2.line) != null ? _file_location_line1 : null,\n        column: (_file_location_column1 = file == null ? void 0 : (_file_location3 = file.location) == null ? void 0 : _file_location3.column) != null ? _file_location_column1 : null\n    };\n    const fileExtension = stackFrame == null ? void 0 : (_stackFrame_file = stackFrame.file) == null ? void 0 : _stackFrame_file.split('.').pop();\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-codeframe\": true,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                className: \"code-frame-header\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"code-frame-link\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            className: \"code-frame-icon\",\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_file.FileIcon, {\n                                lang: fileExtension\n                            })\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            \"data-text\": true,\n                            children: (0, _stackframe.getFrameSource)(stackFrame)\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                            \"aria-label\": \"Open in editor\",\n                            \"data-with-open-in-editor-link-source-file\": true,\n                            onClick: open,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                className: \"code-frame-icon\",\n                                \"data-icon\": \"right\",\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_external.ExternalIcon, {\n                                    width: 16,\n                                    height: 16\n                                })\n                            })\n                        })\n                    ]\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"pre\", {\n                className: \"code-frame-pre\",\n                children: [\n                    decoded.map((entry, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            style: {\n                                color: entry.fg ? \"var(--color-\" + entry.fg + \")\" : undefined,\n                                ...entry.decoration === 'bold' ? // above 600, hence a temporary fix is to use 500 for bold.\n                                {\n                                    fontWeight: 500\n                                } : entry.decoration === 'italic' ? {\n                                    fontStyle: 'italic'\n                                } : undefined\n                            },\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                                text: entry.content\n                            })\n                        }, \"terminal-entry-\" + index)),\n                    importTraceFiles.map((importTraceFile)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_editorlink.EditorLink, {\n                            isSourceFile: false,\n                            file: importTraceFile\n                        }, importTraceFile))\n                ]\n            })\n        ]\n    });\n};\n_s(Terminal, \"nkmao/TIox3Jie/+6JvWO3hTKPQ=\");\n_c = Terminal;\nconst TERMINAL_STYLES = \"\\n  [data-nextjs-terminal]::selection,\\n  [data-nextjs-terminal] *::selection {\\n    background-color: var(--color-ansi-selection);\\n  }\\n\\n  [data-nextjs-terminal] * {\\n    color: inherit;\\n    background-color: transparent;\\n    font-family: var(--font-stack-monospace);\\n  }\\n\\n  [data-nextjs-terminal] > div > p {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    cursor: pointer;\\n    margin: 0;\\n  }\\n  [data-nextjs-terminal] > div > p:hover {\\n    text-decoration: underline dotted;\\n  }\\n  [data-nextjs-terminal] div > pre {\\n    overflow: hidden;\\n    display: inline-block;\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=terminal.js.map\nvar _c;\n$RefreshReg$(_c, \"Terminal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/terminal.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/index.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/index.js ***!
  \*************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Toast: function() {\n        return _toast.Toast;\n    },\n    styles: function() {\n        return _styles.styles;\n    }\n});\nconst _styles = __webpack_require__(/*! ./styles */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/styles.js\");\nconst _toast = __webpack_require__(/*! ./toast */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/toast.js\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy90b2FzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFDU0EsS0FBSztlQUFMQSxPQUFBQSxLQUFLOztJQURMQyxNQUFNO2VBQU5BLFFBQUFBLE1BQU07OztvQ0FBUTttQ0FDRCIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXGNvbXBvbmVudHNcXHRvYXN0XFxpbmRleC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgc3R5bGVzIH0gZnJvbSAnLi9zdHlsZXMnXG5leHBvcnQgeyBUb2FzdCB9IGZyb20gJy4vdG9hc3QnXG4iXSwibmFtZXMiOlsiVG9hc3QiLCJzdHlsZXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/styles.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/styles.js ***!
  \**************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst styles = \"\\n  .nextjs-toast {\\n    position: fixed;\\n    bottom: 16px;\\n    left: 16px;\\n    max-width: 420px;\\n    z-index: 9000;\\n    box-shadow: 0px 16px 32px\\n      rgba(0, 0, 0, 0.25);\\n  }\\n\\n  @media (max-width: 440px) {\\n    .nextjs-toast {\\n      max-width: 90vw;\\n      left: 5vw;\\n    }\\n  }\\n\\n  .nextjs-toast-errors-parent {\\n    padding: 16px;\\n    border-radius: var(--rounded-4xl);\\n    font-weight: 500;\\n    color: var(--color-ansi-bright-white);\\n    background-color: var(--color-ansi-red);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy90b2FzdC9zdHlsZXMuanMiLCJtYXBwaW5ncyI6Ijs7OzswQ0EyQlNBOzs7ZUFBQUE7OztBQTNCVCxNQUFNQSxTQUFVIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcY29tcG9uZW50c1xcdG9hc3RcXHN0eWxlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzdHlsZXMgPSBgXG4gIC5uZXh0anMtdG9hc3Qge1xuICAgIHBvc2l0aW9uOiBmaXhlZDtcbiAgICBib3R0b206IDE2cHg7XG4gICAgbGVmdDogMTZweDtcbiAgICBtYXgtd2lkdGg6IDQyMHB4O1xuICAgIHotaW5kZXg6IDkwMDA7XG4gICAgYm94LXNoYWRvdzogMHB4IDE2cHggMzJweFxuICAgICAgcmdiYSgwLCAwLCAwLCAwLjI1KTtcbiAgfVxuXG4gIEBtZWRpYSAobWF4LXdpZHRoOiA0NDBweCkge1xuICAgIC5uZXh0anMtdG9hc3Qge1xuICAgICAgbWF4LXdpZHRoOiA5MHZ3O1xuICAgICAgbGVmdDogNXZ3O1xuICAgIH1cbiAgfVxuXG4gIC5uZXh0anMtdG9hc3QtZXJyb3JzLXBhcmVudCB7XG4gICAgcGFkZGluZzogMTZweDtcbiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1yb3VuZGVkLTR4bCk7XG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICBjb2xvcjogdmFyKC0tY29sb3ItYW5zaS1icmlnaHQtd2hpdGUpO1xuICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWNvbG9yLWFuc2ktcmVkKTtcbiAgfVxuYFxuXG5leHBvcnQgeyBzdHlsZXMgfVxuIl0sIm5hbWVzIjpbInN0eWxlcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/styles.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/toast.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/toast.js ***!
  \*************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Toast\", ({\n    enumerable: true,\n    get: function() {\n        return Toast;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _cx = __webpack_require__(/*! ../../utils/cx */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js\");\nconst Toast = function Toast(param) {\n    let { onClick, children, className, ...props } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        ...props,\n        onClick: (e)=>{\n            if (!e.target.closest('a')) {\n                e.preventDefault();\n            }\n            return onClick == null ? void 0 : onClick();\n        },\n        className: (0, _cx.cx)('nextjs-toast', className),\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n            \"data-nextjs-toast-wrapper\": true,\n            children: children\n        })\n    });\n};\n_c = Toast;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=toast.js.map\nvar _c;\n$RefreshReg$(_c, \"Toast\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy90b2FzdC90b2FzdC5qcyIsIm1hcHBpbmdzIjoiOzs7O3lDQVFhQTs7O2VBQUFBOzs7Ozs2RUFSVTtnQ0FDSjtBQU9aLGNBQW9DLFNBQVNBLE1BQU0sS0FLekQ7SUFMeUQsTUFDeERDLE9BQU8sRUFDUEMsUUFBUSxFQUNSQyxTQUFTLEVBQ1QsR0FBR0MsT0FDSixHQUx5RDtJQU14RCxxQkFDRSxxQkFBQ0MsT0FBQUE7UUFDRSxHQUFHRCxLQUFLO1FBQ1RILFNBQVMsQ0FBQ0s7WUFDUixJQUFJLENBQUVBLEVBQUVDLE1BQU0sQ0FBaUJDLE9BQU8sQ0FBQyxNQUFNO2dCQUMzQ0YsRUFBRUcsY0FBYztZQUNsQjtZQUNBLE9BQU9SLFdBQUFBLE9BQUFBLEtBQUFBLElBQUFBO1FBQ1Q7UUFDQUUsV0FBV08sQ0FBQUEsR0FBQUEsSUFBQUEsRUFBQUEsRUFBRyxnQkFBZ0JQO2tCQUU5QixtQ0FBQ0UsT0FBQUE7WUFBSU0sMkJBQXlCO3NCQUFFVDs7O0FBR3RDO0tBcEJhRiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXGNvbXBvbmVudHNcXHRvYXN0XFx0b2FzdC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBjeCB9IGZyb20gJy4uLy4uL3V0aWxzL2N4J1xuZXhwb3J0IHR5cGUgVG9hc3RQcm9wcyA9IFJlYWN0LkhUTUxQcm9wczxIVE1MRGl2RWxlbWVudD4gJiB7XG4gIGNoaWxkcmVuPzogUmVhY3QuUmVhY3ROb2RlXG4gIG9uQ2xpY2s/OiAoKSA9PiB2b2lkXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xufVxuXG5leHBvcnQgY29uc3QgVG9hc3Q6IFJlYWN0LkZDPFRvYXN0UHJvcHM+ID0gZnVuY3Rpb24gVG9hc3Qoe1xuICBvbkNsaWNrLFxuICBjaGlsZHJlbixcbiAgY2xhc3NOYW1lLFxuICAuLi5wcm9wc1xufSkge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIHsuLi5wcm9wc31cbiAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgIGlmICghKGUudGFyZ2V0IGFzIEhUTUxFbGVtZW50KS5jbG9zZXN0KCdhJykpIHtcbiAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gb25DbGljaz8uKClcbiAgICAgIH19XG4gICAgICBjbGFzc05hbWU9e2N4KCduZXh0anMtdG9hc3QnLCBjbGFzc05hbWUpfVxuICAgID5cbiAgICAgIDxkaXYgZGF0YS1uZXh0anMtdG9hc3Qtd3JhcHBlcj57Y2hpbGRyZW59PC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJUb2FzdCIsIm9uQ2xpY2siLCJjaGlsZHJlbiIsImNsYXNzTmFtZSIsInByb3BzIiwiZGl2IiwiZSIsInRhcmdldCIsImNsb3Nlc3QiLCJwcmV2ZW50RGVmYXVsdCIsImN4IiwiZGF0YS1uZXh0anMtdG9hc3Qtd3JhcHBlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/toast.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info.js ***!
  \***********************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    VersionStalenessInfo: function() {\n        return VersionStalenessInfo;\n    },\n    getStaleness: function() {\n        return getStaleness;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _cx = __webpack_require__(/*! ../../utils/cx */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js\");\nfunction VersionStalenessInfo(param) {\n    let { versionInfo, isTurbopack } = param;\n    const { staleness } = versionInfo;\n    let { text, indicatorClass, title } = getStaleness(versionInfo);\n    const shouldBeLink = staleness.startsWith('stale');\n    if (shouldBeLink) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"a\", {\n            className: (0, _cx.cx)('nextjs-container-build-error-version-status', 'dialog-exclude-closing-from-outside-click', isTurbopack && 'turbopack-border'),\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            href: \"https://nextjs.org/docs/messages/version-staleness\",\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(Eclipse, {\n                    className: (0, _cx.cx)('version-staleness-indicator', indicatorClass)\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                    \"data-nextjs-version-checker\": true,\n                    title: title,\n                    children: text\n                }),\n                isTurbopack && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                    className: \"turbopack-text\",\n                    children: \"Turbopack\"\n                })\n            ]\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n        className: \"nextjs-container-build-error-version-status dialog-exclude-closing-from-outside-click\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(Eclipse, {\n                className: (0, _cx.cx)('version-staleness-indicator', indicatorClass)\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                \"data-nextjs-version-checker\": true,\n                title: title,\n                children: text\n            }),\n            isTurbopack && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                className: \"turbopack-text\",\n                children: \"Turbopack\"\n            })\n        ]\n    });\n}\n_c = VersionStalenessInfo;\nfunction getStaleness(param) {\n    let { installed, staleness, expected } = param;\n    let text = '';\n    let title = '';\n    let indicatorClass = '';\n    const versionLabel = \"Next.js \" + installed;\n    switch(staleness){\n        case 'newer-than-npm':\n        case 'fresh':\n            text = versionLabel;\n            title = \"Latest available version is detected (\" + installed + \").\";\n            indicatorClass = 'fresh';\n            break;\n        case 'stale-patch':\n        case 'stale-minor':\n            text = \"\" + versionLabel + \" (stale)\";\n            title = \"There is a newer version (\" + expected + \") available, upgrade recommended! \";\n            indicatorClass = 'stale';\n            break;\n        case 'stale-major':\n            {\n                text = \"\" + versionLabel + \" (outdated)\";\n                title = \"An outdated version detected (latest is \" + expected + \"), upgrade is highly recommended!\";\n                indicatorClass = 'outdated';\n                break;\n            }\n        case 'stale-prerelease':\n            {\n                text = \"\" + versionLabel + \" (stale)\";\n                title = \"There is a newer canary version (\" + expected + \") available, please upgrade! \";\n                indicatorClass = 'stale';\n                break;\n            }\n        case 'unknown':\n            text = \"\" + versionLabel + \" (unknown)\";\n            title = 'No Next.js version data was found.';\n            indicatorClass = 'unknown';\n            break;\n        default:\n            break;\n    }\n    return {\n        text,\n        indicatorClass,\n        title\n    };\n}\nconst styles = \"\\n  .nextjs-container-build-error-version-status {\\n    -webkit-font-smoothing: antialiased;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    gap: 4px;\\n\\n    height: var(--size-26);\\n    padding: 6px 8px 6px 6px;\\n    background: var(--color-background-100);\\n    background-clip: padding-box;\\n    border: 1px solid var(--color-gray-alpha-400);\\n    box-shadow: var(--shadow-small);\\n    border-radius: var(--rounded-full);\\n\\n    color: var(--color-gray-900);\\n    font-size: var(--size-12);\\n    font-weight: 500;\\n    line-height: var(--size-16);\\n  }\\n\\n  a.nextjs-container-build-error-version-status {\\n    text-decoration: none;\\n    color: var(--color-gray-900);\\n\\n    &:hover {\\n      background: var(--color-gray-100);\\n    }\\n\\n    &:focus {\\n      outline: var(--focus-ring);\\n    }\\n  }\\n\\n  .version-staleness-indicator.fresh {\\n    fill: var(--color-green-800);\\n    stroke: var(--color-green-300);\\n  }\\n  .version-staleness-indicator.stale {\\n    fill: var(--color-amber-800);\\n    stroke: var(--color-amber-300);\\n  }\\n  .version-staleness-indicator.outdated {\\n    fill: var(--color-red-800);\\n    stroke: var(--color-red-300);\\n  }\\n  .version-staleness-indicator.unknown {\\n    fill: var(--color-gray-800);\\n    stroke: var(--color-gray-300);\\n  }\\n\\n  .nextjs-container-build-error-version-status > .turbopack-text {\\n    background: linear-gradient(\\n      to right,\\n      var(--color-turbopack-text-red) 0%,\\n      var(--color-turbopack-text-blue) 100%\\n    );\\n    background-clip: text;\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n  }\\n\";\nfunction Eclipse(param) {\n    let { className } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"14\",\n        height: \"14\",\n        viewBox: \"0 0 14 14\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"circle\", {\n            className: className,\n            cx: \"7\",\n            cy: \"7\",\n            r: \"5.5\",\n            strokeWidth: \"3\"\n        })\n    });\n}\n_c1 = Eclipse;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=version-staleness-info.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"VersionStalenessInfo\");\n$RefreshReg$(_c1, \"Eclipse\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info.js\n"));

/***/ })

});