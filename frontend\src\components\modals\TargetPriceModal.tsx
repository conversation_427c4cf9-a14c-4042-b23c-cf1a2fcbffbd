"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useTradingContext } from '@/contexts/TradingContext';

interface TargetPriceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSetTargetPrices: (prices: number[]) => void;
}

export function TargetPriceModal({ isOpen, onClose, onSetTargetPrices }: TargetPriceModalProps) {
  const [activeTab, setActiveTab] = useState('manual');
  const [priceInput, setPriceInput] = useState('');
  const { toast } = useToast();

  // Safely get trading context with fallback
  let tradingContext;
  try {
    tradingContext = useTradingContext();
  } catch (error) {
    console.warn('Trading context not available:', error);
    tradingContext = null;
  }

  // Auto generation settings
  const [targetCount, setTargetCount] = useState('8');
  const [priceRange, setPriceRange] = useState('5'); // percentage range - start with 5% as default
  const [distribution, setDistribution] = useState('even'); // even, fibonacci, exponential

  // Get current market price and slippage from context with safe fallbacks
  const currentMarketPrice = tradingContext?.currentMarketPrice || tradingContext?.state?.currentMarketPrice || 100000;
  const slippagePercent = tradingContext?.config?.slippagePercent || tradingContext?.state?.config?.slippagePercent || 0.2;
  const crypto1 = tradingContext?.config?.crypto1 || '';
  const crypto2 = tradingContext?.config?.crypto2 || '';

  // Check if crypto pair is selected
  const isCryptoPairSelected = crypto1 && crypto2;

  const generateAutomaticPrices = () => {
    const count = parseInt(targetCount);
    const range = parseFloat(priceRange);

    if (!count || count < 2 || count > 20 || !range || range <= 0) {
      return [];
    }

    const prices: number[] = [];
    const minPrice = currentMarketPrice * (1 - range / 100);
    const maxPrice = currentMarketPrice * (1 + range / 100);

    if (distribution === 'even') {
      // Even distribution
      for (let i = 0; i < count; i++) {
        const price = minPrice + (maxPrice - minPrice) * (i / (count - 1));
        prices.push(Math.round(price));
      }
    } else if (distribution === 'fibonacci') {
      // Fibonacci-like distribution (more targets near current price)
      const fibRatios = [0, 0.236, 0.382, 0.5, 0.618, 0.764, 0.854, 0.927, 1];
      for (let i = 0; i < count; i++) {
        const ratio = fibRatios[Math.min(i, fibRatios.length - 1)] || (i / (count - 1));
        const price = minPrice + (maxPrice - minPrice) * ratio;
        prices.push(Math.round(price));
      }
    } else if (distribution === 'exponential') {
      // Exponential distribution
      for (let i = 0; i < count; i++) {
        const ratio = Math.pow(i / (count - 1), 1.5);
        const price = minPrice + (maxPrice - minPrice) * ratio;
        prices.push(Math.round(price));
      }
    }

    // Ensure no overlap with slippage zones
    const minGap = currentMarketPrice * (slippagePercent * 3 / 100); // 3x slippage as minimum gap
    const sortedPrices = prices.sort((a, b) => a - b);
    const adjustedPrices: number[] = [];

    for (let i = 0; i < sortedPrices.length; i++) {
      let price = sortedPrices[i];

      // Ensure minimum gap from previous price
      if (adjustedPrices.length > 0) {
        const lastPrice = adjustedPrices[adjustedPrices.length - 1];
        if (price - lastPrice < minGap) {
          price = lastPrice + minGap;
        }
      }

      adjustedPrices.push(Math.round(price));
    }

    return adjustedPrices;
  };

  const handleAutoGenerate = () => {
    const generatedPrices = generateAutomaticPrices();
    setPriceInput(generatedPrices.join('\n'));
  };

  const validateSlippageOverlap = () => {
    const lines = priceInput.split('\n').filter(line => line.trim() !== '');
    const prices = lines.map(line => parseFloat(line.trim())).filter(p => !isNaN(p) && p > 0).sort((a, b) => a - b);

    if (prices.length < 2) return { hasOverlap: false, message: '' };

    const slippageAmount = currentMarketPrice * (slippagePercent / 100);

    for (let i = 0; i < prices.length - 1; i++) {
      const currentMax = prices[i] + slippageAmount;
      const nextMin = prices[i + 1] - slippageAmount;

      if (currentMax >= nextMin) {
        const minGap = slippageAmount * 2;
        const actualGap = prices[i + 1] - prices[i];
        return {
          hasOverlap: true,
          message: `Overlap detected between ${prices[i]} and ${prices[i + 1]}. Minimum gap needed: ${minGap.toFixed(0)}, actual gap: ${actualGap.toFixed(0)}`
        };
      }
    }

    return { hasOverlap: false, message: 'No slippage zone overlaps detected ✓' };
  };

  const handleSave = () => {
    const lines = priceInput.split('\n').map(line => line.trim()).filter(line => line !== '');
    const prices = lines.map(line => parseFloat(line)).filter(price => !isNaN(price) && price > 0);

    if (prices.length === 0 && lines.length > 0) {
      toast({
        title: "Invalid Input",
        description: "No valid prices found. Please enter numbers, one per line.",
        variant: "destructive",
      });
      return;
    }

    // Check for slippage overlaps
    const validation = validateSlippageOverlap();
    if (validation.hasOverlap) {
      toast({
        title: "Slippage Zone Overlap",
        description: validation.message,
        variant: "destructive",
      });
      return;
    }

    onSetTargetPrices(prices);
    toast({
      title: "Target Prices Updated",
      description: `${prices.length} target prices have been set.`,
    });
    setPriceInput(''); // Clear input after saving
    onClose();
  };

  const validation = validateSlippageOverlap();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl bg-card border-2 border-border">
        <DialogHeader>
          <DialogTitle className="text-primary">Set Target Prices</DialogTitle>
          <DialogDescription>
            {isCryptoPairSelected
              ? "Set target prices manually or generate them automatically with optimal spacing to avoid slippage zone overlaps."
              : "Please select both Crypto 1 and Crypto 2 before setting target prices."
            }
          </DialogDescription>
        </DialogHeader>

        {!isCryptoPairSelected ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">
              Please select both Crypto 1 ({crypto1 || 'Not selected'}) and Crypto 2 ({crypto2 || 'Not selected'}) in the Trading Configuration before setting target prices.
            </p>
            <DialogClose asChild>
              <Button variant="outline" className="btn-outline-neo">Close</Button>
            </DialogClose>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="manual">Manual Entry</TabsTrigger>
              <TabsTrigger value="automatic">Automatic Generation</TabsTrigger>
            </TabsList>

          <TabsContent value="manual" className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="target-prices-input" className="text-left">
                Target Prices
              </Label>
              <p className="text-sm text-muted-foreground">
                Paste target prices from Excel or enter manually, one price per line. Invalid entries will be ignored.
              </p>
              <Textarea
                id="target-prices-input"
                value={priceInput}
                onChange={(e) => setPriceInput(e.target.value)}
                placeholder="50000&#10;50500&#10;49800"
                className="min-h-[200px] bg-input border-2 border-border focus:border-primary font-mono"
              />
              {validation.message && (
                <p className={`text-sm ${validation.hasOverlap ? 'text-red-500' : 'text-green-500'}`}>
                  {validation.message}
                </p>
              )}
            </div>
          </TabsContent>

          <TabsContent value="automatic" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="targetCount">Number of Targets</Label>
                <Select value={targetCount} onValueChange={setTargetCount}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[4, 6, 8, 10, 12, 15, 20].map(num => (
                      <SelectItem key={num} value={num.toString()}>{num} targets</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="priceRange">Price Range (%)</Label>
                <Select value={priceRange} onValueChange={setPriceRange}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[2, 2.5, 3, 3.5, 4, 4.5, 5, 6, 7, 8, 10].map(range => (
                      <SelectItem key={range} value={range.toString()}>±{range}%</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="distribution">Distribution Pattern</Label>
              <Select value={distribution} onValueChange={setDistribution}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="even">Even Distribution</SelectItem>
                  <SelectItem value="fibonacci">Fibonacci (More near current price)</SelectItem>
                  <SelectItem value="exponential">Exponential (Wider spread)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="bg-muted p-3 rounded-md">
              <p className="text-sm">
                <strong>Current Market Price:</strong> ${currentMarketPrice.toLocaleString()}<br/>
                <strong>Slippage:</strong> ±{slippagePercent}% (${(currentMarketPrice * slippagePercent / 100).toFixed(0)})<br/>
                <strong>Range:</strong> ${(currentMarketPrice * (1 - parseFloat(priceRange) / 100)).toLocaleString()} - ${(currentMarketPrice * (1 + parseFloat(priceRange) / 100)).toLocaleString()}
              </p>
            </div>

            <Button onClick={handleAutoGenerate} className="w-full btn-neo">
              Generate {targetCount} Target Prices
            </Button>

            <div className="grid gap-2">
              <Label>Generated Prices (Preview)</Label>
              <Textarea
                value={priceInput}
                onChange={(e) => setPriceInput(e.target.value)}
                className="min-h-[150px] bg-input border-2 border-border focus:border-primary font-mono"
                placeholder="Click 'Generate' to create automatic target prices..."
              />
              {validation.message && (
                <p className={`text-sm ${validation.hasOverlap ? 'text-red-500' : 'text-green-500'}`}>
                  {validation.message}
                </p>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline" className="btn-outline-neo">Cancel</Button>
          </DialogClose>
          <Button type="button" onClick={handleSave} disabled={validation.hasOverlap} className="btn-neo">
            Save Prices
          </Button>
        </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
