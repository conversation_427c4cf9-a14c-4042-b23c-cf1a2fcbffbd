'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): State {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // Check if it's a chunk loading error and reload immediately
    const isChunkError = error.name === 'ChunkLoadError' ||
                        error.message.includes('Loading chunk') ||
                        error.message.includes('missing:') ||
                        error.message.includes('timeout');

    if (isChunkError) {
      console.log('Chunk loading error detected, reloading...');
      window.location.reload();
      return;
    }
  }

  render() {
    if (this.state.hasError) {
      // Simple error UI that reloads the page
      return (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: '#1f2937',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontFamily: 'system-ui, -apple-system, sans-serif'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{
              width: '40px',
              height: '40px',
              border: '4px solid #374151',
              borderTop: '4px solid #3b82f6',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              margin: '0 auto 20px'
            }}></div>
            <h2 style={{ margin: '0 0 10px', fontSize: '18px' }}>Loading...</h2>
            <p style={{ margin: 0, opacity: 0.7, fontSize: '14px' }}>Refreshing application</p>
            <style dangerouslySetInnerHTML={{
              __html: `
                @keyframes spin {
                  0% { transform: rotate(0deg); }
                  100% { transform: rotate(360deg); }
                }
              `
            }} />
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
