"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_a"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/assign-location.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/client/assign-location.js ***!
  \**********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"assignLocation\", ({\n    enumerable: true,\n    get: function() {\n        return assignLocation;\n    }\n}));\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nfunction assignLocation(location, url) {\n    if (location.startsWith('.')) {\n        const urlBase = url.origin + url.pathname;\n        return new URL(// new URL('./relative', 'https://example.com/subdir').href -> 'https://example.com/relative'\n        // new URL('./relative', 'https://example.com/subdir/').href -> 'https://example.com/subdir/relative'\n        (urlBase.endsWith('/') ? urlBase : urlBase + '/') + location);\n    }\n    return new URL((0, _addbasepath.addBasePath)(location), url.href);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=assign-location.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/assign-location.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router-announcer.js ***!
  \**************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AppRouterAnnouncer\", ({\n    enumerable: true,\n    get: function() {\n        return AppRouterAnnouncer;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _reactdom = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\nconst ANNOUNCER_TYPE = 'next-route-announcer';\nconst ANNOUNCER_ID = '__next-route-announcer__';\nfunction getAnnouncerNode() {\n    var _existingAnnouncer_shadowRoot;\n    const existingAnnouncer = document.getElementsByName(ANNOUNCER_TYPE)[0];\n    if (existingAnnouncer == null ? void 0 : (_existingAnnouncer_shadowRoot = existingAnnouncer.shadowRoot) == null ? void 0 : _existingAnnouncer_shadowRoot.childNodes[0]) {\n        return existingAnnouncer.shadowRoot.childNodes[0];\n    } else {\n        const container = document.createElement(ANNOUNCER_TYPE);\n        container.style.cssText = 'position:absolute';\n        const announcer = document.createElement('div');\n        announcer.ariaLive = 'assertive';\n        announcer.id = ANNOUNCER_ID;\n        announcer.role = 'alert';\n        announcer.style.cssText = 'position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal';\n        // Use shadow DOM here to avoid any potential CSS bleed\n        const shadow = container.attachShadow({\n            mode: 'open'\n        });\n        shadow.appendChild(announcer);\n        document.body.appendChild(container);\n        return announcer;\n    }\n}\nfunction AppRouterAnnouncer(param) {\n    let { tree } = param;\n    const [portalNode, setPortalNode] = (0, _react.useState)(null);\n    (0, _react.useEffect)(()=>{\n        const announcer = getAnnouncerNode();\n        setPortalNode(announcer);\n        return ()=>{\n            const container = document.getElementsByTagName(ANNOUNCER_TYPE)[0];\n            if (container == null ? void 0 : container.isConnected) {\n                document.body.removeChild(container);\n            }\n        };\n    }, []);\n    const [routeAnnouncement, setRouteAnnouncement] = (0, _react.useState)('');\n    const previousTitle = (0, _react.useRef)(undefined);\n    (0, _react.useEffect)(()=>{\n        let currentTitle = '';\n        if (document.title) {\n            currentTitle = document.title;\n        } else {\n            const pageHeader = document.querySelector('h1');\n            if (pageHeader) {\n                currentTitle = pageHeader.innerText || pageHeader.textContent || '';\n            }\n        }\n        // Only announce the title change, but not for the first load because screen\n        // readers do that automatically.\n        if (previousTitle.current !== undefined && previousTitle.current !== currentTitle) {\n            setRouteAnnouncement(currentTitle);\n        }\n        previousTitle.current = currentTitle;\n    }, [\n        tree\n    ]);\n    return portalNode ? /*#__PURE__*/ (0, _reactdom.createPortal)(routeAnnouncement, portalNode) : null;\n}\n_c = AppRouterAnnouncer;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-announcer.js.map\nvar _c;\n$RefreshReg$(_c, \"AppRouterAnnouncer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router-headers.js ***!
  \************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_HEADER: function() {\n        return ACTION_HEADER;\n    },\n    FLIGHT_HEADERS: function() {\n        return FLIGHT_HEADERS;\n    },\n    NEXT_DID_POSTPONE_HEADER: function() {\n        return NEXT_DID_POSTPONE_HEADER;\n    },\n    NEXT_HMR_REFRESH_HEADER: function() {\n        return NEXT_HMR_REFRESH_HEADER;\n    },\n    NEXT_IS_PRERENDER_HEADER: function() {\n        return NEXT_IS_PRERENDER_HEADER;\n    },\n    NEXT_REWRITTEN_PATH_HEADER: function() {\n        return NEXT_REWRITTEN_PATH_HEADER;\n    },\n    NEXT_REWRITTEN_QUERY_HEADER: function() {\n        return NEXT_REWRITTEN_QUERY_HEADER;\n    },\n    NEXT_ROUTER_PREFETCH_HEADER: function() {\n        return NEXT_ROUTER_PREFETCH_HEADER;\n    },\n    NEXT_ROUTER_SEGMENT_PREFETCH_HEADER: function() {\n        return NEXT_ROUTER_SEGMENT_PREFETCH_HEADER;\n    },\n    NEXT_ROUTER_STALE_TIME_HEADER: function() {\n        return NEXT_ROUTER_STALE_TIME_HEADER;\n    },\n    NEXT_ROUTER_STATE_TREE_HEADER: function() {\n        return NEXT_ROUTER_STATE_TREE_HEADER;\n    },\n    NEXT_RSC_UNION_QUERY: function() {\n        return NEXT_RSC_UNION_QUERY;\n    },\n    NEXT_URL: function() {\n        return NEXT_URL;\n    },\n    RSC_CONTENT_TYPE_HEADER: function() {\n        return RSC_CONTENT_TYPE_HEADER;\n    },\n    RSC_HEADER: function() {\n        return RSC_HEADER;\n    }\n});\nconst RSC_HEADER = 'RSC';\nconst ACTION_HEADER = 'Next-Action';\nconst NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree';\nconst NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch';\nconst NEXT_ROUTER_SEGMENT_PREFETCH_HEADER = 'Next-Router-Segment-Prefetch';\nconst NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh';\nconst NEXT_URL = 'Next-Url';\nconst RSC_CONTENT_TYPE_HEADER = 'text/x-component';\nconst FLIGHT_HEADERS = [\n    RSC_HEADER,\n    NEXT_ROUTER_STATE_TREE_HEADER,\n    NEXT_ROUTER_PREFETCH_HEADER,\n    NEXT_HMR_REFRESH_HEADER,\n    NEXT_ROUTER_SEGMENT_PREFETCH_HEADER\n];\nconst NEXT_RSC_UNION_QUERY = '_rsc';\nconst NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time';\nconst NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed';\nconst NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path';\nconst NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query';\nconst NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender';\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-headers.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci1oZWFkZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQUNhQSxhQUFhO2VBQWJBOztJQWdCQUMsY0FBYztlQUFkQTs7SUFXQUMsd0JBQXdCO2VBQXhCQTs7SUFmQUMsdUJBQXVCO2VBQXZCQTs7SUFrQkFDLHdCQUF3QjtlQUF4QkE7O0lBRkFDLDBCQUEwQjtlQUExQkE7O0lBQ0FDLDJCQUEyQjtlQUEzQkE7O0lBeEJBQywyQkFBMkI7ZUFBM0JBOztJQUtBQyxtQ0FBbUM7ZUFBbkNBOztJQWdCQUMsNkJBQTZCO2VBQTdCQTs7SUF0QkFDLDZCQUE2QjtlQUE3QkE7O0lBb0JBQyxvQkFBb0I7ZUFBcEJBOztJQVhBQyxRQUFRO2VBQVJBOztJQUNBQyx1QkFBdUI7ZUFBdkJBOztJQWZBQyxVQUFVO2VBQVZBOzs7QUFBTixNQUFNQSxhQUFhO0FBQ25CLE1BQU1kLGdCQUFnQjtBQUl0QixNQUFNVSxnQ0FBZ0M7QUFDdEMsTUFBTUgsOEJBQThCO0FBS3BDLE1BQU1DLHNDQUNYO0FBQ0ssTUFBTUwsMEJBQTBCO0FBQ2hDLE1BQU1TLFdBQVc7QUFDakIsTUFBTUMsMEJBQTBCO0FBRWhDLE1BQU1aLGlCQUFpQjtJQUM1QmE7SUFDQUo7SUFDQUg7SUFDQUo7SUFDQUs7Q0FDRDtBQUVNLE1BQU1HLHVCQUF1QjtBQUU3QixNQUFNRixnQ0FBZ0M7QUFDdEMsTUFBTVAsMkJBQTJCO0FBQ2pDLE1BQU1HLDZCQUE2QjtBQUNuQyxNQUFNQyw4QkFBOEI7QUFDcEMsTUFBTUYsMkJBQTJCIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXGFwcC1yb3V0ZXItaGVhZGVycy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgUlNDX0hFQURFUiA9ICdSU0MnIGFzIGNvbnN0XG5leHBvcnQgY29uc3QgQUNUSU9OX0hFQURFUiA9ICdOZXh0LUFjdGlvbicgYXMgY29uc3Rcbi8vIFRPRE86IEluc3RlYWQgb2Ygc2VuZGluZyB0aGUgZnVsbCByb3V0ZXIgc3RhdGUsIHdlIG9ubHkgbmVlZCB0byBzZW5kIHRoZVxuLy8gc2VnbWVudCBwYXRoLiBTYXZlcyBieXRlcy4gVGhlbiB3ZSBjb3VsZCBhbHNvIHVzZSB0aGlzIGZpZWxkIGZvciBzZWdtZW50XG4vLyBwcmVmZXRjaGVzLCB3aGljaCBhbHNvIG5lZWQgdG8gc3BlY2lmeSBhIHBhcnRpY3VsYXIgc2VnbWVudC5cbmV4cG9ydCBjb25zdCBORVhUX1JPVVRFUl9TVEFURV9UUkVFX0hFQURFUiA9ICdOZXh0LVJvdXRlci1TdGF0ZS1UcmVlJyBhcyBjb25zdFxuZXhwb3J0IGNvbnN0IE5FWFRfUk9VVEVSX1BSRUZFVENIX0hFQURFUiA9ICdOZXh0LVJvdXRlci1QcmVmZXRjaCcgYXMgY29uc3Rcbi8vIFRoaXMgY29udGFpbnMgdGhlIHBhdGggdG8gdGhlIHNlZ21lbnQgYmVpbmcgcHJlZmV0Y2hlZC5cbi8vIFRPRE86IElmIHdlIGNoYW5nZSBOZXh0LVJvdXRlci1TdGF0ZS1UcmVlIHRvIGJlIGEgc2VnbWVudCBwYXRoLCB3ZSBjYW4gdXNlXG4vLyB0aGF0IGluc3RlYWQuIFRoZW4gTmV4dC1Sb3V0ZXItUHJlZmV0Y2ggYW5kIE5leHQtUm91dGVyLVNlZ21lbnQtUHJlZmV0Y2ggY2FuXG4vLyBiZSBtZXJnZWQgaW50byBhIHNpbmdsZSBlbnVtLlxuZXhwb3J0IGNvbnN0IE5FWFRfUk9VVEVSX1NFR01FTlRfUFJFRkVUQ0hfSEVBREVSID1cbiAgJ05leHQtUm91dGVyLVNlZ21lbnQtUHJlZmV0Y2gnIGFzIGNvbnN0XG5leHBvcnQgY29uc3QgTkVYVF9ITVJfUkVGUkVTSF9IRUFERVIgPSAnTmV4dC1ITVItUmVmcmVzaCcgYXMgY29uc3RcbmV4cG9ydCBjb25zdCBORVhUX1VSTCA9ICdOZXh0LVVybCcgYXMgY29uc3RcbmV4cG9ydCBjb25zdCBSU0NfQ09OVEVOVF9UWVBFX0hFQURFUiA9ICd0ZXh0L3gtY29tcG9uZW50JyBhcyBjb25zdFxuXG5leHBvcnQgY29uc3QgRkxJR0hUX0hFQURFUlMgPSBbXG4gIFJTQ19IRUFERVIsXG4gIE5FWFRfUk9VVEVSX1NUQVRFX1RSRUVfSEVBREVSLFxuICBORVhUX1JPVVRFUl9QUkVGRVRDSF9IRUFERVIsXG4gIE5FWFRfSE1SX1JFRlJFU0hfSEVBREVSLFxuICBORVhUX1JPVVRFUl9TRUdNRU5UX1BSRUZFVENIX0hFQURFUixcbl0gYXMgY29uc3RcblxuZXhwb3J0IGNvbnN0IE5FWFRfUlNDX1VOSU9OX1FVRVJZID0gJ19yc2MnIGFzIGNvbnN0XG5cbmV4cG9ydCBjb25zdCBORVhUX1JPVVRFUl9TVEFMRV9USU1FX0hFQURFUiA9ICd4LW5leHRqcy1zdGFsZS10aW1lJyBhcyBjb25zdFxuZXhwb3J0IGNvbnN0IE5FWFRfRElEX1BPU1RQT05FX0hFQURFUiA9ICd4LW5leHRqcy1wb3N0cG9uZWQnIGFzIGNvbnN0XG5leHBvcnQgY29uc3QgTkVYVF9SRVdSSVRURU5fUEFUSF9IRUFERVIgPSAneC1uZXh0anMtcmV3cml0dGVuLXBhdGgnIGFzIGNvbnN0XG5leHBvcnQgY29uc3QgTkVYVF9SRVdSSVRURU5fUVVFUllfSEVBREVSID0gJ3gtbmV4dGpzLXJld3JpdHRlbi1xdWVyeScgYXMgY29uc3RcbmV4cG9ydCBjb25zdCBORVhUX0lTX1BSRVJFTkRFUl9IRUFERVIgPSAneC1uZXh0anMtcHJlcmVuZGVyJyBhcyBjb25zdFxuIl0sIm5hbWVzIjpbIkFDVElPTl9IRUFERVIiLCJGTElHSFRfSEVBREVSUyIsIk5FWFRfRElEX1BPU1RQT05FX0hFQURFUiIsIk5FWFRfSE1SX1JFRlJFU0hfSEVBREVSIiwiTkVYVF9JU19QUkVSRU5ERVJfSEVBREVSIiwiTkVYVF9SRVdSSVRURU5fUEFUSF9IRUFERVIiLCJORVhUX1JFV1JJVFRFTl9RVUVSWV9IRUFERVIiLCJORVhUX1JPVVRFUl9QUkVGRVRDSF9IRUFERVIiLCJORVhUX1JPVVRFUl9TRUdNRU5UX1BSRUZFVENIX0hFQURFUiIsIk5FWFRfUk9VVEVSX1NUQUxFX1RJTUVfSEVBREVSIiwiTkVYVF9ST1VURVJfU1RBVEVfVFJFRV9IRUFERVIiLCJORVhUX1JTQ19VTklPTl9RVUVSWSIsIk5FWFRfVVJMIiwiUlNDX0NPTlRFTlRfVFlQRV9IRUFERVIiLCJSU0NfSEVBREVSIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createEmptyCacheNode: function() {\n        return createEmptyCacheNode;\n    },\n    createPrefetchURL: function() {\n        return createPrefetchURL;\n    },\n    default: function() {\n        return AppRouter;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _createhreffromurl = __webpack_require__(/*! ./router-reducer/create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _usereducer = __webpack_require__(/*! ./use-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-reducer.js\");\nconst _errorboundary = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\"));\nconst _isbot = __webpack_require__(/*! ../../shared/lib/router/utils/is-bot */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuterannouncer = __webpack_require__(/*! ./app-router-announcer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _findheadincache = __webpack_require__(/*! ./router-reducer/reducers/find-head-in-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _removebasepath = __webpack_require__(/*! ../remove-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _computechangedpath = __webpack_require__(/*! ./router-reducer/compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _navfailurehandler = __webpack_require__(/*! ./nav-failure-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/nav-failure-handler.js\");\nconst _appcallserver = __webpack_require__(/*! ../app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nconst _redirect = __webpack_require__(/*! ./redirect */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect.js\");\nconst _redirecterror = __webpack_require__(/*! ./redirect-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-error.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./router-reducer/reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst globalMutable = {};\nfunction isExternalURL(url) {\n    return url.origin !== window.location.origin;\n}\nfunction createPrefetchURL(href) {\n    // Don't prefetch for bots as they don't navigate.\n    if ((0, _isbot.isBot)(window.navigator.userAgent)) {\n        return null;\n    }\n    let url;\n    try {\n        url = new URL((0, _addbasepath.addBasePath)(href), window.location.href);\n    } catch (_) {\n        // TODO: Does this need to throw or can we just console.error instead? Does\n        // anyone rely on this throwing? (Seems unlikely.)\n        throw Object.defineProperty(new Error(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E234\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // Don't prefetch during development (improves compilation performance)\n    if (true) {\n        return null;\n    }\n    // External urls can't be prefetched in the same way.\n    if (isExternalURL(url)) {\n        return null;\n    }\n    return url;\n}\nfunction HistoryUpdater(param) {\n    let { appRouterState } = param;\n    (0, _react.useInsertionEffect)(()=>{\n        if (false) {}\n        const { tree, pushRef, canonicalUrl } = appRouterState;\n        const historyState = {\n            ...pushRef.preserveCustomHistoryState ? window.history.state : {},\n            // Identifier is shortened intentionally.\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            // __N is used to identify if the history entry can be handled by the old router.\n            __NA: true,\n            __PRIVATE_NEXTJS_INTERNALS_TREE: tree\n        };\n        if (pushRef.pendingPush && // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n        // This mirrors the browser behavior for normal navigation.\n        (0, _createhreffromurl.createHrefFromUrl)(new URL(window.location.href)) !== canonicalUrl) {\n            // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n            pushRef.pendingPush = false;\n            window.history.pushState(historyState, '', canonicalUrl);\n        } else {\n            window.history.replaceState(historyState, '', canonicalUrl);\n        }\n    }, [\n        appRouterState\n    ]);\n    (0, _react.useEffect)(()=>{\n        // The Next-Url and the base tree may affect the result of a prefetch\n        // task. Re-prefetch all visible links with the updated values. In most\n        // cases, this will not result in any new network requests, only if\n        // the prefetch result actually varies on one of these inputs.\n        if (false) {}\n    }, [\n        appRouterState.nextUrl,\n        appRouterState.tree\n    ]);\n    return null;\n}\n_c = HistoryUpdater;\nfunction createEmptyCacheNode() {\n    return {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null\n    };\n}\n/**\n * Server response that only patches the cache and tree.\n */ function useChangeByServerResponse(dispatch) {\n    return (0, _react.useCallback)((param)=>{\n        let { previousTree, serverResponse } = param;\n        (0, _react.startTransition)(()=>{\n            dispatch({\n                type: _routerreducertypes.ACTION_SERVER_PATCH,\n                previousTree,\n                serverResponse\n            });\n        });\n    }, [\n        dispatch\n    ]);\n}\nfunction useNavigate(dispatch) {\n    return (0, _react.useCallback)((href, navigateType, shouldScroll)=>{\n        const url = new URL((0, _addbasepath.addBasePath)(href), location.href);\n        if (false) {}\n        return dispatch({\n            type: _routerreducertypes.ACTION_NAVIGATE,\n            url,\n            isExternalUrl: isExternalURL(url),\n            locationSearch: location.search,\n            shouldScroll: shouldScroll != null ? shouldScroll : true,\n            navigateType,\n            allowAliasing: true\n        });\n    }, [\n        dispatch\n    ]);\n}\nfunction copyNextJsInternalHistoryState(data) {\n    if (data == null) data = {};\n    const currentState = window.history.state;\n    const __NA = currentState == null ? void 0 : currentState.__NA;\n    if (__NA) {\n        data.__NA = __NA;\n    }\n    const __PRIVATE_NEXTJS_INTERNALS_TREE = currentState == null ? void 0 : currentState.__PRIVATE_NEXTJS_INTERNALS_TREE;\n    if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n        data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE;\n    }\n    return data;\n}\nfunction Head(param) {\n    let { headCacheNode } = param;\n    // If this segment has a `prefetchHead`, it's the statically prefetched data.\n    // We should use that on initial render instead of `head`. Then we'll switch\n    // to `head` when the dynamic response streams in.\n    const head = headCacheNode !== null ? headCacheNode.head : null;\n    const prefetchHead = headCacheNode !== null ? headCacheNode.prefetchHead : null;\n    // If no prefetch data is available, then we go straight to rendering `head`.\n    const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    return (0, _react.useDeferredValue)(head, resolvedPrefetchRsc);\n}\n_c1 = Head;\n/**\n * The global router that wraps the application components.\n */ function Router(param) {\n    _s();\n    let { actionQueue, assetPrefix, globalError } = param;\n    const [state, dispatch] = (0, _usereducer.useReducer)(actionQueue);\n    const { canonicalUrl } = (0, _usereducer.useUnwrapState)(state);\n    // Add memoized pathname/query for useSearchParams and usePathname.\n    const { searchParams, pathname } = (0, _react.useMemo)(()=>{\n        const url = new URL(canonicalUrl,  false ? 0 : window.location.href);\n        return {\n            // This is turned into a readonly class in `useSearchParams`\n            searchParams: url.searchParams,\n            pathname: (0, _hasbasepath.hasBasePath)(url.pathname) ? (0, _removebasepath.removeBasePath)(url.pathname) : url.pathname\n        };\n    }, [\n        canonicalUrl\n    ]);\n    const changeByServerResponse = useChangeByServerResponse(dispatch);\n    const navigate = useNavigate(dispatch);\n    (0, _appcallserver.useServerActionDispatcher)(dispatch);\n    /**\n   * The app router that is exposed through `useRouter`. It's only concerned with dispatching actions to the reducer, does not hold state.\n   */ const appRouter = (0, _react.useMemo)(()=>{\n        const routerInstance = {\n            back: ()=>window.history.back(),\n            forward: ()=>window.history.forward(),\n            prefetch:  false ? // cache. So we don't need to dispatch an action.\n            0 : (href, options)=>{\n                // Use the old prefetch implementation.\n                const url = createPrefetchURL(href);\n                if (url !== null) {\n                    var _options_kind;\n                    // The prefetch reducer doesn't actually update any state or\n                    // trigger a rerender. It just writes to a mutable cache. So we\n                    // shouldn't bother calling setState/dispatch; we can just re-run\n                    // the reducer directly using the current state.\n                    // TODO: Refactor this away from a \"reducer\" so it's\n                    // less confusing.\n                    (0, _prefetchreducer.prefetchReducer)(actionQueue.state, {\n                        type: _routerreducertypes.ACTION_PREFETCH,\n                        url,\n                        kind: (_options_kind = options == null ? void 0 : options.kind) != null ? _options_kind : _routerreducertypes.PrefetchKind.FULL\n                    });\n                }\n            },\n            replace: (href, options)=>{\n                if (options === void 0) options = {};\n                (0, _react.startTransition)(()=>{\n                    var _options_scroll;\n                    navigate(href, 'replace', (_options_scroll = options.scroll) != null ? _options_scroll : true);\n                });\n            },\n            push: (href, options)=>{\n                if (options === void 0) options = {};\n                (0, _react.startTransition)(()=>{\n                    var _options_scroll;\n                    navigate(href, 'push', (_options_scroll = options.scroll) != null ? _options_scroll : true);\n                });\n            },\n            refresh: ()=>{\n                (0, _react.startTransition)(()=>{\n                    dispatch({\n                        type: _routerreducertypes.ACTION_REFRESH,\n                        origin: window.location.origin\n                    });\n                });\n            },\n            hmrRefresh: ()=>{\n                if (false) {} else {\n                    (0, _react.startTransition)(()=>{\n                        dispatch({\n                            type: _routerreducertypes.ACTION_HMR_REFRESH,\n                            origin: window.location.origin\n                        });\n                    });\n                }\n            }\n        };\n        return routerInstance;\n    }, [\n        actionQueue,\n        dispatch,\n        navigate\n    ]);\n    (0, _react.useEffect)(()=>{\n        // Exists for debugging purposes. Don't use in application code.\n        if (window.next) {\n            window.next.router = appRouter;\n        }\n    }, [\n        appRouter\n    ]);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const { cache, prefetchCache, tree } = (0, _usereducer.useUnwrapState)(state);\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            // Add `window.nd` for debugging purposes.\n            // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n            // @ts-ignore this is for debugging\n            window.nd = {\n                router: appRouter,\n                cache,\n                prefetchCache,\n                tree\n            };\n        }, [\n            appRouter,\n            cache,\n            prefetchCache,\n            tree\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        // If the app is restored from bfcache, it's possible that\n        // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n        // would trigger the mpa navigation logic again from the lines below.\n        // This will restore the router to the initial state in the event that the app is restored from bfcache.\n        function handlePageShow(event) {\n            var _window_history_state;\n            if (!event.persisted || !((_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE)) {\n                return;\n            }\n            // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n            // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n            // of the last MPA navigation.\n            globalMutable.pendingMpaPath = undefined;\n            dispatch({\n                type: _routerreducertypes.ACTION_RESTORE,\n                url: new URL(window.location.href),\n                tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n            });\n        }\n        window.addEventListener('pageshow', handlePageShow);\n        return ()=>{\n            window.removeEventListener('pageshow', handlePageShow);\n        };\n    }, [\n        dispatch\n    ]);\n    (0, _react.useEffect)(()=>{\n        // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n        // are caught and handled by the router.\n        function handleUnhandledRedirect(event) {\n            const error = 'reason' in event ? event.reason : event.error;\n            if ((0, _redirecterror.isRedirectError)(error)) {\n                event.preventDefault();\n                const url = (0, _redirect.getURLFromRedirectError)(error);\n                const redirectType = (0, _redirect.getRedirectTypeFromError)(error);\n                if (redirectType === _redirecterror.RedirectType.push) {\n                    appRouter.push(url, {});\n                } else {\n                    appRouter.replace(url, {});\n                }\n            }\n        }\n        window.addEventListener('error', handleUnhandledRedirect);\n        window.addEventListener('unhandledrejection', handleUnhandledRedirect);\n        return ()=>{\n            window.removeEventListener('error', handleUnhandledRedirect);\n            window.removeEventListener('unhandledrejection', handleUnhandledRedirect);\n        };\n    }, [\n        appRouter\n    ]);\n    // When mpaNavigation flag is set do a hard navigation to the new url.\n    // Infinitely suspend because we don't actually want to rerender any child\n    // components with the new URL and any entangled state updates shouldn't\n    // commit either (eg: useTransition isPending should stay true until the page\n    // unloads).\n    //\n    // This is a side effect in render. Don't try this at home, kids. It's\n    // probably safe because we know this is a singleton component and it's never\n    // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n    // but that's... fine?)\n    const { pushRef } = (0, _usereducer.useUnwrapState)(state);\n    if (pushRef.mpaNavigation) {\n        // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n        if (globalMutable.pendingMpaPath !== canonicalUrl) {\n            const location1 = window.location;\n            if (pushRef.pendingPush) {\n                location1.assign(canonicalUrl);\n            } else {\n                location1.replace(canonicalUrl);\n            }\n            globalMutable.pendingMpaPath = canonicalUrl;\n        }\n        // TODO-APP: Should we listen to navigateerror here to catch failed\n        // navigations somehow? And should we call window.stop() if a SPA navigation\n        // should interrupt an MPA one?\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    (0, _react.useEffect)(()=>{\n        const originalPushState = window.history.pushState.bind(window.history);\n        const originalReplaceState = window.history.replaceState.bind(window.history);\n        // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n        const applyUrlFromHistoryPushReplace = (url)=>{\n            var _window_history_state;\n            const href = window.location.href;\n            const tree = (_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE;\n            (0, _react.startTransition)(()=>{\n                dispatch({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(url != null ? url : href, href),\n                    tree\n                });\n            });\n        };\n        /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.pushState = function pushState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalPushState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalPushState(data, _unused, url);\n        };\n        /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.replaceState = function replaceState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalReplaceState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalReplaceState(data, _unused, url);\n        };\n        /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */ const onPopState = (event)=>{\n            if (!event.state) {\n                // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n                return;\n            }\n            // This case happens when the history entry was pushed by the `pages` router.\n            if (!event.state.__NA) {\n                window.location.reload();\n                return;\n            }\n            // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n            // Without startTransition works if the cache is there for this path\n            (0, _react.startTransition)(()=>{\n                dispatch({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(window.location.href),\n                    tree: event.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n                });\n            });\n        };\n        // Register popstate event to call onPopstate.\n        window.addEventListener('popstate', onPopState);\n        return ()=>{\n            window.history.pushState = originalPushState;\n            window.history.replaceState = originalReplaceState;\n            window.removeEventListener('popstate', onPopState);\n        };\n    }, [\n        dispatch\n    ]);\n    const { cache, tree, nextUrl, focusAndScrollRef } = (0, _usereducer.useUnwrapState)(state);\n    const matchingHead = (0, _react.useMemo)(()=>{\n        return (0, _findheadincache.findHeadInCache)(cache, tree[1]);\n    }, [\n        cache,\n        tree\n    ]);\n    // Add memoized pathParams for useParams.\n    const pathParams = (0, _react.useMemo)(()=>{\n        return (0, _computechangedpath.getSelectedParams)(tree);\n    }, [\n        tree\n    ]);\n    const layoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            parentTree: tree,\n            parentCacheNode: cache,\n            parentSegmentPath: null,\n            // Root node always has `url`\n            // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n            url: canonicalUrl\n        };\n    }, [\n        tree,\n        cache,\n        canonicalUrl\n    ]);\n    const globalLayoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            changeByServerResponse,\n            tree,\n            focusAndScrollRef,\n            nextUrl\n        };\n    }, [\n        changeByServerResponse,\n        tree,\n        focusAndScrollRef,\n        nextUrl\n    ]);\n    let head;\n    if (matchingHead !== null) {\n        // The head is wrapped in an extra component so we can use\n        // `useDeferredValue` to swap between the prefetched and final versions of\n        // the head. (This is what LayoutRouter does for segment data, too.)\n        //\n        // The `key` is used to remount the component whenever the head moves to\n        // a different segment.\n        const [headCacheNode, headKey] = matchingHead;\n        head = /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n            headCacheNode: headCacheNode\n        }, headKey);\n    } else {\n        head = null;\n    }\n    let content = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_redirectboundary.RedirectBoundary, {\n        children: [\n            head,\n            cache.rsc,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuterannouncer.AppRouterAnnouncer, {\n                tree: tree\n            })\n        ]\n    });\n    if (true) {\n        // In development, we apply few error boundaries and hot-reloader:\n        // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n        // - HotReloader:\n        //  - hot-reload the app when the code changes\n        //  - render dev overlay\n        //  - catch runtime errors and display global-error when necessary\n        if (true) {\n            const { DevRootHTTPAccessFallbackBoundary } = __webpack_require__(/*! ./dev-root-http-access-fallback-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js\");\n            content = /*#__PURE__*/ (0, _jsxruntime.jsx)(DevRootHTTPAccessFallbackBoundary, {\n                children: content\n            });\n        }\n        const HotReloader = (__webpack_require__(/*! ./react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\")[\"default\"]);\n        content = /*#__PURE__*/ (0, _jsxruntime.jsx)(HotReloader, {\n            assetPrefix: assetPrefix,\n            globalError: globalError,\n            children: content\n        });\n    } else {}\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(HistoryUpdater, {\n                appRouterState: (0, _usereducer.useUnwrapState)(state)\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(RuntimeStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                value: pathParams,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathnameContext.Provider, {\n                    value: pathname,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                        value: searchParams,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.GlobalLayoutRouterContext.Provider, {\n                            value: globalLayoutRouterContext,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n                                value: appRouter,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n                                    value: layoutRouterContext,\n                                    children: content\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        ]\n    });\n}\n_s(Router, \"bU8t8nCPb2ycaFr1siwKA2Gych0=\", false, function() {\n    return [\n        useChangeByServerResponse,\n        useNavigate\n    ];\n});\n_c2 = Router;\nfunction AppRouter(param) {\n    let { actionQueue, globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles], assetPrefix } = param;\n    (0, _navfailurehandler.useNavFailureHandler)();\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n        // At the very top level, use the default GlobalError component as the final fallback.\n        // When the app router itself fails, which means the framework itself fails, we show the default error.\n        errorComponent: _errorboundary.default,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Router, {\n            actionQueue: actionQueue,\n            assetPrefix: assetPrefix,\n            globalError: [\n                globalErrorComponent,\n                globalErrorStyles\n            ]\n        })\n    });\n}\n_c3 = AppRouter;\nconst runtimeStyles = new Set();\nlet runtimeStyleChanged = new Set();\nglobalThis._N_E_STYLE_LOAD = function(href) {\n    let len = runtimeStyles.size;\n    runtimeStyles.add(href);\n    if (runtimeStyles.size !== len) {\n        runtimeStyleChanged.forEach((cb)=>cb());\n    }\n    // TODO figure out how to get a promise here\n    // But maybe it's not necessary as react would block rendering until it's loaded\n    return Promise.resolve();\n};\nfunction RuntimeStyles() {\n    _s1();\n    const [, forceUpdate] = _react.default.useState(0);\n    const renderedStylesSize = runtimeStyles.size;\n    (0, _react.useEffect)(()=>{\n        const changed = ()=>forceUpdate((c)=>c + 1);\n        runtimeStyleChanged.add(changed);\n        if (renderedStylesSize !== runtimeStyles.size) {\n            changed();\n        }\n        return ()=>{\n            runtimeStyleChanged.delete(changed);\n        };\n    }, [\n        renderedStylesSize,\n        forceUpdate\n    ]);\n    const dplId =  false ? 0 : '';\n    return [\n        ...runtimeStyles\n    ].map((href, i)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            rel: \"stylesheet\",\n            href: \"\" + href + dplId,\n            // @ts-ignore\n            precedence: \"next\"\n        }, i));\n}\n_s1(RuntimeStyles, \"Eht7Kgdrrgt5B4LSklQ7qDPo8Aw=\");\n_c4 = RuntimeStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HistoryUpdater\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Router\");\n$RefreshReg$(_c3, \"AppRouter\");\n$RefreshReg$(_c4, \"RuntimeStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/client-page.js ***!
  \*****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ClientPageRoot\", ({\n    enumerable: true,\n    get: function() {\n        return ClientPageRoot;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nfunction ClientPageRoot(param) {\n    let { Component, searchParams, params, promises } = param;\n    if (false) {} else {\n        const { createRenderSearchParamsFromClient } = __webpack_require__(/*! ../request/search-params.browser */ \"(app-pages-browser)/./node_modules/next/dist/client/request/search-params.browser.js\");\n        const clientSearchParams = createRenderSearchParamsFromClient(searchParams);\n        const { createRenderParamsFromClient } = __webpack_require__(/*! ../request/params.browser */ \"(app-pages-browser)/./node_modules/next/dist/client/request/params.browser.js\");\n        const clientParams = createRenderParamsFromClient(params);\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {\n            params: clientParams,\n            searchParams: clientSearchParams\n        });\n    }\n}\n_c = ClientPageRoot;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=client-page.js.map\nvar _c;\n$RefreshReg$(_c, \"ClientPageRoot\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/client-segment.js ***!
  \********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ClientSegmentRoot\", ({\n    enumerable: true,\n    get: function() {\n        return ClientSegmentRoot;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nfunction ClientSegmentRoot(param) {\n    let { Component, slots, params, promise } = param;\n    if (false) {} else {\n        const { createRenderParamsFromClient } = __webpack_require__(/*! ../request/params.browser */ \"(app-pages-browser)/./node_modules/next/dist/client/request/params.browser.js\");\n        const clientParams = createRenderParamsFromClient(params);\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {\n            ...slots,\n            params: clientParams\n        });\n    }\n}\n_c = ClientSegmentRoot;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=client-segment.js.map\nvar _c;\n$RefreshReg$(_c, \"ClientSegmentRoot\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js ***!
  \********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DevRootHTTPAccessFallbackBoundary: function() {\n        return DevRootHTTPAccessFallbackBoundary;\n    },\n    bailOnRootNotFound: function() {\n        return bailOnRootNotFound;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _errorboundary = __webpack_require__(/*! ./http-access-fallback/error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\");\nfunction bailOnRootNotFound() {\n    throw Object.defineProperty(new Error('notFound() is not allowed to use in root layout'), \"__NEXT_ERROR_CODE\", {\n        value: \"E192\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction NotAllowedRootHTTPFallbackError() {\n    bailOnRootNotFound();\n    return null;\n}\n_c = NotAllowedRootHTTPFallbackError;\nfunction DevRootHTTPAccessFallbackBoundary(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.HTTPAccessFallbackBoundary, {\n        notFound: /*#__PURE__*/ (0, _jsxruntime.jsx)(NotAllowedRootHTTPFallbackError, {}),\n        children: children\n    });\n}\n_c1 = DevRootHTTPAccessFallbackBoundary;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-root-http-access-fallback-boundary.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"NotAllowedRootHTTPFallbackError\");\n$RefreshReg$(_c1, \"DevRootHTTPAccessFallbackBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZGV2LXJvb3QtaHR0cC1hY2Nlc3MtZmFsbGJhY2stYm91bmRhcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBZWdCQSxpQ0FBaUM7ZUFBakNBOztJQVRBQyxrQkFBa0I7ZUFBbEJBOzs7Ozs0RUFKRTsyQ0FDeUI7QUFHcEMsU0FBU0E7SUFDZCxNQUFNLHFCQUE0RCxDQUE1RCxJQUFJQyxNQUFNLG9EQUFWO2VBQUE7b0JBQUE7c0JBQUE7SUFBMkQ7QUFDbkU7QUFFQTtJQUNFRDtJQUNBLE9BQU87QUFDVDtLQUhTRTtBQUtGLDJDQUEyQyxLQUlqRDtJQUppRCxNQUNoREMsUUFBUSxFQUdULEdBSmlEO0lBS2hELHFCQUNFLHFCQUFDQyxlQUFBQSwwQkFBMEI7UUFBQ0MsVUFBQUEsV0FBQUEsR0FBVSxxQkFBQ0gsaUNBQUFBLENBQUFBO2tCQUNwQ0M7O0FBR1A7TUFWZ0JKIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXGRldi1yb290LWh0dHAtYWNjZXNzLWZhbGxiYWNrLWJvdW5kYXJ5LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgSFRUUEFjY2Vzc0ZhbGxiYWNrQm91bmRhcnkgfSBmcm9tICcuL2h0dHAtYWNjZXNzLWZhbGxiYWNrL2Vycm9yLWJvdW5kYXJ5J1xuXG4vLyBUT0RPOiBlcnJvciBvbiB1c2luZyBmb3JiaWRkZW4gYW5kIHVuYXV0aG9yaXplZCBpbiByb290IGxheW91dFxuZXhwb3J0IGZ1bmN0aW9uIGJhaWxPblJvb3ROb3RGb3VuZCgpIHtcbiAgdGhyb3cgbmV3IEVycm9yKCdub3RGb3VuZCgpIGlzIG5vdCBhbGxvd2VkIHRvIHVzZSBpbiByb290IGxheW91dCcpXG59XG5cbmZ1bmN0aW9uIE5vdEFsbG93ZWRSb290SFRUUEZhbGxiYWNrRXJyb3IoKSB7XG4gIGJhaWxPblJvb3ROb3RGb3VuZCgpXG4gIHJldHVybiBudWxsXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBEZXZSb290SFRUUEFjY2Vzc0ZhbGxiYWNrQm91bmRhcnkoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxIVFRQQWNjZXNzRmFsbGJhY2tCb3VuZGFyeSBub3RGb3VuZD17PE5vdEFsbG93ZWRSb290SFRUUEZhbGxiYWNrRXJyb3IgLz59PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvSFRUUEFjY2Vzc0ZhbGxiYWNrQm91bmRhcnk+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJEZXZSb290SFRUUEFjY2Vzc0ZhbGxiYWNrQm91bmRhcnkiLCJiYWlsT25Sb290Tm90Rm91bmQiLCJFcnJvciIsIk5vdEFsbG93ZWRSb290SFRUUEZhbGxiYWNrRXJyb3IiLCJjaGlsZHJlbiIsIkhUVFBBY2Nlc3NGYWxsYmFja0JvdW5kYXJ5Iiwibm90Rm91bmQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/error-boundary.js ***!
  \********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorBoundary: function() {\n        return ErrorBoundary;\n    },\n    ErrorBoundaryHandler: function() {\n        return ErrorBoundaryHandler;\n    },\n    GlobalError: function() {\n        return GlobalError;\n    },\n    // Exported so that the import signature in the loaders can be identical to user\n    // supplied custom global error signatures.\n    default: function() {\n        return _default;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _navigationuntracked = __webpack_require__(/*! ./navigation-untracked */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation-untracked.js\");\nconst _isnextroutererror = __webpack_require__(/*! ./is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _navfailurehandler = __webpack_require__(/*! ./nav-failure-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/nav-failure-handler.js\");\nconst workAsyncStorage =  false ? 0 : undefined;\nconst styles = {\n    error: {\n        // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n        fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n        height: '100vh',\n        textAlign: 'center',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center'\n    },\n    text: {\n        fontSize: '14px',\n        fontWeight: 400,\n        lineHeight: '28px',\n        margin: '0 8px'\n    }\n};\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nfunction HandleISRError(param) {\n    let { error } = param;\n    if (workAsyncStorage) {\n        const store = workAsyncStorage.getStore();\n        if ((store == null ? void 0 : store.isRevalidate) || (store == null ? void 0 : store.isStaticGeneration)) {\n            console.error(error);\n            throw error;\n        }\n    }\n    return null;\n}\n_c = HandleISRError;\nclass ErrorBoundaryHandler extends _react.default.Component {\n    static getDerivedStateFromError(error) {\n        if ((0, _isnextroutererror.isNextRouterError)(error)) {\n            // Re-throw if an expected internal Next.js router error occurs\n            // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n            throw error;\n        }\n        return {\n            error\n        };\n    }\n    static getDerivedStateFromProps(props, state) {\n        const { error } = state;\n        // if we encounter an error while\n        // a navigation is pending we shouldn't render\n        // the error boundary and instead should fallback\n        // to a hard navigation to attempt recovering\n        if (false) {}\n        /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */ if (props.pathname !== state.previousPathname && state.error) {\n            return {\n                error: null,\n                previousPathname: props.pathname\n            };\n        }\n        return {\n            error: state.error,\n            previousPathname: props.pathname\n        };\n    }\n    // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n    render() {\n        if (this.state.error) {\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(HandleISRError, {\n                        error: this.state.error\n                    }),\n                    this.props.errorStyles,\n                    this.props.errorScripts,\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(this.props.errorComponent, {\n                        error: this.state.error,\n                        reset: this.reset\n                    })\n                ]\n            });\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props), this.reset = ()=>{\n            this.setState({\n                error: null\n            });\n        };\n        this.state = {\n            error: null,\n            previousPathname: this.props.pathname\n        };\n    }\n}\nfunction GlobalError(param) {\n    let { error } = param;\n    const digest = error == null ? void 0 : error.digest;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"html\", {\n        id: \"__next_error__\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"head\", {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(HandleISRError, {\n                        error: error\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                        style: styles.error,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"h2\", {\n                                    style: styles.text,\n                                    children: [\n                                        \"Application error: a \",\n                                        digest ? 'server' : 'client',\n                                        \"-side exception has occurred while loading \",\n                                        window.location.hostname,\n                                        \" (see the\",\n                                        ' ',\n                                        digest ? 'server logs' : 'browser console',\n                                        \" for more information).\"\n                                    ]\n                                }),\n                                digest ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    style: styles.text,\n                                    children: \"Digest: \" + digest\n                                }) : null\n                            ]\n                        })\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c1 = GlobalError;\nconst _default = GlobalError;\nfunction ErrorBoundary(param) {\n    let { errorComponent, errorStyles, errorScripts, children } = param;\n    // When we're rendering the missing params shell, this will return null. This\n    // is because we won't be rendering any not found boundaries or error\n    // boundaries for the missing params shell. When this runs on the client\n    // (where these errors can occur), we will get the correct pathname.\n    const pathname = (0, _navigationuntracked.useUntrackedPathname)();\n    if (errorComponent) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(ErrorBoundaryHandler, {\n            pathname: pathname,\n            errorComponent: errorComponent,\n            errorStyles: errorStyles,\n            errorScripts: errorScripts,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c2 = ErrorBoundary;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"HandleISRError\");\n$RefreshReg$(_c1, \"GlobalError\");\n$RefreshReg$(_c2, \"ErrorBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"attachHydrationErrorState\", ({\n    enumerable: true,\n    get: function() {\n        return attachHydrationErrorState;\n    }\n}));\nconst _ishydrationerror = __webpack_require__(/*! ../is-hydration-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\");\nconst _hydrationerrorinfo = __webpack_require__(/*! ./hydration-error-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js\");\nfunction attachHydrationErrorState(error) {\n    let parsedHydrationErrorState = {};\n    const isHydrationWarning = (0, _ishydrationerror.testReactHydrationWarning)(error.message);\n    const isHydrationRuntimeError = (0, _ishydrationerror.isHydrationError)(error);\n    // If it's not hydration warnings or errors, skip\n    if (!(isHydrationRuntimeError || isHydrationWarning)) {\n        return;\n    }\n    const reactHydrationDiffSegments = (0, _hydrationerrorinfo.getReactHydrationDiffSegments)(error.message);\n    // If the reactHydrationDiffSegments exists\n    // and the diff (reactHydrationDiffSegments[1]) exists\n    // e.g. the hydration diff log error.\n    if (reactHydrationDiffSegments) {\n        const diff = reactHydrationDiffSegments[1];\n        parsedHydrationErrorState = {\n            ...error.details,\n            ..._hydrationerrorinfo.hydrationErrorState,\n            // If diff is present in error, we don't need to pick up the console logged warning.\n            // - if hydration error has diff, and is not hydration diff log, then it's a normal hydration error.\n            // - if hydration error no diff, then leverage the one from the hydration diff log.\n            warning: (diff && !isHydrationWarning ? null : _hydrationerrorinfo.hydrationErrorState.warning) || [\n                (0, _ishydrationerror.getDefaultHydrationErrorMessage)()\n            ],\n            // When it's hydration diff log, do not show notes section.\n            // This condition is only for the 1st squashed error.\n            notes: isHydrationWarning ? '' : reactHydrationDiffSegments[0],\n            reactOutputComponentDiff: diff\n        };\n        // Cache the `reactOutputComponentDiff` into hydrationErrorState.\n        // This is only required for now when we still squashed the hydration diff log into hydration error.\n        // Once the all error is logged to dev overlay in order, this will go away.\n        if (!_hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff && diff) {\n            _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff = diff;\n        }\n        // If it's hydration runtime error that doesn't contain the diff, combine the diff from the cached hydration diff.\n        if (!diff && isHydrationRuntimeError && _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff) {\n            parsedHydrationErrorState.reactOutputComponentDiff = _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff;\n        }\n    } else {\n        // Normal runtime error, where it doesn't contain the hydration diff.\n        // If there's any extra information in the error message to display,\n        // append it to the error message details property\n        if (_hydrationerrorinfo.hydrationErrorState.warning) {\n            // The patched console.error found hydration errors logged by React\n            // Append the logged warning to the error message\n            parsedHydrationErrorState = {\n                ...error.details,\n                // It contains the warning, component stack, server and client tag names\n                ..._hydrationerrorinfo.hydrationErrorState\n            };\n        }\n        // Consume the cached hydration diff.\n        // This is only required for now when we still squashed the hydration diff log into hydration error.\n        // Once the all error is logged to dev overlay in order, this will go away.\n        if (_hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff) {\n            parsedHydrationErrorState.reactOutputComponentDiff = _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff;\n        }\n    }\n    // If it's a hydration error, store the hydration error state into the error object\n    ;\n    error.details = parsedHydrationErrorState;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=attach-hydration-error-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/errors/console-error.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/console-error.js ***!
  \**************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// To distinguish from React error.digest, we use a different symbol here to determine if the error is from console.error or unhandled promise rejection.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createUnhandledError: function() {\n        return createUnhandledError;\n    },\n    getUnhandledErrorType: function() {\n        return getUnhandledErrorType;\n    },\n    isUnhandledConsoleOrRejection: function() {\n        return isUnhandledConsoleOrRejection;\n    }\n});\nconst digestSym = Symbol.for('next.console.error.digest');\nconst consoleTypeSym = Symbol.for('next.console.error.type');\nfunction createUnhandledError(message, environmentName) {\n    const error = typeof message === 'string' ? Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    }) : message;\n    error[digestSym] = 'NEXT_UNHANDLED_ERROR';\n    error[consoleTypeSym] = typeof message === 'string' ? 'string' : 'error';\n    if (environmentName && !error.environmentName) {\n        error.environmentName = environmentName;\n    }\n    return error;\n}\nconst isUnhandledConsoleOrRejection = (error)=>{\n    return error && error[digestSym] === 'NEXT_UNHANDLED_ERROR';\n};\nconst getUnhandledErrorType = (error)=>{\n    return error[consoleTypeSym];\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=console-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/errors/console-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/errors/enqueue-client-error.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/enqueue-client-error.js ***!
  \*********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// Dedupe the two consecutive errors: If the previous one is same as current one, ignore the current one.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"enqueueConsecutiveDedupedError\", ({\n    enumerable: true,\n    get: function() {\n        return enqueueConsecutiveDedupedError;\n    }\n}));\nfunction enqueueConsecutiveDedupedError(queue, error) {\n    const previousError = queue[queue.length - 1];\n    // Compare the error stack to dedupe the consecutive errors\n    if (previousError && previousError.stack === error.stack) {\n        return;\n    }\n    queue.push(error);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=enqueue-client-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3JzL2VucXVldWUtY2xpZW50LWVycm9yLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUF5Rzs7Ozs7a0VBQ3pGQTs7O2VBQUFBOzs7QUFBVCxTQUFTQSwrQkFDZEMsS0FBbUIsRUFDbkJDLEtBQVk7SUFFWixNQUFNQyxnQkFBZ0JGLEtBQUssQ0FBQ0EsTUFBTUcsTUFBTSxHQUFHLEVBQUU7SUFDN0MsMkRBQTJEO0lBQzNELElBQUlELGlCQUFpQkEsY0FBY0UsS0FBSyxLQUFLSCxNQUFNRyxLQUFLLEVBQUU7UUFDeEQ7SUFDRjtJQUNBSixNQUFNSyxJQUFJLENBQUNKO0FBQ2IiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xcZXJyb3JzXFxlbnF1ZXVlLWNsaWVudC1lcnJvci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBEZWR1cGUgdGhlIHR3byBjb25zZWN1dGl2ZSBlcnJvcnM6IElmIHRoZSBwcmV2aW91cyBvbmUgaXMgc2FtZSBhcyBjdXJyZW50IG9uZSwgaWdub3JlIHRoZSBjdXJyZW50IG9uZS5cbmV4cG9ydCBmdW5jdGlvbiBlbnF1ZXVlQ29uc2VjdXRpdmVEZWR1cGVkRXJyb3IoXG4gIHF1ZXVlOiBBcnJheTxFcnJvcj4sXG4gIGVycm9yOiBFcnJvclxuKSB7XG4gIGNvbnN0IHByZXZpb3VzRXJyb3IgPSBxdWV1ZVtxdWV1ZS5sZW5ndGggLSAxXVxuICAvLyBDb21wYXJlIHRoZSBlcnJvciBzdGFjayB0byBkZWR1cGUgdGhlIGNvbnNlY3V0aXZlIGVycm9yc1xuICBpZiAocHJldmlvdXNFcnJvciAmJiBwcmV2aW91c0Vycm9yLnN0YWNrID09PSBlcnJvci5zdGFjaykge1xuICAgIHJldHVyblxuICB9XG4gIHF1ZXVlLnB1c2goZXJyb3IpXG59XG4iXSwibmFtZXMiOlsiZW5xdWV1ZUNvbnNlY3V0aXZlRGVkdXBlZEVycm9yIiwicXVldWUiLCJlcnJvciIsInByZXZpb3VzRXJyb3IiLCJsZW5ndGgiLCJzdGFjayIsInB1c2giXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/errors/enqueue-client-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/hydration-error-info.js ***!
  \*********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getHydrationWarningType: function() {\n        return getHydrationWarningType;\n    },\n    getReactHydrationDiffSegments: function() {\n        return getReactHydrationDiffSegments;\n    },\n    hydrationErrorState: function() {\n        return hydrationErrorState;\n    },\n    storeHydrationErrorStateFromConsoleArgs: function() {\n        return storeHydrationErrorStateFromConsoleArgs;\n    }\n});\nconst _ishydrationerror = __webpack_require__(/*! ../is-hydration-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\");\nconst hydrationErrorState = {};\n// https://github.com/facebook/react/blob/main/packages/react-dom/src/__tests__/ReactDOMHydrationDiff-test.js used as a reference\nconst htmlTagsWarnings = new Set([\n    'Warning: In HTML, %s cannot be a child of <%s>.%s\\nThis will cause a hydration error.%s',\n    'Warning: In HTML, %s cannot be a descendant of <%s>.\\nThis will cause a hydration error.%s',\n    'Warning: In HTML, text nodes cannot be a child of <%s>.\\nThis will cause a hydration error.',\n    \"Warning: In HTML, whitespace text nodes cannot be a child of <%s>. Make sure you don't have any extra whitespace between tags on each line of your source code.\\nThis will cause a hydration error.\",\n    'Warning: Expected server HTML to contain a matching <%s> in <%s>.%s',\n    'Warning: Did not expect server HTML to contain a <%s> in <%s>.%s'\n]);\nconst textAndTagsMismatchWarnings = new Set([\n    'Warning: Expected server HTML to contain a matching text node for \"%s\" in <%s>.%s',\n    'Warning: Did not expect server HTML to contain the text node \"%s\" in <%s>.%s'\n]);\nconst getHydrationWarningType = (message)=>{\n    if (typeof message !== 'string') {\n        // TODO: Doesn't make sense to treat no message as a hydration error message.\n        // We should bail out somewhere earlier.\n        return 'text';\n    }\n    const normalizedMessage = message.startsWith('Warning: ') ? message : \"Warning: \" + message;\n    if (isHtmlTagsWarning(normalizedMessage)) return 'tag';\n    if (isTextInTagsMismatchWarning(normalizedMessage)) return 'text-in-tag';\n    return 'text';\n};\nconst isHtmlTagsWarning = (message)=>htmlTagsWarnings.has(message);\nconst isTextInTagsMismatchWarning = (msg)=>textAndTagsMismatchWarnings.has(msg);\nconst getReactHydrationDiffSegments = (msg)=>{\n    if (msg) {\n        const { message, diff } = (0, _ishydrationerror.getHydrationErrorStackInfo)(msg);\n        if (message) return [\n            message,\n            diff\n        ];\n    }\n    return undefined;\n};\nfunction storeHydrationErrorStateFromConsoleArgs() {\n    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n        args[_key] = arguments[_key];\n    }\n    let [msg, firstContent, secondContent, ...rest] = args;\n    if ((0, _ishydrationerror.testReactHydrationWarning)(msg)) {\n        // Some hydration warnings has 4 arguments, some has 3, fallback to the last argument\n        // when the 3rd argument is not the component stack but an empty string\n        const isReact18 = msg.startsWith('Warning: ');\n        // For some warnings, there's only 1 argument for template.\n        // The second argument is the diff or component stack.\n        if (args.length === 3) {\n            secondContent = '';\n        }\n        const warning = [\n            // remove the last %s from the message\n            msg,\n            firstContent,\n            secondContent\n        ];\n        const lastArg = (rest[rest.length - 1] || '').trim();\n        if (!isReact18) {\n            hydrationErrorState.reactOutputComponentDiff = lastArg;\n        } else {\n            hydrationErrorState.reactOutputComponentDiff = generateHydrationDiffReact18(msg, firstContent, secondContent, lastArg);\n        }\n        hydrationErrorState.warning = warning;\n        hydrationErrorState.serverContent = firstContent;\n        hydrationErrorState.clientContent = secondContent;\n    }\n}\n/*\n * Some hydration errors in React 18 does not have the diff in the error message.\n * Instead it has the error stack trace which is component stack that we can leverage.\n * Will parse the diff from the error stack trace\n *  e.g.\n *  Warning: Expected server HTML to contain a matching <div> in <p>.\n *    at div\n *    at p\n *    at div\n *    at div\n *    at Page\n *  output:\n *    <Page>\n *      <div>\n *        <p>\n *  >       <div>\n *\n */ function generateHydrationDiffReact18(message, firstContent, secondContent, lastArg) {\n    const componentStack = lastArg;\n    let firstIndex = -1;\n    let secondIndex = -1;\n    const hydrationWarningType = getHydrationWarningType(message);\n    // at div\\n at Foo\\n at Bar (....)\\n -> [div, Foo]\n    const components = componentStack.split('\\n') // .reverse()\n    .map((line, index)=>{\n        // `<space>at <component> (<location>)` -> `at <component> (<location>)`\n        line = line.trim();\n        // extract `<space>at <component>` to `<<component>>`\n        // e.g. `  at Foo` -> `<Foo>`\n        const [, component, location] = /at (\\w+)( \\((.*)\\))?/.exec(line) || [];\n        // If there's no location then it's user-land stack frame\n        if (!location) {\n            if (component === firstContent && firstIndex === -1) {\n                firstIndex = index;\n            } else if (component === secondContent && secondIndex === -1) {\n                secondIndex = index;\n            }\n        }\n        return location ? '' : component;\n    }).filter(Boolean).reverse();\n    let diff = '';\n    for(let i = 0; i < components.length; i++){\n        const component = components[i];\n        const matchFirstContent = hydrationWarningType === 'tag' && i === components.length - firstIndex - 1;\n        const matchSecondContent = hydrationWarningType === 'tag' && i === components.length - secondIndex - 1;\n        if (matchFirstContent || matchSecondContent) {\n            const spaces = ' '.repeat(Math.max(i * 2 - 2, 0) + 2);\n            diff += \"> \" + spaces + \"<\" + component + \">\\n\";\n        } else {\n            const spaces = ' '.repeat(i * 2 + 2);\n            diff += spaces + \"<\" + component + \">\\n\";\n        }\n    }\n    if (hydrationWarningType === 'text') {\n        const spaces = ' '.repeat(components.length * 2);\n        diff += \"+ \" + spaces + '\"' + firstContent + '\"\\n';\n        diff += \"- \" + spaces + '\"' + secondContent + '\"\\n';\n    } else if (hydrationWarningType === 'text-in-tag') {\n        const spaces = ' '.repeat(components.length * 2);\n        diff += \"> \" + spaces + \"<\" + secondContent + \">\\n\";\n        diff += \">   \" + spaces + '\"' + firstContent + '\"\\n';\n    }\n    return diff;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hydration-error-info.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/errors/runtime-error-handler.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/runtime-error-handler.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RuntimeErrorHandler\", ({\n    enumerable: true,\n    get: function() {\n        return RuntimeErrorHandler;\n    }\n}));\nconst RuntimeErrorHandler = {\n    hadRuntimeError: false\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=runtime-error-handler.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3JzL3J1bnRpbWUtZXJyb3ItaGFuZGxlci5qcyIsIm1hcHBpbmdzIjoiOzs7O3VEQUFhQTs7O2VBQUFBOzs7QUFBTixNQUFNQSxzQkFBc0I7SUFDakNDLGlCQUFpQjtBQUNuQiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxlcnJvcnNcXHJ1bnRpbWUtZXJyb3ItaGFuZGxlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgUnVudGltZUVycm9ySGFuZGxlciA9IHtcbiAgaGFkUnVudGltZUVycm9yOiBmYWxzZSxcbn1cbiJdLCJuYW1lcyI6WyJSdW50aW1lRXJyb3JIYW5kbGVyIiwiaGFkUnVudGltZUVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/errors/runtime-error-handler.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/stitched-error.js ***!
  \***************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getReactStitchedError\", ({\n    enumerable: true,\n    get: function() {\n        return getReactStitchedError;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _errortelemetryutils = __webpack_require__(/*! ../../../lib/error-telemetry-utils */ \"(app-pages-browser)/./node_modules/next/dist/lib/error-telemetry-utils.js\");\nconst REACT_ERROR_STACK_BOTTOM_FRAME = 'react-stack-bottom-frame';\nconst REACT_ERROR_STACK_BOTTOM_FRAME_REGEX = new RegExp(\"(at \" + REACT_ERROR_STACK_BOTTOM_FRAME + \" )|(\" + REACT_ERROR_STACK_BOTTOM_FRAME + \"\\\\@)\");\nfunction getReactStitchedError(err) {\n    const isErrorInstance = (0, _iserror.default)(err);\n    const originStack = isErrorInstance ? err.stack || '' : '';\n    const originMessage = isErrorInstance ? err.message : '';\n    const stackLines = originStack.split('\\n');\n    const indexOfSplit = stackLines.findIndex((line)=>REACT_ERROR_STACK_BOTTOM_FRAME_REGEX.test(line));\n    const isOriginalReactError = indexOfSplit >= 0 // has the react-stack-bottom-frame\n    ;\n    let newStack = isOriginalReactError ? stackLines.slice(0, indexOfSplit).join('\\n') : originStack;\n    const newError = Object.defineProperty(new Error(originMessage), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    // Copy all enumerable properties, e.g. digest\n    Object.assign(newError, err);\n    (0, _errortelemetryutils.copyNextErrorCode)(err, newError);\n    newError.stack = newStack;\n    // Avoid duplicate overriding stack frames\n    appendOwnerStack(newError);\n    return newError;\n}\nfunction appendOwnerStack(error) {\n    if (!_react.default.captureOwnerStack) {\n        return;\n    }\n    let stack = error.stack || '';\n    // This module is only bundled in development mode so this is safe.\n    const ownerStack = _react.default.captureOwnerStack();\n    // Avoid duplicate overriding stack frames\n    if (ownerStack && stack.endsWith(ownerStack) === false) {\n        stack += ownerStack;\n        // Override stack\n        error.stack = stack;\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=stitched-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/use-error-handler.js ***!
  \******************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    handleClientError: function() {\n        return handleClientError;\n    },\n    handleGlobalErrors: function() {\n        return handleGlobalErrors;\n    },\n    useErrorHandler: function() {\n        return useErrorHandler;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _attachhydrationerrorstate = __webpack_require__(/*! ./attach-hydration-error-state */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _hydrationerrorinfo = __webpack_require__(/*! ./hydration-error-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js\");\nconst _console = __webpack_require__(/*! ../../lib/console */ \"(app-pages-browser)/./node_modules/next/dist/client/lib/console.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _consoleerror = __webpack_require__(/*! ./console-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/console-error.js\");\nconst _enqueueclienterror = __webpack_require__(/*! ./enqueue-client-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/enqueue-client-error.js\");\nconst _stitchederror = __webpack_require__(/*! ../errors/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst queueMicroTask = globalThis.queueMicrotask || ((cb)=>Promise.resolve().then(cb));\nconst errorQueue = [];\nconst errorHandlers = [];\nconst rejectionQueue = [];\nconst rejectionHandlers = [];\nfunction handleClientError(originError, consoleErrorArgs, capturedFromConsole) {\n    if (capturedFromConsole === void 0) capturedFromConsole = false;\n    let error;\n    if (!originError || !(0, _iserror.default)(originError)) {\n        // If it's not an error, format the args into an error\n        const formattedErrorMessage = (0, _console.formatConsoleArgs)(consoleErrorArgs);\n        const { environmentName } = (0, _console.parseConsoleArgs)(consoleErrorArgs);\n        error = (0, _consoleerror.createUnhandledError)(formattedErrorMessage, environmentName);\n    } else {\n        error = capturedFromConsole ? (0, _consoleerror.createUnhandledError)(originError) : originError;\n    }\n    error = (0, _stitchederror.getReactStitchedError)(error);\n    (0, _hydrationerrorinfo.storeHydrationErrorStateFromConsoleArgs)(...consoleErrorArgs);\n    (0, _attachhydrationerrorstate.attachHydrationErrorState)(error);\n    (0, _enqueueclienterror.enqueueConsecutiveDedupedError)(errorQueue, error);\n    for (const handler of errorHandlers){\n        // Delayed the error being passed to React Dev Overlay,\n        // avoid the state being synchronously updated in the component.\n        queueMicroTask(()=>{\n            handler(error);\n        });\n    }\n}\nfunction useErrorHandler(handleOnUnhandledError, handleOnUnhandledRejection) {\n    (0, _react.useEffect)(()=>{\n        // Handle queued errors.\n        errorQueue.forEach(handleOnUnhandledError);\n        rejectionQueue.forEach(handleOnUnhandledRejection);\n        // Listen to new errors.\n        errorHandlers.push(handleOnUnhandledError);\n        rejectionHandlers.push(handleOnUnhandledRejection);\n        return ()=>{\n            // Remove listeners.\n            errorHandlers.splice(errorHandlers.indexOf(handleOnUnhandledError), 1);\n            rejectionHandlers.splice(rejectionHandlers.indexOf(handleOnUnhandledRejection), 1);\n            // Reset error queues.\n            errorQueue.splice(0, errorQueue.length);\n            rejectionQueue.splice(0, rejectionQueue.length);\n        };\n    }, [\n        handleOnUnhandledError,\n        handleOnUnhandledRejection\n    ]);\n}\nfunction onUnhandledError(event) {\n    if ((0, _isnextroutererror.isNextRouterError)(event.error)) {\n        event.preventDefault();\n        return false;\n    }\n    // When there's an error property present, we log the error to error overlay.\n    // Otherwise we don't do anything as it's not logging in the console either.\n    if (event.error) {\n        handleClientError(event.error, []);\n    }\n}\nfunction onUnhandledRejection(ev) {\n    const reason = ev == null ? void 0 : ev.reason;\n    if ((0, _isnextroutererror.isNextRouterError)(reason)) {\n        ev.preventDefault();\n        return;\n    }\n    let error = reason;\n    if (error && !(0, _iserror.default)(error)) {\n        error = (0, _consoleerror.createUnhandledError)(error + '');\n    }\n    rejectionQueue.push(error);\n    for (const handler of rejectionHandlers){\n        handler(error);\n    }\n}\nfunction handleGlobalErrors() {\n    if (true) {\n        try {\n            // Increase the number of stack frames on the client\n            Error.stackTraceLimit = 50;\n        } catch (e) {}\n        window.addEventListener('error', onUnhandledError);\n        window.addEventListener('unhandledrejection', onUnhandledRejection);\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-error-handler.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"attachHydrationErrorState\", ({\n    enumerable: true,\n    get: function() {\n        return attachHydrationErrorState;\n    }\n}));\nconst _ishydrationerror = __webpack_require__(/*! ../is-hydration-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\");\nconst _hydrationerrorinfo = __webpack_require__(/*! ./hydration-error-info */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js\");\nfunction attachHydrationErrorState(error) {\n    let parsedHydrationErrorState = {};\n    const isHydrationWarning = (0, _ishydrationerror.testReactHydrationWarning)(error.message);\n    const isHydrationRuntimeError = (0, _ishydrationerror.isHydrationError)(error);\n    // If it's not hydration warnings or errors, skip\n    if (!(isHydrationRuntimeError || isHydrationWarning)) {\n        return;\n    }\n    const reactHydrationDiffSegments = (0, _hydrationerrorinfo.getReactHydrationDiffSegments)(error.message);\n    // If the reactHydrationDiffSegments exists\n    // and the diff (reactHydrationDiffSegments[1]) exists\n    // e.g. the hydration diff log error.\n    if (reactHydrationDiffSegments) {\n        const diff = reactHydrationDiffSegments[1];\n        parsedHydrationErrorState = {\n            ...error.details,\n            ..._hydrationerrorinfo.hydrationErrorState,\n            // If diff is present in error, we don't need to pick up the console logged warning.\n            // - if hydration error has diff, and is not hydration diff log, then it's a normal hydration error.\n            // - if hydration error no diff, then leverage the one from the hydration diff log.\n            warning: (diff && !isHydrationWarning ? null : _hydrationerrorinfo.hydrationErrorState.warning) || [\n                (0, _ishydrationerror.getDefaultHydrationErrorMessage)()\n            ],\n            // When it's hydration diff log, do not show notes section.\n            // This condition is only for the 1st squashed error.\n            notes: isHydrationWarning ? '' : reactHydrationDiffSegments[0],\n            reactOutputComponentDiff: diff\n        };\n        // Cache the `reactOutputComponentDiff` into hydrationErrorState.\n        // This is only required for now when we still squashed the hydration diff log into hydration error.\n        // Once the all error is logged to dev overlay in order, this will go away.\n        if (!_hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff && diff) {\n            _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff = diff;\n        }\n        // If it's hydration runtime error that doesn't contain the diff, combine the diff from the cached hydration diff.\n        if (!diff && isHydrationRuntimeError && _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff) {\n            parsedHydrationErrorState.reactOutputComponentDiff = _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff;\n        }\n    } else {\n        // Normal runtime error, where it doesn't contain the hydration diff.\n        // If there's any extra information in the error message to display,\n        // append it to the error message details property\n        if (_hydrationerrorinfo.hydrationErrorState.warning) {\n            // The patched console.error found hydration errors logged by React\n            // Append the logged warning to the error message\n            parsedHydrationErrorState = {\n                ...error.details,\n                // It contains the warning, component stack, server and client tag names\n                ..._hydrationerrorinfo.hydrationErrorState\n            };\n        }\n        // Consume the cached hydration diff.\n        // This is only required for now when we still squashed the hydration diff log into hydration error.\n        // Once the all error is logged to dev overlay in order, this will go away.\n        if (_hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff) {\n            parsedHydrationErrorState.reactOutputComponentDiff = _hydrationerrorinfo.hydrationErrorState.reactOutputComponentDiff;\n        }\n    }\n    // If it's a hydration error, store the hydration error state into the error object\n    ;\n    error.details = parsedHydrationErrorState;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=attach-hydration-error-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/errors/console-error.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/console-error.js ***!
  \**************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// To distinguish from React error.digest, we use a different symbol here to determine if the error is from console.error or unhandled promise rejection.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createUnhandledError: function() {\n        return createUnhandledError;\n    },\n    getUnhandledErrorType: function() {\n        return getUnhandledErrorType;\n    },\n    isUnhandledConsoleOrRejection: function() {\n        return isUnhandledConsoleOrRejection;\n    }\n});\nconst digestSym = Symbol.for('next.console.error.digest');\nconst consoleTypeSym = Symbol.for('next.console.error.type');\nfunction createUnhandledError(message, environmentName) {\n    const error = typeof message === 'string' ? Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    }) : message;\n    error[digestSym] = 'NEXT_UNHANDLED_ERROR';\n    error[consoleTypeSym] = typeof message === 'string' ? 'string' : 'error';\n    if (environmentName && !error.environmentName) {\n        error.environmentName = environmentName;\n    }\n    return error;\n}\nconst isUnhandledConsoleOrRejection = (error)=>{\n    return error && error[digestSym] === 'NEXT_UNHANDLED_ERROR';\n};\nconst getUnhandledErrorType = (error)=>{\n    return error[consoleTypeSym];\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=console-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/errors/console-error.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/hydration-error-info.js ***!
  \*********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getHydrationWarningType: function() {\n        return getHydrationWarningType;\n    },\n    getReactHydrationDiffSegments: function() {\n        return getReactHydrationDiffSegments;\n    },\n    hydrationErrorState: function() {\n        return hydrationErrorState;\n    },\n    storeHydrationErrorStateFromConsoleArgs: function() {\n        return storeHydrationErrorStateFromConsoleArgs;\n    }\n});\nconst _ishydrationerror = __webpack_require__(/*! ../is-hydration-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\");\nconst hydrationErrorState = {};\n// https://github.com/facebook/react/blob/main/packages/react-dom/src/__tests__/ReactDOMHydrationDiff-test.js used as a reference\nconst htmlTagsWarnings = new Set([\n    'Warning: In HTML, %s cannot be a child of <%s>.%s\\nThis will cause a hydration error.%s',\n    'Warning: In HTML, %s cannot be a descendant of <%s>.\\nThis will cause a hydration error.%s',\n    'Warning: In HTML, text nodes cannot be a child of <%s>.\\nThis will cause a hydration error.',\n    \"Warning: In HTML, whitespace text nodes cannot be a child of <%s>. Make sure you don't have any extra whitespace between tags on each line of your source code.\\nThis will cause a hydration error.\",\n    'Warning: Expected server HTML to contain a matching <%s> in <%s>.%s',\n    'Warning: Did not expect server HTML to contain a <%s> in <%s>.%s'\n]);\nconst textAndTagsMismatchWarnings = new Set([\n    'Warning: Expected server HTML to contain a matching text node for \"%s\" in <%s>.%s',\n    'Warning: Did not expect server HTML to contain the text node \"%s\" in <%s>.%s'\n]);\nconst getHydrationWarningType = (message)=>{\n    if (typeof message !== 'string') {\n        // TODO: Doesn't make sense to treat no message as a hydration error message.\n        // We should bail out somewhere earlier.\n        return 'text';\n    }\n    const normalizedMessage = message.startsWith('Warning: ') ? message : \"Warning: \" + message;\n    if (isHtmlTagsWarning(normalizedMessage)) return 'tag';\n    if (isTextInTagsMismatchWarning(normalizedMessage)) return 'text-in-tag';\n    return 'text';\n};\nconst isHtmlTagsWarning = (message)=>htmlTagsWarnings.has(message);\nconst isTextInTagsMismatchWarning = (msg)=>textAndTagsMismatchWarnings.has(msg);\nconst getReactHydrationDiffSegments = (msg)=>{\n    if (msg) {\n        const { message, diff } = (0, _ishydrationerror.getHydrationErrorStackInfo)(msg);\n        if (message) return [\n            message,\n            diff\n        ];\n    }\n    return undefined;\n};\nfunction storeHydrationErrorStateFromConsoleArgs() {\n    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n        args[_key] = arguments[_key];\n    }\n    let [msg, firstContent, secondContent, ...rest] = args;\n    if ((0, _ishydrationerror.testReactHydrationWarning)(msg)) {\n        // Some hydration warnings has 4 arguments, some has 3, fallback to the last argument\n        // when the 3rd argument is not the component stack but an empty string\n        const isReact18 = msg.startsWith('Warning: ');\n        // For some warnings, there's only 1 argument for template.\n        // The second argument is the diff or component stack.\n        if (args.length === 3) {\n            secondContent = '';\n        }\n        const warning = [\n            // remove the last %s from the message\n            msg,\n            firstContent,\n            secondContent\n        ];\n        const lastArg = (rest[rest.length - 1] || '').trim();\n        if (!isReact18) {\n            hydrationErrorState.reactOutputComponentDiff = lastArg;\n        } else {\n            hydrationErrorState.reactOutputComponentDiff = generateHydrationDiffReact18(msg, firstContent, secondContent, lastArg);\n        }\n        hydrationErrorState.warning = warning;\n        hydrationErrorState.serverContent = firstContent;\n        hydrationErrorState.clientContent = secondContent;\n    }\n}\n/*\n * Some hydration errors in React 18 does not have the diff in the error message.\n * Instead it has the error stack trace which is component stack that we can leverage.\n * Will parse the diff from the error stack trace\n *  e.g.\n *  Warning: Expected server HTML to contain a matching <div> in <p>.\n *    at div\n *    at p\n *    at div\n *    at div\n *    at Page\n *  output:\n *    <Page>\n *      <div>\n *        <p>\n *  >       <div>\n *\n */ function generateHydrationDiffReact18(message, firstContent, secondContent, lastArg) {\n    const componentStack = lastArg;\n    let firstIndex = -1;\n    let secondIndex = -1;\n    const hydrationWarningType = getHydrationWarningType(message);\n    // at div\\n at Foo\\n at Bar (....)\\n -> [div, Foo]\n    const components = componentStack.split('\\n') // .reverse()\n    .map((line, index)=>{\n        // `<space>at <component> (<location>)` -> `at <component> (<location>)`\n        line = line.trim();\n        // extract `<space>at <component>` to `<<component>>`\n        // e.g. `  at Foo` -> `<Foo>`\n        const [, component, location] = /at (\\w+)( \\((.*)\\))?/.exec(line) || [];\n        // If there's no location then it's user-land stack frame\n        if (!location) {\n            if (component === firstContent && firstIndex === -1) {\n                firstIndex = index;\n            } else if (component === secondContent && secondIndex === -1) {\n                secondIndex = index;\n            }\n        }\n        return location ? '' : component;\n    }).filter(Boolean).reverse();\n    let diff = '';\n    for(let i = 0; i < components.length; i++){\n        const component = components[i];\n        const matchFirstContent = hydrationWarningType === 'tag' && i === components.length - firstIndex - 1;\n        const matchSecondContent = hydrationWarningType === 'tag' && i === components.length - secondIndex - 1;\n        if (matchFirstContent || matchSecondContent) {\n            const spaces = ' '.repeat(Math.max(i * 2 - 2, 0) + 2);\n            diff += \"> \" + spaces + \"<\" + component + \">\\n\";\n        } else {\n            const spaces = ' '.repeat(i * 2 + 2);\n            diff += spaces + \"<\" + component + \">\\n\";\n        }\n    }\n    if (hydrationWarningType === 'text') {\n        const spaces = ' '.repeat(components.length * 2);\n        diff += \"+ \" + spaces + '\"' + firstContent + '\"\\n';\n        diff += \"- \" + spaces + '\"' + secondContent + '\"\\n';\n    } else if (hydrationWarningType === 'text-in-tag') {\n        const spaces = ' '.repeat(components.length * 2);\n        diff += \"> \" + spaces + \"<\" + secondContent + \">\\n\";\n        diff += \">   \" + spaces + '\"' + firstContent + '\"\\n';\n    }\n    return diff;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hydration-error-info.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/errors/runtime-error-handler.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/runtime-error-handler.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RuntimeErrorHandler\", ({\n    enumerable: true,\n    get: function() {\n        return RuntimeErrorHandler;\n    }\n}));\nconst RuntimeErrorHandler = {\n    hadRuntimeError: false\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=runtime-error-handler.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3JzL3J1bnRpbWUtZXJyb3ItaGFuZGxlci5qcyIsIm1hcHBpbmdzIjoiOzs7O3VEQUFhQTs7O2VBQUFBOzs7QUFBTixNQUFNQSxzQkFBc0I7SUFDakNDLGlCQUFpQjtBQUNuQiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxlcnJvcnNcXHJ1bnRpbWUtZXJyb3ItaGFuZGxlci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgUnVudGltZUVycm9ySGFuZGxlciA9IHtcbiAgaGFkUnVudGltZUVycm9yOiBmYWxzZSxcbn1cbiJdLCJuYW1lcyI6WyJSdW50aW1lRXJyb3JIYW5kbGVyIiwiaGFkUnVudGltZUVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/errors/runtime-error-handler.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/errors/stitched-error.js ***!
  \***************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getReactStitchedError\", ({\n    enumerable: true,\n    get: function() {\n        return getReactStitchedError;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(pages-dir-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _errortelemetryutils = __webpack_require__(/*! ../../../lib/error-telemetry-utils */ \"(pages-dir-browser)/./node_modules/next/dist/lib/error-telemetry-utils.js\");\nconst REACT_ERROR_STACK_BOTTOM_FRAME = 'react-stack-bottom-frame';\nconst REACT_ERROR_STACK_BOTTOM_FRAME_REGEX = new RegExp(\"(at \" + REACT_ERROR_STACK_BOTTOM_FRAME + \" )|(\" + REACT_ERROR_STACK_BOTTOM_FRAME + \"\\\\@)\");\nfunction getReactStitchedError(err) {\n    const isErrorInstance = (0, _iserror.default)(err);\n    const originStack = isErrorInstance ? err.stack || '' : '';\n    const originMessage = isErrorInstance ? err.message : '';\n    const stackLines = originStack.split('\\n');\n    const indexOfSplit = stackLines.findIndex((line)=>REACT_ERROR_STACK_BOTTOM_FRAME_REGEX.test(line));\n    const isOriginalReactError = indexOfSplit >= 0 // has the react-stack-bottom-frame\n    ;\n    let newStack = isOriginalReactError ? stackLines.slice(0, indexOfSplit).join('\\n') : originStack;\n    const newError = Object.defineProperty(new Error(originMessage), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    // Copy all enumerable properties, e.g. digest\n    Object.assign(newError, err);\n    (0, _errortelemetryutils.copyNextErrorCode)(err, newError);\n    newError.stack = newStack;\n    // Avoid duplicate overriding stack frames\n    appendOwnerStack(newError);\n    return newError;\n}\nfunction appendOwnerStack(error) {\n    if (!_react.default.captureOwnerStack) {\n        return;\n    }\n    let stack = error.stack || '';\n    // This module is only bundled in development mode so this is safe.\n    const ownerStack = _react.default.captureOwnerStack();\n    // Avoid duplicate overriding stack frames\n    if (ownerStack && stack.endsWith(ownerStack) === false) {\n        stack += ownerStack;\n        // Override stack\n        error.stack = stack;\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=stitched-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\n"));

/***/ })

}]);