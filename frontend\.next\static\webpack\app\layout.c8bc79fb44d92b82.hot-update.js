"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/AIContext.tsx":
/*!************************************!*\
  !*** ./src/contexts/AIContext.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIProvider: () => (/* binding */ AIProvider),\n/* harmony export */   useAIContext: () => (/* binding */ useAIContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AIProvider,useAIContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst AIContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AIProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [suggestion, setSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getTradingModeSuggestion = async (input)=>{\n        setIsLoading(true);\n        setError(null);\n        setSuggestion(null);\n        try {\n            // For now, provide a mock response to avoid server import issues\n            // TODO: Implement proper API call to server action\n            const result = {\n                suggestedMode: input.riskTolerance === 'low' ? 'Stablecoin Swap' : 'Simple Spot',\n                reason: input.riskTolerance === 'low' ? 'Based on your low risk tolerance, Stablecoin Swap mode is recommended for more stable returns.' : 'Based on your risk profile, Simple Spot mode offers better profit potential for active trading.'\n            };\n            setSuggestion(result);\n        } catch (e) {\n            setError(e instanceof Error ? e.message : \"An unknown error occurred during AI suggestion.\");\n            console.error(\"Error fetching trading mode suggestion:\", e);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIContext.Provider, {\n        value: {\n            suggestion,\n            isLoading,\n            error,\n            getTradingModeSuggestion\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AIContext.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AIProvider, \"rPgUNEoXKOCKVFy/IBbFUr3NZqY=\");\n_c = AIProvider;\nconst useAIContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AIContext);\n    if (context === undefined) {\n        throw new Error('useAIContext must be used within an AIProvider');\n    }\n    return context;\n};\n_s1(useAIContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AIProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AIContext.tsx\n"));

/***/ })

});