// Initialize global error handler FIRST - before any other imports
import '@/lib/global-error-handler';

import type { Metadata } from 'next';
import { GeistSans } from 'geist/font/sans'; // Correct import for Geist Sans
import './globals.css';
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider } from '@/contexts/AuthContext';
import { TradingProvider } from '@/contexts/TradingContext';
import { AIProvider } from '@/contexts/AIContext';
import ErrorBoundary from '@/components/ErrorBoundary';

// GeistSans from 'geist/font/sans' directly provides .variable and .className
// No need to call it as a function like with next/font/google.
// The variable it sets is typically --font-geist-sans.

export const metadata: Metadata = {
  title: 'Pluto Trading Bot',
  description: 'Simulated cryptocurrency trading bot with Neo Brutalist UI.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={GeistSans.variable} suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Early chunk loading error handler
              (function() {
                var reloadAttempts = parseInt(sessionStorage.getItem('chunkReloadAttempts') || '0');
                var maxAttempts = 2;

                function isChunkError(error) {
                  if (!error) return false;
                  var msg = error.message || error.toString();
                  return msg.includes('Loading chunk') ||
                         msg.includes('ChunkLoadError') ||
                         msg.includes('missing:') ||
                         msg.includes('Unexpected token');
                }

                function handleError(error) {
                  if (isChunkError(error) && reloadAttempts < maxAttempts) {
                    console.log('Early chunk error detected, reloading...');
                    sessionStorage.setItem('chunkReloadAttempts', String(reloadAttempts + 1));
                    setTimeout(function() { window.location.reload(); }, 100);
                  }
                }

                window.addEventListener('error', function(e) { handleError(e.error); });
                window.addEventListener('unhandledrejection', function(e) { handleError(e.reason); });

                // Clear reload attempts after successful load
                window.addEventListener('load', function() {
                  sessionStorage.removeItem('chunkReloadAttempts');
                });
              })();
            `,
          }}
        />
      </head>
      <body className="font-sans antialiased" suppressHydrationWarning> {/* Tailwind's font-sans will pick up the CSS variable */}
        <ErrorBoundary>
          <AuthProvider>
            <TradingProvider>
              <AIProvider>
                {children}
                <Toaster />
              </AIProvider>
            </TradingProvider>
          </AuthProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
