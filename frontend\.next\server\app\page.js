/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(action-browser)/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive":
/*!***********************************************************************************!*\
  !*** ./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync ***!
  \***********************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(action-browser)/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Cai%5C%5Cflows%5C%5Ctrading-mode-suggestion.ts%22%2C%5B%7B%22id%22%3A%224052d68cef5842cf51673e261240ff414a60693aaa%22%2C%22exportedName%22%3A%22suggestTradingMode%22%7D%5D%5D%5D&__client_imported__=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Cai%5C%5Cflows%5C%5Ctrading-mode-suggestion.ts%22%2C%5B%7B%22id%22%3A%224052d68cef5842cf51673e261240ff414a60693aaa%22%2C%22exportedName%22%3A%22suggestTradingMode%22%7D%5D%5D%5D&__client_imported__=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"4052d68cef5842cf51673e261240ff414a60693aaa\": () => (/* reexport safe */ E_bot_tradingbot_final_frontend_src_ai_flows_trading_mode_suggestion_ts__WEBPACK_IMPORTED_MODULE_0__.suggestTradingMode)\n/* harmony export */ });\n/* harmony import */ var E_bot_tradingbot_final_frontend_src_ai_flows_trading_mode_suggestion_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/ai/flows/trading-mode-suggestion.ts */ \"(action-browser)/./src/ai/flows/trading-mode-suggestion.ts\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWFjdGlvbi1lbnRyeS1sb2FkZXIuanM/YWN0aW9ucz0lNUIlNUIlMjJFJTNBJTVDJTVDYm90JTVDJTVDdHJhZGluZ2JvdF9maW5hbCU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYWklNUMlNUNmbG93cyU1QyU1Q3RyYWRpbmctbW9kZS1zdWdnZXN0aW9uLnRzJTIyJTJDJTVCJTdCJTIyaWQlMjIlM0ElMjI0MDUyZDY4Y2VmNTg0MmNmNTE2NzNlMjYxMjQwZmY0MTRhNjA2OTNhYWElMjIlMkMlMjJleHBvcnRlZE5hbWUlMjIlM0ElMjJzdWdnZXN0VHJhZGluZ01vZGUlMjIlN0QlNUQlNUQlNUQmX19jbGllbnRfaW1wb3J0ZWRfXz10cnVlISIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDb0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IHN1Z2dlc3RUcmFkaW5nTW9kZSBhcyBcIjQwNTJkNjhjZWY1ODQyY2Y1MTY3M2UyNjEyNDBmZjQxNGE2MDY5M2FhYVwiIH0gZnJvbSBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhaVxcXFxmbG93c1xcXFx0cmFkaW5nLW1vZGUtc3VnZ2VzdGlvbi50c1wiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Cai%5C%5Cflows%5C%5Ctrading-mode-suggestion.ts%22%2C%5B%7B%22id%22%3A%224052d68cef5842cf51673e261240ff414a60693aaa%22%2C%22exportedName%22%3A%22suggestTradingMode%22%7D%5D%5D%5D&__client_imported__=true!\n");

/***/ }),

/***/ "(action-browser)/./src/ai/flows/trading-mode-suggestion.ts":
/*!*************************************************!*\
  !*** ./src/ai/flows/trading-mode-suggestion.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   suggestTradingMode: () => (/* binding */ suggestTradingMode)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(action-browser)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ai_genkit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/ai/genkit */ \"(action-browser)/./src/ai/genkit.ts\");\n/* harmony import */ var genkit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! genkit */ \"(action-browser)/./node_modules/genkit/lib/index.mjs\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n// src/ai/flows/trading-mode-suggestion.ts\n/* __next_internal_action_entry_do_not_use__ {\"4052d68cef5842cf51673e261240ff414a60693aaa\":\"suggestTradingMode\"} */ \n\n/**\n * @fileOverview This file implements the Trading Mode Suggestion AI agent.\n *\n * - suggestTradingMode - A function that suggests the most suitable trading mode.\n * - TradingModeSuggestionInput - The input type for the suggestTradingMode function.\n * - TradingModeSuggestionOutput - The return type for the suggestTradingMode function.\n */ \n\nconst TradingModeSuggestionInputSchema = genkit__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    riskTolerance: genkit__WEBPACK_IMPORTED_MODULE_3__.z.string().describe('The user risk tolerance, can be low, medium, or high.'),\n    preferredCryptocurrencies: genkit__WEBPACK_IMPORTED_MODULE_3__.z.string().describe('The user preferred cryptocurrencies, comma separated.'),\n    investmentGoals: genkit__WEBPACK_IMPORTED_MODULE_3__.z.string().describe('The user investment goals, such as long term investment or short term profit.')\n});\nconst TradingModeSuggestionOutputSchema = genkit__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    suggestedMode: genkit__WEBPACK_IMPORTED_MODULE_3__.z.enum([\n        'Simple Spot',\n        'Stablecoin Swap'\n    ]).describe('The suggested trading mode.'),\n    reason: genkit__WEBPACK_IMPORTED_MODULE_3__.z.string().describe('The reason for the suggestion.')\n});\nasync function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ suggestTradingMode(input) {\n    return suggestTradingModeFlow(input);\n}\nconst prompt = _ai_genkit__WEBPACK_IMPORTED_MODULE_2__.ai.definePrompt({\n    name: 'tradingModeSuggestionPrompt',\n    input: {\n        schema: TradingModeSuggestionInputSchema\n    },\n    output: {\n        schema: TradingModeSuggestionOutputSchema\n    },\n    prompt: `You are an expert in trading mode selection. You will suggest the most suitable trading mode (Simple Spot or Stablecoin Swap) based on the user's risk tolerance, preferred cryptocurrencies, and investment goals.\n\nRisk Tolerance: {{{riskTolerance}}}\nPreferred Cryptocurrencies: {{{preferredCryptocurrencies}}}\nInvestment Goals: {{{investmentGoals}}}\n\nConsider the following:\n\n*   Simple Spot Mode is suitable for users who are comfortable with higher risk and are looking for short term profits.\n*   Stablecoin Swap Mode is suitable for users who are risk averse and are looking for long term investment.\n\nBased on the information above, suggest a trading mode and explain your reasoning.`\n});\nconst suggestTradingModeFlow = _ai_genkit__WEBPACK_IMPORTED_MODULE_2__.ai.defineFlow({\n    name: 'suggestTradingModeFlow',\n    inputSchema: TradingModeSuggestionInputSchema,\n    outputSchema: TradingModeSuggestionOutputSchema\n}, async (input)=>{\n    const { output } = await prompt(input);\n    return output;\n});\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__.ensureServerEntryExports)([\n    suggestTradingMode\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(suggestTradingMode, \"4052d68cef5842cf51673e261240ff414a60693aaa\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./src/ai/flows/trading-mode-suggestion.ts\n");

/***/ }),

/***/ "(action-browser)/./src/ai/genkit.ts":
/*!**************************!*\
  !*** ./src/ai/genkit.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ai: () => (/* binding */ ai)\n/* harmony export */ });\n/* harmony import */ var genkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! genkit */ \"(action-browser)/./node_modules/genkit/lib/index.mjs\");\n/* harmony import */ var _genkit_ai_googleai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @genkit-ai/googleai */ \"(action-browser)/./node_modules/@genkit-ai/googleai/lib/index.mjs\");\n\n\nconst ai = (0,genkit__WEBPACK_IMPORTED_MODULE_0__.genkit)({\n    plugins: [\n        (0,_genkit_ai_googleai__WEBPACK_IMPORTED_MODULE_1__.googleAI)()\n    ],\n    model: 'googleai/gemini-2.0-flash'\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL3NyYy9haS9nZW5raXQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThCO0FBQ2U7QUFFdEMsTUFBTUUsS0FBS0YsOENBQU1BLENBQUM7SUFDdkJHLFNBQVM7UUFBQ0YsNkRBQVFBO0tBQUc7SUFDckJHLE9BQU87QUFDVCxHQUFHIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxhaVxcZ2Vua2l0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Z2Vua2l0fSBmcm9tICdnZW5raXQnO1xuaW1wb3J0IHtnb29nbGVBSX0gZnJvbSAnQGdlbmtpdC1haS9nb29nbGVhaSc7XG5cbmV4cG9ydCBjb25zdCBhaSA9IGdlbmtpdCh7XG4gIHBsdWdpbnM6IFtnb29nbGVBSSgpXSxcbiAgbW9kZWw6ICdnb29nbGVhaS9nZW1pbmktMi4wLWZsYXNoJyxcbn0pO1xuIl0sIm5hbWVzIjpbImdlbmtpdCIsImdvb2dsZUFJIiwiYWkiLCJwbHVnaW5zIiwibW9kZWwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./src/ai/genkit.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAIContext.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CTradingContext.tsx%22%2C%22ids%22%3A%5B%22TradingProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAIContext.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CTradingContext.tsx%22%2C%22ids%22%3A%5B%22TradingProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toaster.tsx */ \"(rsc)/./src/components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AIContext.tsx */ \"(rsc)/./src/contexts/AIContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/TradingContext.tsx */ \"(rsc)/./src/contexts/TradingContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAIContext.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CTradingContext.tsx%22%2C%22ids%22%3A%5B%22TradingProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNib3QlNUMlNUN0cmFkaW5nYm90X2ZpbmFsJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQTRGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxib3RcXFxcdHJhZGluZ2JvdF9maW5hbFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fab99f159d6e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZmFiOTlmMTU5ZDZlXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var geist_font_sans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! geist/font/sans */ \"(rsc)/./node_modules/geist/dist/sans.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./src/components/ui/toaster.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(rsc)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _contexts_AIContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AIContext */ \"(rsc)/./src/contexts/AIContext.tsx\");\n\n // Correct import for Geist Sans\n\n\n\n\n\n// GeistSans from 'geist/font/sans' directly provides .variable and .className\n// No need to call it as a function like with next/font/google.\n// The variable it sets is typically --font-geist-sans.\nconst metadata = {\n    title: 'Pluto Trading Bot',\n    description: 'Simulated cryptocurrency trading bot with Neo Brutalist UI.'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: geist_font_sans__WEBPACK_IMPORTED_MODULE_1__.GeistSans.variable,\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans antialiased\",\n            suppressHydrationWarning: true,\n            children: [\n                \" \",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__.TradingProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AIContext__WEBPACK_IMPORTED_MODULE_6__.AIProvider, {\n                            children: [\n                                children,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\components\\ui\\toaster.tsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./src/contexts/AIContext.tsx":
/*!************************************!*\
  !*** ./src/contexts/AIContext.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AIProvider: () => (/* binding */ AIProvider),
/* harmony export */   useAIContext: () => (/* binding */ useAIContext)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AIProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AIProvider() from the server but AIProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AIContext.tsx",
"AIProvider",
);const useAIContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAIContext() from the server but useAIContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AIContext.tsx",
"useAIContext",
);

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AuthContext.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),
/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const TradingProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call TradingProvider() from the server but TradingProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\TradingContext.tsx",
"TradingProvider",
);const useTradingContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTradingContext() from the server but useTradingContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\TradingContext.tsx",
"useTradingContext",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNib3QlNUMlNUN0cmFkaW5nYm90X2ZpbmFsJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDYm90JTVDJTVDdHJhZGluZ2JvdF9maW5hbCU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2JvdCU1QyU1Q3RyYWRpbmdib3RfZmluYWwlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNib3QlNUMlNUN0cmFkaW5nYm90X2ZpbmFsJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNib3QlNUMlNUN0cmFkaW5nYm90X2ZpbmFsJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNib3QlNUMlNUN0cmFkaW5nYm90X2ZpbmFsJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNib3QlNUMlNUN0cmFkaW5nYm90X2ZpbmFsJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNib3QlNUMlNUN0cmFkaW5nYm90X2ZpbmFsJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBc0k7QUFDdEk7QUFDQSwwT0FBeUk7QUFDekk7QUFDQSwwT0FBeUk7QUFDekk7QUFDQSxvUkFBK0o7QUFDL0o7QUFDQSx3T0FBd0k7QUFDeEk7QUFDQSw0UEFBbUo7QUFDbko7QUFDQSxrUUFBc0o7QUFDdEo7QUFDQSxzUUFBdUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcYm90XFxcXHRyYWRpbmdib3RfZmluYWxcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcYm90XFxcXHRyYWRpbmdib3RfZmluYWxcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcYm90XFxcXHRyYWRpbmdib3RfZmluYWxcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAIContext.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CTradingContext.tsx%22%2C%22ids%22%3A%5B%22TradingProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAIContext.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CTradingContext.tsx%22%2C%22ids%22%3A%5B%22TradingProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toaster.tsx */ \"(ssr)/./src/components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AIContext.tsx */ \"(ssr)/./src/contexts/AIContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/TradingContext.tsx */ \"(ssr)/./src/contexts/TradingContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAIContext.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CTradingContext.tsx%22%2C%22ids%22%3A%5B%22TradingProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNib3QlNUMlNUN0cmFkaW5nYm90X2ZpbmFsJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQTRGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxib3RcXFxcdHJhZGluZ2JvdF9maW5hbFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/ai/flows/trading-mode-suggestion.ts":
/*!*************************************************!*\
  !*** ./src/ai/flows/trading-mode-suggestion.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   suggestTradingMode: () => (/* binding */ suggestTradingMode)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__);\n// src/ai/flows/trading-mode-suggestion.ts\n/* __next_internal_action_entry_do_not_use__ {\"4052d68cef5842cf51673e261240ff414a60693aaa\":\"suggestTradingMode\"} */ \nvar suggestTradingMode = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"4052d68cef5842cf51673e261240ff414a60693aaa\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"suggestTradingMode\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYWkvZmxvd3MvdHJhZGluZy1tb2RlLXN1Z2dlc3Rpb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsMENBQTBDOztJQXFDcEJBLG1DQUFBQSw2RkFBQUEsK0NBQUFBLDhFQUFBQSxVQUFBQSxvRkFBQUEiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxzcmNcXGFpXFxmbG93c1xcdHJhZGluZy1tb2RlLXN1Z2dlc3Rpb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2FpL2Zsb3dzL3RyYWRpbmctbW9kZS1zdWdnZXN0aW9uLnRzXG4ndXNlIHNlcnZlcic7XG5cbi8qKlxuICogQGZpbGVPdmVydmlldyBUaGlzIGZpbGUgaW1wbGVtZW50cyB0aGUgVHJhZGluZyBNb2RlIFN1Z2dlc3Rpb24gQUkgYWdlbnQuXG4gKlxuICogLSBzdWdnZXN0VHJhZGluZ01vZGUgLSBBIGZ1bmN0aW9uIHRoYXQgc3VnZ2VzdHMgdGhlIG1vc3Qgc3VpdGFibGUgdHJhZGluZyBtb2RlLlxuICogLSBUcmFkaW5nTW9kZVN1Z2dlc3Rpb25JbnB1dCAtIFRoZSBpbnB1dCB0eXBlIGZvciB0aGUgc3VnZ2VzdFRyYWRpbmdNb2RlIGZ1bmN0aW9uLlxuICogLSBUcmFkaW5nTW9kZVN1Z2dlc3Rpb25PdXRwdXQgLSBUaGUgcmV0dXJuIHR5cGUgZm9yIHRoZSBzdWdnZXN0VHJhZGluZ01vZGUgZnVuY3Rpb24uXG4gKi9cblxuaW1wb3J0IHthaX0gZnJvbSAnQC9haS9nZW5raXQnO1xuaW1wb3J0IHt6fSBmcm9tICdnZW5raXQnO1xuXG5jb25zdCBUcmFkaW5nTW9kZVN1Z2dlc3Rpb25JbnB1dFNjaGVtYSA9IHoub2JqZWN0KHtcbiAgcmlza1RvbGVyYW5jZTogelxuICAgIC5zdHJpbmcoKVxuICAgIC5kZXNjcmliZShcbiAgICAgICdUaGUgdXNlciByaXNrIHRvbGVyYW5jZSwgY2FuIGJlIGxvdywgbWVkaXVtLCBvciBoaWdoLidcbiAgICApLFxuICBwcmVmZXJyZWRDcnlwdG9jdXJyZW5jaWVzOiB6XG4gICAgLnN0cmluZygpXG4gICAgLmRlc2NyaWJlKCdUaGUgdXNlciBwcmVmZXJyZWQgY3J5cHRvY3VycmVuY2llcywgY29tbWEgc2VwYXJhdGVkLicpLFxuICBpbnZlc3RtZW50R29hbHM6IHpcbiAgICAuc3RyaW5nKClcbiAgICAuZGVzY3JpYmUoJ1RoZSB1c2VyIGludmVzdG1lbnQgZ29hbHMsIHN1Y2ggYXMgbG9uZyB0ZXJtIGludmVzdG1lbnQgb3Igc2hvcnQgdGVybSBwcm9maXQuJyksXG59KTtcbmV4cG9ydCB0eXBlIFRyYWRpbmdNb2RlU3VnZ2VzdGlvbklucHV0ID0gei5pbmZlcjx0eXBlb2YgVHJhZGluZ01vZGVTdWdnZXN0aW9uSW5wdXRTY2hlbWE+O1xuXG5jb25zdCBUcmFkaW5nTW9kZVN1Z2dlc3Rpb25PdXRwdXRTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIHN1Z2dlc3RlZE1vZGU6IHpcbiAgICAuZW51bShbJ1NpbXBsZSBTcG90JywgJ1N0YWJsZWNvaW4gU3dhcCddKVxuICAgIC5kZXNjcmliZSgnVGhlIHN1Z2dlc3RlZCB0cmFkaW5nIG1vZGUuJyksXG4gIHJlYXNvbjogei5zdHJpbmcoKS5kZXNjcmliZSgnVGhlIHJlYXNvbiBmb3IgdGhlIHN1Z2dlc3Rpb24uJyksXG59KTtcbmV4cG9ydCB0eXBlIFRyYWRpbmdNb2RlU3VnZ2VzdGlvbk91dHB1dCA9IHouaW5mZXI8dHlwZW9mIFRyYWRpbmdNb2RlU3VnZ2VzdGlvbk91dHB1dFNjaGVtYT47XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzdWdnZXN0VHJhZGluZ01vZGUoXG4gIGlucHV0OiBUcmFkaW5nTW9kZVN1Z2dlc3Rpb25JbnB1dFxuKTogUHJvbWlzZTxUcmFkaW5nTW9kZVN1Z2dlc3Rpb25PdXRwdXQ+IHtcbiAgcmV0dXJuIHN1Z2dlc3RUcmFkaW5nTW9kZUZsb3coaW5wdXQpO1xufVxuXG5jb25zdCBwcm9tcHQgPSBhaS5kZWZpbmVQcm9tcHQoe1xuICBuYW1lOiAndHJhZGluZ01vZGVTdWdnZXN0aW9uUHJvbXB0JyxcbiAgaW5wdXQ6IHtzY2hlbWE6IFRyYWRpbmdNb2RlU3VnZ2VzdGlvbklucHV0U2NoZW1hfSxcbiAgb3V0cHV0OiB7c2NoZW1hOiBUcmFkaW5nTW9kZVN1Z2dlc3Rpb25PdXRwdXRTY2hlbWF9LFxuICBwcm9tcHQ6IGBZb3UgYXJlIGFuIGV4cGVydCBpbiB0cmFkaW5nIG1vZGUgc2VsZWN0aW9uLiBZb3Ugd2lsbCBzdWdnZXN0IHRoZSBtb3N0IHN1aXRhYmxlIHRyYWRpbmcgbW9kZSAoU2ltcGxlIFNwb3Qgb3IgU3RhYmxlY29pbiBTd2FwKSBiYXNlZCBvbiB0aGUgdXNlcidzIHJpc2sgdG9sZXJhbmNlLCBwcmVmZXJyZWQgY3J5cHRvY3VycmVuY2llcywgYW5kIGludmVzdG1lbnQgZ29hbHMuXG5cblJpc2sgVG9sZXJhbmNlOiB7e3tyaXNrVG9sZXJhbmNlfX19XG5QcmVmZXJyZWQgQ3J5cHRvY3VycmVuY2llczoge3t7cHJlZmVycmVkQ3J5cHRvY3VycmVuY2llc319fVxuSW52ZXN0bWVudCBHb2Fsczoge3t7aW52ZXN0bWVudEdvYWxzfX19XG5cbkNvbnNpZGVyIHRoZSBmb2xsb3dpbmc6XG5cbiogICBTaW1wbGUgU3BvdCBNb2RlIGlzIHN1aXRhYmxlIGZvciB1c2VycyB3aG8gYXJlIGNvbWZvcnRhYmxlIHdpdGggaGlnaGVyIHJpc2sgYW5kIGFyZSBsb29raW5nIGZvciBzaG9ydCB0ZXJtIHByb2ZpdHMuXG4qICAgU3RhYmxlY29pbiBTd2FwIE1vZGUgaXMgc3VpdGFibGUgZm9yIHVzZXJzIHdobyBhcmUgcmlzayBhdmVyc2UgYW5kIGFyZSBsb29raW5nIGZvciBsb25nIHRlcm0gaW52ZXN0bWVudC5cblxuQmFzZWQgb24gdGhlIGluZm9ybWF0aW9uIGFib3ZlLCBzdWdnZXN0IGEgdHJhZGluZyBtb2RlIGFuZCBleHBsYWluIHlvdXIgcmVhc29uaW5nLmAsXG59KTtcblxuY29uc3Qgc3VnZ2VzdFRyYWRpbmdNb2RlRmxvdyA9IGFpLmRlZmluZUZsb3coXG4gIHtcbiAgICBuYW1lOiAnc3VnZ2VzdFRyYWRpbmdNb2RlRmxvdycsXG4gICAgaW5wdXRTY2hlbWE6IFRyYWRpbmdNb2RlU3VnZ2VzdGlvbklucHV0U2NoZW1hLFxuICAgIG91dHB1dFNjaGVtYTogVHJhZGluZ01vZGVTdWdnZXN0aW9uT3V0cHV0U2NoZW1hLFxuICB9LFxuICBhc3luYyBpbnB1dCA9PiB7XG4gICAgY29uc3Qge291dHB1dH0gPSBhd2FpdCBwcm9tcHQoaW5wdXQpO1xuICAgIHJldHVybiBvdXRwdXQhO1xuICB9XG4pO1xuIl0sIm5hbWVzIjpbInN1Z2dlc3RUcmFkaW5nTW9kZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/ai/flows/trading-mode-suggestion.ts\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction HomePage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isAuthenticated, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            if (!isLoading) {\n                if (isAuthenticated) {\n                    router.replace('/dashboard');\n                } else {\n                    router.replace('/login');\n                }\n            }\n        }\n    }[\"HomePage.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-12 w-12 animate-spin text-primary\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"ml-4 text-xl\",\n                children: \"Initializing Pluto...\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive group border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 77,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./src/components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastClose, {}, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastViewport, {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AIContext.tsx":
/*!************************************!*\
  !*** ./src/contexts/AIContext.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIProvider: () => (/* binding */ AIProvider),\n/* harmony export */   useAIContext: () => (/* binding */ useAIContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ai_flows_trading_mode_suggestion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/ai/flows/trading-mode-suggestion */ \"(ssr)/./src/ai/flows/trading-mode-suggestion.ts\");\n/* __next_internal_client_entry_do_not_use__ AIProvider,useAIContext auto */ \n\n\nconst AIContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AIProvider = ({ children })=>{\n    const [suggestion, setSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getTradingModeSuggestion = async (input)=>{\n        setIsLoading(true);\n        setError(null);\n        setSuggestion(null);\n        try {\n            const result = await (0,_ai_flows_trading_mode_suggestion__WEBPACK_IMPORTED_MODULE_2__.suggestTradingMode)(input);\n            setSuggestion(result);\n        } catch (e) {\n            setError(e instanceof Error ? e.message : \"An unknown error occurred during AI suggestion.\");\n            console.error(\"Error fetching trading mode suggestion:\", e);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIContext.Provider, {\n        value: {\n            suggestion,\n            isLoading,\n            error,\n            getTradingModeSuggestion\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AIContext.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAIContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AIContext);\n    if (context === undefined) {\n        throw new Error('useAIContext must be used within an AIProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AIContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check auth status from localStorage\n            const storedAuthStatus = localStorage.getItem('plutoAuth');\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (storedAuthStatus === 'true' && authToken) {\n                setIsAuthenticated(true);\n            }\n            setIsLoading(false);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated && pathname !== '/login') {\n                router.push('/login');\n            } else if (!isLoading && isAuthenticated && pathname === '/login') {\n                router.push('/dashboard');\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        pathname,\n        router\n    ]);\n    const login = async (username, password)=>{\n        setIsLoading(true);\n        try {\n            const success = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authApi.login(username, password);\n            if (success) {\n                setIsAuthenticated(true);\n                router.push('/dashboard');\n                return true;\n            }\n            setIsAuthenticated(false);\n            return false;\n        } catch (error) {\n            console.error('Login failed:', error);\n            setIsAuthenticated(false);\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authApi.logout();\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            setIsAuthenticated(false);\n            router.push('/login');\n        }\n    };\n    // Consistent loading display across contexts\n    if (isLoading && !pathname?.startsWith('/_next/static/')) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-screen bg-background text-foreground\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-12 w-12 animate-spin text-primary\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"ml-4 text-xl\",\n                    children: \"Loading Pluto...\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!isAuthenticated && pathname !== '/login' && !pathname?.startsWith('/_next/static/')) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-screen bg-background text-foreground\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-12 w-12 animate-spin text-primary\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"ml-4 text-xl\",\n                    children: \"Redirecting to login...\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            isAuthenticated,\n            login,\n            logout,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvQXV0aENvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFHOEU7QUFDckI7QUFDbEI7QUFDSDtBQVFwQyxNQUFNUyw0QkFBY1Isb0RBQWFBLENBQThCUztBQUV4RCxNQUFNQyxlQUFlLENBQUMsRUFBRUMsUUFBUSxFQUEyQjtJQUNoRSxNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUdYLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ1ksV0FBV0MsYUFBYSxHQUFHYiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNYyxTQUFTWiwwREFBU0E7SUFDeEIsTUFBTWEsV0FBV1osNERBQVdBO0lBRTVCRixnREFBU0E7a0NBQUM7WUFDUixzQ0FBc0M7WUFDdEMsTUFBTWUsbUJBQW1CQyxhQUFhQyxPQUFPLENBQUM7WUFDOUMsTUFBTUMsWUFBWUYsYUFBYUMsT0FBTyxDQUFDO1lBQ3ZDLElBQUlGLHFCQUFxQixVQUFVRyxXQUFXO2dCQUM1Q1IsbUJBQW1CO1lBQ3JCO1lBQ0FFLGFBQWE7UUFDZjtpQ0FBRyxFQUFFO0lBRUxaLGdEQUFTQTtrQ0FBQztZQUNSLElBQUksQ0FBQ1csYUFBYSxDQUFDRixtQkFBbUJLLGFBQWEsVUFBVTtnQkFDM0RELE9BQU9NLElBQUksQ0FBQztZQUNkLE9BQU8sSUFBSSxDQUFDUixhQUFhRixtQkFBbUJLLGFBQWEsVUFBVTtnQkFDakVELE9BQU9NLElBQUksQ0FBQztZQUNkO1FBQ0Y7aUNBQUc7UUFBQ1Y7UUFBaUJFO1FBQVdHO1FBQVVEO0tBQU87SUFFakQsTUFBTU8sUUFBUSxPQUFPQyxVQUFrQkM7UUFDckNWLGFBQWE7UUFDYixJQUFJO1lBQ0YsTUFBTVcsVUFBVSxNQUFNbkIsNkNBQU9BLENBQUNnQixLQUFLLENBQUNDLFVBQVVDO1lBQzlDLElBQUlDLFNBQVM7Z0JBQ1hiLG1CQUFtQjtnQkFDbkJHLE9BQU9NLElBQUksQ0FBQztnQkFDWixPQUFPO1lBQ1Q7WUFDQVQsbUJBQW1CO1lBQ25CLE9BQU87UUFDVCxFQUFFLE9BQU9jLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGlCQUFpQkE7WUFDL0JkLG1CQUFtQjtZQUNuQixPQUFPO1FBQ1QsU0FBVTtZQUNSRSxhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU1jLFNBQVM7UUFDYixJQUFJO1lBQ0YsTUFBTXRCLDZDQUFPQSxDQUFDc0IsTUFBTTtRQUN0QixFQUFFLE9BQU9GLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGlCQUFpQkE7UUFDakMsU0FBVTtZQUNSZCxtQkFBbUI7WUFDbkJHLE9BQU9NLElBQUksQ0FBQztRQUNkO0lBQ0Y7SUFFQSw2Q0FBNkM7SUFDN0MsSUFBSVIsYUFBYSxDQUFDRyxVQUFVYSxXQUFXLG1CQUFtQjtRQUN4RCxxQkFDRSw4REFBQ0M7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUMxQixtRkFBT0E7b0JBQUMwQixXQUFVOzs7Ozs7OEJBQ25CLDhEQUFDQztvQkFBRUQsV0FBVTs4QkFBZTs7Ozs7Ozs7Ozs7O0lBR2xDO0lBRUEsSUFBSSxDQUFDcEIsbUJBQW1CSyxhQUFhLFlBQVksQ0FBQ0EsVUFBVWEsV0FBVyxtQkFBbUI7UUFDeEYscUJBQ0UsOERBQUNDO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDMUIsbUZBQU9BO29CQUFDMEIsV0FBVTs7Ozs7OzhCQUNuQiw4REFBQ0M7b0JBQUVELFdBQVU7OEJBQWU7Ozs7Ozs7Ozs7OztJQUdsQztJQUVBLHFCQUNFLDhEQUFDeEIsWUFBWTBCLFFBQVE7UUFBQ0MsT0FBTztZQUFFdkI7WUFBaUJXO1lBQU9NO1lBQVFmO1FBQVU7a0JBQ3RFSDs7Ozs7O0FBR1AsRUFBRTtBQUVLLE1BQU15QixVQUFVO0lBQ3JCLE1BQU1DLFVBQVVwQyxpREFBVUEsQ0FBQ087SUFDM0IsSUFBSTZCLFlBQVk1QixXQUFXO1FBQ3pCLE1BQU0sSUFBSTZCLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNULEVBQUUiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxzcmNcXGNvbnRleHRzXFxBdXRoQ29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXG5cInVzZSBjbGllbnRcIjtcbmltcG9ydCB0eXBlIHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IExvYWRlcjIgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgYXV0aEFwaSB9IGZyb20gJ0AvbGliL2FwaSc7XG5cbmludGVyZmFjZSBBdXRoQ29udGV4dFR5cGUge1xuICBpc0F1dGhlbnRpY2F0ZWQ6IGJvb2xlYW47XG4gIGxvZ2luOiAodXNlcm5hbWU6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykgPT4gUHJvbWlzZTxib29sZWFuPjtcbiAgbG9nb3V0OiAoKSA9PiB2b2lkO1xuICBpc0xvYWRpbmc6IGJvb2xlYW47XG59XG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QXV0aENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG5leHBvcnQgY29uc3QgQXV0aFByb3ZpZGVyID0gKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3ROb2RlIH0pID0+IHtcbiAgY29uc3QgW2lzQXV0aGVudGljYXRlZCwgc2V0SXNBdXRoZW50aWNhdGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xuICBcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBDaGVjayBhdXRoIHN0YXR1cyBmcm9tIGxvY2FsU3RvcmFnZVxuICAgIGNvbnN0IHN0b3JlZEF1dGhTdGF0dXMgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncGx1dG9BdXRoJyk7XG4gICAgY29uc3QgYXV0aFRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3BsdXRvQXV0aFRva2VuJyk7XG4gICAgaWYgKHN0b3JlZEF1dGhTdGF0dXMgPT09ICd0cnVlJyAmJiBhdXRoVG9rZW4pIHtcbiAgICAgIHNldElzQXV0aGVudGljYXRlZCh0cnVlKTtcbiAgICB9XG4gICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgfSwgW10pO1xuICBcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWlzTG9hZGluZyAmJiAhaXNBdXRoZW50aWNhdGVkICYmIHBhdGhuYW1lICE9PSAnL2xvZ2luJykge1xuICAgICAgcm91dGVyLnB1c2goJy9sb2dpbicpO1xuICAgIH0gZWxzZSBpZiAoIWlzTG9hZGluZyAmJiBpc0F1dGhlbnRpY2F0ZWQgJiYgcGF0aG5hbWUgPT09ICcvbG9naW4nKSB7XG4gICAgICByb3V0ZXIucHVzaCgnL2Rhc2hib2FyZCcpO1xuICAgIH1cbiAgfSwgW2lzQXV0aGVudGljYXRlZCwgaXNMb2FkaW5nLCBwYXRobmFtZSwgcm91dGVyXSk7XG4gIFxuICBjb25zdCBsb2dpbiA9IGFzeW5jICh1c2VybmFtZTogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBzdWNjZXNzID0gYXdhaXQgYXV0aEFwaS5sb2dpbih1c2VybmFtZSwgcGFzc3dvcmQpO1xuICAgICAgaWYgKHN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0SXNBdXRoZW50aWNhdGVkKHRydWUpO1xuICAgICAgICByb3V0ZXIucHVzaCgnL2Rhc2hib2FyZCcpO1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgIH1cbiAgICAgIHNldElzQXV0aGVudGljYXRlZChmYWxzZSk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0xvZ2luIGZhaWxlZDonLCBlcnJvcik7XG4gICAgICBzZXRJc0F1dGhlbnRpY2F0ZWQoZmFsc2UpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcbiAgXG4gIGNvbnN0IGxvZ291dCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgYXdhaXQgYXV0aEFwaS5sb2dvdXQoKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignTG9nb3V0IGVycm9yOicsIGVycm9yKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNBdXRoZW50aWNhdGVkKGZhbHNlKTtcbiAgICAgIHJvdXRlci5wdXNoKCcvbG9naW4nKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gQ29uc2lzdGVudCBsb2FkaW5nIGRpc3BsYXkgYWNyb3NzIGNvbnRleHRzXG4gIGlmIChpc0xvYWRpbmcgJiYgIXBhdGhuYW1lPy5zdGFydHNXaXRoKCcvX25leHQvc3RhdGljLycpKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC1zY3JlZW4gYmctYmFja2dyb3VuZCB0ZXh0LWZvcmVncm91bmRcIj5cbiAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwiaC0xMiB3LTEyIGFuaW1hdGUtc3BpbiB0ZXh0LXByaW1hcnlcIiAvPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJtbC00IHRleHQteGxcIj5Mb2FkaW5nIFBsdXRvLi4uPC9wPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuICBcbiAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQgJiYgcGF0aG5hbWUgIT09ICcvbG9naW4nICYmICFwYXRobmFtZT8uc3RhcnRzV2l0aCgnL19uZXh0L3N0YXRpYy8nKSkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtc2NyZWVuIGJnLWJhY2tncm91bmQgdGV4dC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtMTIgdy0xMiBhbmltYXRlLXNwaW4gdGV4dC1wcmltYXJ5XCIgLz5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwibWwtNCB0ZXh0LXhsXCI+UmVkaXJlY3RpbmcgdG8gbG9naW4uLi48L3A+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8QXV0aENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3sgaXNBdXRoZW50aWNhdGVkLCBsb2dpbiwgbG9nb3V0LCBpc0xvYWRpbmcgfX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9BdXRoQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn07XG5cbmV4cG9ydCBjb25zdCB1c2VBdXRoID0gKCk6IEF1dGhDb250ZXh0VHlwZSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJ1c2VQYXRobmFtZSIsIkxvYWRlcjIiLCJhdXRoQXBpIiwiQXV0aENvbnRleHQiLCJ1bmRlZmluZWQiLCJBdXRoUHJvdmlkZXIiLCJjaGlsZHJlbiIsImlzQXV0aGVudGljYXRlZCIsInNldElzQXV0aGVudGljYXRlZCIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsInJvdXRlciIsInBhdGhuYW1lIiwic3RvcmVkQXV0aFN0YXR1cyIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJhdXRoVG9rZW4iLCJwdXNoIiwibG9naW4iLCJ1c2VybmFtZSIsInBhc3N3b3JkIiwic3VjY2VzcyIsImVycm9yIiwiY29uc29sZSIsImxvZ291dCIsInN0YXJ0c1dpdGgiLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZUF1dGgiLCJjb250ZXh0IiwiRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(ssr)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(ssr)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(ssr)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Default fallback value\n    return 1.0;\n};\n// Enhanced API function to get market price for any trading pair\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Try multiple API endpoints for better coverage\n        const symbol = `${config.crypto1}${config.crypto2}`.toUpperCase();\n        // First try Binance API\n        try {\n            const response = await fetch(`https://api.binance.com/api/v3/ticker/price?symbol=${symbol}`);\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(`✅ Price fetched from Binance: ${config.crypto1}/${config.crypto2} = ${price}`);\n                    return price;\n                }\n            }\n        } catch (binanceError) {\n            console.warn('Binance API failed, trying alternative...', binanceError);\n        }\n        // Fallback to CoinGecko API for broader pair support\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${crypto1Id}&vs_currencies=${crypto2Id}`);\n                if (response.ok) {\n                    const data = await response.json();\n                    const price = data[crypto1Id]?.[crypto2Id];\n                    if (price > 0) {\n                        console.log(`✅ Price fetched from CoinGecko: ${config.crypto1}/${config.crypto2} = ${price}`);\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed, using mock price...', geckoError);\n        }\n        // Final fallback to mock price\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(`⚠️ Using mock price: ${config.crypto1}/${config.crypto2} = ${mockPrice}`);\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${cryptoId}&vs_currencies=${stablecoinId}`);\n            if (response.ok) {\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? data[cryptoId]?.[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(`📊 Stablecoin rate: ${crypto}/${stablecoin} = ${rate}`);\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(`📊 Fallback stablecoin rate: ${crypto}/${stablecoin} = ${rate} (via USD)`);\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Helper function to get USD price (extracted from calculateFallbackMarketPrice)\nconst getUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies\n        'BTC': 109000,\n        'ETH': 4000,\n        'SOL': 240,\n        'ADA': 1.2,\n        'DOGE': 0.4,\n        'LINK': 25,\n        'MATIC': 0.5,\n        'DOT': 8,\n        'AVAX': 45,\n        'SHIB': 0.000030,\n        'XRP': 2.5,\n        'LTC': 110,\n        'BCH': 500,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    // Add small random fluctuation\n    const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%\n    const finalPrice = basePrice * (1 + fluctuation);\n    console.log(`📊 Fallback price calculation: ${config.crypto1} ($${crypto1USDPrice}) / ${config.crypto2} ($${crypto2USDPrice}) = ${finalPrice.toFixed(6)}`);\n    return finalPrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_CRYPTOS[0],\n    crypto2: (_lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_QUOTES_SIMPLE[_lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_CRYPTOS[0]] || DEFAULT_QUOTE_CURRENCIES)[0],\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown',\n    connectionStatus: 'online'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (false) {}\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (false) {}\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        case 'FLUCTUATE_MARKET_PRICE':\n            {\n                if (state.currentMarketPrice <= 0) return state;\n                // More realistic fluctuation: smaller, more frequent changes\n                const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update\n                const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);\n                return {\n                    ...state,\n                    currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice\n                };\n            }\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset)\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SET_CONNECTION_STATUS':\n            // If connection goes offline and bot is running, stop it\n            if (action.payload === 'offline' && state.botSystemStatus === 'Running') {\n                console.warn('🔴 Connection lost - stopping bot automatically');\n                return {\n                    ...state,\n                    connectionStatus: action.payload,\n                    botSystemStatus: 'Stopped'\n                };\n            }\n            return {\n                ...state,\n                connectionStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Reset all target price rows and clear history\n            const resetTargetPriceRows = state.targetPriceRows.map((row)=>({\n                    ...row,\n                    status: 'Free',\n                    orderLevel: 0,\n                    valueLevel: state.config.baseBid,\n                    crypto1AmountHeld: undefined,\n                    originalCostCrypto2: undefined,\n                    crypto1Var: undefined,\n                    crypto2Var: undefined,\n                    lastActionTimestamp: undefined\n                }));\n            lastActionTimestampPerCounter.clear();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: resetTargetPriceRows,\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = ({ children })=>{\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                const price = await getMarketPriceFromAPI(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Market price fluctuation effect - simulates real-time price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up price fluctuation interval for simulation\n            const priceFluctuationInterval = setInterval({\n                \"TradingProvider.useEffect.priceFluctuationInterval\": ()=>{\n                    dispatch({\n                        type: 'FLUCTUATE_MARKET_PRICE'\n                    });\n                }\n            }[\"TradingProvider.useEffect.priceFluctuationInterval\"], 2000); // Update every 2 seconds for realistic simulation\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceFluctuationInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        dispatch\n    ]);\n    // Other effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            if (state.appSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && state.appSettings.alertOnOrderExecution) {\n                    soundPath = state.appSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && state.appSettings.alertOnError) {\n                    soundPath = state.appSettings.soundError;\n                }\n                if (soundPath) {\n                    audioRef.current.src = soundPath;\n                    audioRef.current.currentTime = 0; // Reset to beginning\n                    // Play the sound and limit duration to 2 seconds\n                    audioRef.current.play().then({\n                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                            // Set a timeout to pause the audio after 2 seconds\n                            setTimeout({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.pause();\n                                        audioRef.current.currentTime = 0; // Reset for next play\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                        }\n                    }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                        \"TradingProvider.useCallback[playSound]\": (err)=>console.error(\"Error playing sound:\", err)\n                    }[\"TradingProvider.useCallback[playSound]\"]);\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Telegram notification function\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async (message)=>{\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(`https://api.telegram.org/bot${telegramToken}/sendMessage`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Only check essential conditions including connection status\n            if (state.botSystemStatus !== 'Running' || state.connectionStatus !== 'online' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0) {\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(`🚀 CONTINUOUS TRADING: Price $${currentMarketPrice.toFixed(2)} | Targets: ${sortedRowsForLogic.length} | Balance: $${currentCrypto2Balance} ${config.crypto2}`);\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(`🎯 TARGETS IN RANGE (±${config.slippagePercent}%):`, targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>`Counter ${row.counter} (${row.status})`\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: `${config.crypto1}/${config.crypto2}`,\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(`✅ BUY: Counter ${activeRow.counter} bought ${amountCrypto1Bought.toFixed(6)} ${config.crypto1} at $${currentMarketPrice.toFixed(2)}`);\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: `Counter ${activeRow.counter}: ${amountCrypto1Bought.toFixed(6)} ${config.crypto1}`,\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(`🟢 <b>BUY EXECUTED</b>\\n` + `📊 Counter: ${activeRow.counter}\\n` + `💰 Amount: ${amountCrypto1Bought.toFixed(6)} ${config.crypto1}\\n` + `💵 Price: $${currentMarketPrice.toFixed(2)}\\n` + `💸 Cost: $${costCrypto2.toFixed(2)} ${config.crypto2}\\n` + `📈 Mode: Simple Spot`);\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: `${config.crypto1}/${config.crypto2}`,\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(`✅ SELL: Counter ${currentCounter - 1} sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1}. Profit: $${realizedProfit.toFixed(2)}`);\n                            toast({\n                                title: \"SELL Executed\",\n                                description: `Counter ${currentCounter - 1}: Profit $${realizedProfit.toFixed(2)}`,\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(`🔴 <b>SELL EXECUTED</b>\\n` + `📊 Counter: ${currentCounter - 1}\\n` + `💰 Amount: ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1}\\n` + `💵 Price: $${currentMarketPrice.toFixed(2)}\\n` + `💸 Received: $${crypto2Received.toFixed(2)} ${config.crypto2}\\n` + `${profitEmoji} Profit: $${realizedProfit.toFixed(2)} ${config.crypto2}\\n` + `📈 Mode: Simple Spot`);\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Get real market price for Crypto2/Stablecoin pair (synchronous for now)\n                                const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Get real market price for Crypto1/Stablecoin pair\n                                const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: `${config.crypto2}/${config.preferredStablecoin}`,\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: `${config.crypto1}/${config.preferredStablecoin}`,\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(`✅ STABLECOIN BUY: Counter ${activeRow.counter} | Step 1: Sold ${amountCrypto2ToUse} ${config.crypto2} → ${stablecoinObtained.toFixed(2)} ${config.preferredStablecoin} | Step 2: Bought ${crypto1Bought.toFixed(6)} ${config.crypto1} | Level: ${activeRow.orderLevel} → ${newLevel}`);\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: `Counter ${activeRow.counter}: ${crypto1Bought.toFixed(6)} ${config.crypto1} via ${config.preferredStablecoin}`,\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(`🟢 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n` + `📊 Counter: ${activeRow.counter}\\n` + `🔄 Step 1: Sold ${amountCrypto2ToUse.toFixed(2)} ${config.crypto2} → ${stablecoinObtained.toFixed(2)} ${config.preferredStablecoin}\\n` + `🔄 Step 2: Bought ${crypto1Bought.toFixed(6)} ${config.crypto1}\\n` + `📊 Level: ${activeRow.orderLevel} → ${newLevel}\\n` + `📈 Mode: Stablecoin Swap`);\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            const realizedProfitInCrypto2 = crypto2Reacquired - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 * config.incomeSplitCrypto1Percent / 100 / crypto1StablecoinPrice : 0;\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: `${config.crypto1}/${config.preferredStablecoin}`,\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: `${config.crypto2}/${config.preferredStablecoin}`,\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(`✅ STABLECOIN SELL: Counter ${currentCounter - 1} | Step A: Sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1} → ${stablecoinFromC1Sell.toFixed(2)} ${config.preferredStablecoin} | Step B: Bought ${crypto2Reacquired.toFixed(2)} ${config.crypto2} | Profit: ${realizedProfitInCrypto2.toFixed(2)} ${config.crypto2} | Level: ${inferiorRow.orderLevel} (unchanged)`);\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: `Counter ${currentCounter - 1}: Profit ${realizedProfitInCrypto2.toFixed(2)} ${config.crypto2} via ${config.preferredStablecoin}`,\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(`🔴 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n` + `📊 Counter: ${currentCounter - 1}\\n` + `🔄 Step A: Sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1} → ${stablecoinFromC1Sell.toFixed(2)} ${config.preferredStablecoin}\\n` + `🔄 Step B: Bought ${crypto2Reacquired.toFixed(2)} ${config.crypto2}\\n` + `${profitEmoji} Profit: ${realizedProfitInCrypto2.toFixed(2)} ${config.crypto2}\\n` + `📊 Level: ${inferiorRow.orderLevel} (unchanged)\\n` + `📈 Mode: Stablecoin Swap`);\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(`🎯 CYCLE COMPLETE: ${actionsTaken} actions taken at price $${currentMarketPrice.toFixed(2)}`);\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                const configData = {\n                    name: `${config.crypto1}/${config.crypto2} ${config.tradingMode}`,\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return response.config?.id || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(`${apiUrl}/health/`);\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(`Backend health check failed with status: ${healthResponse.status} ${healthResponse.statusText}`);\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', `${apiUrl}/health/`);\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline)=>{\n                    console.log(`🌐 Network status changed: ${isOnline ? 'Online' : 'Offline'}`);\n                    if (!isOnline) {\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"You are currently offline. Data will be saved locally.\",\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    } else {\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. Auto-saving enabled.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(`🧠 High memory usage: ${usedMB.toFixed(2)}MB`);\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: `Memory usage is high (${usedMB.toFixed(0)}MB). Consider refreshing the page.`,\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Connection status setter\n    const setConnectionStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setConnectionStatus]\": (status)=>{\n            dispatch({\n                type: 'SET_CONNECTION_STATUS',\n                payload: status\n            });\n        }\n    }[\"TradingProvider.useCallback[setConnectionStatus]\"], [\n        dispatch\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        setConnectionStatus,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        backendStatus: state.backendStatus,\n        connectionStatus: state.connectionStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1298,\n        columnNumber: 5\n    }, undefined);\n};\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/TradingContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-toast.ts":
/*!********************************!*\
  !*** ./src/hooks/use-toast.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ // Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiApi: () => (/* binding */ aiApi),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   sessionApi: () => (/* binding */ sessionApi),\n/* harmony export */   tradingApi: () => (/* binding */ tradingApi),\n/* harmony export */   userApi: () => (/* binding */ userApi)\n/* harmony export */ });\n// API utility functions for backend communication\n// Configure base URL - can be overridden if needed for different environments\nconst API_BASE_URL = \"http://localhost:5000\" || 0;\nconsole.log('API Base URL:', API_BASE_URL); // Log the URL to help with debugging\n// Generic fetch wrapper with error handling\nasync function fetchWithAuth(endpoint, options = {}) {\n    const url = `${API_BASE_URL}${endpoint}`;\n    // Add auth token if available\n    const token = localStorage.getItem('plutoAuthToken');\n    const headers = {\n        'Content-Type': 'application/json',\n        ...token ? {\n            'Authorization': `Bearer ${token}`\n        } : {},\n        ...options.headers\n    };\n    try {\n        // Add timeout to fetch request\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 10000); // 10 second timeout\n        const response = await fetch(url, {\n            ...options,\n            headers,\n            signal: controller.signal\n        }).finally(()=>clearTimeout(timeoutId));\n        // Handle unauthorized responses\n        if (response.status === 401) {\n            localStorage.removeItem('plutoAuth');\n            localStorage.removeItem('plutoAuthToken');\n            localStorage.removeItem('plutoUser');\n            if (false) {}\n            throw new Error('Authentication expired. Please login again.');\n        }\n        // Parse JSON response (safely)\n        let data;\n        const contentType = response.headers.get('content-type');\n        if (contentType && contentType.includes('application/json')) {\n            data = await response.json();\n        } else {\n            // Handle non-JSON responses\n            const text = await response.text();\n            try {\n                data = JSON.parse(text);\n            } catch (e) {\n                // If it's not parseable JSON, use the text as is\n                data = {\n                    message: text\n                };\n            }\n        }\n        // Handle API errors\n        if (!response.ok) {\n            console.error('API error response:', data);\n            throw new Error(data.error || data.message || `API error: ${response.status}`);\n        }\n        return data;\n    } catch (error) {\n        // Handle network errors specifically\n        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {\n            console.error('Network error - Is the backend server running?:', error);\n            throw new Error('Cannot connect to server. Please check if the backend is running.');\n        }\n        // Handle timeout\n        if (error.name === 'AbortError') {\n            console.error('Request timeout:', error);\n            throw new Error('Request timed out. Server may be unavailable.');\n        }\n        console.error('API request failed:', error);\n        throw error;\n    }\n}\n// Auth API functions\nconst authApi = {\n    login: async (username, password)=>{\n        try {\n            // Use retry mechanism for login attempts\n            const data = await retryWithBackoff(async ()=>{\n                return await fetchWithAuth('/auth/login', {\n                    method: 'POST',\n                    body: JSON.stringify({\n                        username,\n                        password\n                    })\n                });\n            });\n            // The backend returns access_token in the response\n            if (data && data.access_token) {\n                localStorage.setItem('plutoAuthToken', data.access_token);\n                localStorage.setItem('plutoAuth', 'true');\n                // Also store user data if available\n                if (data.user) {\n                    localStorage.setItem('plutoUser', JSON.stringify(data.user));\n                }\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error('Login API error:', error);\n            return false;\n        }\n    },\n    register: async (username, password, email)=>{\n        // Use retry mechanism for registration attempts\n        return retryWithBackoff(async ()=>{\n            return await fetchWithAuth('/auth/register', {\n                method: 'POST',\n                body: JSON.stringify({\n                    username,\n                    password,\n                    email\n                })\n            });\n        });\n    },\n    logout: async ()=>{\n        localStorage.removeItem('plutoAuth');\n        localStorage.removeItem('plutoAuthToken');\n        localStorage.removeItem('plutoUser');\n        return true;\n    }\n};\n// Trading API functions\nconst tradingApi = {\n    getConfig: async (configId)=>{\n        return fetchWithAuth(configId ? `/trading/config/${configId}` : '/trading/config');\n    },\n    saveConfig: async (config)=>{\n        return fetchWithAuth('/trading/config', {\n            method: 'POST',\n            body: JSON.stringify(config)\n        });\n    },\n    updateConfig: async (configId, config)=>{\n        return fetchWithAuth(`/trading/config/${configId}`, {\n            method: 'PUT',\n            body: JSON.stringify(config)\n        });\n    },\n    startBot: async (configId)=>{\n        return fetchWithAuth(`/trading/bot/start/${configId}`, {\n            method: 'POST'\n        });\n    },\n    stopBot: async (configId)=>{\n        return fetchWithAuth(`/trading/bot/stop/${configId}`, {\n            method: 'POST'\n        });\n    },\n    getBotStatus: async (configId)=>{\n        return fetchWithAuth(`/trading/bot/status/${configId}`);\n    },\n    getTradeHistory: async (configId)=>{\n        const params = configId ? `?configId=${configId}` : '';\n        return fetchWithAuth(`/trading/history${params}`);\n    },\n    getBalances: async ()=>{\n        return fetchWithAuth('/trading/balances');\n    },\n    getMarketPrice: async (symbol)=>{\n        return fetchWithAuth(`/trading/market-data/${symbol}`);\n    },\n    getTradingPairs: async (exchange = 'binance')=>{\n        return fetchWithAuth(`/trading/exchange/trading-pairs?exchange=${exchange}`);\n    },\n    getCryptocurrencies: async (exchange = 'binance')=>{\n        return fetchWithAuth(`/trading/exchange/cryptocurrencies?exchange=${exchange}`);\n    }\n};\n// User API functions\nconst userApi = {\n    getProfile: async ()=>{\n        return fetchWithAuth('/user/profile');\n    },\n    updateProfile: async (profileData)=>{\n        return fetchWithAuth('/user/profile', {\n            method: 'PUT',\n            body: JSON.stringify(profileData)\n        });\n    },\n    saveApiKeys: async (exchangeData)=>{\n        return fetchWithAuth('/user/apikeys', {\n            method: 'POST',\n            body: JSON.stringify(exchangeData)\n        });\n    },\n    getApiKeys: async ()=>{\n        return fetchWithAuth('/user/apikeys');\n    }\n};\n// AI API functions\nconst aiApi = {\n    getTradingSuggestion: async (data)=>{\n        return fetchWithAuth('/ai/trading-suggestion', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n};\n// Session API functions\nconst sessionApi = {\n    getAllSessions: async (includeInactive = true)=>{\n        return fetchWithAuth(`/sessions/?include_inactive=${includeInactive}`);\n    },\n    createSession: async (sessionData)=>{\n        return fetchWithAuth('/sessions/', {\n            method: 'POST',\n            body: JSON.stringify(sessionData)\n        });\n    },\n    getSession: async (sessionId)=>{\n        return fetchWithAuth(`/sessions/${sessionId}`);\n    },\n    updateSession: async (sessionId, sessionData)=>{\n        return fetchWithAuth(`/sessions/${sessionId}`, {\n            method: 'PUT',\n            body: JSON.stringify(sessionData)\n        });\n    },\n    deleteSession: async (sessionId)=>{\n        return fetchWithAuth(`/sessions/${sessionId}`, {\n            method: 'DELETE'\n        });\n    },\n    activateSession: async (sessionId)=>{\n        return fetchWithAuth(`/sessions/${sessionId}/activate`, {\n            method: 'POST'\n        });\n    },\n    getSessionHistory: async (sessionId)=>{\n        return fetchWithAuth(`/sessions/${sessionId}/history`);\n    },\n    getActiveSession: async ()=>{\n        return fetchWithAuth('/sessions/active');\n    }\n};\n// Add a retry mechanism for transient connection issues\nconst retryWithBackoff = async (fn, maxRetries = 3)=>{\n    let retries = 0;\n    const execute = async ()=>{\n        try {\n            return await fn();\n        } catch (error) {\n            // Only retry on network errors, not on 4xx/5xx responses\n            if (error instanceof TypeError && error.message.includes('Failed to fetch') || error.name === 'AbortError') {\n                if (retries < maxRetries) {\n                    const delay = Math.pow(2, retries) * 500; // Exponential backoff\n                    console.log(`Retrying after ${delay}ms (attempt ${retries + 1}/${maxRetries})...`);\n                    retries++;\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                    return execute();\n                }\n            }\n            throw error;\n        }\n    };\n    return execute();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/network-monitor.ts":
/*!************************************!*\
  !*** ./src/lib/network-monitor.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoSaveManager: () => (/* binding */ AutoSaveManager),\n/* harmony export */   MemoryMonitor: () => (/* binding */ MemoryMonitor),\n/* harmony export */   NetworkMonitor: () => (/* binding */ NetworkMonitor)\n/* harmony export */ });\nclass NetworkMonitor {\n    constructor(){\n        this.isOnline = navigator.onLine;\n        this.listeners = new Set();\n        this.lastOnlineTime = Date.now();\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.reconnectInterval = 5000 // 5 seconds\n        ;\n        this.setupEventListeners();\n        this.startPeriodicCheck();\n    }\n    static getInstance() {\n        if (!NetworkMonitor.instance) {\n            NetworkMonitor.instance = new NetworkMonitor();\n        }\n        return NetworkMonitor.instance;\n    }\n    setupEventListeners() {\n        window.addEventListener('online', this.handleOnline.bind(this));\n        window.addEventListener('offline', this.handleOffline.bind(this));\n        // Listen for visibility change to check connection when tab becomes active\n        document.addEventListener('visibilitychange', ()=>{\n            if (!document.hidden) {\n                this.checkConnection();\n            }\n        });\n    }\n    handleOnline() {\n        console.log('🌐 Network: Back online');\n        this.isOnline = true;\n        this.lastOnlineTime = Date.now();\n        this.reconnectAttempts = 0;\n        this.notifyListeners(true);\n    }\n    handleOffline() {\n        console.log('🌐 Network: Gone offline');\n        this.isOnline = false;\n        this.notifyListeners(false);\n    }\n    async checkConnection() {\n        try {\n            // Try to fetch a small resource to verify actual connectivity\n            const response = await fetch('/api/health', {\n                method: 'HEAD',\n                cache: 'no-cache',\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            const isConnected = response.ok;\n            if (isConnected !== this.isOnline) {\n                this.isOnline = isConnected;\n                this.notifyListeners(isConnected);\n                if (isConnected) {\n                    this.lastOnlineTime = Date.now();\n                    this.reconnectAttempts = 0;\n                }\n            }\n            return isConnected;\n        } catch (error) {\n            // If fetch fails, we're likely offline\n            if (this.isOnline) {\n                this.isOnline = false;\n                this.notifyListeners(false);\n            }\n            return false;\n        }\n    }\n    startPeriodicCheck() {\n        // Use a more efficient interval with cleanup\n        const interval = setInterval(()=>{\n            this.checkConnection();\n        }, 30000); // Check every 30 seconds\n        // Store interval for cleanup\n        this.periodicInterval = interval;\n    }\n    cleanup() {\n        if (this.periodicInterval) {\n            clearInterval(this.periodicInterval);\n        }\n        this.listeners.clear();\n    }\n    notifyListeners(isOnline) {\n        this.listeners.forEach((listener)=>{\n            try {\n                listener(isOnline);\n            } catch (error) {\n                console.error('Error in network status listener:', error);\n            }\n        });\n    }\n    addListener(listener) {\n        this.listeners.add(listener);\n        // Return unsubscribe function\n        return ()=>{\n            this.listeners.delete(listener);\n        };\n    }\n    getStatus() {\n        return {\n            isOnline: this.isOnline,\n            lastOnlineTime: this.lastOnlineTime,\n            reconnectAttempts: this.reconnectAttempts\n        };\n    }\n    async forceCheck() {\n        return await this.checkConnection();\n    }\n    // Attempt to reconnect with exponential backoff\n    async attemptReconnect() {\n        if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n            console.log('🌐 Network: Max reconnect attempts reached');\n            return false;\n        }\n        this.reconnectAttempts++;\n        const delay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1), 30000);\n        console.log(`🌐 Network: Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);\n        await new Promise((resolve)=>setTimeout(resolve, delay));\n        const isConnected = await this.checkConnection();\n        if (!isConnected && this.reconnectAttempts < this.maxReconnectAttempts) {\n            // Schedule next attempt\n            setTimeout(()=>this.attemptReconnect(), 1000);\n        }\n        return isConnected;\n    }\n}\n// Auto-save functionality\nclass AutoSaveManager {\n    constructor(){\n        this.saveInterval = null;\n        this.saveFunction = null;\n        this.intervalMs = 30000 // 30 seconds default\n        ;\n        this.isEnabled = true;\n        this.lastSaveTime = 0;\n        this.networkMonitor = NetworkMonitor.getInstance();\n        this.setupNetworkListener();\n        this.setupBeforeUnloadListener();\n    }\n    static getInstance() {\n        if (!AutoSaveManager.instance) {\n            AutoSaveManager.instance = new AutoSaveManager();\n        }\n        return AutoSaveManager.instance;\n    }\n    setupNetworkListener() {\n        this.networkMonitor.addListener((isOnline)=>{\n            if (isOnline && this.saveFunction) {\n                // Save immediately when coming back online\n                console.log('💾 Auto-save: Saving on network reconnection');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        });\n    }\n    setupBeforeUnloadListener() {\n        window.addEventListener('beforeunload', ()=>{\n            if (this.saveFunction) {\n                console.log('💾 Auto-save: Saving before page unload');\n                this.saveFunction();\n            }\n        });\n        // Also save on page visibility change (when user switches tabs)\n        document.addEventListener('visibilitychange', ()=>{\n            if (document.hidden && this.saveFunction) {\n                console.log('💾 Auto-save: Saving on tab switch');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        });\n    }\n    enable(saveFunction, intervalMs = 30000) {\n        this.saveFunction = saveFunction;\n        this.intervalMs = intervalMs;\n        this.isEnabled = true;\n        this.stop(); // Clear any existing interval\n        this.saveInterval = setInterval(()=>{\n            if (this.isEnabled && this.saveFunction && this.networkMonitor.getStatus().isOnline) {\n                console.log('💾 Auto-save: Periodic save');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        }, this.intervalMs);\n        console.log(`💾 Auto-save: Enabled with ${intervalMs}ms interval`);\n    }\n    disable() {\n        this.isEnabled = false;\n        this.stop();\n        console.log('💾 Auto-save: Disabled');\n    }\n    stop() {\n        if (this.saveInterval) {\n            clearInterval(this.saveInterval);\n            this.saveInterval = null;\n        }\n    }\n    saveNow() {\n        if (this.saveFunction && this.networkMonitor.getStatus().isOnline) {\n            console.log('💾 Auto-save: Manual save triggered');\n            this.saveFunction();\n            this.lastSaveTime = Date.now();\n        }\n    }\n    getStatus() {\n        return {\n            isEnabled: this.isEnabled,\n            lastSaveTime: this.lastSaveTime,\n            intervalMs: this.intervalMs,\n            isOnline: this.networkMonitor.getStatus().isOnline\n        };\n    }\n}\n// Memory usage monitor to prevent memory leaks\nclass MemoryMonitor {\n    constructor(){\n        this.checkInterval = null;\n        this.warningThreshold = 100 * 1024 * 1024 // 100MB\n        ;\n        this.criticalThreshold = 200 * 1024 * 1024 // 200MB\n        ;\n        this.listeners = new Set();\n        this.startMonitoring();\n    }\n    static getInstance() {\n        if (!MemoryMonitor.instance) {\n            MemoryMonitor.instance = new MemoryMonitor();\n        }\n        return MemoryMonitor.instance;\n    }\n    startMonitoring() {\n        // Only monitor if performance.memory is available (Chrome)\n        if ('memory' in performance) {\n            this.checkInterval = setInterval(()=>{\n                this.checkMemoryUsage();\n            }, 60000); // Check every minute\n        }\n    }\n    checkMemoryUsage() {\n        if ('memory' in performance) {\n            const memory = performance.memory;\n            const usedJSHeapSize = memory.usedJSHeapSize;\n            this.notifyListeners(memory);\n            if (usedJSHeapSize > this.criticalThreshold) {\n                console.warn('🧠 Memory: Critical memory usage detected:', {\n                    used: `${(usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,\n                    total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,\n                    limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`\n                });\n                // Trigger garbage collection if possible\n                if ('gc' in window) {\n                    window.gc();\n                }\n            } else if (usedJSHeapSize > this.warningThreshold) {\n                console.log('🧠 Memory: High memory usage:', {\n                    used: `${(usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,\n                    total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`\n                });\n            }\n        }\n    }\n    notifyListeners(memory) {\n        this.listeners.forEach((listener)=>{\n            try {\n                listener(memory);\n            } catch (error) {\n                console.error('Error in memory monitor listener:', error);\n            }\n        });\n    }\n    addListener(listener) {\n        this.listeners.add(listener);\n        return ()=>this.listeners.delete(listener);\n    }\n    getMemoryUsage() {\n        if ('memory' in performance) {\n            return performance.memory;\n        }\n        return null;\n    }\n    stop() {\n        if (this.checkInterval) {\n            clearInterval(this.checkInterval);\n            this.checkInterval = null;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/network-monitor.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\nclass SessionManager {\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.loadSessionsFromStorage();\n        // Start with localStorage mode, check backend in background\n        this.useBackend = false;\n        // Check backend connection in background without blocking\n        setTimeout(()=>{\n            this.checkBackendConnection().catch(()=>{\n            // Silently fail and continue with localStorage\n            });\n        }, 1000);\n    }\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    async checkBackendConnection() {\n        try {\n            // Try a simple ping to the backend\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 1500); // 1.5 second timeout\n            const response = await fetch('http://localhost:5000/', {\n                method: 'GET',\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.status < 500) {\n                this.useBackend = true;\n                console.log('✅ Session Manager: Backend connection established');\n            } else {\n                throw new Error('Backend returned server error');\n            }\n        } catch (error) {\n            this.useBackend = false;\n            console.warn('⚠️ Session Manager: Backend unavailable, using localStorage fallback');\n            console.warn('💡 To enable backend features, start the backend server: python run.py');\n        }\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (true) return;\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            const currentSessionId = localStorage.getItem(CURRENT_SESSION_KEY);\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (true) return;\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            if (this.currentSessionId) {\n                localStorage.setItem(CURRENT_SESSION_KEY, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSession(name, config) {\n        if (this.useBackend) {\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                    name,\n                    config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0\n                });\n                const sessionId = response.session.id;\n                console.log('✅ Session created on backend:', sessionId);\n                return sessionId;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: 10,\n            crypto2Balance: 100000,\n            stablecoinBalance: 0,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, isActive = false) {\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: session.runtime + (Date.now() - session.lastModified)\n            };\n            this.sessions.set(sessionId, updatedSession);\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    deleteSession(sessionId) {\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                localStorage.removeItem(CURRENT_SESSION_KEY);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: `${session.config.crypto1}/${session.config.crypto2}`,\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: session.runtime,\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            localStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>[\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    entry.amountCrypto1?.toFixed(session.config.numDigits) || '',\n                    entry.avgPrice?.toFixed(session.config.numDigits) || '',\n                    entry.valueCrypto2?.toFixed(session.config.numDigits) || '',\n                    entry.price1?.toFixed(session.config.numDigits) || '',\n                    entry.crypto1Symbol,\n                    entry.price2?.toFixed(session.config.numDigits) || '',\n                    entry.crypto2Symbol,\n                    entry.realizedProfitLossCrypto1?.toFixed(session.config.numDigits) || '',\n                    entry.realizedProfitLossCrypto2?.toFixed(session.config.numDigits) || ''\n                ].join(','))\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        localStorage.removeItem(CURRENT_SESSION_KEY);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData, intervalMs = 30000 // 30 seconds\n    ) {\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/session-manager.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/types.tsx":
/*!***************************!*\
  !*** ./src/lib/types.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AVAILABLE_CRYPTOS: () => (/* binding */ AVAILABLE_CRYPTOS),\n/* harmony export */   AVAILABLE_QUOTES_SIMPLE: () => (/* binding */ AVAILABLE_QUOTES_SIMPLE),\n/* harmony export */   AVAILABLE_STABLECOINS: () => (/* binding */ AVAILABLE_STABLECOINS),\n/* harmony export */   DEFAULT_APP_SETTINGS: () => (/* binding */ DEFAULT_APP_SETTINGS)\n/* harmony export */ });\nconst DEFAULT_APP_SETTINGS = {\n    soundAlertsEnabled: true,\n    alertOnOrderExecution: true,\n    alertOnError: true,\n    soundOrderExecution: '/sounds/order-executed.mp3',\n    soundError: '/sounds/error.mp3',\n    clearOrderHistoryOnStart: false\n};\n// Dummy constants, replace with actual values or logic as needed\nconst AVAILABLE_CRYPTOS = [\n    'BTC',\n    'ETH',\n    'ADA',\n    'SOL',\n    'DOGE',\n    'LINK',\n    'MATIC',\n    'DOT',\n    'AVAX',\n    'XRP',\n    'LTC',\n    'BCH',\n    'BNB',\n    'SHIB'\n];\nconst AVAILABLE_QUOTES_SIMPLE = {\n    BTC: [\n        'USDT',\n        'USDC',\n        'FDUSD',\n        'EUR'\n    ],\n    ETH: [\n        'USDT',\n        'USDC',\n        'FDUSD',\n        'BTC',\n        'EUR'\n    ],\n    ADA: [\n        'USDT',\n        'USDC',\n        'BTC',\n        'ETH'\n    ],\n    SOL: [\n        'USDT',\n        'USDC',\n        'BTC',\n        'ETH'\n    ]\n};\nconst AVAILABLE_STABLECOINS = [\n    'USDT',\n    'USDC',\n    'FDUSD',\n    'DAI'\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/types.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXHNyY1xcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dgram":
/*!************************!*\
  !*** external "dgram" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("dgram");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "express":
/*!**************************!*\
  !*** external "express" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("express");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "import-in-the-middle":
/*!***************************************!*\
  !*** external "import-in-the-middle" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("import-in-the-middle");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream/web");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "require-in-the-middle":
/*!****************************************!*\
  !*** external "require-in-the-middle" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("require-in-the-middle");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@genkit-ai","vendor-chunks/zod-to-json-schema","vendor-chunks/yaml","vendor-chunks/ajv","vendor-chunks/@grpc","vendor-chunks/semver","vendor-chunks/protobufjs","vendor-chunks/thriftrw","vendor-chunks/handlebars","vendor-chunks/bufrw","vendor-chunks/opentracing","vendor-chunks/@radix-ui","vendor-chunks/genkit","vendor-chunks/zod","vendor-chunks/@protobufjs","vendor-chunks/lucide-react","vendor-chunks/jaeger-client","vendor-chunks/uuid","vendor-chunks/hexer","vendor-chunks/@swc","vendor-chunks/fast-uri","vendor-chunks/ajv-formats","vendor-chunks/xtend","vendor-chunks/partial-json","vendor-chunks/error","vendor-chunks/@google","vendor-chunks/json5","vendor-chunks/geist","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority","vendor-chunks/long","vendor-chunks/xorshift","vendor-chunks/string-template","vendor-chunks/shimmer","vendor-chunks/node-int64","vendor-chunks/lodash.camelcase","vendor-chunks/json-schema-traverse","vendor-chunks/get-port","vendor-chunks/fast-deep-equal","vendor-chunks/dotprompt","vendor-chunks/ansi-color","vendor-chunks/@js-sdsl"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();