"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Save, 
  FolderOpen, 
  Plus, 
  Trash2, 
  Download, 
  Upload, 
  Edit2, 
  Clock,
  TrendingUp,
  Activity,
  FileText
} from 'lucide-react';
import { SessionManager as SessionManagerService } from '@/lib/session-manager';
import { useTradingContext } from '@/contexts/TradingContext';
import { useToast } from '@/hooks/use-toast';
import type { SessionMetadata } from '@/lib/types';
import { format } from 'date-fns';

export function SessionManager() {
  const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = useTradingContext();
  const { toast } = useToast();
  const [sessions, setSessions] = useState<SessionMetadata[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [newSessionName, setNewSessionName] = useState('');
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const sessionManager = SessionManagerService.getInstance();

  useEffect(() => {
    loadSessions();
    setCurrentSessionId(sessionManager.getCurrentSessionId());
  }, []);

  const loadSessions = () => {
    const allSessions = sessionManager.getAllSessions();
    setSessions(allSessions.sort((a, b) => b.lastModified - a.lastModified));
  };

  const handleCreateNewSession = async () => {
    if (!newSessionName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a session name",
        variant: "destructive"
      });
      return;
    }

    try {
      const sessionId = await sessionManager.createNewSession(newSessionName.trim(), config);
      sessionManager.setCurrentSession(sessionId);
      setCurrentSessionId(sessionId);
      setNewSessionName('');
      loadSessions();

      toast({
        title: "Session Created",
        description: `New session "${newSessionName}" has been created`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create session. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleSaveCurrentSession = () => {
    if (!currentSessionId) {
      toast({
        title: "Error",
        description: "No active session to save",
        variant: "destructive"
      });
      return;
    }

    const success = sessionManager.saveSession(
      currentSessionId,
      config,
      targetPriceRows,
      orderHistory,
      currentMarketPrice,
      crypto1Balance,
      crypto2Balance,
      stablecoinBalance,
      botSystemStatus === 'Running'
    );

    if (success) {
      loadSessions();
      toast({
        title: "Session Saved",
        description: "Current session has been saved successfully",
      });
    } else {
      toast({
        title: "Error",
        description: "Failed to save session",
        variant: "destructive"
      });
    }
  };

  const handleLoadSession = (sessionId: string) => {
    const session = sessionManager.loadSession(sessionId);
    if (!session) {
      toast({
        title: "Error",
        description: "Failed to load session",
        variant: "destructive"
      });
      return;
    }

    // Load session data into context
    dispatch({ type: 'SET_CONFIG', payload: session.config });
    dispatch({ type: 'SET_TARGET_PRICE_ROWS', payload: session.targetPriceRows });
    dispatch({ type: 'CLEAR_ORDER_HISTORY' });
    session.orderHistory.forEach(entry => {
      dispatch({ type: 'ADD_ORDER_HISTORY_ENTRY', payload: entry });
    });
    dispatch({ type: 'SET_MARKET_PRICE', payload: session.currentMarketPrice });
    dispatch({ type: 'SET_BALANCES', payload: { 
      crypto1: session.crypto1Balance, 
      crypto2: session.crypto2Balance 
    }});

    sessionManager.setCurrentSession(sessionId);
    setCurrentSessionId(sessionId);
    loadSessions();

    toast({
      title: "Session Loaded",
      description: `Session "${session.name}" has been loaded`,
    });
  };

  const handleDeleteSession = (sessionId: string) => {
    const success = sessionManager.deleteSession(sessionId);
    if (success) {
      if (currentSessionId === sessionId) {
        setCurrentSessionId(null);
      }
      loadSessions();
      toast({
        title: "Session Deleted",
        description: "Session has been deleted successfully",
      });
    }
  };

  const handleRenameSession = (sessionId: string) => {
    if (!editingName.trim()) return;
    
    const success = sessionManager.renameSession(sessionId, editingName.trim());
    if (success) {
      setEditingSessionId(null);
      setEditingName('');
      loadSessions();
      toast({
        title: "Session Renamed",
        description: "Session has been renamed successfully",
      });
    }
  };

  const handleExportSession = (sessionId: string) => {
    const csvContent = sessionManager.exportSessionToCSV(sessionId);
    if (!csvContent) {
      toast({
        title: "Error",
        description: "Failed to export session",
        variant: "destructive"
      });
      return;
    }

    const session = sessionManager.loadSession(sessionId);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${session?.name || 'session'}_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Export Complete",
      description: "Session data has been exported to CSV",
    });
  };

  const formatRuntime = (runtime?: number) => {
    if (!runtime) return '0m';
    const minutes = Math.floor(runtime / 60000);
    const hours = Math.floor(minutes / 60);
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };

  const getCurrentSession = () => {
    return sessions.find(s => s.id === currentSessionId);
  };

  return (
    <div className="space-y-6">
      {/* Current Session */}
      <Card className="bg-card-foreground/5 border-border border-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Current Session
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {getCurrentSession() ? (
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-medium">{getCurrentSession()?.name}</h3>
                  <p className="text-sm text-muted-foreground">{getCurrentSession()?.pair}</p>
                </div>
                <Badge variant={botSystemStatus === 'Running' ? 'default' : 'secondary'}>
                  {botSystemStatus === 'Running' ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Runtime:</span>
                  <span className="ml-2 font-medium">{formatRuntime(getCurrentSession()?.runtime)}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">Trades:</span>
                  <span className="ml-2 font-medium">{getCurrentSession()?.totalTrades || 0}</span>
                </div>
              </div>
              <Button onClick={handleSaveCurrentSession} className="w-full btn-neo">
                <Save className="mr-2 h-4 w-4" />
                Save Session
              </Button>
            </div>
          ) : (
            <div className="text-center text-muted-foreground">
              <p>No active session</p>
              <p className="text-xs">Create a new session to get started</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create New Session */}
      <Card className="bg-card-foreground/5 border-border border-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Create New Session
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="session-name">Session Name</Label>
            <Input
              id="session-name"
              value={newSessionName}
              onChange={(e) => setNewSessionName(e.target.value)}
              placeholder="e.g., BTC/USDT SimpleSpot"
              onKeyPress={(e) => e.key === 'Enter' && handleCreateNewSession()}
            />
          </div>
          <Button onClick={handleCreateNewSession} className="w-full btn-neo">
            <Plus className="mr-2 h-4 w-4" />
            Create New Session
          </Button>
        </CardContent>
      </Card>

      {/* Past Sessions */}
      <Card className="bg-card-foreground/5 border-border border-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Past Sessions ({sessions.filter(s => s.id !== currentSessionId).length})
          </CardTitle>
          <CardDescription>
            Auto-saved: {sessions.filter(s => s.id !== currentSessionId).length} | Manual: 0
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px]">
            {sessions.filter(s => s.id !== currentSessionId).length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No saved sessions yet.</p>
                <p className="text-xs">Save your current session to get started.</p>
              </div>
            ) : (
              <div className="space-y-3">
                {sessions.filter(s => s.id !== currentSessionId).map((session) => (
                  <Card key={session.id} className="border border-border">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          {editingSessionId === session.id ? (
                            <div className="flex gap-2 mb-2">
                              <Input
                                value={editingName}
                                onChange={(e) => setEditingName(e.target.value)}
                                onKeyPress={(e) => e.key === 'Enter' && handleRenameSession(session.id)}
                                className="text-sm"
                              />
                              <Button size="sm" onClick={() => handleRenameSession(session.id)}>
                                <Save className="h-3 w-3" />
                              </Button>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2 mb-2">
                              <h4 className="font-medium">{session.name}</h4>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => {
                                  setEditingSessionId(session.id);
                                  setEditingName(session.name);
                                }}
                              >
                                <Edit2 className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                          <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                            <div>Pair: {session.pair}</div>
                            <div>Trades: {session.totalTrades}</div>
                            <div>Runtime: {formatRuntime(session.runtime)}</div>
                            <div>P/L: {session.totalProfitLoss.toFixed(2)}</div>
                          </div>
                          <div className="text-xs text-muted-foreground mt-1">
                            {format(new Date(session.lastModified), 'MMM dd, yyyy HH:mm')}
                          </div>
                        </div>
                        <div className="flex gap-1 ml-4">
                          <Button size="sm" variant="outline" onClick={() => handleLoadSession(session.id)}>
                            <FolderOpen className="h-3 w-3" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => handleExportSession(session.id)}>
                            <Download className="h-3 w-3" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => handleDeleteSession(session.id)}>
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}
