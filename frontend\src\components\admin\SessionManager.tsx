"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Save, 
  FolderOpen, 
  Plus, 
  Trash2, 
  Download, 
  Upload, 
  Edit2, 
  Clock,
  TrendingUp,
  Activity,
  FileText
} from 'lucide-react';
import { SessionManager as SessionManagerService } from '@/lib/session-manager';
import { useTradingContext } from '@/contexts/TradingContext';
import { useToast } from '@/hooks/use-toast';
import type { SessionMetadata } from '@/lib/types';
import { format } from 'date-fns';

export function SessionManager() {
  const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = useTradingContext();
  const { toast } = useToast();
  const [sessions, setSessions] = useState<SessionMetadata[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [newSessionName, setNewSessionName] = useState('');
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [runtimeTick, setRuntimeTick] = useState(0);
  const sessionManager = SessionManagerService.getInstance();

  useEffect(() => {
    loadSessions();
    setCurrentSessionId(sessionManager.getCurrentSessionId());
  }, []);

  // Update runtime display every second for active sessions
  useEffect(() => {
    const interval = setInterval(() => {
      if (botSystemStatus === 'Running') {
        setRuntimeTick(prev => prev + 1);
      }
    }, 1000); // Update every second for real-time display

    return () => clearInterval(interval);
  }, [botSystemStatus]);

  const loadSessions = () => {
    const allSessions = sessionManager.getAllSessions();
    setSessions(allSessions.sort((a, b) => b.lastModified - a.lastModified));
  };

  const handleCreateNewSession = async () => {
    if (!newSessionName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a session name",
        variant: "destructive"
      });
      return;
    }

    try {
      const sessionId = await sessionManager.createNewSession(newSessionName.trim(), config);
      sessionManager.setCurrentSession(sessionId);
      setCurrentSessionId(sessionId);
      setNewSessionName('');
      loadSessions();

      toast({
        title: "Session Created",
        description: `New session "${newSessionName}" has been created`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create session. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleSaveCurrentSession = () => {
    if (!currentSessionId) {
      toast({
        title: "Error",
        description: "No active session to save",
        variant: "destructive"
      });
      return;
    }

    const success = sessionManager.saveSession(
      currentSessionId,
      config,
      targetPriceRows,
      orderHistory,
      currentMarketPrice,
      crypto1Balance,
      crypto2Balance,
      stablecoinBalance,
      botSystemStatus === 'Running'
    );

    if (success) {
      loadSessions();
      toast({
        title: "Session Saved",
        description: "Current session has been saved successfully",
      });
    } else {
      toast({
        title: "Error",
        description: "Failed to save session",
        variant: "destructive"
      });
    }
  };

  const handleLoadSession = (sessionId: string) => {
    const session = sessionManager.loadSession(sessionId);
    if (!session) {
      toast({
        title: "Error",
        description: "Failed to load session",
        variant: "destructive"
      });
      return;
    }

    // Load session data into context
    dispatch({ type: 'SET_CONFIG', payload: session.config });
    dispatch({ type: 'SET_TARGET_PRICE_ROWS', payload: session.targetPriceRows });
    dispatch({ type: 'CLEAR_ORDER_HISTORY' });
    session.orderHistory.forEach(entry => {
      dispatch({ type: 'ADD_ORDER_HISTORY_ENTRY', payload: entry });
    });
    dispatch({ type: 'SET_MARKET_PRICE', payload: session.currentMarketPrice });
    dispatch({ type: 'SET_BALANCES', payload: { 
      crypto1: session.crypto1Balance, 
      crypto2: session.crypto2Balance 
    }});

    sessionManager.setCurrentSession(sessionId);
    setCurrentSessionId(sessionId);
    loadSessions();

    toast({
      title: "Session Loaded",
      description: `Session "${session.name}" has been loaded`,
    });
  };

  const handleDeleteSession = (sessionId: string) => {
    const success = sessionManager.deleteSession(sessionId);
    if (success) {
      if (currentSessionId === sessionId) {
        setCurrentSessionId(null);
      }
      loadSessions();
      toast({
        title: "Session Deleted",
        description: "Session has been deleted successfully",
      });
    }
  };

  const handleRenameSession = (sessionId: string) => {
    if (!editingName.trim()) return;
    
    const success = sessionManager.renameSession(sessionId, editingName.trim());
    if (success) {
      setEditingSessionId(null);
      setEditingName('');
      loadSessions();
      toast({
        title: "Session Renamed",
        description: "Session has been renamed successfully",
      });
    }
  };

  const handleExportSession = (sessionId: string) => {
    const csvContent = sessionManager.exportSessionToCSV(sessionId);
    if (!csvContent) {
      toast({
        title: "Error",
        description: "Failed to export session",
        variant: "destructive"
      });
      return;
    }

    const session = sessionManager.loadSession(sessionId);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${session?.name || 'session'}_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Export Complete",
      description: "Session data has been exported to CSV",
    });
  };

  const formatRuntime = (runtime?: number, isActive: boolean = false, lastModified?: number) => {
    let totalRuntime = runtime || 0;

    // If session is active, add current session time
    if (isActive && lastModified) {
      totalRuntime += (Date.now() - lastModified);
    }

    if (totalRuntime === 0) return '0s';

    const totalSeconds = Math.floor(totalRuntime / 1000);
    const seconds = totalSeconds % 60;
    const minutes = Math.floor(totalSeconds / 60) % 60;
    const hours = Math.floor(totalSeconds / 3600) % 24;
    const days = Math.floor(totalSeconds / 86400);

    const parts = [];
    if (days > 0) parts.push(`${days}d`);
    if (hours > 0) parts.push(`${hours}h`);
    if (minutes > 0) parts.push(`${minutes}m`);
    if (seconds > 0 || parts.length === 0) parts.push(`${seconds}s`);

    // Show maximum 3 parts for readability
    return parts.slice(0, 3).join(' ');
  };

  const getCurrentSession = () => {
    return sessions.find(s => s.id === currentSessionId);
  };

  return (
    <div className="space-y-6">
      {/* Current Session */}
      <Card className="bg-card-foreground/5 border-border border-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Current Session
          </CardTitle>
        </CardHeader>
        <CardContent>
          {getCurrentSession() ? (
            <div className="grid grid-cols-4 gap-4 items-center">
              {/* Session Name with Edit */}
              <div className="flex items-center gap-2">
                {editingSessionId === getCurrentSession()?.id ? (
                  <div className="flex gap-2">
                    <Input
                      value={editingName}
                      onChange={(e) => setEditingName(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleRenameSession(getCurrentSession()!.id)}
                      className="text-sm"
                    />
                    <Button size="sm" onClick={() => handleRenameSession(getCurrentSession()!.id)}>
                      <Save className="h-3 w-3" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{getCurrentSession()?.name}</span>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => {
                        setEditingSessionId(getCurrentSession()!.id);
                        setEditingName(getCurrentSession()!.name);
                      }}
                    >
                      <Edit2 className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>

              {/* Status */}
              <div>
                <Badge variant={botSystemStatus === 'Running' ? 'default' : 'secondary'}>
                  {botSystemStatus === 'Running' ? 'Active' : 'Inactive'}
                </Badge>
              </div>

              {/* Runtime */}
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>{formatRuntime(getCurrentSession()?.runtime, botSystemStatus === 'Running', getCurrentSession()?.lastModified)}</span>
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <Button onClick={handleSaveCurrentSession} className="btn-neo">
                  <Save className="mr-2 h-4 w-4" />
                  Save Session
                </Button>
                <div className="relative">
                  <Button variant="outline" className="px-3">
                    Save As...
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center text-muted-foreground py-8">
              <p>No active session</p>
              <p className="text-xs">Create a new session to get started</p>
            </div>
          )}
        </CardContent>
      </Card>



      {/* Past Sessions */}
      <Card className="bg-card-foreground/5 border-border border-2">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Past Sessions ({sessions.filter(s => s.id !== currentSessionId).length})
            </CardTitle>
          </div>
          <div className="text-sm text-muted-foreground">
            Auto-saved: {sessions.filter(s => s.id !== currentSessionId).length} | Manual: 0
          </div>
        </CardHeader>
        <CardContent>
          {sessions.filter(s => s.id !== currentSessionId).length === 0 ? (
            <div className="text-center text-muted-foreground py-16">
              <Clock className="h-16 w-16 mx-auto mb-4 opacity-30" />
              <p className="text-lg font-medium">No saved sessions yet.</p>
              <p className="text-sm">Save your current session to get started.</p>
            </div>
          ) : (
            <ScrollArea className="h-[400px]">
              <div className="space-y-3">
                {sessions.filter(s => s.id !== currentSessionId).map((session) => (
                  <Card key={session.id} className="border border-border">
                    <CardContent className="p-4">
                      <div className="grid grid-cols-4 gap-4 items-center">
                        {/* Session Name with Edit */}
                        <div className="flex items-center gap-2">
                          {editingSessionId === session.id ? (
                            <div className="flex gap-2">
                              <Input
                                value={editingName}
                                onChange={(e) => setEditingName(e.target.value)}
                                onKeyPress={(e) => e.key === 'Enter' && handleRenameSession(session.id)}
                                className="text-sm"
                              />
                              <Button size="sm" onClick={() => handleRenameSession(session.id)}>
                                <Save className="h-3 w-3" />
                              </Button>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{session.name}</span>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => {
                                  setEditingSessionId(session.id);
                                  setEditingName(session.name);
                                }}
                              >
                                <Edit2 className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </div>

                        {/* Status */}
                        <div>
                          <Badge variant="secondary">
                            Inactive
                          </Badge>
                        </div>

                        {/* Runtime */}
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span>{formatRuntime(session.runtime)}</span>
                        </div>

                        {/* Actions */}
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline" onClick={() => handleLoadSession(session.id)} className="btn-neo">
                            <FolderOpen className="mr-2 h-3 w-3" />
                            Load Session
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
