{"c": ["app/layout", "app/page", "webpack"], "r": ["vendors-_app-pages-browser_node_modules_next_dist_api_navigation_js-_app-pages-browser_node_m-e2bb8c"], "m": ["(app-pages-browser)/./src/components/ErrorBoundary.tsx", "(app-pages-browser)/./src/components/ui/toast.tsx", "(app-pages-browser)/./src/components/ui/toaster.tsx", "(app-pages-browser)/./src/contexts/AIContext.tsx", "(app-pages-browser)/./src/contexts/TradingContext.tsx", "(app-pages-browser)/./src/hooks/use-toast.ts", "(app-pages-browser)/./src/lib/network-monitor.ts", "(app-pages-browser)/./src/lib/session-manager.ts", "(app-pages-browser)/./src/lib/types.tsx", "(app-pages-browser)/./src/lib/utils.ts", "(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-collection/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-context/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-portal/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-presence/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-toast/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs", "(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs", "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js", "(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js"]}