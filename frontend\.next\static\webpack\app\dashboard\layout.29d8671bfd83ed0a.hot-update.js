"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx":
/*!********************************************************!*\
  !*** ./src/components/layout/TradingConfigSidebar.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TradingConfigSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _contexts_AIContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AIContext */ \"(app-pages-browser)/./src/contexts/AIContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/TargetPriceModal */ \"(app-pages-browser)/./src/components/modals/TargetPriceModal.tsx\");\n/* harmony import */ var _components_modals_AlarmConfigModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/AlarmConfigModal */ \"(app-pages-browser)/./src/components/modals/AlarmConfigModal.tsx\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var _components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/crypto-input */ \"(app-pages-browser)/./src/components/ui/crypto-input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power-off.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Force the correct stablecoins to ensure all 6 are available\nconst FORCE_STABLECOINS = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n// Allowed Crypto 1 (Base) currencies as specified by user\nconst ALLOWED_CRYPTO1 = [\n    \"BTC\",\n    \"ETH\",\n    \"BNB\",\n    \"SOL\",\n    \"LINK\",\n    \"AVAX\",\n    \"DOT\",\n    \"UNI\",\n    \"NEAR\",\n    \"AAVE\",\n    \"ATOM\",\n    \"VET\",\n    \"RENDER\",\n    \"POL\",\n    \"ALGO\",\n    \"ARB\",\n    \"FET\",\n    \"PAXG\",\n    \"GALA\",\n    \"CRV\",\n    \"COMP\",\n    \"ENJ\"\n];\n// Allowed Crypto 2 (Quote) currencies as specified by user\nconst ALLOWED_CRYPTO2 = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n\n\n\nfunction TradingConfigSidebar() {\n    _s();\n    // Safely get trading context with error handling\n    let tradingContext;\n    try {\n        tradingContext = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    } catch (error) {\n        console.error('Trading context not available:', error);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n            className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"Trading context not available. Please refresh the page.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this);\n    }\n    const { config, dispatch, botSystemStatus, appSettings, setTargetPrices: contextSetTargetPrices } = tradingContext;\n    const isBotActive = botSystemStatus === 'Running';\n    const isBotWarmingUp = botSystemStatus === 'WarmingUp';\n    // Safely get AI context\n    let aiContext;\n    try {\n        aiContext = (0,_contexts_AIContext__WEBPACK_IMPORTED_MODULE_3__.useAIContext)();\n    } catch (error) {\n        console.warn('AI context not available:', error);\n        aiContext = {\n            suggestion: null,\n            isLoading: false,\n            error: null,\n            getTradingModeSuggestion: ()=>Promise.resolve()\n        };\n    }\n    const { suggestion: aiSuggestion, isLoading: aiLoading, error: aiError, getTradingModeSuggestion } = aiContext;\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__.useToast)();\n    const [isTargetModalOpen, setIsTargetModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAlarmModalOpen, setIsAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // AI Suggestion form state\n    const [riskTolerance, setRiskTolerance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"medium\");\n    const [preferredCryptos, setPreferredCryptos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [investmentGoals, setInvestmentGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        let val;\n        if (type === 'checkbox') {\n            val = checked;\n        } else if (type === 'number') {\n            // Handle empty string or invalid numbers gracefully\n            if (value === '' || value === null || value === undefined) {\n                val = 0;\n            } else {\n                const parsed = parseFloat(value);\n                val = isNaN(parsed) ? 0 : parsed;\n            }\n        } else {\n            val = value;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: val\n            }\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: value\n            }\n        });\n        if (name === 'crypto1') {\n            // Reset crypto2 if new crypto1 doesn't support current crypto2\n            const validQuotes = _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_QUOTES_SIMPLE[value] || DEFAULT_QUOTE_CURRENCIES || [\n                \"USDT\",\n                \"USDC\",\n                \"BTC\"\n            ];\n            if (!config.crypto2 || !Array.isArray(validQuotes) || !validQuotes.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: validQuotes[0] || 'USDT'\n                    }\n                }); // Ensure crypto2 gets a valid default\n            }\n        }\n    };\n    const handleCrypto1Selection = (crypto)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto1: crypto\n            }\n        });\n        // Reset crypto2 to first allowed crypto2 when crypto1 changes\n        const allowedCrypto2 = ALLOWED_CRYPTO2 || [\n            \"USDT\",\n            \"USDC\",\n            \"DAI\"\n        ];\n        if (!allowedCrypto2.includes(config.crypto2)) {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    crypto2: allowedCrypto2[0]\n                }\n            });\n        }\n    };\n    const handleCrypto2Selection = (crypto)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto2: crypto\n            }\n        });\n    };\n    const handleTradingModeChange = (checked)=>{\n        const newMode = checked ? \"StablecoinSwap\" : \"SimpleSpot\";\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                tradingMode: newMode\n            }\n        });\n    };\n    const handleIncomeSplitChange = (cryptoKey, value)=>{\n        let percent = parseFloat(value);\n        if (isNaN(percent)) percent = 0;\n        if (percent < 0) percent = 0;\n        if (percent > 100) percent = 100;\n        if (cryptoKey === 'incomeSplitCrypto1Percent') {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto1Percent: percent,\n                    incomeSplitCrypto2Percent: 100 - percent\n                }\n            });\n        } else {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto2Percent: percent,\n                    incomeSplitCrypto1Percent: 100 - percent\n                }\n            });\n        }\n    };\n    const handleAISuggestion = async ()=>{\n        if (!riskTolerance || !preferredCryptos || !investmentGoals) {\n            toast({\n                title: \"AI Suggestion Error\",\n                description: \"Please fill all AI suggestion fields.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        await getTradingModeSuggestion({\n            riskTolerance,\n            preferredCryptocurrencies: preferredCryptos,\n            investmentGoals\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingConfigSidebar.useEffect\": ()=>{\n            if (aiSuggestion) {\n                toast({\n                    title: \"AI Suggestion Received\",\n                    description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Mode:\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    aiSuggestion.suggestedMode\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Reason:\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    aiSuggestion.reason\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                size: \"sm\",\n                                className: \"mt-2 btn-neo\",\n                                onClick: {\n                                    \"TradingConfigSidebar.useEffect\": ()=>dispatch({\n                                            type: 'SET_CONFIG',\n                                            payload: {\n                                                tradingMode: aiSuggestion.suggestedMode === 'Simple Spot' ? 'SimpleSpot' : 'StablecoinSwap'\n                                            }\n                                        })\n                                }[\"TradingConfigSidebar.useEffect\"],\n                                children: \"Apply Suggestion\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    duration: Infinity\n                });\n            }\n            if (aiError) {\n                toast({\n                    title: \"AI Suggestion Error\",\n                    description: aiError,\n                    variant: \"destructive\",\n                    duration: Infinity\n                });\n            }\n        }\n    }[\"TradingConfigSidebar.useEffect\"], [\n        aiSuggestion,\n        aiError,\n        toast,\n        dispatch\n    ]);\n    // Use static crypto lists\n    const crypto1Options = _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS || [];\n    const crypto2Options = config.tradingMode === \"SimpleSpot\" ? _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_QUOTES_SIMPLE[config.crypto1] || DEFAULT_QUOTE_CURRENCIES || [\n        \"USDT\",\n        \"USDC\",\n        \"BTC\"\n    ] : (_lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS || []).filter((c)=>c !== config.crypto1); // Ensure Crypto2 is not same as Crypto1 in Swap mode\n    // Debug log to see what's actually loaded\n    console.log('🔍 DEBUG: AVAILABLE_CRYPTOS length:', _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS.length);\n    console.log('🔍 DEBUG: crypto1Options length:', crypto1Options.length);\n    console.log('🔍 DEBUG: First 20 cryptos:', _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS.slice(0, 20));\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO1:', ALLOWED_CRYPTO1);\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO2:', ALLOWED_CRYPTO2);\n    console.log('🔍 DEBUG: AVAILABLE_STABLECOINS:', _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_STABLECOINS);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-sidebar-primary\",\n                    children: \"Trading Configuration\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_10__.ScrollArea, {\n                className: \"flex-1 pr-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Mode\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__.Checkbox, {\n                                                    id: \"enableStablecoinSwap\",\n                                                    checked: config.tradingMode === \"StablecoinSwap\",\n                                                    onCheckedChange: handleTradingModeChange\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"enableStablecoinSwap\",\n                                                    className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n                                                    children: \"Enable Stablecoin Swap Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"Current mode: \",\n                                                config.tradingMode === \"SimpleSpot\" ? \"Simple Spot\" : \"Stablecoin Swap\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this),\n                                        config.tradingMode === \"StablecoinSwap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"preferredStablecoin\",\n                                                    children: \"Preferred Stablecoin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                    name: \"preferredStablecoin\",\n                                                    value: config.preferredStablecoin,\n                                                    onValueChange: (val)=>handleSelectChange(\"preferredStablecoin\", val),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                            id: \"preferredStablecoin\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                placeholder: \"Select stablecoin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                            className: \"max-h-[300px] overflow-y-auto\",\n                                                            children: _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_STABLECOINS && _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_STABLECOINS.map((sc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: sc,\n                                                                    children: sc\n                                                                }, sc, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 81\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 18\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 87\n                                            }, this),\n                                            \" AI Mode Suggestion\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"riskTolerance\",\n                                                    children: \"Risk Tolerance\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                    value: riskTolerance,\n                                                    onValueChange: setRiskTolerance,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                            id: \"riskTolerance\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                placeholder: \"Select risk tolerance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 53\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: \"low\",\n                                                                    children: \"Low\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: \"medium\",\n                                                                    children: \"Medium\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: \"high\",\n                                                                    children: \"High\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"preferredCryptos\",\n                                                    children: \"Preferred Cryptocurrencies (comma-separated)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"preferredCryptos\",\n                                                    value: preferredCryptos,\n                                                    onChange: (e)=>setPreferredCryptos(e.target.value),\n                                                    placeholder: \"e.g., BTC, ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"investmentGoals\",\n                                                    children: \"Investment Goals\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"investmentGoals\",\n                                                    value: investmentGoals,\n                                                    onChange: (e)=>setInvestmentGoals(e.target.value),\n                                                    placeholder: \"e.g., Long term, Short term profit\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleAISuggestion,\n                                            disabled: aiLoading,\n                                            className: \"w-full btn-neo\",\n                                            children: [\n                                                aiLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 31\n                                                }, this),\n                                                \"Get AI Suggestion\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Pair\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_15__.CryptoInput, {\n                                            label: \"Crypto 1 (Base)\",\n                                            value: config.crypto1,\n                                            allowedCryptos: config.tradingMode === \"StablecoinSwap\" ? ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ] : ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ],\n                                            onValidCrypto: handleCrypto1Selection,\n                                            placeholder: \"e.g., BTC, ETH, SOL\",\n                                            description: \"Enter the base cryptocurrency symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_15__.CryptoInput, {\n                                            label: config.tradingMode === \"StablecoinSwap\" ? \"Crypto 2\" : \"Crypto 2 (Stablecoin)\",\n                                            value: config.crypto2,\n                                            allowedCryptos: config.tradingMode === \"StablecoinSwap\" ? ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ] : ALLOWED_CRYPTO2 || [\n                                                \"USDC\",\n                                                \"DAI\",\n                                                \"TUSD\",\n                                                \"FDUSD\",\n                                                \"USDT\",\n                                                \"EUR\"\n                                            ],\n                                            onValidCrypto: handleCrypto2Selection,\n                                            placeholder: config.tradingMode === \"StablecoinSwap\" ? \"e.g., BTC, ETH, SOL\" : \"e.g., USDT, USDC, DAI\",\n                                            description: config.tradingMode === \"StablecoinSwap\" ? \"Enter the second cryptocurrency symbol\" : \"Enter the quote/stablecoin symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Parameters\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        [\n                                            {\n                                                name: \"baseBid\",\n                                                label: \"Base Bid (Crypto 2)\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            },\n                                            {\n                                                name: \"multiplier\",\n                                                label: \"Multiplier\",\n                                                type: \"number\",\n                                                step: \"0.001\"\n                                            },\n                                            {\n                                                name: \"numDigits\",\n                                                label: \"Display Digits\",\n                                                type: \"number\",\n                                                step: \"1\"\n                                            },\n                                            {\n                                                name: \"slippagePercent\",\n                                                label: \"Slippage %\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            }\n                                        ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: field.name,\n                                                        children: field.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: field.name,\n                                                        name: field.name,\n                                                        type: field.type,\n                                                        value: config[field.name],\n                                                        onChange: handleInputChange,\n                                                        step: field.step,\n                                                        min: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    children: \"Couple Income % Split (must sum to 100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto1Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto1,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"incomeSplitCrypto1Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto1Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto1Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto2Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto2,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"incomeSplitCrypto2Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto2Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto2Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {\n                        className: \"mb-4 bg-sidebar-border\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_17__.cn)(\"text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border\", isBotActive ? \"bg-green-600 text-primary-foreground\" : \"bg-muted text-muted-foreground\"),\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 129\n                                    }, this),\n                                    \"Bot Status: \",\n                                    isBotActive ? 'Running' : isBotWarmingUp ? 'Warming Up' : 'Stopped'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setIsTargetModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                disabled: !config.crypto1 || !config.crypto2,\n                                children: !config.crypto1 || !config.crypto2 ? 'Select Crypto Pair First' : 'Set Target Prices'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setIsAlarmModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Alarm\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>{\n                                    if (isBotActive) {\n                                        dispatch({\n                                            type: 'SYSTEM_STOP_BOT'\n                                        });\n                                    } else {\n                                        dispatch({\n                                            type: 'SYSTEM_START_BOT_INITIATE'\n                                        });\n                                    }\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_17__.cn)('w-full btn-neo', isBotActive || isBotWarmingUp ? 'bg-destructive hover:bg-destructive/90' : 'bg-green-600 hover:bg-green-600/90'),\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 129\n                                    }, this),\n                                    isBotActive ? 'Stop Bot' : isBotWarmingUp ? 'Warming Up...' : 'Start Bot'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>{\n                                    dispatch({\n                                        type: 'SYSTEM_RESET_BOT'\n                                    });\n                                    toast({\n                                        title: \"Bot Reset\",\n                                        description: \"Trading bot has been reset to fresh state. All positions cleared.\",\n                                        duration: 3000\n                                    });\n                                },\n                                variant: \"outline\",\n                                className: \"w-full btn-outline-neo\",\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reset Bot\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_12__.TargetPriceModal, {\n                isOpen: isTargetModalOpen,\n                onClose: ()=>setIsTargetModalOpen(false),\n                onSetTargetPrices: contextSetTargetPrices\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 398,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_AlarmConfigModal__WEBPACK_IMPORTED_MODULE_13__.AlarmConfigModal, {\n                isOpen: isAlarmModalOpen,\n                onClose: ()=>setIsAlarmModalOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\n_s(TradingConfigSidebar, \"VLL9LNLRLp1B1k6hkOPIBw7LnUM=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_16__.useToast\n    ];\n});\n_c = TradingConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TradingConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx\n"));

/***/ })

});