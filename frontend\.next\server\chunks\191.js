"use strict";exports.id=191,exports.ids=[191],exports.modules={43:(e,t,r)=>{r.d(t,{jH:()=>i});var n=r(43210);r(60687);var o=n.createContext(void 0);function i(e){let t=n.useContext(o);return e||t||"ltr"}},1359:(e,t,r)=>{r.d(t,{Oh:()=>i});var n=r(43210),o=0;function i(){n.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??l()),document.body.insertAdjacentElement("beforeend",e[1]??l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},18853:(e,t,r)=>{r.d(t,{X:()=>i});var n=r(43210),o=r(66156);function i(e){let[t,r]=n.useState(void 0);return(0,o.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},24026:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},28850:(e,t,r)=>{r.d(t,{UC:()=>t4,YJ:()=>t7,In:()=>t9,q7:()=>rt,VF:()=>rn,p4:()=>rr,JU:()=>re,ZL:()=>t3,bL:()=>t2,wn:()=>ri,PP:()=>ro,wv:()=>rl,l9:()=>t6,WT:()=>t5,LM:()=>t8});var n=r(43210),o=r(51215),i=r(67969),l=r(70569),a=r(9510),c=r(98599),s=r(11273),u=r(43),d=r(31355),f=r(1359),p=r(32547),h=r(96963);let v=["top","right","bottom","left"],m=Math.min,g=Math.max,w=Math.round,y=Math.floor,b=e=>({x:e,y:e}),x={left:"right",right:"left",bottom:"top",top:"bottom"},S={start:"end",end:"start"};function E(e,t){return"function"==typeof e?e(t):e}function C(e){return e.split("-")[0]}function R(e){return e.split("-")[1]}function T(e){return"x"===e?"y":"x"}function A(e){return"y"===e?"height":"width"}function L(e){return["top","bottom"].includes(C(e))?"y":"x"}function P(e){return e.replace(/start|end/g,e=>S[e])}function j(e){return e.replace(/left|right|bottom|top/g,e=>x[e])}function k(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function N(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function D(e,t,r){let n,{reference:o,floating:i}=e,l=L(t),a=T(L(t)),c=A(a),s=C(t),u="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[c]/2-i[c]/2;switch(s){case"top":n={x:d,y:o.y-i.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-i.width,y:f};break;default:n={x:o.x,y:o.y}}switch(R(t)){case"start":n[a]-=p*(r&&u?-1:1);break;case"end":n[a]+=p*(r&&u?-1:1)}return n}let I=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:l}=r,a=i.filter(Boolean),c=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=D(s,n,c),f=n,p={},h=0;for(let r=0;r<a.length;r++){let{name:i,fn:v}=a[r],{x:m,y:g,data:w,reset:y}=await v({x:u,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});u=null!=m?m:u,d=null!=g?g:d,p={...p,[i]:{...p[i],...w}},y&&h<=50&&(h++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(s=!0===y.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:u,y:d}=D(s,f,c)),r=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}};async function M(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:i,rects:l,elements:a,strategy:c}=e,{boundary:s="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=E(t,e),h=k(p),v=a[f?"floating"===d?"reference":"floating":d],m=N(await i.getClippingRect({element:null==(r=await (null==i.isElement?void 0:i.isElement(v)))||r?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:s,rootBoundary:u,strategy:c})),g="floating"===d?{x:n,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),y=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},b=N(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:w,strategy:c}):g);return{top:(m.top-b.top+h.top)/y.y,bottom:(b.bottom-m.bottom+h.bottom)/y.y,left:(m.left-b.left+h.left)/y.x,right:(b.right-m.right+h.right)/y.x}}function O(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function H(e){return v.some(t=>e[t]>=0)}async function F(e,t){let{placement:r,platform:n,elements:o}=e,i=await (null==n.isRTL?void 0:n.isRTL(o.floating)),l=C(r),a=R(r),c="y"===L(r),s=["left","top"].includes(l)?-1:1,u=i&&c?-1:1,d=E(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),c?{x:p*u,y:f*s}:{x:f*s,y:p*u}}function W(){return"undefined"!=typeof window}function _(e){return V(e)?(e.nodeName||"").toLowerCase():"#document"}function B(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function z(e){var t;return null==(t=(V(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function V(e){return!!W()&&(e instanceof Node||e instanceof B(e).Node)}function G(e){return!!W()&&(e instanceof Element||e instanceof B(e).Element)}function K(e){return!!W()&&(e instanceof HTMLElement||e instanceof B(e).HTMLElement)}function X(e){return!!W()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof B(e).ShadowRoot)}function U(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=J(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function Y(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function $(e){let t=q(),r=G(e)?J(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function q(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function Z(e){return["html","body","#document"].includes(_(e))}function J(e){return B(e).getComputedStyle(e)}function Q(e){return G(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ee(e){if("html"===_(e))return e;let t=e.assignedSlot||e.parentNode||X(e)&&e.host||z(e);return X(t)?t.host:t}function et(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=ee(t);return Z(r)?t.ownerDocument?t.ownerDocument.body:t.body:K(r)&&U(r)?r:e(r)}(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),l=B(o);if(i){let e=er(l);return t.concat(l,l.visualViewport||[],U(o)?o:[],e&&r?et(e):[])}return t.concat(o,et(o,[],r))}function er(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function en(e){let t=J(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=K(e),i=o?e.offsetWidth:r,l=o?e.offsetHeight:n,a=w(r)!==i||w(n)!==l;return a&&(r=i,n=l),{width:r,height:n,$:a}}function eo(e){return G(e)?e:e.contextElement}function ei(e){let t=eo(e);if(!K(t))return b(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:i}=en(t),l=(i?w(r.width):r.width)/n,a=(i?w(r.height):r.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let el=b(0);function ea(e){let t=B(e);return q()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:el}function ec(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let i=e.getBoundingClientRect(),l=eo(e),a=b(1);t&&(n?G(n)&&(a=ei(n)):a=ei(e));let c=(void 0===(o=r)&&(o=!1),n&&(!o||n===B(l))&&o)?ea(l):b(0),s=(i.left+c.x)/a.x,u=(i.top+c.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=B(l),t=n&&G(n)?B(n):n,r=e,o=er(r);for(;o&&n&&t!==r;){let e=ei(o),t=o.getBoundingClientRect(),n=J(o),i=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;s*=e.x,u*=e.y,d*=e.x,f*=e.y,s+=i,u+=l,o=er(r=B(o))}}return N({width:d,height:f,x:s,y:u})}function es(e,t){let r=Q(e).scrollLeft;return t?t.left+r:ec(z(e)).left+r}function eu(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:es(e,n)),y:n.top+t.scrollTop}}function ed(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=B(e),n=z(e),o=r.visualViewport,i=n.clientWidth,l=n.clientHeight,a=0,c=0;if(o){i=o.width,l=o.height;let e=q();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,c=o.offsetTop)}return{width:i,height:l,x:a,y:c}}(e,r);else if("document"===t)n=function(e){let t=z(e),r=Q(e),n=e.ownerDocument.body,o=g(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=g(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),l=-r.scrollLeft+es(e),a=-r.scrollTop;return"rtl"===J(n).direction&&(l+=g(t.clientWidth,n.clientWidth)-o),{width:o,height:i,x:l,y:a}}(z(e));else if(G(t))n=function(e,t){let r=ec(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,i=K(e)?ei(e):b(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:n*i.y}}(t,r);else{let r=ea(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return N(n)}function ef(e){return"static"===J(e).position}function ep(e,t){if(!K(e)||"fixed"===J(e).position)return null;if(t)return t(e);let r=e.offsetParent;return z(e)===r&&(r=r.ownerDocument.body),r}function eh(e,t){let r=B(e);if(Y(e))return r;if(!K(e)){let t=ee(e);for(;t&&!Z(t);){if(G(t)&&!ef(t))return t;t=ee(t)}return r}let n=ep(e,t);for(;n&&["table","td","th"].includes(_(n))&&ef(n);)n=ep(n,t);return n&&Z(n)&&ef(n)&&!$(n)?r:n||function(e){let t=ee(e);for(;K(t)&&!Z(t);){if($(t))return t;if(Y(t))break;t=ee(t)}return null}(e)||r}let ev=async function(e){let t=this.getOffsetParent||eh,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=K(t),o=z(t),i="fixed"===r,l=ec(e,!0,i,t),a={scrollLeft:0,scrollTop:0},c=b(0);if(n||!n&&!i){if(("body"!==_(t)||U(o))&&(a=Q(t)),n){let e=ec(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=es(o))}let s=!o||n||i?b(0):eu(o,a);return{x:l.left+a.scrollLeft-c.x-s.x,y:l.top+a.scrollTop-c.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},em={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,i="fixed"===o,l=z(n),a=!!t&&Y(t.floating);if(n===l||a&&i)return r;let c={scrollLeft:0,scrollTop:0},s=b(1),u=b(0),d=K(n);if((d||!d&&!i)&&(("body"!==_(n)||U(l))&&(c=Q(n)),K(n))){let e=ec(n);s=ei(n),u.x=e.x+n.clientLeft,u.y=e.y+n.clientTop}let f=!l||d||i?b(0):eu(l,c,!0);return{width:r.width*s.x,height:r.height*s.y,x:r.x*s.x-c.scrollLeft*s.x+u.x+f.x,y:r.y*s.y-c.scrollTop*s.y+u.y+f.y}},getDocumentElement:z,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,i=[..."clippingAncestors"===r?Y(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=et(e,[],!1).filter(e=>G(e)&&"body"!==_(e)),o=null,i="fixed"===J(e).position,l=i?ee(e):e;for(;G(l)&&!Z(l);){let t=J(l),r=$(l);r||"fixed"!==t.position||(o=null),(i?!r&&!o:!r&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||U(l)&&!r&&function e(t,r){let n=ee(t);return!(n===r||!G(n)||Z(n))&&("fixed"===J(n).position||e(n,r))}(e,l))?n=n.filter(e=>e!==l):o=t,l=ee(l)}return t.set(e,n),n}(t,this._c):[].concat(r),n],l=i[0],a=i.reduce((e,r)=>{let n=ed(t,r,o);return e.top=g(n.top,e.top),e.right=m(n.right,e.right),e.bottom=m(n.bottom,e.bottom),e.left=g(n.left,e.left),e},ed(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eh,getElementRects:ev,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=en(e);return{width:t,height:r}},getScale:ei,isElement:G,isRTL:function(e){return"rtl"===J(e).direction}};function eg(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ew=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:i,platform:l,elements:a,middlewareData:c}=t,{element:s,padding:u=0}=E(e,t)||{};if(null==s)return{};let d=k(u),f={x:r,y:n},p=T(L(o)),h=A(p),v=await l.getDimensions(s),w="y"===p,y=w?"clientHeight":"clientWidth",b=i.reference[h]+i.reference[p]-f[p]-i.floating[h],x=f[p]-i.reference[p],S=await (null==l.getOffsetParent?void 0:l.getOffsetParent(s)),C=S?S[y]:0;C&&await (null==l.isElement?void 0:l.isElement(S))||(C=a.floating[y]||i.floating[h]);let P=C/2-v[h]/2-1,j=m(d[w?"top":"left"],P),N=m(d[w?"bottom":"right"],P),D=C-v[h]-N,I=C/2-v[h]/2+(b/2-x/2),M=g(j,m(I,D)),O=!c.arrow&&null!=R(o)&&I!==M&&i.reference[h]/2-(I<j?j:N)-v[h]/2<0,H=O?I<j?I-j:I-D:0;return{[p]:f[p]+H,data:{[p]:M,centerOffset:I-M-H,...O&&{alignmentOffset:H}},reset:O}}}),ey=(e,t,r)=>{let n=new Map,o={platform:em,...r},i={...o.platform,_c:n};return I(e,t,{...o,platform:i})};var eb="undefined"!=typeof document?n.useLayoutEffect:n.useEffect;function ex(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!ex(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!ex(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function eS(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eE(e,t){let r=eS(e);return Math.round(t*r)/r}function eC(e){let t=n.useRef(e);return eb(()=>{t.current=e}),t}let eR=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?ew({element:r.current,padding:n}).fn(t):{}:r?ew({element:r,padding:n}).fn(t):{}}}),eT=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:i,placement:l,middlewareData:a}=t,c=await F(t,e);return l===(null==(r=a.offset)?void 0:r.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:l}}}}}(e),options:[e,t]}),eA=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...c}=E(e,t),s={x:r,y:n},u=await M(t,c),d=L(C(o)),f=T(d),p=s[f],h=s[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=p+u[e],n=p-u[t];p=g(r,m(p,n))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",r=h+u[e],n=h-u[t];h=g(r,m(h,n))}let v=a.fn({...t,[f]:p,[d]:h});return{...v,data:{x:v.x-r,y:v.y-n,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}),eL=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:c=!0,crossAxis:s=!0}=E(e,t),u={x:r,y:n},d=L(o),f=T(d),p=u[f],h=u[d],v=E(a,t),m="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(c){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+m.mainAxis,r=i.reference[f]+i.reference[e]-m.mainAxis;p<t?p=t:p>r&&(p=r)}if(s){var g,w;let e="y"===f?"width":"height",t=["top","left"].includes(C(o)),r=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:m.crossAxis),n=i.reference[d]+i.reference[e]+(t?0:(null==(w=l.offset)?void 0:w[d])||0)-(t?m.crossAxis:0);h<r?h=r:h>n&&(h=n)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,i,l;let{placement:a,middlewareData:c,rects:s,initialPlacement:u,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:v,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:w=!0,...y}=E(e,t);if(null!=(r=c.arrow)&&r.alignmentOffset)return{};let b=C(a),x=L(u),S=C(u)===u,k=await (null==d.isRTL?void 0:d.isRTL(f.floating)),N=v||(S||!w?[j(u)]:function(e){let t=j(e);return[P(e),t,P(t)]}(u)),D="none"!==g;!v&&D&&N.push(...function(e,t,r,n){let o=R(e),i=function(e,t,r){let n=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(r)return t?o:n;return t?n:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(C(e),"start"===r,n);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(P)))),i}(u,w,g,k));let I=[u,...N],O=await M(t,y),H=[],F=(null==(n=c.flip)?void 0:n.overflows)||[];if(p&&H.push(O[b]),h){let e=function(e,t,r){void 0===r&&(r=!1);let n=R(e),o=T(L(e)),i=A(o),l="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=j(l)),[l,j(l)]}(a,s,k);H.push(O[e[0]],O[e[1]])}if(F=[...F,{placement:a,overflows:H}],!H.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=I[e];if(t)return{data:{index:e,overflows:F},reset:{placement:t}};let r=null==(i=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!r)switch(m){case"bestFit":{let e=null==(l=F.filter(e=>{if(D){let t=L(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(r=e);break}case"initialPlacement":r=u}if(a!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),ej=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,i;let{placement:l,rects:a,platform:c,elements:s}=t,{apply:u=()=>{},...d}=E(e,t),f=await M(t,d),p=C(l),h=R(l),v="y"===L(l),{width:w,height:y}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==c.isRTL?void 0:c.isRTL(s.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let b=y-f.top-f.bottom,x=w-f.left-f.right,S=m(y-f[o],b),T=m(w-f[i],x),A=!t.middlewareData.shift,P=S,j=T;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(j=x),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(P=b),A&&!h){let e=g(f.left,0),t=g(f.right,0),r=g(f.top,0),n=g(f.bottom,0);v?j=w-2*(0!==e||0!==t?e+t:g(f.left,f.right)):P=y-2*(0!==r||0!==n?r+n:g(f.top,f.bottom))}await u({...t,availableWidth:j,availableHeight:P});let k=await c.getDimensions(s.floating);return w!==k.width||y!==k.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ek=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=E(e,t);switch(n){case"referenceHidden":{let e=O(await M(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:H(e)}}}case"escaped":{let e=O(await M(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:H(e)}}}default:return{}}}}}(e),options:[e,t]}),eN=(e,t)=>({...eR(e),options:[e,t]});var eD=r(14163),eI=r(60687),eM=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,eI.jsx)(eD.sG.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eI.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eM.displayName="Arrow";var eO=r(13495),eH=r(66156),eF=r(18853),eW="Popper",[e_,eB]=(0,s.A)(eW),[ez,eV]=e_(eW),eG=e=>{let{__scopePopper:t,children:r}=e,[o,i]=n.useState(null);return(0,eI.jsx)(ez,{scope:t,anchor:o,onAnchorChange:i,children:r})};eG.displayName=eW;var eK="PopperAnchor",eX=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...i}=e,l=eV(eK,r),a=n.useRef(null),s=(0,c.s)(t,a);return n.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eI.jsx)(eD.sG.div,{...i,ref:s})});eX.displayName=eK;var eU="PopperContent",[eY,e$]=e_(eU),eq=n.forwardRef((e,t)=>{let{__scopePopper:r,side:i="bottom",sideOffset:l=0,align:a="center",alignOffset:s=0,arrowPadding:u=0,avoidCollisions:d=!0,collisionBoundary:f=[],collisionPadding:p=0,sticky:h="partial",hideWhenDetached:v=!1,updatePositionStrategy:w="optimized",onPlaced:b,...x}=e,S=eV(eU,r),[E,C]=n.useState(null),R=(0,c.s)(t,e=>C(e)),[T,A]=n.useState(null),L=(0,eF.X)(T),P=L?.width??0,j=L?.height??0,k="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},N=Array.isArray(f)?f:[f],D=N.length>0,I={padding:k,boundary:N.filter(e0),altBoundary:D},{refs:M,floatingStyles:O,placement:H,isPositioned:F,middlewareData:W}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:i=[],platform:l,elements:{reference:a,floating:c}={},transform:s=!0,whileElementsMounted:u,open:d}=e,[f,p]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[h,v]=n.useState(i);ex(h,i)||v(i);let[m,g]=n.useState(null),[w,y]=n.useState(null),b=n.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=n.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),S=a||m,E=c||w,C=n.useRef(null),R=n.useRef(null),T=n.useRef(f),A=null!=u,L=eC(u),P=eC(l),j=eC(d),k=n.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:r,middleware:h};P.current&&(e.platform=P.current),ey(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==j.current};N.current&&!ex(T.current,t)&&(T.current=t,o.flushSync(()=>{p(t)}))})},[h,t,r,P,j]);eb(()=>{!1===d&&T.current.isPositioned&&(T.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let N=n.useRef(!1);eb(()=>(N.current=!0,()=>{N.current=!1}),[]),eb(()=>{if(S&&(C.current=S),E&&(R.current=E),S&&E){if(L.current)return L.current(S,E,k);k()}},[S,E,k,L,A]);let D=n.useMemo(()=>({reference:C,floating:R,setReference:b,setFloating:x}),[b,x]),I=n.useMemo(()=>({reference:S,floating:E}),[S,E]),M=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!I.floating)return e;let t=eE(I.floating,f.x),n=eE(I.floating,f.y);return s?{...e,transform:"translate("+t+"px, "+n+"px)",...eS(I.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,s,I.floating,f.x,f.y]);return n.useMemo(()=>({...f,update:k,refs:D,elements:I,floatingStyles:M}),[f,k,D,I,M])}({strategy:"fixed",placement:i+("center"!==a?"-"+a:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:s=!1}=n,u=eo(e),d=i||l?[...u?et(u):[],...et(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",r,{passive:!0}),l&&e.addEventListener("resize",r)});let f=u&&c?function(e,t){let r,n=null,o=z(e);function i(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function l(a,c){void 0===a&&(a=!1),void 0===c&&(c=1),i();let s=e.getBoundingClientRect(),{left:u,top:d,width:f,height:p}=s;if(a||t(),!f||!p)return;let h=y(d),v=y(o.clientWidth-(u+f)),w={rootMargin:-h+"px "+-v+"px "+-y(o.clientHeight-(d+p))+"px "+-y(u)+"px",threshold:g(0,m(1,c))||1},b=!0;function x(t){let n=t[0].intersectionRatio;if(n!==c){if(!b)return l();n?l(!1,n):r=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==n||eg(s,e.getBoundingClientRect())||l(),b=!1}try{n=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(x,w)}n.observe(e)}(!0),i}(u,r):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[n]=e;n&&n.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),r()}),u&&!s&&h.observe(u),h.observe(t));let v=s?ec(e):null;return s&&function t(){let n=ec(e);v&&!eg(v,n)&&r(),v=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",r),l&&e.removeEventListener("resize",r)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,s&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===w}),elements:{reference:S.anchor},middleware:[eT({mainAxis:l+j,alignmentAxis:s}),d&&eA({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?eL():void 0,...I}),d&&eP({...I}),ej({...I,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${r}px`),l.setProperty("--radix-popper-available-height",`${n}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),T&&eN({element:T,padding:u}),e1({arrowWidth:P,arrowHeight:j}),v&&ek({strategy:"referenceHidden",...I})]}),[_,B]=e2(H),V=(0,eO.c)(b);(0,eH.N)(()=>{F&&V?.()},[F,V]);let G=W.arrow?.x,K=W.arrow?.y,X=W.arrow?.centerOffset!==0,[U,Y]=n.useState();return(0,eH.N)(()=>{E&&Y(window.getComputedStyle(E).zIndex)},[E]),(0,eI.jsx)("div",{ref:M.setFloating,"data-radix-popper-content-wrapper":"",style:{...O,transform:F?O.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:U,"--radix-popper-transform-origin":[W.transformOrigin?.x,W.transformOrigin?.y].join(" "),...W.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eI.jsx)(eY,{scope:r,placedSide:_,onArrowChange:A,arrowX:G,arrowY:K,shouldHideArrow:X,children:(0,eI.jsx)(eD.sG.div,{"data-side":_,"data-align":B,...x,ref:R,style:{...x.style,animation:F?void 0:"none"}})})})});eq.displayName=eU;var eZ="PopperArrow",eJ={top:"bottom",right:"left",bottom:"top",left:"right"},eQ=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=e$(eZ,r),i=eJ[o.placedSide];return(0,eI.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eI.jsx)(eM,{...n,ref:t,style:{...n.style,display:"block"}})})});function e0(e){return null!==e}eQ.displayName=eZ;var e1=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[c,s]=e2(r),u={start:"0%",center:"50%",end:"100%"}[s],d=(o.arrow?.x??0)+l/2,f=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===c?(p=i?u:`${d}px`,h=`${-a}px`):"top"===c?(p=i?u:`${d}px`,h=`${n.floating.height+a}px`):"right"===c?(p=`${-a}px`,h=i?u:`${f}px`):"left"===c&&(p=`${n.floating.width+a}px`,h=i?u:`${f}px`),{data:{x:p,y:h}}}});function e2(e){let[t,r="center"]=e.split("-");return[t,r]}var e6=r(25028),e5=r(8730),e9=r(65551),e3=r(83721),e4=r(69024),e8=r(63376),e7=r(42247),te=[" ","Enter","ArrowUp","ArrowDown"],tt=[" ","Enter"],tr="Select",[tn,to,ti]=(0,a.N)(tr),[tl,ta]=(0,s.A)(tr,[ti,eB]),tc=eB(),[ts,tu]=tl(tr),[td,tf]=tl(tr),tp=e=>{let{__scopeSelect:t,children:r,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:c,onValueChange:s,dir:d,name:f,autoComplete:p,disabled:v,required:m,form:g}=e,w=tc(t),[y,b]=n.useState(null),[x,S]=n.useState(null),[E,C]=n.useState(!1),R=(0,u.jH)(d),[T=!1,A]=(0,e9.i)({prop:o,defaultProp:i,onChange:l}),[L,P]=(0,e9.i)({prop:a,defaultProp:c,onChange:s}),j=n.useRef(null),k=!y||g||!!y.closest("form"),[N,D]=n.useState(new Set),I=Array.from(N).map(e=>e.props.value).join(";");return(0,eI.jsx)(eG,{...w,children:(0,eI.jsxs)(ts,{required:m,scope:t,trigger:y,onTriggerChange:b,valueNode:x,onValueNodeChange:S,valueNodeHasChildren:E,onValueNodeHasChildrenChange:C,contentId:(0,h.B)(),value:L,onValueChange:P,open:T,onOpenChange:A,dir:R,triggerPointerDownPosRef:j,disabled:v,children:[(0,eI.jsx)(tn.Provider,{scope:t,children:(0,eI.jsx)(td,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{D(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{D(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),k?(0,eI.jsxs)(tQ,{"aria-hidden":!0,required:m,tabIndex:-1,name:f,autoComplete:p,value:L,onChange:e=>P(e.target.value),disabled:v,form:g,children:[void 0===L?(0,eI.jsx)("option",{value:""}):null,Array.from(N)]},I):null]})})};tp.displayName=tr;var th="SelectTrigger",tv=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:o=!1,...i}=e,a=tc(r),s=tu(th,r),u=s.disabled||o,d=(0,c.s)(t,s.onTriggerChange),f=to(r),p=n.useRef("touch"),[h,v,m]=t0(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===s.value),n=t1(t,e,r);void 0!==n&&s.onValueChange(n.value)}),g=e=>{u||(s.onOpenChange(!0),m()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,eI.jsx)(eX,{asChild:!0,...a,children:(0,eI.jsx)(eD.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":tJ(s.value)?"":void 0,...i,ref:d,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&g(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&te.includes(e.key)&&(g(),e.preventDefault())})})})});tv.displayName=th;var tm="SelectValue",tg=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:i,placeholder:l="",...a}=e,s=tu(tm,r),{onValueNodeHasChildrenChange:u}=s,d=void 0!==i,f=(0,c.s)(t,s.onValueNodeChange);return(0,eH.N)(()=>{u(d)},[u,d]),(0,eI.jsx)(eD.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:tJ(s.value)?(0,eI.jsx)(eI.Fragment,{children:l}):i})});tg.displayName=tm;var tw=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,eI.jsx)(eD.sG.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});tw.displayName="SelectIcon";var ty=e=>(0,eI.jsx)(e6.Z,{asChild:!0,...e});ty.displayName="SelectPortal";var tb="SelectContent",tx=n.forwardRef((e,t)=>{let r=tu(tb,e.__scopeSelect),[i,l]=n.useState();return((0,eH.N)(()=>{l(new DocumentFragment)},[]),r.open)?(0,eI.jsx)(tC,{...e,ref:t}):i?o.createPortal((0,eI.jsx)(tS,{scope:e.__scopeSelect,children:(0,eI.jsx)(tn.Slot,{scope:e.__scopeSelect,children:(0,eI.jsx)("div",{children:e.children})})}),i):null});tx.displayName=tb;var[tS,tE]=tl(tb),tC=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:s,side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:w,collisionPadding:y,sticky:b,hideWhenDetached:x,avoidCollisions:S,...E}=e,C=tu(tb,r),[R,T]=n.useState(null),[A,L]=n.useState(null),P=(0,c.s)(t,e=>T(e)),[j,k]=n.useState(null),[N,D]=n.useState(null),I=to(r),[M,O]=n.useState(!1),H=n.useRef(!1);n.useEffect(()=>{if(R)return(0,e8.Eq)(R)},[R]),(0,f.Oh)();let F=n.useCallback(e=>{let[t,...r]=I().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&A&&(A.scrollTop=0),r===n&&A&&(A.scrollTop=A.scrollHeight),r?.focus(),document.activeElement!==o))return},[I,A]),W=n.useCallback(()=>F([j,R]),[F,j,R]);n.useEffect(()=>{M&&W()},[M,W]);let{onOpenChange:_,triggerPointerDownPosRef:B}=C;n.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(B.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(B.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():R.contains(r.target)||_(!1),document.removeEventListener("pointermove",t),B.current=null};return null!==B.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[R,_,B]),n.useEffect(()=>{let e=()=>_(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[_]);let[z,V]=t0(e=>{let t=I().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=t1(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),G=n.useCallback((e,t,r)=>{let n=!H.current&&!r;(void 0!==C.value&&C.value===t||n)&&(k(e),n&&(H.current=!0))},[C.value]),K=n.useCallback(()=>R?.focus(),[R]),X=n.useCallback((e,t,r)=>{let n=!H.current&&!r;(void 0!==C.value&&C.value===t||n)&&D(e)},[C.value]),U="popper"===o?tT:tR,Y=U===tT?{side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:w,collisionPadding:y,sticky:b,hideWhenDetached:x,avoidCollisions:S}:{};return(0,eI.jsx)(tS,{scope:r,content:R,viewport:A,onViewportChange:L,itemRefCallback:G,selectedItem:j,onItemLeave:K,itemTextRefCallback:X,focusSelectedItem:W,selectedItemText:N,position:o,isPositioned:M,searchRef:z,children:(0,eI.jsx)(e7.A,{as:e5.DX,allowPinchZoom:!0,children:(0,eI.jsx)(p.n,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{C.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,eI.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,eI.jsx)(U,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...E,...Y,onPlaced:()=>O(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...E.style},onKeyDown:(0,l.m)(E.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||V(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=I().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});tC.displayName="SelectContentImpl";var tR=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:o,...l}=e,a=tu(tb,r),s=tE(tb,r),[u,d]=n.useState(null),[f,p]=n.useState(null),h=(0,c.s)(t,e=>p(e)),v=to(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:w,selectedItem:y,selectedItemText:b,focusSelectedItem:x}=s,S=n.useCallback(()=>{if(a.trigger&&a.valueNode&&u&&f&&w&&y&&b){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=a.valueNode.getBoundingClientRect(),n=b.getBoundingClientRect();if("rtl"!==a.dir){let o=n.left-t.left,l=r.left-o,a=e.left-l,c=e.width+a,s=Math.max(c,t.width),d=window.innerWidth-10,f=(0,i.q)(l,[10,Math.max(10,d-s)]);u.style.minWidth=c+"px",u.style.left=f+"px"}else{let o=t.right-n.right,l=window.innerWidth-r.right-o,a=window.innerWidth-e.right-l,c=e.width+a,s=Math.max(c,t.width),d=window.innerWidth-10,f=(0,i.q)(l,[10,Math.max(10,d-s)]);u.style.minWidth=c+"px",u.style.right=f+"px"}let l=v(),c=window.innerHeight-20,s=w.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),x=p+h+s+parseInt(d.paddingBottom,10)+g,S=Math.min(5*y.offsetHeight,x),E=window.getComputedStyle(w),C=parseInt(E.paddingTop,10),R=parseInt(E.paddingBottom,10),T=e.top+e.height/2-10,A=y.offsetHeight/2,L=p+h+(y.offsetTop+A);if(L<=T){let e=l.length>0&&y===l[l.length-1].ref.current;u.style.bottom="0px";let t=Math.max(c-T,A+(e?R:0)+(f.clientHeight-w.offsetTop-w.offsetHeight)+g);u.style.height=L+t+"px"}else{let e=l.length>0&&y===l[0].ref.current;u.style.top="0px";let t=Math.max(T,p+w.offsetTop+(e?C:0)+A);u.style.height=t+(x-L)+"px",w.scrollTop=L-T+w.offsetTop}u.style.margin="10px 0",u.style.minHeight=S+"px",u.style.maxHeight=c+"px",o?.(),requestAnimationFrame(()=>m.current=!0)}},[v,a.trigger,a.valueNode,u,f,w,y,b,a.dir,o]);(0,eH.N)(()=>S(),[S]);let[E,C]=n.useState();(0,eH.N)(()=>{f&&C(window.getComputedStyle(f).zIndex)},[f]);let R=n.useCallback(e=>{e&&!0===g.current&&(S(),x?.(),g.current=!1)},[S,x]);return(0,eI.jsx)(tA,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:R,children:(0,eI.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:(0,eI.jsx)(eD.sG.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});tR.displayName="SelectItemAlignedPosition";var tT=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...i}=e,l=tc(r);return(0,eI.jsx)(eq,{...l,...i,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tT.displayName="SelectPopperPosition";var[tA,tL]=tl(tb,{}),tP="SelectViewport",tj=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:o,...i}=e,a=tE(tP,r),s=tL(tP,r),u=(0,c.s)(t,a.onViewportChange),d=n.useRef(0);return(0,eI.jsxs)(eI.Fragment,{children:[(0,eI.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,eI.jsx)(tn.Slot,{scope:r,children:(0,eI.jsx)(eD.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=s;if(n?.current&&r){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let i=o+e,l=Math.min(n,i),a=i-l;r.style.height=l+"px","0px"===r.style.bottom&&(t.scrollTop=a>0?a:0,r.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});tj.displayName=tP;var tk="SelectGroup",[tN,tD]=tl(tk),tI=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=(0,h.B)();return(0,eI.jsx)(tN,{scope:r,id:o,children:(0,eI.jsx)(eD.sG.div,{role:"group","aria-labelledby":o,...n,ref:t})})});tI.displayName=tk;var tM="SelectLabel",tO=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=tD(tM,r);return(0,eI.jsx)(eD.sG.div,{id:o.id,...n,ref:t})});tO.displayName=tM;var tH="SelectItem",[tF,tW]=tl(tH),t_=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,disabled:i=!1,textValue:a,...s}=e,u=tu(tH,r),d=tE(tH,r),f=u.value===o,[p,v]=n.useState(a??""),[m,g]=n.useState(!1),w=(0,c.s)(t,e=>d.itemRefCallback?.(e,o,i)),y=(0,h.B)(),b=n.useRef("touch"),x=()=>{i||(u.onValueChange(o),u.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,eI.jsx)(tF,{scope:r,value:o,disabled:i,textId:y,isSelected:f,onItemTextChange:n.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,eI.jsx)(tn.ItemSlot,{scope:r,value:o,disabled:i,textValue:p,children:(0,eI.jsx)(eD.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":f&&m,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...s,ref:w,onFocus:(0,l.m)(s.onFocus,()=>g(!0)),onBlur:(0,l.m)(s.onBlur,()=>g(!1)),onClick:(0,l.m)(s.onClick,()=>{"mouse"!==b.current&&x()}),onPointerUp:(0,l.m)(s.onPointerUp,()=>{"mouse"===b.current&&x()}),onPointerDown:(0,l.m)(s.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,l.m)(s.onPointerMove,e=>{b.current=e.pointerType,i?d.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,l.m)(s.onKeyDown,e=>{(d.searchRef?.current===""||" "!==e.key)&&(tt.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});t_.displayName=tH;var tB="SelectItemText",tz=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:i,style:l,...a}=e,s=tu(tB,r),u=tE(tB,r),d=tW(tB,r),f=tf(tB,r),[p,h]=n.useState(null),v=(0,c.s)(t,e=>h(e),d.onItemTextChange,e=>u.itemTextRefCallback?.(e,d.value,d.disabled)),m=p?.textContent,g=n.useMemo(()=>(0,eI.jsx)("option",{value:d.value,disabled:d.disabled,children:m},d.value),[d.disabled,d.value,m]),{onNativeOptionAdd:w,onNativeOptionRemove:y}=f;return(0,eH.N)(()=>(w(g),()=>y(g)),[w,y,g]),(0,eI.jsxs)(eI.Fragment,{children:[(0,eI.jsx)(eD.sG.span,{id:d.textId,...a,ref:v}),d.isSelected&&s.valueNode&&!s.valueNodeHasChildren?o.createPortal(a.children,s.valueNode):null]})});tz.displayName=tB;var tV="SelectItemIndicator",tG=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return tW(tV,r).isSelected?(0,eI.jsx)(eD.sG.span,{"aria-hidden":!0,...n,ref:t}):null});tG.displayName=tV;var tK="SelectScrollUpButton",tX=n.forwardRef((e,t)=>{let r=tE(tK,e.__scopeSelect),o=tL(tK,e.__scopeSelect),[i,l]=n.useState(!1),a=(0,c.s)(t,o.onScrollButtonChange);return(0,eH.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){l(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),i?(0,eI.jsx)(t$,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});tX.displayName=tK;var tU="SelectScrollDownButton",tY=n.forwardRef((e,t)=>{let r=tE(tU,e.__scopeSelect),o=tL(tU,e.__scopeSelect),[i,l]=n.useState(!1),a=(0,c.s)(t,o.onScrollButtonChange);return(0,eH.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),i?(0,eI.jsx)(t$,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});tY.displayName=tU;var t$=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:o,...i}=e,a=tE("SelectScrollButton",r),c=n.useRef(null),s=to(r),u=n.useCallback(()=>{null!==c.current&&(window.clearInterval(c.current),c.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,eH.N)(()=>{let e=s().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[s]),(0,eI.jsx)(eD.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===c.current&&(c.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{a.onItemLeave?.(),null===c.current&&(c.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{u()})})}),tq=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,eI.jsx)(eD.sG.div,{"aria-hidden":!0,...n,ref:t})});tq.displayName="SelectSeparator";var tZ="SelectArrow";function tJ(e){return""===e||void 0===e}n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=tc(r),i=tu(tZ,r),l=tE(tZ,r);return i.open&&"popper"===l.position?(0,eI.jsx)(eQ,{...o,...n,ref:t}):null}).displayName=tZ;var tQ=n.forwardRef((e,t)=>{let{value:r,...o}=e,i=n.useRef(null),l=(0,c.s)(t,i),a=(0,e3.Z)(r);return n.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[a,r]),(0,eI.jsx)(e4.s,{asChild:!0,children:(0,eI.jsx)("select",{...o,ref:l,defaultValue:r})})});function t0(e){let t=(0,eO.c)(e),r=n.useRef(""),o=n.useRef(0),i=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),l=n.useCallback(()=>{r.current="",window.clearTimeout(o.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),[r,i,l]}function t1(e,t,r){var n,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=r?e.indexOf(r):-1,a=(n=e,o=Math.max(l,0),n.map((e,t)=>n[(o+t)%n.length]));1===i.length&&(a=a.filter(e=>e!==r));let c=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return c!==r?c:void 0}tQ.displayName="BubbleSelect";var t2=tp,t6=tv,t5=tg,t9=tw,t3=ty,t4=tx,t8=tj,t7=tI,re=tO,rt=t_,rr=tz,rn=tG,ro=tX,ri=tY,rl=tq},32547:(e,t,r)=>{r.d(t,{n:()=>d});var n=r(43210),o=r(98599),i=r(14163),l=r(13495),a=r(60687),c="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",u={bubbles:!1,cancelable:!0},d=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:d=!1,onMountAutoFocus:m,onUnmountAutoFocus:g,...w}=e,[y,b]=n.useState(null),x=(0,l.c)(m),S=(0,l.c)(g),E=n.useRef(null),C=(0,o.s)(t,e=>b(e)),R=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(d){let e=function(e){if(R.paused||!y)return;let t=e.target;y.contains(t)?E.current=t:h(E.current,{select:!0})},t=function(e){if(R.paused||!y)return;let t=e.relatedTarget;null===t||y.contains(t)||h(E.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(y)});return y&&r.observe(y,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[d,y,R.paused]),n.useEffect(()=>{if(y){v.add(R);let e=document.activeElement;if(!y.contains(e)){let t=new CustomEvent(c,u);y.addEventListener(c,x),y.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(h(n,{select:t}),document.activeElement!==r)return}(f(y).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(y))}return()=>{y.removeEventListener(c,x),setTimeout(()=>{let t=new CustomEvent(s,u);y.addEventListener(s,S),y.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),y.removeEventListener(s,S),v.remove(R)},0)}}},[y,x,S,R]);let T=n.useCallback(e=>{if(!r&&!d||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||n!==i?e.shiftKey&&n===o&&(e.preventDefault(),r&&h(i,{select:!0})):(e.preventDefault(),r&&h(o,{select:!0})):n===t&&e.preventDefault()}},[r,d,R.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...w,ref:C,onKeyDown:T})});function f(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function p(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function h(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}d.displayName="FocusScope";var v=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=m(e,t)).unshift(t)},remove(t){e=m(e,t),e[0]?.resume()}}}();function m(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},41360:(e,t,r)=>{r.d(t,{UC:()=>$,B8:()=>U,bL:()=>X,l9:()=>Y});var n=r(43210),o=r(70569),i=r(11273),l=r(9510),a=r(98599),c=r(96963),s=r(14163),u=r(13495),d=r(65551),f=r(43),p=r(60687),h="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[g,w,y]=(0,l.N)(m),[b,x]=(0,i.A)(m,[y]),[S,E]=b(m),C=n.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(R,{...e,ref:t})})}));C.displayName=m;var R=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:l=!1,dir:c,currentTabStopId:m,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:y,onEntryFocus:b,preventScrollOnEntryFocus:x=!1,...E}=e,C=n.useRef(null),R=(0,a.s)(t,C),T=(0,f.jH)(c),[A=null,L]=(0,d.i)({prop:m,defaultProp:g,onChange:y}),[j,k]=n.useState(!1),N=(0,u.c)(b),D=w(r),I=n.useRef(!1),[M,O]=n.useState(0);return n.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(h,N),()=>e.removeEventListener(h,N)},[N]),(0,p.jsx)(S,{scope:r,orientation:i,dir:T,loop:l,currentTabStopId:A,onItemFocus:n.useCallback(e=>L(e),[L]),onItemShiftTab:n.useCallback(()=>k(!0),[]),onFocusableItemAdd:n.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>O(e=>e-1),[]),children:(0,p.jsx)(s.sG.div,{tabIndex:j||0===M?-1:0,"data-orientation":i,...E,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{I.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!I.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(h,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);P([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),x)}}I.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>k(!1))})})}),T="RovingFocusGroupItem",A=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:l=!1,tabStopId:a,...u}=e,d=(0,c.B)(),f=a||d,h=E(T,r),v=h.currentTabStopId===f,m=w(r),{onFocusableItemAdd:y,onFocusableItemRemove:b}=h;return n.useEffect(()=>{if(i)return y(),()=>b()},[i,y,b]),(0,p.jsx)(g.ItemSlot,{scope:r,id:f,focusable:i,active:l,children:(0,p.jsx)(s.sG.span,{tabIndex:v?0:-1,"data-orientation":h.orientation,...u,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?h.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>h.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){h.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return L[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=m().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=h.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>P(r))}})})})});A.displayName=T;var L={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function P(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var j=r(46059),k="Tabs",[N,D]=(0,i.A)(k,[x]),I=x(),[M,O]=N(k),H=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:i,orientation:l="horizontal",dir:a,activationMode:u="automatic",...h}=e,v=(0,f.jH)(a),[m,g]=(0,d.i)({prop:n,onChange:o,defaultProp:i});return(0,p.jsx)(M,{scope:r,baseId:(0,c.B)(),value:m,onValueChange:g,orientation:l,dir:v,activationMode:u,children:(0,p.jsx)(s.sG.div,{dir:v,"data-orientation":l,...h,ref:t})})});H.displayName=k;var F="TabsList",W=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,i=O(F,r),l=I(r);return(0,p.jsx)(C,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:n,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});W.displayName=F;var _="TabsTrigger",B=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:i=!1,...l}=e,a=O(_,r),c=I(r),u=G(a.baseId,n),d=K(a.baseId,n),f=n===a.value;return(0,p.jsx)(A,{asChild:!0,...c,focusable:!i,active:f,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...l,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():a.onValueChange(n)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&a.onValueChange(n)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==a.activationMode;f||i||!e||a.onValueChange(n)})})})});B.displayName=_;var z="TabsContent",V=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:i,children:l,...a}=e,c=O(z,r),u=G(c.baseId,o),d=K(c.baseId,o),f=o===c.value,h=n.useRef(f);return n.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(j.C,{present:i||f,children:({present:r})=>(0,p.jsx)(s.sG.div,{"data-state":f?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:d,tabIndex:0,...a,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:r&&l})})});function G(e,t){return`${e}-trigger-${t}`}function K(e,t){return`${e}-content-${t}`}V.displayName=z;var X=H,U=W,Y=B,$=V},42247:(e,t,r)=>{r.d(t,{A:()=>K});var n,o=function(){return(o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,r(43210)),a="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var u="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,r,n,i,l=(t=null,void 0===r&&(r=f),n=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,i);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){i=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var o=function(){var r=t;t=[],r.forEach(e)},l=function(){return Promise.resolve().then(o)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return l.options=o({async:!0,ssr:!1},e),l}(),h=function(){},v=l.forwardRef(function(e,t){var r,n,a,c,f=l.useRef(null),v=l.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),m=v[0],g=v[1],w=e.forwardProps,y=e.children,b=e.className,x=e.removeScrollBar,S=e.enabled,E=e.shards,C=e.sideCar,R=e.noIsolation,T=e.inert,A=e.allowPinchZoom,L=e.as,P=e.gapMode,j=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=(r=[f,t],n=function(e){return r.forEach(function(t){return s(t,e)})},(a=(0,l.useState)(function(){return{value:null,callback:n,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=n,c=a.facade,u(function(){var e=d.get(c);if(e){var t=new Set(e),n=new Set(r),o=c.current;t.forEach(function(e){n.has(e)||s(e,null)}),n.forEach(function(e){t.has(e)||s(e,o)})}d.set(c,r)},[r]),c),N=o(o({},j),m);return l.createElement(l.Fragment,null,S&&l.createElement(C,{sideCar:p,removeScrollBar:x,shards:E,noIsolation:R,inert:T,setCallbacks:g,allowPinchZoom:!!A,lockRef:f,gapMode:P}),w?l.cloneElement(l.Children.only(y),o(o({},N),{ref:k})):l.createElement(void 0===L?"div":L,o({},N,{className:b,ref:k}),y))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:a};var m=function(e){var t=e.sideCar,r=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return l.createElement(n,o({},r))};m.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=n||r.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=g();return function(t,r){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},y=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(r),x(n),x(o)]},E=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=S(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},C=y(),R="data-scroll-locked",T=function(e,t,r,n){var o=e.left,i=e.top,l=e.right,s=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(s,"px ").concat(n,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(s,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(s,"px ").concat(n,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(s,"px ").concat(n,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},A=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},L=function(){l.useEffect(function(){return document.body.setAttribute(R,(A()+1).toString()),function(){var e=A()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},P=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;L();var i=l.useMemo(function(){return E(o)},[o]);return l.createElement(C,{styles:T(i,!t,o,r?"":"!important")})},j=!1;if("undefined"!=typeof window)try{var k=Object.defineProperty({},"passive",{get:function(){return j=!0,!0}});window.addEventListener("test",k,k),window.removeEventListener("test",k,k)}catch(e){j=!1}var N=!!j&&{passive:!1},D=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},I=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),M(e,n)){var o=O(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},M=function(e,t){return"v"===e?D(t,"overflowY"):D(t,"overflowX")},O=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},H=function(e,t,r,n,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*n,c=r.target,s=t.contains(c),u=!1,d=a>0,f=0,p=0;do{var h=O(e,c),v=h[0],m=h[1]-h[2]-l*v;(v||m)&&M(e,c)&&(f+=m,p+=v),c=c instanceof ShadowRoot?c.host:c.parentNode}while(!s&&c!==document.body||s&&(t.contains(c)||t===c));return d&&(o&&1>Math.abs(f)||!o&&a>f)?u=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(u=!0),u},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},W=function(e){return[e.deltaX,e.deltaY]},_=function(e){return e&&"current"in e?e.current:e},B=0,z=[];let V=(p.useMedium(function(e){var t=l.useRef([]),r=l.useRef([0,0]),n=l.useRef(),o=l.useState(B++)[0],i=l.useState(y)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(_),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=F(e),l=r.current,c="deltaX"in e?e.deltaX:l[0]-i[0],s="deltaY"in e?e.deltaY:l[1]-i[1],u=e.target,d=Math.abs(c)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=I(d,u);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=I(d,u)),!f)return!1;if(!n.current&&"changedTouches"in e&&(c||s)&&(n.current=o),!o)return!0;var p=n.current||o;return H(p,t,e,"h"===p?c:s,!0)},[]),s=l.useCallback(function(e){if(z.length&&z[z.length-1]===i){var r="deltaY"in e?W(e):F(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(a.current.shards||[]).map(_).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),u=l.useCallback(function(e,r,n,o){var i={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){r.current=F(e),n.current=void 0},[]),f=l.useCallback(function(t){u(t.type,W(t),t.target,c(t,e.lockRef.current))},[]),p=l.useCallback(function(t){u(t.type,F(t),t.target,c(t,e.lockRef.current))},[]);l.useEffect(function(){return z.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,N),document.addEventListener("touchmove",s,N),document.addEventListener("touchstart",d,N),function(){z=z.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,N),document.removeEventListener("touchmove",s,N),document.removeEventListener("touchstart",d,N)}},[]);var h=e.removeScrollBar,v=e.inert;return l.createElement(l.Fragment,null,v?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(P,{gapMode:e.gapMode}):null)}),m);var G=l.forwardRef(function(e,t){return l.createElement(v,o({},e,{ref:t,sideCar:V}))});G.classNames=v.classNames;let K=G},58450:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},61662:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},63376:(e,t,r)=>{r.d(t,{Eq:()=>u});var n=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,l={},a=0,c=function(e){return e&&(e.host||c(e.parentNode))},s=function(e,t,r,n){var s=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=c(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[r]||(l[r]=new WeakMap);var u=l[r],d=[],f=new Set,p=new Set(s),h=function(e){!(!e||f.has(e))&&(f.add(e),h(e.parentNode))};s.forEach(h);var v=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))v(e);else try{var t=e.getAttribute(n),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,c=(u.get(e)||0)+1;o.set(e,a),u.set(e,c),d.push(e),1===a&&l&&i.set(e,!0),1===c&&e.setAttribute(r,"true"),l||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),f.clear(),a++,function(){d.forEach(function(e){var t=o.get(e)-1,l=u.get(e)-1;o.set(e,t),u.set(e,l),t||(i.has(e)||e.removeAttribute(n),i.delete(e)),l||e.removeAttribute(r)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},u=function(e,t,r){void 0===r&&(r="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||n(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),s(o,i,r,"aria-hidden")):function(){return null}}},67969:(e,t,r)=>{r.d(t,{q:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},68123:(e,t,r)=>{r.d(t,{LM:()=>Y,OK:()=>$,VM:()=>E,bL:()=>U,lr:()=>I});var n=r(43210),o=r(14163),i=r(46059),l=r(11273),a=r(98599),c=r(13495),s=r(43),u=r(66156),d=r(67969),f=r(70569),p=r(60687),h="ScrollArea",[v,m]=(0,l.A)(h),[g,w]=v(h),y=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:i="hover",dir:l,scrollHideDelay:c=600,...u}=e,[d,f]=n.useState(null),[h,v]=n.useState(null),[m,w]=n.useState(null),[y,b]=n.useState(null),[x,S]=n.useState(null),[E,C]=n.useState(0),[R,T]=n.useState(0),[A,L]=n.useState(!1),[P,j]=n.useState(!1),k=(0,a.s)(t,e=>f(e)),N=(0,s.jH)(l);return(0,p.jsx)(g,{scope:r,type:i,dir:N,scrollHideDelay:c,scrollArea:d,viewport:h,onViewportChange:v,content:m,onContentChange:w,scrollbarX:y,onScrollbarXChange:b,scrollbarXEnabled:A,onScrollbarXEnabledChange:L,scrollbarY:x,onScrollbarYChange:S,scrollbarYEnabled:P,onScrollbarYEnabledChange:j,onCornerWidthChange:C,onCornerHeightChange:T,children:(0,p.jsx)(o.sG.div,{dir:N,...u,ref:k,style:{position:"relative","--radix-scroll-area-corner-width":E+"px","--radix-scroll-area-corner-height":R+"px",...e.style}})})});y.displayName=h;var b="ScrollAreaViewport",x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:i,nonce:l,...c}=e,s=w(b,r),u=n.useRef(null),d=(0,a.s)(t,u,s.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,p.jsx)(o.sG.div,{"data-radix-scroll-area-viewport":"",...c,ref:d,style:{overflowX:s.scrollbarXEnabled?"scroll":"hidden",overflowY:s.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:s.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});x.displayName=b;var S="ScrollAreaScrollbar",E=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=w(S,e.__scopeScrollArea),{onScrollbarXEnabledChange:l,onScrollbarYEnabledChange:a}=i,c="horizontal"===e.orientation;return n.useEffect(()=>(c?l(!0):a(!0),()=>{c?l(!1):a(!1)}),[c,l,a]),"hover"===i.type?(0,p.jsx)(C,{...o,ref:t,forceMount:r}):"scroll"===i.type?(0,p.jsx)(R,{...o,ref:t,forceMount:r}):"auto"===i.type?(0,p.jsx)(T,{...o,ref:t,forceMount:r}):"always"===i.type?(0,p.jsx)(A,{...o,ref:t}):null});E.displayName=S;var C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=w(S,e.__scopeScrollArea),[a,c]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),c(!0)},n=()=>{t=window.setTimeout(()=>c(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,p.jsx)(i.C,{present:r||a,children:(0,p.jsx)(T,{"data-state":a?"visible":"hidden",...o,ref:t})})}),R=n.forwardRef((e,t)=>{var r;let{forceMount:o,...l}=e,a=w(S,e.__scopeScrollArea),c="horizontal"===e.orientation,s=K(()=>d("SCROLL_END"),100),[u,d]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>r[e][t]??e,"hidden"));return n.useEffect(()=>{if("idle"===u){let e=window.setTimeout(()=>d("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(e)}},[u,a.scrollHideDelay,d]),n.useEffect(()=>{let e=a.viewport,t=c?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(d("SCROLL"),s()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[a.viewport,c,d,s]),(0,p.jsx)(i.C,{present:o||"hidden"!==u,children:(0,p.jsx)(A,{"data-state":"hidden"===u?"hidden":"visible",...l,ref:t,onPointerEnter:(0,f.m)(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:(0,f.m)(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),T=n.forwardRef((e,t)=>{let r=w(S,e.__scopeScrollArea),{forceMount:o,...l}=e,[a,c]=n.useState(!1),s="horizontal"===e.orientation,u=K(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;c(s?e:t)}},10);return X(r.viewport,u),X(r.content,u),(0,p.jsx)(i.C,{present:o||a,children:(0,p.jsx)(A,{"data-state":a?"visible":"hidden",...l,ref:t})})}),A=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,i=w(S,e.__scopeScrollArea),l=n.useRef(null),a=n.useRef(0),[c,s]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=_(c.viewport,c.content),d={...o,sizes:c,onSizesChange:s,hasThumb:!!(u>0&&u<1),onThumbChange:e=>l.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function f(e,t){return function(e,t,r,n="ltr"){let o=B(r),i=t||o/2,l=r.scrollbar.paddingStart+i,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-i),c=r.content-r.viewport;return V([l,a],"ltr"===n?[0,c]:[-1*c,0])(e)}(e,a.current,c,t)}return"horizontal"===r?(0,p.jsx)(L,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&l.current){let e=z(i.viewport.scrollLeft,c,i.dir);l.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=f(e,i.dir))}}):"vertical"===r?(0,p.jsx)(P,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&l.current){let e=z(i.viewport.scrollTop,c);l.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=f(e))}}):null}),L=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,l=w(S,e.__scopeScrollArea),[c,s]=n.useState(),u=n.useRef(null),d=(0,a.s)(t,u,l.onScrollbarXChange);return n.useEffect(()=>{u.current&&s(getComputedStyle(u.current))},[u]),(0,p.jsx)(N,{"data-orientation":"horizontal",...i,ref:d,sizes:r,style:{bottom:0,left:"rtl"===l.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===l.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":B(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{u.current&&l.viewport&&c&&o({content:l.viewport.scrollWidth,viewport:l.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:W(c.paddingLeft),paddingEnd:W(c.paddingRight)}})}})}),P=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,l=w(S,e.__scopeScrollArea),[c,s]=n.useState(),u=n.useRef(null),d=(0,a.s)(t,u,l.onScrollbarYChange);return n.useEffect(()=>{u.current&&s(getComputedStyle(u.current))},[u]),(0,p.jsx)(N,{"data-orientation":"vertical",...i,ref:d,sizes:r,style:{top:0,right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":B(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{u.current&&l.viewport&&c&&o({content:l.viewport.scrollHeight,viewport:l.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:W(c.paddingTop),paddingEnd:W(c.paddingBottom)}})}})}),[j,k]=v(S),N=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:i,hasThumb:l,onThumbChange:s,onThumbPointerUp:u,onThumbPointerDown:d,onThumbPositionChange:h,onDragScroll:v,onWheelScroll:m,onResize:g,...y}=e,b=w(S,r),[x,E]=n.useState(null),C=(0,a.s)(t,e=>E(e)),R=n.useRef(null),T=n.useRef(""),A=b.viewport,L=i.content-i.viewport,P=(0,c.c)(m),k=(0,c.c)(h),N=K(g,10);function D(e){R.current&&v({x:e.clientX-R.current.left,y:e.clientY-R.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;x?.contains(t)&&P(e,L)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[A,x,L,P]),n.useEffect(k,[i,k]),X(x,N),X(b.content,N),(0,p.jsx)(j,{scope:r,scrollbar:x,hasThumb:l,onThumbChange:(0,c.c)(s),onThumbPointerUp:(0,c.c)(u),onThumbPositionChange:k,onThumbPointerDown:(0,c.c)(d),children:(0,p.jsx)(o.sG.div,{...y,ref:C,style:{position:"absolute",...y.style},onPointerDown:(0,f.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),R.current=x.getBoundingClientRect(),T.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),D(e))}),onPointerMove:(0,f.m)(e.onPointerMove,D),onPointerUp:(0,f.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=T.current,b.viewport&&(b.viewport.style.scrollBehavior=""),R.current=null})})})}),D="ScrollAreaThumb",I=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=k(D,e.__scopeScrollArea);return(0,p.jsx)(i.C,{present:r||o.hasThumb,children:(0,p.jsx)(M,{ref:t,...n})})}),M=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:i,...l}=e,c=w(D,r),s=k(D,r),{onThumbPositionChange:u}=s,d=(0,a.s)(t,e=>s.onThumbChange(e)),h=n.useRef(void 0),v=K(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=c.viewport;if(e){let t=()=>{v(),h.current||(h.current=G(e,u),u())};return u(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[c.viewport,v,u]),(0,p.jsx)(o.sG.div,{"data-state":s.hasThumb?"visible":"hidden",...l,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,f.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;s.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,f.m)(e.onPointerUp,s.onThumbPointerUp)})});I.displayName=D;var O="ScrollAreaCorner",H=n.forwardRef((e,t)=>{let r=w(O,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,p.jsx)(F,{...e,ref:t}):null});H.displayName=O;var F=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...i}=e,l=w(O,r),[a,c]=n.useState(0),[s,u]=n.useState(0),d=!!(a&&s);return X(l.scrollbarX,()=>{let e=l.scrollbarX?.offsetHeight||0;l.onCornerHeightChange(e),u(e)}),X(l.scrollbarY,()=>{let e=l.scrollbarY?.offsetWidth||0;l.onCornerWidthChange(e),c(e)}),d?(0,p.jsx)(o.sG.div,{...i,ref:t,style:{width:a,height:s,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function W(e){return e?parseInt(e,10):0}function _(e,t){let r=e/t;return isNaN(r)?0:r}function B(e){let t=_(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function z(e,t,r="ltr"){let n=B(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,l=t.content-t.viewport,a=(0,d.q)(e,"ltr"===r?[0,l]:[-1*l,0]);return V([0,l],[0,i-n])(a)}function V(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var G=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function o(){let i={left:e.scrollLeft,top:e.scrollTop},l=r.left!==i.left,a=r.top!==i.top;(l||a)&&t(),r=i,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function K(e,t){let r=(0,c.c)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function X(e,t){let r=(0,c.c)(t);(0,u.N)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var U=y,Y=x,$=H},83721:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(43210);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},89743:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},90270:(e,t,r)=>{r.d(t,{bL:()=>S,zi:()=>E});var n=r(43210),o=r(70569),i=r(98599),l=r(11273),a=r(65551),c=r(83721),s=r(18853),u=r(14163),d=r(60687),f="Switch",[p,h]=(0,l.A)(f),[v,m]=p(f),g=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:l,checked:c,defaultChecked:s,required:f,disabled:p,value:h="on",onCheckedChange:m,form:g,...w}=e,[y,S]=n.useState(null),E=(0,i.s)(t,e=>S(e)),C=n.useRef(!1),R=!y||g||!!y.closest("form"),[T=!1,A]=(0,a.i)({prop:c,defaultProp:s,onChange:m});return(0,d.jsxs)(v,{scope:r,checked:T,disabled:p,children:[(0,d.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":T,"aria-required":f,"data-state":x(T),"data-disabled":p?"":void 0,disabled:p,value:h,...w,ref:E,onClick:(0,o.m)(e.onClick,e=>{A(e=>!e),R&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),R&&(0,d.jsx)(b,{control:y,bubbles:!C.current,name:l,value:h,checked:T,required:f,disabled:p,form:g,style:{transform:"translateX(-100%)"}})]})});g.displayName=f;var w="SwitchThumb",y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=m(w,r);return(0,d.jsx)(u.sG.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});y.displayName=w;var b=e=>{let{control:t,checked:r,bubbles:o=!0,...i}=e,l=n.useRef(null),a=(0,c.Z)(r),u=(0,s.X)(t);return n.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(a!==r&&t){let n=new Event("click",{bubbles:o});t.call(e,r),e.dispatchEvent(n)}},[a,r,o]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...i,tabIndex:-1,ref:l,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function x(e){return e?"checked":"unchecked"}var S=g,E=y},96963:(e,t,r)=>{r.d(t,{B:()=>c});var n,o=r(43210),i=r(66156),l=(n||(n=r.t(o,2)))["useId".toString()]||(()=>void 0),a=0;function c(e){let[t,r]=o.useState(l());return(0,i.N)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}}};