/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer, dev }) => {
    // Ignore handlebars warnings
    config.module.rules.push({
      test: /\.handlebars$/,
      use: 'null-loader'
    });

    // Ignore specific source map warnings
    config.ignoreWarnings = [
      { module: /node_modules\/handlebars/ },
      { file: /node_modules\/handlebars/ }
    ];

    // Optimize chunk loading and reduce bundle size
    if (!isServer) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 10,
              reuseExistingChunk: true,
            },
            common: {
              name: 'common',
              minChunks: 2,
              chunks: 'all',
              priority: 5,
              reuseExistingChunk: true,
            },
          },
        },
      };
    }

    // Increase timeout for chunk loading in development
    if (dev) {
      config.devtool = 'eval-source-map';
    }

    return config;
  },

  // Performance optimizations
  reactStrictMode: true,
  swcMinify: true,

  // Experimental features for better performance
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },

  // Improve loading performance
  poweredByHeader: false,
  compress: true,

  // Handle static file serving better
  assetPrefix: process.env.NODE_ENV === 'production' ? undefined : '',
};

export default nextConfig;
