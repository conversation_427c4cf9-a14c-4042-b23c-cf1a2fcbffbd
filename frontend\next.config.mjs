/** @type {import('next').NextConfig} */
const nextConfig = {
  // Minimal configuration to prevent chunk loading issues
  webpack: (config, { isServer, dev }) => {
    // Ignore handlebars warnings
    config.module.rules.push({
      test: /\.handlebars$/,
      use: 'null-loader'
    });

    // Ignore specific source map warnings
    config.ignoreWarnings = [
      { module: /node_modules\/handlebars/ },
      { file: /node_modules\/handlebars/ }
    ];

    // Disable complex chunk splitting in development
    if (!isServer && dev) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          default: false,
          vendors: false,
        },
      };
    }

    return config;
  },

  // Minimal settings to prevent issues
  reactStrictMode: false,
  poweredByHeader: false,

  // Disable features that can cause chunk loading issues
  experimental: {
    // Disable experimental features in development
  },
};

export default nextConfig;
