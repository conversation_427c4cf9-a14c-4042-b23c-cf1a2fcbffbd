/** @type {import('next').NextConfig} */
const nextConfig = {
  // Disable static optimization to prevent chunk loading issues
  output: 'standalone',

  webpack: (config, { isServer, dev }) => {
    // Ignore handlebars warnings
    config.module.rules.push({
      test: /\.handlebars$/,
      use: 'null-loader'
    });

    // Ignore specific source map warnings
    config.ignoreWarnings = [
      { module: /node_modules\/handlebars/ },
      { file: /node_modules\/handlebars/ }
    ];

    // Simplified chunk configuration to prevent loading issues
    if (!isServer) {
      // Disable aggressive chunk splitting that can cause loading issues
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          maxSize: 244000,
          cacheGroups: {
            default: {
              minChunks: 2,
              priority: -20,
              reuseExistingChunk: true,
            },
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              priority: -10,
              reuseExistingChunk: true,
            },
          },
        },
      };
    }

    // Better source maps for development
    if (dev) {
      config.devtool = 'cheap-module-source-map';
    }

    return config;
  },

  // Basic optimizations only
  reactStrictMode: false, // Disable to prevent double rendering issues

  // Remove experimental features that might cause issues
  // experimental: {
  //   optimizeCss: true,
  //   optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  // },

  // Basic performance settings
  poweredByHeader: false,
  compress: true,

  // Ensure proper asset serving
  trailingSlash: false,

  // Add headers to prevent caching issues
  async headers() {
    return [
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
