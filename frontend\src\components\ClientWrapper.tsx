'use client';

import React, { ReactNode } from 'react';
import { AuthProvider } from '@/contexts/AuthContext';
import { TradingProvider } from '@/contexts/TradingContext';
import { AIProvider } from '@/contexts/AIContext';
import { Toaster } from "@/components/ui/toaster";
import ErrorBoundary from '@/components/ErrorBoundary';

interface ClientWrapperProps {
  children: ReactNode;
}

export default function ClientWrapper({ children }: ClientWrapperProps) {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <TradingProvider>
          <AIProvider>
            {children}
            <Toaster />
          </AIProvider>
        </TradingProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}
