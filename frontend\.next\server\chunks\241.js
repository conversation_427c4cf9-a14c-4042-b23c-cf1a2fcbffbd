exports.id=241,exports.ids=[241],exports.modules={8417:(e,r,s)=>{"use strict";s.d(r,{A:()=>n});var t=s(60687);s(43210);var a=s(60240);let n=({className:e,useFullName:r=!0})=>(0,t.jsxs)("div",{className:`flex items-center text-2xl font-bold text-primary ${e}`,children:[(0,t.jsx)(a.A,{className:"mr-2 h-7 w-7"}),(0,t.jsxs)("span",{children:["Pluto",r?" Trading Bot":""]})]})},15079:(e,r,s)=>{"use strict";s.d(r,{bq:()=>m,eb:()=>h,gC:()=>f,l6:()=>c,yv:()=>u});var t=s(60687),a=s(43210),n=s(28850),l=s(61662),i=s(89743),o=s(58450),d=s(4780);let c=n.bL;n.YJ;let u=n.WT,m=a.forwardRef(({className:e,children:r,...s},a)=>(0,t.jsxs)(n.l9,{ref:a,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[r,(0,t.jsx)(n.In,{asChild:!0,children:(0,t.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=n.l9.displayName;let p=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(n.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})}));p.displayName=n.PP.displayName;let x=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(n.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,t.jsx)(l.A,{className:"h-4 w-4"})}));x.displayName=n.wn.displayName;let f=a.forwardRef(({className:e,children:r,position:s="popper",...a},l)=>(0,t.jsx)(n.ZL,{children:(0,t.jsxs)(n.UC,{ref:l,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...a,children:[(0,t.jsx)(p,{}),(0,t.jsx)(n.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,t.jsx)(x,{})]})}));f.displayName=n.UC.displayName,a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(n.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...r})).displayName=n.JU.displayName;let h=a.forwardRef(({className:e,children:r,...s},a)=>(0,t.jsxs)(n.q7,{ref:a,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(n.VF,{children:(0,t.jsx)(o.A,{className:"h-4 w-4"})})}),(0,t.jsx)(n.p4,{children:r})]}));h.displayName=n.q7.displayName,a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(n.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...r})).displayName=n.wv.displayName},20674:(e,r,s)=>{Promise.resolve().then(s.bind(s,63144))},29523:(e,r,s)=>{"use strict";s.d(r,{$:()=>d});var t=s(60687),a=s(43210),n=s(8730),l=s(24224),i=s(4780);let o=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:r,size:s,asChild:a=!1,...l},d)=>{let c=a?n.DX:"button";return(0,t.jsx)(c,{className:(0,i.cn)(o({variant:r,size:s,className:e})),ref:d,...l})});d.displayName="Button"},35950:(e,r,s)=>{"use strict";s.d(r,{w:()=>i});var t=s(60687),a=s(43210),n=s(62369),l=s(4780);let i=a.forwardRef(({className:e,orientation:r="horizontal",decorative:s=!0,...a},i)=>(0,t.jsx)(n.b,{ref:i,decorative:s,orientation:r,className:(0,l.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",e),...a}));i.displayName=n.b.displayName},37079:(e,r,s)=>{"use strict";s.d(r,{A:()=>c});var t=s(60687);s(43210);var a=s(85763),n=s(16189),l=s(29272),i=s(44610),o=s(3341);let d=[{value:"orders",label:"Orders",href:"/dashboard",icon:(0,t.jsx)(l.A,{})},{value:"history",label:"History",href:"/dashboard/history",icon:(0,t.jsx)(i.A,{})},{value:"analytics",label:"Analytics",href:"/dashboard/analytics",icon:(0,t.jsx)(o.A,{})}];function c(){let e=(0,n.useRouter)(),r=(0,n.usePathname)(),s="orders";return"/dashboard/history"===r?s="history":"/dashboard/analytics"===r&&(s="analytics"),(0,t.jsx)(a.tU,{value:s,onValueChange:r=>{let s=d.find(e=>e.value===r);s&&e.push(s.href)},className:"w-full mb-6",children:(0,t.jsx)(a.j7,{className:"grid w-full grid-cols-3 bg-card border-2 border-border",children:d.map(e=>(0,t.jsx)(a.Xi,{value:e.value,className:"text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-bold",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,e.label]})},e.value))})})}},38826:(e,r,s)=>{Promise.resolve().then(s.bind(s,62189))},42692:(e,r,s)=>{"use strict";s.d(r,{$:()=>o,F:()=>i});var t=s(60687),a=s(43210),n=s(68123),l=s(4780);let i=a.forwardRef(({className:e,children:r,...s},a)=>(0,t.jsxs)(n.bL,{ref:a,className:(0,l.cn)("relative overflow-hidden",e),...s,children:[(0,t.jsx)(n.LM,{className:"h-full w-full rounded-[inherit]",children:r}),(0,t.jsx)(o,{}),(0,t.jsx)(n.OK,{})]}));i.displayName=n.bL.displayName;let o=a.forwardRef(({className:e,orientation:r="vertical",...s},a)=>(0,t.jsx)(n.VM,{ref:a,orientation:r,className:(0,l.cn)("flex touch-none select-none transition-colors","vertical"===r&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...s,children:(0,t.jsx)(n.lr,{className:"relative flex-1 rounded-full bg-border"})}));o.displayName=n.VM.displayName},44493:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>i});var t=s(60687),a=s(43210),n=s(4780);let l=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",e),...r}));l.displayName="Card";let i=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-4 md:p-6",e),...r}));i.displayName="CardHeader";let o=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("h3",{ref:s,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("p-4 md:p-6 pt-0",e),...r}));c.displayName="CardContent",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-4 md:p-6 pt-0",e),...r})).displayName="CardFooter"},54987:(e,r,s)=>{"use strict";s.d(r,{d:()=>i});var t=s(60687),a=s(43210),n=s(90270),l=s(4780);let i=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(n.bL,{className:(0,l.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...r,ref:s,children:(0,t.jsx)(n.zi,{className:(0,l.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));i.displayName=n.bL.displayName},62189:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>ed});var t=s(60687),a=s(43210),n=s(85814),l=s.n(n),i=s(16189),o=s(8417),d=s(29523),c=s(63213),u=s(24026),m=s(33886),p=s(40196),x=s(49497),f=s(78895),h=s(67857),b=s(8158),g=s(4780);function j({className:e,onConnectionChange:r}){let[s,n]=(0,a.useState)(!0),[l,i]=(0,a.useState)(new Date);return(0,t.jsxs)("div",{className:(0,g.cn)("flex items-center space-x-2 px-3 py-1 rounded-md text-sm font-medium transition-colors",s?"bg-green-100 text-green-800 border border-green-200":"bg-red-100 text-red-800 border border-red-200",e),children:[s?(0,t.jsx)(h.A,{className:"h-4 w-4"}):(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:s?"Online":"Offline"}),(0,t.jsx)("span",{className:"text-xs opacity-70",children:l.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})}function v(){let e;let{logout:r}=(0,c.A)();(0,i.useRouter)();let s=(0,i.usePathname)();try{e=(0,f.U)()}catch(r){console.warn("Trading context not available in header:",r),e=null}let a=[{href:"/dashboard",label:"Home",icon:(0,t.jsx)(u.A,{})},{href:"/admin",label:"Admin Panel",icon:(0,t.jsx)(m.A,{})}];return(0,t.jsxs)("header",{className:"sticky top-0 z-50 flex items-center justify-between h-16 px-4 md:px-6 bg-card border-b-2 border-border",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(o.A,{useFullName:!1}),(0,t.jsx)(j,{className:"hidden sm:flex",onConnectionChange:r=>{e&&e.setConnectionStatus&&e.setConnectionStatus(r?"online":"offline")}})]}),(0,t.jsxs)("nav",{className:"flex items-center gap-2 sm:gap-3",children:[a.map(e=>(0,t.jsx)(d.$,{variant:s===e.href?"default":"ghost",size:"sm",asChild:!0,className:`${s===e.href?"btn-neo":"hover:bg-accent/50"}`,children:(0,t.jsxs)(l(),{href:e.href,className:"flex items-center gap-2",children:[e.icon,(0,t.jsx)("span",{className:"hidden sm:inline",children:e.label})]})},e.label)),(0,t.jsxs)(d.$,{variant:"ghost",size:"sm",onClick:()=>{window.open("/dashboard","_blank")},className:"hover:bg-accent/50 flex items-center gap-2",title:"Open a new independent trading session in a new tab",children:[(0,t.jsx)(p.A,{}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"New Session"})]}),(0,t.jsxs)(d.$,{variant:"ghost",size:"sm",onClick:()=>{r()},className:"hover:bg-destructive/80 hover:text-destructive-foreground flex items-center gap-2",children:[(0,t.jsx)(x.A,{}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Logout"})]})]})]})}var y=s(8641),N=s(89667),S=s(80013),w=s(40211),C=s(58450);let E=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(w.bL,{ref:s,className:(0,g.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...r,children:(0,t.jsx)(w.C1,{className:(0,g.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(C.A,{className:"h-4 w-4"})})}));E.displayName=w.bL.displayName;var A=s(15079),O=s(44493),T=s(42692),k=s(35950),R=s(26134),U=s(78726);let P=R.bL;R.l9;let D=R.ZL,F=R.bm,B=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(R.hJ,{ref:s,className:(0,g.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...r}));B.displayName=R.hJ.displayName;let L=a.forwardRef(({className:e,children:r,...s},a)=>(0,t.jsxs)(D,{children:[(0,t.jsx)(B,{}),(0,t.jsxs)(R.UC,{ref:a,className:(0,g.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[r,(0,t.jsxs)(R.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(U.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));L.displayName=R.UC.displayName;let M=({className:e,...r})=>(0,t.jsx)("div",{className:(0,g.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...r});M.displayName="DialogHeader";let I=({className:e,...r})=>(0,t.jsx)("div",{className:(0,g.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...r});I.displayName="DialogFooter";let $=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(R.hE,{ref:s,className:(0,g.cn)("text-lg font-semibold leading-none tracking-tight",e),...r}));$.displayName=R.hE.displayName;let _=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(R.VY,{ref:s,className:(0,g.cn)("text-sm text-muted-foreground",e),...r}));_.displayName=R.VY.displayName;let G=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("textarea",{className:(0,g.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...r}));G.displayName="Textarea";var V=s(85763),J=s(29867);function z({isOpen:e,onClose:r,onSetTargetPrices:s}){let n;let[l,i]=(0,a.useState)("manual"),[o,c]=(0,a.useState)(""),{toast:u}=(0,J.dj)();try{n=(0,f.U)()}catch(e){console.warn("Trading context not available:",e),n=null}let[m,p]=(0,a.useState)("8"),[x,h]=(0,a.useState)("5"),[b,g]=(0,a.useState)("even"),j=n?.currentMarketPrice||n?.state?.currentMarketPrice||1e5,v=n?.config?.slippagePercent||n?.state?.config?.slippagePercent||.2,y=()=>{let e=parseInt(m),r=parseFloat(x);if(!e||e<2||e>20||!r||r<=0)return[];let s=[],t=j*(1-r/100),a=j*(1+r/100);if("even"===b)for(let r=0;r<e;r++){let n=t+r/(e-1)*(a-t);s.push(Math.round(n))}else if("fibonacci"===b){let r=[0,.236,.382,.5,.618,.764,.854,.927,1];for(let n=0;n<e;n++){let l=t+(a-t)*(r[Math.min(n,r.length-1)]||n/(e-1));s.push(Math.round(l))}}else if("exponential"===b)for(let r=0;r<e;r++){let n=t+(a-t)*Math.pow(r/(e-1),1.5);s.push(Math.round(n))}let n=3*v/100*j,l=s.sort((e,r)=>e-r),i=[];for(let e=0;e<l.length;e++){let r=l[e];if(i.length>0){let e=i[i.length-1];r-e<n&&(r=e+n)}i.push(Math.round(r))}return i},N=()=>{let e=o.split("\n").filter(e=>""!==e.trim()).map(e=>parseFloat(e.trim())).filter(e=>!isNaN(e)&&e>0).sort((e,r)=>e-r);if(e.length<2)return{hasOverlap:!1,message:""};let r=v/100*j;for(let s=0;s<e.length-1;s++)if(e[s]+r>=e[s+1]-r){let t=2*r,a=e[s+1]-e[s];return{hasOverlap:!0,message:`Overlap detected between ${e[s]} and ${e[s+1]}. Minimum gap needed: ${t.toFixed(0)}, actual gap: ${a.toFixed(0)}`}}return{hasOverlap:!1,message:"No slippage zone overlaps detected ✓"}},w=N();return(0,t.jsx)(P,{open:e,onOpenChange:r,children:(0,t.jsxs)(L,{className:"sm:max-w-2xl bg-card border-2 border-border",children:[(0,t.jsxs)(M,{children:[(0,t.jsx)($,{className:"text-primary",children:"Set Target Prices"}),(0,t.jsx)(_,{children:"Set target prices manually or generate them automatically with optimal spacing to avoid slippage zone overlaps."})]}),(0,t.jsxs)(V.tU,{value:l,onValueChange:i,className:"w-full",children:[(0,t.jsxs)(V.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsx)(V.Xi,{value:"manual",children:"Manual Entry"}),(0,t.jsx)(V.Xi,{value:"automatic",children:"Automatic Generation"})]}),(0,t.jsx)(V.av,{value:"manual",className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(S.J,{htmlFor:"target-prices-input",className:"text-left",children:"Target Prices"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Paste target prices from Excel or enter manually, one price per line. Invalid entries will be ignored."}),(0,t.jsx)(G,{id:"target-prices-input",value:o,onChange:e=>c(e.target.value),placeholder:"50000 50500 49800",className:"min-h-[200px] bg-input border-2 border-border focus:border-primary font-mono"}),w.message&&(0,t.jsx)("p",{className:`text-sm ${w.hasOverlap?"text-red-500":"text-green-500"}`,children:w.message})]})}),(0,t.jsxs)(V.av,{value:"automatic",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"targetCount",children:"Number of Targets"}),(0,t.jsxs)(A.l6,{value:m,onValueChange:p,children:[(0,t.jsx)(A.bq,{children:(0,t.jsx)(A.yv,{})}),(0,t.jsx)(A.gC,{children:[4,6,8,10,12,15,20].map(e=>(0,t.jsxs)(A.eb,{value:e.toString(),children:[e," targets"]},e))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"priceRange",children:"Price Range (%)"}),(0,t.jsxs)(A.l6,{value:x,onValueChange:h,children:[(0,t.jsx)(A.bq,{children:(0,t.jsx)(A.yv,{})}),(0,t.jsx)(A.gC,{children:[2,2.5,3,3.5,4,4.5,5,6,7,8,10].map(e=>(0,t.jsxs)(A.eb,{value:e.toString(),children:["\xb1",e,"%"]},e))})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"distribution",children:"Distribution Pattern"}),(0,t.jsxs)(A.l6,{value:b,onValueChange:g,children:[(0,t.jsx)(A.bq,{children:(0,t.jsx)(A.yv,{})}),(0,t.jsxs)(A.gC,{children:[(0,t.jsx)(A.eb,{value:"even",children:"Even Distribution"}),(0,t.jsx)(A.eb,{value:"fibonacci",children:"Fibonacci (More near current price)"}),(0,t.jsx)(A.eb,{value:"exponential",children:"Exponential (Wider spread)"})]})]})]}),(0,t.jsx)("div",{className:"bg-muted p-3 rounded-md",children:(0,t.jsxs)("p",{className:"text-sm",children:[(0,t.jsx)("strong",{children:"Current Market Price:"})," $",j.toLocaleString(),(0,t.jsx)("br",{}),(0,t.jsx)("strong",{children:"Slippage:"})," \xb1",v,"% ($",(j*v/100).toFixed(0),")",(0,t.jsx)("br",{}),(0,t.jsx)("strong",{children:"Range:"})," $",(j*(1-parseFloat(x)/100)).toLocaleString()," - $",(j*(1+parseFloat(x)/100)).toLocaleString()]})}),(0,t.jsxs)(d.$,{onClick:()=>{c(y().join("\n"))},className:"w-full btn-neo",children:["Generate ",m," Target Prices"]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(S.J,{children:"Generated Prices (Preview)"}),(0,t.jsx)(G,{value:o,onChange:e=>c(e.target.value),className:"min-h-[150px] bg-input border-2 border-border focus:border-primary font-mono",placeholder:"Click 'Generate' to create automatic target prices..."}),w.message&&(0,t.jsx)("p",{className:`text-sm ${w.hasOverlap?"text-red-500":"text-green-500"}`,children:w.message})]})]})]}),(0,t.jsxs)(I,{children:[(0,t.jsx)(F,{asChild:!0,children:(0,t.jsx)(d.$,{type:"button",variant:"outline",className:"btn-outline-neo",children:"Cancel"})}),(0,t.jsx)(d.$,{type:"button",onClick:()=>{let e=o.split("\n").map(e=>e.trim()).filter(e=>""!==e),t=e.map(e=>parseFloat(e)).filter(e=>!isNaN(e)&&e>0);if(0===t.length&&e.length>0){u({title:"Invalid Input",description:"No valid prices found. Please enter numbers, one per line.",variant:"destructive"});return}let a=N();if(a.hasOverlap){u({title:"Slippage Zone Overlap",description:a.message,variant:"destructive"});return}s(t),u({title:"Target Prices Updated",description:`${t.length} target prices have been set.`}),c(""),r()},disabled:w.hasOverlap,className:"btn-neo",children:"Save Prices"})]})]})})}var Z=s(54987),W=s(55280),q=s(1833);let H="custom_sound_execution",X="custom_sound_error";function Y({isOpen:e,onClose:r}){let{appSettings:s,dispatch:n}=(0,f.U)(),[l,i]=(0,a.useState)(s),o=(0,a.useRef)(null),{toast:c}=(0,J.dj)(),u=(e,r)=>{i(s=>({...s,[e]:r}))},m=(e,r)=>{i(s=>({...s,[e]:r}))},p=(e,r)=>{let s=e.target.files?.[0];if(s){let e=new FileReader;e.onload=e=>{let t=e.target?.result;"orderExecution"===r?i(e=>({...e,customSoundOrderExecutionDataUri:t,soundOrderExecution:H})):i(e=>({...e,customSoundErrorDataUri:t,soundError:X})),c({title:"File Uploaded",description:`${s.name} ready to be used.`})},e.readAsDataURL(s)}},x=e=>{let r=l[e];"soundOrderExecution"===e&&l.soundOrderExecution===H?r=l.customSoundOrderExecutionDataUri:"soundError"===e&&l.soundError===X&&(r=l.customSoundErrorDataUri),o.current&&r?(o.current.src=r,o.current.currentTime=0,o.current.play().then(()=>{setTimeout(()=>{o.current&&(o.current.pause(),o.current.currentTime=0)},2e3)}).catch(e=>{console.error("Error playing sound:",e),c({title:"Sound Error",description:"Could not play test sound. Ensure file is valid or browser permissions are set.",variant:"destructive"})})):c({title:"No Sound",description:"No sound selected or custom sound not uploaded.",variant:"default"})};return(0,t.jsx)(P,{open:e,onOpenChange:r,children:(0,t.jsxs)(L,{className:"sm:max-w-md bg-card border-2 border-border",children:[(0,t.jsxs)(M,{children:[(0,t.jsx)($,{className:"text-primary",children:"Alarm Configuration"}),(0,t.jsx)(_,{children:"Configure sound alerts for trading events. Ensure browser has audio permissions. Uploaded files are stored as data URIs."})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(S.J,{htmlFor:"soundAlertsEnabled",className:"text-base",children:"Enable Sound Alerts"}),(0,t.jsx)(Z.d,{id:"soundAlertsEnabled",checked:!!l.soundAlertsEnabled,onCheckedChange:e=>u("soundAlertsEnabled",e)})]}),l.soundAlertsEnabled&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"space-y-3 p-4 border-2 border-border rounded-sm",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(S.J,{htmlFor:"alertOnOrderExecution",children:"Alert on Successful Order Execution"}),(0,t.jsx)(Z.d,{id:"alertOnOrderExecution",checked:!!l.alertOnOrderExecution,onCheckedChange:e=>u("alertOnOrderExecution",e)})]}),l.alertOnOrderExecution&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(A.l6,{value:l.soundOrderExecution,onValueChange:e=>m("soundOrderExecution",e),children:[(0,t.jsx)(A.bq,{className:"flex-grow",children:(0,t.jsx)(A.yv,{placeholder:"Select sound"})}),(0,t.jsx)(A.gC,{children:[{value:H,label:"Custom (Upload)"}].map(e=>(0,t.jsx)(A.eb,{value:e.value,children:e.label},e.value))})]}),(0,t.jsx)(d.$,{variant:"outline",size:"icon",onClick:()=>x("soundOrderExecution"),className:"btn-outline-neo p-2",children:(0,t.jsx)(q.A,{className:"h-4 w-4"})})]}),l.soundOrderExecution===H&&(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"customSoundOrderFile",className:"text-xs",children:"Upload Execution Sound (.mp3, .wav, etc.)"}),(0,t.jsx)(N.p,{id:"customSoundOrderFile",type:"file",accept:"audio/*",onChange:e=>p(e,"orderExecution"),className:(0,g.cn)("text-xs mt-1","focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:border-primary")}),l.customSoundOrderExecutionDataUri&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground truncate mt-1",children:"Current: Custom sound uploaded"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3 p-4 border-2 border-border rounded-sm",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(S.J,{htmlFor:"alertOnError",children:"Alert on Errors/Failures"}),(0,t.jsx)(Z.d,{id:"alertOnError",checked:!!l.alertOnError,onCheckedChange:e=>u("alertOnError",e)})]}),l.alertOnError&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(A.l6,{value:l.soundError,onValueChange:e=>m("soundError",e),children:[(0,t.jsx)(A.bq,{className:"flex-grow",children:(0,t.jsx)(A.yv,{placeholder:"Select sound"})}),(0,t.jsx)(A.gC,{children:[{value:X,label:"Custom (Upload)"}].map(e=>(0,t.jsx)(A.eb,{value:e.value,children:e.label},e.value))})]}),(0,t.jsx)(d.$,{variant:"outline",size:"icon",onClick:()=>x("soundError"),className:"btn-outline-neo p-2",children:(0,t.jsx)(q.A,{className:"h-4 w-4"})})]}),l.soundError===X&&(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"customSoundErrorFile",className:"text-xs",children:"Upload Error Sound (.mp3, .wav, etc.)"}),(0,t.jsx)(N.p,{id:"customSoundErrorFile",type:"file",accept:"audio/*",onChange:e=>p(e,"error"),className:(0,g.cn)("text-xs mt-1","focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:border-primary")}),l.customSoundErrorDataUri&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground truncate mt-1",children:"Current: Custom sound uploaded"})]})]})]})]})]}),(0,t.jsxs)(I,{children:[(0,t.jsx)(F,{asChild:!0,children:(0,t.jsx)(d.$,{type:"button",variant:"outline",className:"btn-outline-neo",children:"Cancel"})}),(0,t.jsx)(d.$,{type:"button",onClick:()=>{let e={...l};l.soundOrderExecution===H&&l.customSoundOrderExecutionDataUri?e.soundOrderExecution=l.customSoundOrderExecutionDataUri:l.soundOrderExecution!==H||l.customSoundOrderExecutionDataUri||(e.soundOrderExecution=W.Oh.soundOrderExecution,c({title:"Notice",description:"No custom execution sound uploaded, default used.",variant:"default"})),l.soundError===X&&l.customSoundErrorDataUri?e.soundError=l.customSoundErrorDataUri:l.soundError!==X||l.customSoundErrorDataUri||(e.soundError=W.Oh.soundError,c({title:"Notice",description:"No custom error sound uploaded, default used.",variant:"default"})),delete e.customSoundOrderExecutionDataUri,delete e.customSoundErrorDataUri,n({type:"SET_APP_SETTINGS",payload:e}),c({title:"Alarm Settings Saved",description:"Your sound alert preferences have been updated."}),r()},className:"btn-neo",children:"Save Settings"})]})]})})}var K=s(72963);function Q({label:e,value:r,allowedCryptos:s,onValidCrypto:n,placeholder:l="Enter crypto symbol",description:i,className:o}){let[c,u]=(0,a.useState)(""),[m,p]=(0,a.useState)("idle"),[x,f]=(0,a.useState)(""),[h,b]=(0,a.useState)(""),j=()=>{let e=c.toUpperCase().trim();if(!e){p("invalid"),f("Please enter a crypto symbol");return}if(!s||!Array.isArray(s)){p("invalid"),f("No allowed cryptocurrencies configured");return}s.includes(e)?(p("valid"),f(""),b(e),n(e)):(p("invalid"),f("Not available"),b(""))};return(0,t.jsxs)("div",{className:(0,g.cn)("space-y-2",o),children:[(0,t.jsx)(S.J,{htmlFor:`crypto-input-${e}`,children:e}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)(N.p,{id:`crypto-input-${e}`,value:c,onChange:e=>{u(e.target.value),p("idle"),f(""),h&&b("")},onKeyPress:e=>{"Enter"===e.key&&j()},placeholder:l,className:(0,g.cn)("pr-8",(()=>{switch(m){case"valid":return"border-green-500";case"invalid":return"border-red-500";default:return""}})())}),"idle"!==m&&(0,t.jsx)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2",children:(()=>{switch(m){case"valid":return(0,t.jsx)(C.A,{className:"h-4 w-4 text-green-500"});case"invalid":return(0,t.jsx)(U.A,{className:"h-4 w-4 text-red-500"});default:return null}})()})]}),(0,t.jsx)(d.$,{onClick:j,variant:"outline",className:"btn-neo",disabled:!c.trim(),children:"Check"})]}),h&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsxs)("span",{className:"text-green-600 font-medium",children:["Selected: ",h]})]}),"invalid"===m&&x&&(0,t.jsxs)("div",{className:"flex items-start space-x-2 text-sm text-red-600",children:[(0,t.jsx)(K.A,{className:"h-4 w-4 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{children:x})]}),i&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:i}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[s&&Array.isArray(s)?s.length:0," cryptocurrencies available"]})]})}var ee=s(76485),er=s(11516),es=s(92375),et=s(25371),ea=s(1305);let en=["USDT","USDC","BTC"],el=["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"],ei=["USDC","DAI","TUSD","FDUSD","USDT","EUR"];function eo(){let e,r;try{e=(0,f.U)()}catch(e){return console.error("Trading context not available:",e),(0,t.jsx)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsx)("p",{className:"text-red-500",children:"Trading context not available. Please refresh the page."})})})}let{config:s,dispatch:n,botSystemStatus:l,appSettings:i,setTargetPrices:o}=e,c="Running"===l,u="WarmingUp"===l;try{r=(0,y.f)()}catch(e){console.warn("AI context not available:",e),r={suggestion:null,isLoading:!1,error:null,getTradingModeSuggestion:()=>Promise.resolve()}}let{suggestion:m,isLoading:p,error:x,getTradingModeSuggestion:h}=r,{toast:b}=(0,J.dj)(),[j,v]=(0,a.useState)(!1),[w,C]=(0,a.useState)(!1),[R,U]=(0,a.useState)("medium"),[P,D]=(0,a.useState)(""),[F,B]=(0,a.useState)(""),L=e=>{let r;let{name:s,value:t,type:a,checked:l}=e.target;if("checkbox"===a)r=l;else if("number"===a){if(""===t||null==t)r=0;else{let e=parseFloat(t);r=isNaN(e)?0:e}}else r=t;n({type:"SET_CONFIG",payload:{[s]:r}})},M=(e,r)=>{if(n({type:"SET_CONFIG",payload:{[e]:r}}),"crypto1"===e){let e=W.vA[r]||en||["USDT","USDC","BTC"];s.crypto2&&Array.isArray(e)&&e.includes(s.crypto2)||n({type:"SET_CONFIG",payload:{crypto2:e[0]||"USDT"}})}},I=(e,r)=>{let s=parseFloat(r);isNaN(s)&&(s=0),s<0&&(s=0),s>100&&(s=100),"incomeSplitCrypto1Percent"===e?n({type:"SET_CONFIG",payload:{incomeSplitCrypto1Percent:s,incomeSplitCrypto2Percent:100-s}}):n({type:"SET_CONFIG",payload:{incomeSplitCrypto2Percent:s,incomeSplitCrypto1Percent:100-s}})},$=async()=>{if(!R||!P||!F){b({title:"AI Suggestion Error",description:"Please fill all AI suggestion fields.",variant:"destructive"});return}await h({riskTolerance:R,preferredCryptocurrencies:P,investmentGoals:F})},_=W.hg||[];return"SimpleSpot"===s.tradingMode?W.vA[s.crypto1]:(W.hg||[]).filter(e=>e!==s.crypto1),console.log("\uD83D\uDD0D DEBUG: AVAILABLE_CRYPTOS length:",W.hg?.length),console.log("\uD83D\uDD0D DEBUG: crypto1Options length:",_.length),console.log("\uD83D\uDD0D DEBUG: First 20 cryptos:",W.hg?.slice(0,20)),console.log("\uD83D\uDD0D DEBUG: ALLOWED_CRYPTO1:",el),console.log("\uD83D\uDD0D DEBUG: ALLOWED_CRYPTO2:",ei),(0,t.jsxs)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen",children:[(0,t.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,t.jsx)("h2",{className:"text-2xl font-bold text-sidebar-primary",children:"Trading Configuration"})}),(0,t.jsx)(T.F,{className:"flex-1 pr-2",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(O.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,t.jsx)(O.aR,{children:(0,t.jsx)(O.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Mode"})}),(0,t.jsxs)(O.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(E,{id:"enableStablecoinSwap",checked:"StablecoinSwap"===s.tradingMode,onCheckedChange:e=>{n({type:"SET_CONFIG",payload:{tradingMode:e?"StablecoinSwap":"SimpleSpot"}})}}),(0,t.jsx)(S.J,{htmlFor:"enableStablecoinSwap",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Enable Stablecoin Swap Mode"})]}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Current mode: ","SimpleSpot"===s.tradingMode?"Simple Spot":"Stablecoin Swap"]}),"StablecoinSwap"===s.tradingMode&&(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"preferredStablecoin",children:"Preferred Stablecoin"}),(0,t.jsxs)(A.l6,{name:"preferredStablecoin",value:s.preferredStablecoin,onValueChange:e=>M("preferredStablecoin",e),children:[(0,t.jsx)(A.bq,{id:"preferredStablecoin",children:(0,t.jsx)(A.yv,{placeholder:"Select stablecoin"})}),(0,t.jsx)(A.gC,{className:"max-h-[300px] overflow-y-auto",children:W.Ql&&W.Ql.map(e=>(0,t.jsx)(A.eb,{value:e,children:e},e))})]})]})]})]}),(0,t.jsxs)(O.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,t.jsx)(O.aR,{children:(0,t.jsxs)(O.ZB,{className:"text-sidebar-accent-foreground flex items-center",children:[(0,t.jsx)(ee.A,{className:"mr-2 h-5 w-5 text-primary"})," AI Mode Suggestion"]})}),(0,t.jsxs)(O.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"riskTolerance",children:"Risk Tolerance"}),(0,t.jsxs)(A.l6,{value:R,onValueChange:U,children:[(0,t.jsx)(A.bq,{id:"riskTolerance",children:(0,t.jsx)(A.yv,{placeholder:"Select risk tolerance"})}),(0,t.jsxs)(A.gC,{children:[(0,t.jsx)(A.eb,{value:"low",children:"Low"}),(0,t.jsx)(A.eb,{value:"medium",children:"Medium"}),(0,t.jsx)(A.eb,{value:"high",children:"High"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"preferredCryptos",children:"Preferred Cryptocurrencies (comma-separated)"}),(0,t.jsx)(N.p,{id:"preferredCryptos",value:P,onChange:e=>D(e.target.value),placeholder:"e.g., BTC, ETH"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"investmentGoals",children:"Investment Goals"}),(0,t.jsx)(N.p,{id:"investmentGoals",value:F,onChange:e=>B(e.target.value),placeholder:"e.g., Long term, Short term profit"})]}),(0,t.jsxs)(d.$,{onClick:$,disabled:p,className:"w-full btn-neo",children:[p&&(0,t.jsx)(er.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Get AI Suggestion"]})]})]}),(0,t.jsxs)(O.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,t.jsx)(O.aR,{children:(0,t.jsx)(O.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Pair"})}),(0,t.jsxs)(O.Wu,{className:"space-y-4",children:[(0,t.jsx)(Q,{label:"Crypto 1 (Base)",value:s.crypto1,allowedCryptos:(s.tradingMode,el||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]),onValidCrypto:e=>{n({type:"SET_CONFIG",payload:{crypto1:e}});let r=ei||["USDT","USDC","DAI"];r.includes(s.crypto2)||n({type:"SET_CONFIG",payload:{crypto2:r[0]}})},placeholder:"e.g., BTC, ETH, SOL",description:"Enter the base cryptocurrency symbol"}),(0,t.jsx)(Q,{label:"StablecoinSwap"===s.tradingMode?"Crypto 2":"Crypto 2 (Stablecoin)",value:s.crypto2,allowedCryptos:"StablecoinSwap"===s.tradingMode?el||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]:ei||["USDC","DAI","TUSD","FDUSD","USDT","EUR"],onValidCrypto:e=>{n({type:"SET_CONFIG",payload:{crypto2:e}})},placeholder:"StablecoinSwap"===s.tradingMode?"e.g., BTC, ETH, SOL":"e.g., USDT, USDC, DAI",description:"StablecoinSwap"===s.tradingMode?"Enter the second cryptocurrency symbol":"Enter the quote/stablecoin symbol"})]})]}),(0,t.jsxs)(O.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,t.jsx)(O.aR,{children:(0,t.jsx)(O.ZB,{className:"text-sidebar-accent-foreground",children:"Parameters"})}),(0,t.jsxs)(O.Wu,{className:"space-y-3",children:[[{name:"baseBid",label:"Base Bid (Crypto 2)",type:"number",step:"0.01"},{name:"multiplier",label:"Multiplier",type:"number",step:"0.001"},{name:"numDigits",label:"Display Digits",type:"number",step:"1"},{name:"slippagePercent",label:"Slippage %",type:"number",step:"0.01"}].map(e=>(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:e.name,children:e.label}),(0,t.jsx)(N.p,{id:e.name,name:e.name,type:e.type,value:s[e.name],onChange:L,step:e.step,min:"0"})]},e.name)),(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{children:"Couple Income % Split (must sum to 100)"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(S.J,{htmlFor:"incomeSplitCrypto1Percent",className:"text-xs",children:[s.crypto1,"%"]}),(0,t.jsx)(N.p,{id:"incomeSplitCrypto1Percent",type:"number",value:s.incomeSplitCrypto1Percent,onChange:e=>I("incomeSplitCrypto1Percent",e.target.value),min:"0",max:"100"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)(S.J,{htmlFor:"incomeSplitCrypto2Percent",className:"text-xs",children:[s.crypto2,"%"]}),(0,t.jsx)(N.p,{id:"incomeSplitCrypto2Percent",type:"number",value:s.incomeSplitCrypto2Percent,onChange:e=>I("incomeSplitCrypto2Percent",e.target.value),min:"0",max:"100"})]})]})]})]})]})]})}),(0,t.jsxs)("div",{className:"flex-shrink-0 mt-4",children:[(0,t.jsx)(k.w,{className:"mb-4 bg-sidebar-border"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:(0,g.cn)("text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border",c?"bg-green-600 text-primary-foreground":"bg-muted text-muted-foreground"),children:[c?(0,t.jsx)(es.A,{className:"h-4 w-4"}):u?(0,t.jsx)(er.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,t.jsx)(et.A,{className:"h-4 w-4"}),"Bot Status: ",c?"Running":u?"Warming Up":"Stopped"]}),(0,t.jsx)(d.$,{onClick:()=>v(!0),className:"w-full btn-outline-neo",children:"Set Target Prices"}),(0,t.jsx)(d.$,{onClick:()=>C(!0),className:"w-full btn-outline-neo",children:"Set Alarm"}),(0,t.jsxs)(d.$,{onClick:()=>{c?n({type:"SYSTEM_STOP_BOT"}):n({type:"SYSTEM_START_BOT_INITIATE"})},className:(0,g.cn)("w-full btn-neo",c||u?"bg-destructive hover:bg-destructive/90":"bg-green-600 hover:bg-green-600/90"),disabled:u,children:[c?(0,t.jsx)(es.A,{className:"h-4 w-4"}):u?(0,t.jsx)(er.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,t.jsx)(et.A,{className:"h-4 w-4"}),c?"Stop Bot":u?"Warming Up...":"Start Bot"]}),(0,t.jsxs)(d.$,{onClick:()=>{n({type:"SYSTEM_RESET_BOT"}),b({title:"Bot Reset",description:"Trading bot has been reset to fresh state. All positions cleared.",duration:3e3})},variant:"outline",className:"w-full btn-outline-neo",disabled:u,children:[(0,t.jsx)(ea.A,{className:"h-4 w-4 mr-2"}),"Reset Bot"]})]})]}),(0,t.jsx)(z,{isOpen:j,onClose:()=>v(!1),onSetTargetPrices:o}),(0,t.jsx)(Y,{isOpen:w,onClose:()=>C(!1)})]})}function ed({children:e}){let{isAuthenticated:r,isLoading:s}=(0,c.A)(),a=(0,i.useRouter)();return s?(0,t.jsx)("div",{className:"flex items-center justify-center h-screen bg-background",children:(0,t.jsx)(er.A,{className:"h-12 w-12 animate-spin text-primary"})}):r?(0,t.jsxs)("div",{className:"flex flex-col min-h-screen bg-background text-foreground",children:[(0,t.jsx)(v,{}),(0,t.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,t.jsx)(eo,{}),(0,t.jsx)("main",{className:"flex-1 overflow-y-auto p-4 md:p-6 lg:p-8",children:e})]})]}):(a.push("/login"),null)}},63144:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\layout.tsx","default")},70428:(e,r,s)=>{"use strict";s.d(r,{A:()=>f});var t=s(60687),a=s(43210),n=s(78895),l=s(44493),i=s(89667),o=s(29523),d=s(58450),c=s(78726),u=s(91840),m=s(59892),p=s(21277),x=s(9812);function f(){let{crypto1Balance:e,crypto2Balance:r,stablecoinBalance:s,config:f,dispatch:h}=(0,n.U)(),[b,g]=(0,a.useState)(null),[j,v]=(0,a.useState)({crypto1:e.toString(),crypto2:r.toString(),stablecoin:s.toString()}),y=e=>e.toFixed(f.numDigits),N=t=>{g(t),v({crypto1:e.toString(),crypto2:r.toString(),stablecoin:s.toString()})},S=s=>{let t=parseFloat(j[s]);!isNaN(t)&&t>=0&&("crypto1"===s?h({type:"UPDATE_BALANCES",payload:{crypto1:t,crypto2:r}}):"crypto2"===s?h({type:"UPDATE_BALANCES",payload:{crypto1:e,crypto2:t}}):"stablecoin"===s&&h({type:"UPDATE_STABLECOIN_BALANCE",payload:t})),g(null)},w=()=>{g(null),v({crypto1:e.toString(),crypto2:r.toString(),stablecoin:s.toString()})},C=(e,r,s,a,n)=>(0,t.jsxs)(l.Zp,{className:"border-2 border-border",children:[(0,t.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(l.ZB,{className:"text-sm font-medium text-muted-foreground",children:e}),a]}),(0,t.jsx)(l.Wu,{children:b===s?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(i.p,{type:"number",value:j[s],onChange:e=>v(r=>({...r,[s]:e.target.value})),className:"text-lg font-bold",step:"any",min:"0"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(o.$,{size:"sm",onClick:()=>S(s),className:"flex-1",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-1"}),"Save"]}),(0,t.jsxs)(o.$,{size:"sm",variant:"outline",onClick:w,className:"flex-1",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-1"}),"Cancel"]})]})]}):(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-foreground",children:y(r)}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Available ",n]})]}),(0,t.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>N(s),className:"ml-2",children:(0,t.jsx)(u.A,{className:"h-4 w-4"})})]})})]});return(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-3 mb-6",children:[C(`${f.crypto1} Balance`,e,"crypto1",(0,t.jsx)(m.A,{className:"h-5 w-5 text-primary"}),f.crypto1),C(`${f.crypto2} Balance`,r,"crypto2",(0,t.jsx)(p.A,{className:"h-5 w-5 text-primary"}),f.crypto2),C(`Stablecoin Balance (${f.preferredStablecoin||"N/A"})`,s,"stablecoin",(0,t.jsx)(x.A,{className:"h-5 w-5 text-primary"}),"Stablecoins")]})}},80013:(e,r,s)=>{"use strict";s.d(r,{J:()=>d});var t=s(60687),a=s(43210),n=s(78148),l=s(24224),i=s(4780);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(n.b,{ref:s,className:(0,i.cn)(o(),e),...r}));d.displayName=n.b.displayName},85763:(e,r,s)=>{"use strict";s.d(r,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>i});var t=s(60687),a=s(43210),n=s(41360),l=s(4780);let i=n.bL,o=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(n.B8,{ref:s,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...r}));o.displayName=n.B8.displayName;let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(n.l9,{ref:s,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...r}));d.displayName=n.l9.displayName;let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)(n.UC,{ref:s,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...r}));c.displayName=n.UC.displayName},89667:(e,r,s)=>{"use strict";s.d(r,{p:()=>l});var t=s(60687),a=s(43210),n=s(4780);let l=a.forwardRef(({className:e,type:r,...s},a)=>(0,t.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...s}));l.displayName="Input"},96834:(e,r,s)=>{"use strict";s.d(r,{E:()=>i});var t=s(60687);s(43210);var a=s(24224),n=s(4780);let l=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:r,...s}){return(0,t.jsx)("div",{className:(0,n.cn)(l({variant:r}),e),...s})}}};