(()=>{var e={};e.id=698,e.ids=[698],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\admin\\page.tsx","default")},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7252:e=>{"use strict";e.exports=require("express")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11003:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(82614).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},15079:(e,s,t)=>{"use strict";t.d(s,{bq:()=>p,eb:()=>f,gC:()=>h,l6:()=>c,yv:()=>u});var r=t(60687),a=t(43210),i=t(28850),n=t(61662),l=t(89743),o=t(58450),d=t(4780);let c=i.bL;i.YJ;let u=i.WT,p=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(i.l9,{ref:a,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,(0,r.jsx)(i.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));p.displayName=i.l9.displayName;let m=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}));m.displayName=i.PP.displayName;let x=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})}));x.displayName=i.wn.displayName;let h=a.forwardRef(({className:e,children:s,position:t="popper",...a},n)=>(0,r.jsx)(i.ZL,{children:(0,r.jsxs)(i.UC,{ref:n,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...a,children:[(0,r.jsx)(m,{}),(0,r.jsx)(i.LM,{className:(0,d.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(x,{})]})}));h.displayName=i.UC.displayName,a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=i.JU.displayName;let f=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(i.q7,{ref:a,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(i.p4,{children:s})]}));f.displayName=i.q7.displayName,a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.wv.displayName},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19185:e=>{"use strict";e.exports=require("dgram")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23056:(e,s,t)=>{Promise.resolve().then(t.bind(t,31066))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,s,t)=>{"use strict";t.d(s,{$:()=>d});var r=t(60687),a=t(43210),i=t(8730),n=t(24224),l=t(4780);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:s,size:t,asChild:a=!1,...n},d)=>{let c=a?i.DX:"button";return(0,r.jsx)(c,{className:(0,l.cn)(o({variant:s,size:t,className:e})),ref:d,...n})});d.displayName="Button"},29976:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\admin\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},31066:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>z});var r=t(60687),a=t(43210),i=t(16189),n=t(85763),l=t(29523),o=t(89667),d=t(80013),c=t(44493),u=t(15079),p=t(54987),m=t(78895),x=t(55280),h=t(29867),f=t(42692),g=t(82614);let v=(0,g.A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),b=(0,g.A)("BellRing",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M22 8c0-2.3-.8-4.3-2-6",key:"5bb3ad"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}],["path",{d:"M4 2C2.8 3.7 2 5.7 2 8",key:"tap9e0"}]]),y=(0,g.A)("KeyRound",[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]),j=(0,g.A)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]),N=(0,g.A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var w=t(24026),k=t(11003),C=t(76311),S=t(96834);let A=(0,g.A)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),T=(0,g.A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),R=(0,g.A)("Pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]);var E=t(15036);let M=(0,g.A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),P=(0,g.A)("FolderOpen",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]),q=(0,g.A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);var _=t(57207),I=t(5551),B=t(43010);function D(){let{config:e,targetPriceRows:s,orderHistory:t,currentMarketPrice:i,crypto1Balance:n,crypto2Balance:u,stablecoinBalance:p,botSystemStatus:x,dispatch:g}=(0,m.U)(),{toast:v}=(0,h.dj)(),[b,y]=(0,a.useState)([]),[j,N]=(0,a.useState)(null),[w,k]=(0,a.useState)(""),[C,D]=(0,a.useState)(null),[z,F]=(0,a.useState)(""),$=I.C.getInstance(),O=()=>{y($.getAllSessions().sort((e,s)=>s.lastModified-e.lastModified))},L=async()=>{if(!w.trim()){v({title:"Error",description:"Please enter a session name",variant:"destructive"});return}try{let s=await $.createNewSession(w.trim(),e);$.setCurrentSession(s),N(s),k(""),O(),v({title:"Session Created",description:`New session "${w}" has been created`})}catch(e){v({title:"Error",description:"Failed to create session. Please try again.",variant:"destructive"})}},K=e=>{let s=$.loadSession(e);if(!s){v({title:"Error",description:"Failed to load session",variant:"destructive"});return}g({type:"SET_CONFIG",payload:s.config}),g({type:"SET_TARGET_PRICE_ROWS",payload:s.targetPriceRows}),g({type:"CLEAR_ORDER_HISTORY"}),s.orderHistory.forEach(e=>{g({type:"ADD_ORDER_HISTORY_ENTRY",payload:e})}),g({type:"SET_MARKET_PRICE",payload:s.currentMarketPrice}),g({type:"SET_BALANCES",payload:{crypto1:s.crypto1Balance,crypto2:s.crypto2Balance}}),$.setCurrentSession(e),N(e),O(),v({title:"Session Loaded",description:`Session "${s.name}" has been loaded`})},Z=e=>{$.deleteSession(e)&&(j===e&&N(null),O(),v({title:"Session Deleted",description:"Session has been deleted successfully"}))},H=e=>{z.trim()&&$.renameSession(e,z.trim())&&(D(null),F(""),O(),v({title:"Session Renamed",description:"Session has been renamed successfully"}))},U=e=>{let s=$.exportSessionToCSV(e);if(!s){v({title:"Error",description:"Failed to export session",variant:"destructive"});return}let t=$.loadSession(e),r=new Blob([s],{type:"text/csv;charset=utf-8;"}),a=document.createElement("a"),i=URL.createObjectURL(r);a.setAttribute("href",i),a.setAttribute("download",`${t?.name||"session"}_${new Date().toISOString().split("T")[0]}.csv`),a.style.visibility="hidden",document.body.appendChild(a),a.click(),document.body.removeChild(a),v({title:"Export Complete",description:"Session data has been exported to CSV"})},V=e=>{if(!e)return"0m";let s=Math.floor(e/6e4),t=Math.floor(s/60);return t>0?`${t}h ${s%60}m`:`${s}m`},J=()=>b.find(e=>e.id===j);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsx)(c.aR,{children:(0,r.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(A,{className:"h-5 w-5"}),"Current Session"]})}),(0,r.jsx)(c.Wu,{children:J()?(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-4 items-center",children:[(0,r.jsx)("div",{className:"flex items-center gap-2",children:C===J()?.id?(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(o.p,{value:z,onChange:e=>F(e.target.value),onKeyPress:e=>"Enter"===e.key&&H(J().id),className:"text-sm"}),(0,r.jsx)(l.$,{size:"sm",onClick:()=>H(J().id),children:(0,r.jsx)(T,{className:"h-3 w-3"})})]}):(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"font-medium",children:J()?.name}),(0,r.jsx)(l.$,{size:"sm",variant:"ghost",onClick:()=>{D(J().id),F(J().name)},children:(0,r.jsx)(R,{className:"h-3 w-3"})})]})}),(0,r.jsx)("div",{children:(0,r.jsx)(S.E,{variant:"Running"===x?"default":"secondary",children:"Running"===x?"Active":"Inactive"})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(E.A,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsx)("span",{children:V(J()?.runtime)})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(l.$,{onClick:()=>{if(!j){v({title:"Error",description:"No active session to save",variant:"destructive"});return}$.saveSession(j,e,s,t,i,n,u,p,"Running"===x)?(O(),v({title:"Session Saved",description:"Current session has been saved successfully"})):v({title:"Error",description:"Failed to save session",variant:"destructive"})},className:"btn-neo",children:[(0,r.jsx)(T,{className:"mr-2 h-4 w-4"}),"Save Session"]}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)(l.$,{variant:"outline",className:"px-3",children:"Save As..."})})]})]}):(0,r.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,r.jsx)("p",{children:"No active session"}),(0,r.jsx)("p",{className:"text-xs",children:"Create a new session to get started"})]})})]}),(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsx)(c.aR,{children:(0,r.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(M,{className:"h-5 w-5"}),"Create New Session"]})}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{htmlFor:"session-name",children:"Session Name"}),(0,r.jsx)(o.p,{id:"session-name",value:w,onChange:e=>k(e.target.value),placeholder:"e.g., BTC/USDT SimpleSpot",onKeyPress:e=>"Enter"===e.key&&L()})]}),(0,r.jsxs)(l.$,{onClick:L,className:"w-full btn-neo",children:[(0,r.jsx)(M,{className:"mr-2 h-4 w-4"}),"Create New Session"]})]})]}),(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsxs)(c.aR,{className:"flex flex-row items-center justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(E.A,{className:"h-5 w-5"}),"Past Sessions (",b.filter(e=>e.id!==j).length,")"]})}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Auto-saved: ",b.filter(e=>e.id!==j).length," | Manual: 0"]})]}),(0,r.jsx)(c.Wu,{children:0===b.filter(e=>e.id!==j).length?(0,r.jsxs)("div",{className:"text-center text-muted-foreground py-16",children:[(0,r.jsx)(E.A,{className:"h-16 w-16 mx-auto mb-4 opacity-30"}),(0,r.jsx)("p",{className:"text-lg font-medium",children:"No saved sessions yet."}),(0,r.jsx)("p",{className:"text-sm",children:"Save your current session to get started."})]}):(0,r.jsx)(f.F,{className:"h-[400px]",children:(0,r.jsx)("div",{className:"space-y-3",children:b.filter(e=>e.id!==j).map(e=>(0,r.jsx)(c.Zp,{className:"border border-border",children:(0,r.jsx)(c.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1",children:[C===e.id?(0,r.jsxs)("div",{className:"flex gap-2 mb-2",children:[(0,r.jsx)(o.p,{value:z,onChange:e=>F(e.target.value),onKeyPress:s=>"Enter"===s.key&&H(e.id),className:"text-sm"}),(0,r.jsx)(l.$,{size:"sm",onClick:()=>H(e.id),children:(0,r.jsx)(T,{className:"h-3 w-3"})})]}):(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)("h4",{className:"font-medium",children:e.name}),(0,r.jsx)(l.$,{size:"sm",variant:"ghost",onClick:()=>{D(e.id),F(e.name)},children:(0,r.jsx)(R,{className:"h-3 w-3"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs text-muted-foreground",children:[(0,r.jsxs)("div",{children:["Pair: ",e.pair]}),(0,r.jsxs)("div",{children:["Trades: ",e.totalTrades]}),(0,r.jsxs)("div",{children:["Runtime: ",V(e.runtime)]}),(0,r.jsxs)("div",{children:["P/L: ",e.totalProfitLoss.toFixed(2)]})]}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground mt-1",children:(0,B.GP)(new Date(e.lastModified),"MMM dd, yyyy HH:mm")})]}),(0,r.jsxs)("div",{className:"flex gap-1 ml-4",children:[(0,r.jsx)(l.$,{size:"sm",variant:"outline",onClick:()=>K(e.id),children:(0,r.jsx)(P,{className:"h-3 w-3"})}),(0,r.jsx)(l.$,{size:"sm",variant:"outline",onClick:()=>U(e.id),children:(0,r.jsx)(q,{className:"h-3 w-3"})}),(0,r.jsx)(l.$,{size:"sm",variant:"outline",onClick:()=>Z(e.id),children:(0,r.jsx)(_.A,{className:"h-3 w-3"})})]})]})})},e.id))})})})]})]})}function z(){let{appSettings:e,dispatch:s,botSystemStatus:t}=(0,m.U)(),[g,S]=(0,a.useState)(e),[A,T]=(0,a.useState)("iVIOLOnigRM31Qzm4UoLYsJo4QYIsd1XeXKztnwHfcijpWiAaWQKRsmx3NO7LrLA"),[R,E]=(0,a.useState)("jzAnpgIFFv3Ypdhf4jEXljjbkBpfJE5W2aN0zrtypmD3RAjoh2vdQXMr66LOv5fp"),[M,P]=(0,a.useState)(!1),[q,_]=(0,a.useState)(!1),[I,B]=(0,a.useState)(""),[z,F]=(0,a.useState)(""),{toast:$}=(0,h.dj)(),O=(0,i.useRouter)(),L=(e,s)=>{S(t=>({...t,[e]:s}))},K=async()=>{try{localStorage.setItem("binance_api_key",A),localStorage.setItem("binance_api_secret",R),console.log("API Keys Saved:",{apiKey:A.substring(0,10)+"...",apiSecret:R.substring(0,10)+"..."}),$({title:"API Keys Saved",description:"Binance API keys have been saved securely."})}catch(e){$({title:"Error",description:"Failed to save API keys.",variant:"destructive"})}},Z=async()=>{try{(await fetch("https://api.binance.com/api/v3/ping")).ok?$({title:"API Connection Test",description:"Successfully connected to Binance API!"}):$({title:"Connection Failed",description:"Unable to connect to Binance API.",variant:"destructive"})}catch(e){$({title:"Connection Error",description:"Network error while testing API connection.",variant:"destructive"})}},H=async()=>{if(!I||!z){$({title:"Missing Configuration",description:"Please enter both Telegram bot token and chat ID.",variant:"destructive"});return}try{(await fetch(`https://api.telegram.org/bot${I}/sendMessage`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:z,text:"\uD83E\uDD16 Test message from Pluto Trading Bot! Your Telegram integration is working correctly."})})).ok?$({title:"Telegram Test Successful",description:"Test message sent successfully!"}):$({title:"Telegram Test Failed",description:"Failed to send test message. Check your token and chat ID.",variant:"destructive"})}catch(e){$({title:"Telegram Error",description:"Network error while testing Telegram integration.",variant:"destructive"})}},U=[{value:"systemTools",label:"System Tools",icon:(0,r.jsx)(v,{className:"mr-2 h-4 w-4"})},{value:"appSettings",label:"App Settings",icon:(0,r.jsx)(b,{className:"mr-2 h-4 w-4"})},{value:"apiKeys",label:"Exchange API Keys",icon:(0,r.jsx)(y,{className:"mr-2 h-4 w-4"})},{value:"telegram",label:"Telegram Integration",icon:(0,r.jsx)(j,{className:"mr-2 h-4 w-4"})},{value:"sessionManager",label:"Session Manager",icon:(0,r.jsx)(N,{className:"mr-2 h-4 w-4"})}];return(0,r.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,r.jsxs)(c.Zp,{className:"border-2 border-border",children:[(0,r.jsxs)(c.aR,{className:"flex flex-row justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(c.ZB,{className:"text-3xl font-bold text-primary",children:"Admin Panel"}),(0,r.jsx)(c.BT,{children:"Manage global settings and tools for Pluto Trading Bot."})]}),(0,r.jsxs)(l.$,{variant:"outline",onClick:()=>O.push("/dashboard"),className:"btn-outline-neo",children:[(0,r.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"Return to Dashboard"]})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsxs)(n.tU,{defaultValue:"systemTools",className:"w-full",children:[(0,r.jsx)(f.F,{className:"pb-2",children:(0,r.jsx)(n.j7,{className:"bg-card border-border border-2 p-1",children:U.map(e=>(0,r.jsx)(n.Xi,{value:e.value,className:"px-4 py-2 text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",children:(0,r.jsxs)("div",{className:"flex items-center",children:[e.icon," ",e.label]})},e.value))})}),(0,r.jsx)(n.av,{value:"systemTools",className:"mt-6",children:(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"System Tools"})}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Database Editor, Clean Duplicates, Export/Import, Backup/Restore, Diagnostics - (Placeholders for future implementation)."}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,r.jsx)(l.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>$({title:"DB Editor Clicked"}),children:"View Database (Read-Only)"}),(0,r.jsx)(l.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>$({title:"Export Orders Clicked"}),children:"Export Orders to Excel"}),(0,r.jsx)(l.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>$({title:"Export History Clicked"}),children:"Export History to Excel"}),(0,r.jsx)(l.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>$({title:"Backup DB Clicked"}),children:"Backup Database"}),(0,r.jsx)(l.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>$({title:"Restore DB Clicked"}),disabled:!0,children:"Restore Database"}),(0,r.jsx)(l.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>$({title:"Diagnostics Clicked"}),children:"Run System Diagnostics"})]})]})]})})}),(0,r.jsx)(n.av,{value:"appSettings",className:"mt-6",children:(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Application Settings"})}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{htmlFor:"preferredStablecoin",children:"Preferred Stablecoin (for Swap Mode)"}),(0,r.jsxs)(u.l6,{value:g.preferredStablecoin,onValueChange:e=>L("preferredStablecoin",e),children:[(0,r.jsx)(u.bq,{id:"preferredStablecoin",children:(0,r.jsx)(u.yv,{placeholder:"Select Stablecoin"})}),(0,r.jsx)(u.gC,{children:x.Ql.map(e=>(0,r.jsx)(u.eb,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{htmlFor:"priceUpdateIntervalMs",children:"Price Update Interval (ms)"}),(0,r.jsx)(o.p,{id:"priceUpdateIntervalMs",type:"number",value:g.priceUpdateIntervalMs||1e3,onChange:e=>L("priceUpdateIntervalMs",parseInt(e.target.value)||1e3)})]}),(0,r.jsx)(l.$,{onClick:()=>{s({type:"SET_APP_SETTINGS",payload:g}),$({title:"App Settings Saved",description:"Global application settings have been updated."})},className:"btn-neo",children:"Save App Settings"})]})]})}),(0,r.jsx)(n.av,{value:"apiKeys",className:"mt-6",children:(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Exchange API Keys (Binance)"})}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Configure your Binance API keys for real trading. Keys are stored securely in browser storage."}),(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{htmlFor:"apiKey",children:"API Key"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.p,{id:"apiKey",type:M?"text":"password",value:A,onChange:e=>T(e.target.value),placeholder:"Enter your Binance API key",className:"pr-10"}),(0,r.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>P(!M),children:M?(0,r.jsx)(k.A,{className:"h-4 w-4"}):(0,r.jsx)(C.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{htmlFor:"apiSecret",children:"API Secret"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.p,{id:"apiSecret",type:q?"text":"password",value:R,onChange:e=>E(e.target.value),placeholder:"Enter your Binance API secret",className:"pr-10"}),(0,r.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>_(!q),children:q?(0,r.jsx)(k.A,{className:"h-4 w-4"}):(0,r.jsx)(C.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(l.$,{onClick:K,className:"btn-neo",children:"Save API Keys"}),(0,r.jsx)(l.$,{onClick:Z,variant:"outline",className:"btn-outline-neo",children:"Test Connection"})]})]})]})}),(0,r.jsx)(n.av,{value:"telegram",className:"mt-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"Telegram Configuration"}),(0,r.jsx)(c.BT,{children:"Configure Telegram bot for real-time trading notifications."})]}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{htmlFor:"telegramToken",children:"Telegram Bot Token"}),(0,r.jsx)(o.p,{id:"telegramToken",type:"password",value:I,onChange:e=>B(e.target.value),placeholder:"Enter your Telegram bot token"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{htmlFor:"telegramChatId",children:"Telegram Chat ID"}),(0,r.jsx)(o.p,{id:"telegramChatId",value:z,onChange:e=>F(e.target.value),placeholder:"Enter your Telegram chat ID"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(p.d,{id:"notifyOnOrder"}),(0,r.jsx)(d.J,{htmlFor:"notifyOnOrder",children:"Notify on Order Execution"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(p.d,{id:"notifyOnErrors"}),(0,r.jsx)(d.J,{htmlFor:"notifyOnErrors",children:"Notify on Errors"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(l.$,{onClick:()=>{try{localStorage.setItem("telegram_bot_token",I),localStorage.setItem("telegram_chat_id",z),console.log("Telegram Config Saved:",{telegramToken:I.substring(0,10)+"...",telegramChatId:z}),$({title:"Telegram Config Saved",description:"Telegram settings have been saved successfully."})}catch(e){$({title:"Error",description:"Failed to save Telegram configuration.",variant:"destructive"})}},className:"btn-neo",children:"Save Telegram Config"}),(0,r.jsx)(l.$,{onClick:H,variant:"outline",className:"btn-outline-neo",children:"Test Telegram"})]})]})]}),(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Setup Guide"})}),(0,r.jsxs)(c.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-yellow-500 mb-3",children:"Step 1: Create a Telegram Bot"}),(0,r.jsxs)("ol",{className:"text-sm space-y-1 list-decimal list-inside text-muted-foreground",children:[(0,r.jsxs)("li",{children:["Open Telegram and search for ",(0,r.jsx)("code",{className:"bg-muted px-1 rounded",children:"@BotFather"})]}),(0,r.jsxs)("li",{children:["Send ",(0,r.jsx)("code",{className:"bg-muted px-1 rounded",children:"/newbot"})," command"]}),(0,r.jsx)("li",{children:'Choose a name for your bot (e.g., "My Trading Bot")'}),(0,r.jsx)("li",{children:'Choose a username ending with "bot" (e.g., "mytradingbot")'}),(0,r.jsx)("li",{children:"Copy the bot token provided by BotFather"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-yellow-500 mb-3",children:"Step 2: Get Your Chat ID"}),(0,r.jsxs)("ol",{className:"text-sm space-y-1 list-decimal list-inside text-muted-foreground",children:[(0,r.jsx)("li",{children:"Start a chat with your new bot"}),(0,r.jsx)("li",{children:"Send any message to the bot"}),(0,r.jsxs)("li",{children:["Visit: ",(0,r.jsx)("code",{className:"bg-muted px-1 rounded text-xs",children:"https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates"})]}),(0,r.jsxs)("li",{children:['Look for "chat":',"{",'id": in the response']}),(0,r.jsx)("li",{children:"Copy the chat ID number"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-yellow-500 mb-3",children:"Step 3: Configure Bot"}),(0,r.jsxs)("ul",{className:"text-sm space-y-1 list-disc list-inside text-muted-foreground",children:[(0,r.jsx)("li",{children:'Paste the bot token in the "Telegram Bot Token" field'}),(0,r.jsx)("li",{children:'Paste the chat ID in the "Telegram Chat ID" field'}),(0,r.jsx)("li",{children:"Choose your notification preferences"}),(0,r.jsx)("li",{children:'Click "Save Telegram Config"'}),(0,r.jsx)("li",{children:'Test the connection with "Test Telegram"'})]})]}),(0,r.jsx)("div",{className:"bg-yellow-500/10 border border-yellow-500/20 rounded-md p-4",children:(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)("span",{className:"text-yellow-500",children:"\uD83D\uDCA1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"font-semibold text-yellow-600 mb-1",children:"Pro Tip:"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Keep your bot token secure and never share it publicly. You can regenerate it anytime via BotFather if needed."})]})]})})]})]})]})}),(0,r.jsx)(n.av,{value:"sessionManager",className:"mt-6",children:(0,r.jsx)(D,{})})]})})]})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37830:e=>{"use strict";e.exports=require("node:stream/web")},42692:(e,s,t)=>{"use strict";t.d(s,{$:()=>o,F:()=>l});var r=t(60687),a=t(43210),i=t(68123),n=t(4780);let l=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(i.bL,{ref:a,className:(0,n.cn)("relative overflow-hidden",e),...t,children:[(0,r.jsx)(i.LM,{className:"h-full w-full rounded-[inherit]",children:s}),(0,r.jsx)(o,{}),(0,r.jsx)(i.OK,{})]}));l.displayName=i.bL.displayName;let o=a.forwardRef(({className:e,orientation:s="vertical",...t},a)=>(0,r.jsx)(i.VM,{ref:a,orientation:s,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===s&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===s&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...t,children:(0,r.jsx)(i.lr,{className:"relative flex-1 rounded-full bg-border"})}));o.displayName=i.VM.displayName},44493:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>l});var r=t(60687),a=t(43210),i=t(4780);let n=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",e),...s}));n.displayName="Card";let l=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-4 md:p-6",e),...s}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("h3",{ref:t,className:(0,i.cn)("text-xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("p-4 md:p-6 pt-0",e),...s}));c.displayName="CardContent",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-4 md:p-6 pt-0",e),...s})).displayName="CardFooter"},44708:e=>{"use strict";e.exports=require("node:https")},54379:e=>{"use strict";e.exports=require("node:path")},54987:(e,s,t)=>{"use strict";t.d(s,{d:()=>l});var r=t(60687),a=t(43210),i=t(90270),n=t(4780);let l=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.bL,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:t,children:(0,r.jsx)(i.zi,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));l.displayName=i.bL.displayName},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57207:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(82614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64912:(e,s,t)=>{Promise.resolve().then(t.bind(t,1132))},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},76311:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(82614).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78148:(e,s,t)=>{"use strict";t.d(s,{b:()=>l});var r=t(43210),a=t(14163),i=t(60687),n=r.forwardRef((e,s)=>(0,i.jsx)(a.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=n},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80013:(e,s,t)=>{"use strict";t.d(s,{J:()=>d});var r=t(60687),a=t(43210),i=t(78148),n=t(24224),l=t(4780);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.b,{ref:t,className:(0,l.cn)(o(),e),...s}));d.displayName=i.b.displayName},81630:e=>{"use strict";e.exports=require("http")},84297:e=>{"use strict";e.exports=require("async_hooks")},85763:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>l});var r=t(60687),a=t(43210),i=t(41360),n=t(4780);let l=i.bL,o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.B8,{ref:t,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));o.displayName=i.B8.displayName;let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.l9,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=i.l9.displayName;let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.UC,{ref:t,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));c.displayName=i.UC.displayName},89667:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var r=t(60687),a=t(43210),i=t(4780);let n=a.forwardRef(({className:e,type:s,...t},a)=>(0,r.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));n.displayName="Input"},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96834:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var r=t(60687);t(43210);var a=t(24224),i=t(4780);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:s,...t}){return(0,r.jsx)("div",{className:(0,i.cn)(n({variant:s}),e),...t})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[585,191,112,124],()=>t(29976));module.exports=r})();