"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_r"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/ppr-navigations.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/ppr-navigations.js ***!
  \************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    abortTask: function() {\n        return abortTask;\n    },\n    listenForDynamicRequest: function() {\n        return listenForDynamicRequest;\n    },\n    startPPRNavigation: function() {\n        return startPPRNavigation;\n    },\n    updateCacheNodeOnPopstateRestoration: function() {\n        return updateCacheNodeOnPopstateRestoration;\n    }\n});\nconst _segment = __webpack_require__(/*! ../../../shared/lib/segment */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/segment.js\");\nconst _matchsegments = __webpack_require__(/*! ../match-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/match-segments.js\");\nconst _createroutercachekey = __webpack_require__(/*! ./create-router-cache-key */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ./is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst MPA_NAVIGATION_TASK = {\n    route: null,\n    node: null,\n    dynamicRequestTree: null,\n    children: null\n};\nfunction startPPRNavigation(oldCacheNode, oldRouterState, newRouterState, prefetchData, prefetchHead, isPrefetchHeadPartial, isSamePageNavigation, scrollableSegmentsResult) {\n    const segmentPath = [];\n    return updateCacheNodeOnNavigation(oldCacheNode, oldRouterState, newRouterState, false, prefetchData, prefetchHead, isPrefetchHeadPartial, isSamePageNavigation, segmentPath, scrollableSegmentsResult);\n}\nfunction updateCacheNodeOnNavigation(oldCacheNode, oldRouterState, newRouterState, didFindRootLayout, prefetchData, prefetchHead, isPrefetchHeadPartial, isSamePageNavigation, segmentPath, scrollableSegmentsResult) {\n    // Diff the old and new trees to reuse the shared layouts.\n    const oldRouterStateChildren = oldRouterState[1];\n    const newRouterStateChildren = newRouterState[1];\n    const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null;\n    if (!didFindRootLayout) {\n        // We're currently traversing the part of the tree that was also part of\n        // the previous route. If we discover a root layout, then we don't need to\n        // trigger an MPA navigation. See beginRenderingNewRouteTree for context.\n        const isRootLayout = newRouterState[4] === true;\n        if (isRootLayout) {\n            // Found a matching root layout.\n            didFindRootLayout = true;\n        }\n    }\n    const oldParallelRoutes = oldCacheNode.parallelRoutes;\n    // Clone the current set of segment children, even if they aren't active in\n    // the new tree.\n    // TODO: We currently retain all the inactive segments indefinitely, until\n    // there's an explicit refresh, or a parent layout is lazily refreshed. We\n    // rely on this for popstate navigations, which update the Router State Tree\n    // but do not eagerly perform a data fetch, because they expect the segment\n    // data to already be in the Cache Node tree. For highly static sites that\n    // are mostly read-only, this may happen only rarely, causing memory to\n    // leak. We should figure out a better model for the lifetime of inactive\n    // segments, so we can maintain instant back/forward navigations without\n    // leaking memory indefinitely.\n    const prefetchParallelRoutes = new Map(oldParallelRoutes);\n    // As we diff the trees, we may sometimes modify (copy-on-write, not mutate)\n    // the Route Tree that was returned by the server — for example, in the case\n    // of default parallel routes, we preserve the currently active segment. To\n    // avoid mutating the original tree, we clone the router state children along\n    // the return path.\n    let patchedRouterStateChildren = {};\n    let taskChildren = null;\n    // Most navigations require a request to fetch additional data from the\n    // server, either because the data was not already prefetched, or because the\n    // target route contains dynamic data that cannot be prefetched.\n    //\n    // However, if the target route is fully static, and it's already completely\n    // loaded into the segment cache, then we can skip the server request.\n    //\n    // This starts off as `false`, and is set to `true` if any of the child\n    // routes requires a dynamic request.\n    let needsDynamicRequest = false;\n    // As we traverse the children, we'll construct a FlightRouterState that can\n    // be sent to the server to request the dynamic data. If it turns out that\n    // nothing in the subtree is dynamic (i.e. needsDynamicRequest is false at the\n    // end), then this will be discarded.\n    // TODO: We can probably optimize the format of this data structure to only\n    // include paths that are dynamic. Instead of reusing the\n    // FlightRouterState type.\n    let dynamicRequestTreeChildren = {};\n    for(let parallelRouteKey in newRouterStateChildren){\n        const newRouterStateChild = newRouterStateChildren[parallelRouteKey];\n        const oldRouterStateChild = oldRouterStateChildren[parallelRouteKey];\n        const oldSegmentMapChild = oldParallelRoutes.get(parallelRouteKey);\n        const prefetchDataChild = prefetchDataChildren !== null ? prefetchDataChildren[parallelRouteKey] : null;\n        const newSegmentChild = newRouterStateChild[0];\n        const newSegmentPathChild = segmentPath.concat([\n            parallelRouteKey,\n            newSegmentChild\n        ]);\n        const newSegmentKeyChild = (0, _createroutercachekey.createRouterCacheKey)(newSegmentChild);\n        const oldSegmentChild = oldRouterStateChild !== undefined ? oldRouterStateChild[0] : undefined;\n        const oldCacheNodeChild = oldSegmentMapChild !== undefined ? oldSegmentMapChild.get(newSegmentKeyChild) : undefined;\n        let taskChild;\n        if (newSegmentChild === _segment.DEFAULT_SEGMENT_KEY) {\n            // This is another kind of leaf segment — a default route.\n            //\n            // Default routes have special behavior. When there's no matching segment\n            // for a parallel route, Next.js preserves the currently active segment\n            // during a client navigation — but not for initial render. The server\n            // leaves it to the client to account for this. So we need to handle\n            // it here.\n            if (oldRouterStateChild !== undefined) {\n                // Reuse the existing Router State for this segment. We spawn a \"task\"\n                // just to keep track of the updated router state; unlike most, it's\n                // already fulfilled and won't be affected by the dynamic response.\n                taskChild = spawnReusedTask(oldRouterStateChild);\n            } else {\n                // There's no currently active segment. Switch to the \"create\" path.\n                taskChild = beginRenderingNewRouteTree(oldRouterStateChild, newRouterStateChild, didFindRootLayout, prefetchDataChild !== undefined ? prefetchDataChild : null, prefetchHead, isPrefetchHeadPartial, newSegmentPathChild, scrollableSegmentsResult);\n            }\n        } else if (isSamePageNavigation && // Check if this is a page segment.\n        // TODO: We're not consistent about how we do this check. Some places\n        // check if the segment starts with PAGE_SEGMENT_KEY, but most seem to\n        // check if there any any children, which is why I'm doing it here. We\n        // should probably encode an empty children set as `null` though. Either\n        // way, we should update all the checks to be consistent.\n        Object.keys(newRouterStateChild[1]).length === 0) {\n            // We special case navigations to the exact same URL as the current\n            // location. It's a common UI pattern for apps to refresh when you click a\n            // link to the current page. So when this happens, we refresh the dynamic\n            // data in the page segments.\n            //\n            // Note that this does not apply if the any part of the hash or search\n            // query has changed. This might feel a bit weird but it makes more sense\n            // when you consider that the way to trigger this behavior is to click\n            // the same link multiple times.\n            //\n            // TODO: We should probably refresh the *entire* route when this case\n            // occurs, not just the page segments. Essentially treating it the same as\n            // a refresh() triggered by an action, which is the more explicit way of\n            // modeling the UI pattern described above.\n            //\n            // Also note that this only refreshes the dynamic data, not static/\n            // cached data. If the page segment is fully static and prefetched, the\n            // request is skipped. (This is also how refresh() works.)\n            taskChild = beginRenderingNewRouteTree(oldRouterStateChild, newRouterStateChild, didFindRootLayout, prefetchDataChild !== undefined ? prefetchDataChild : null, prefetchHead, isPrefetchHeadPartial, newSegmentPathChild, scrollableSegmentsResult);\n        } else if (oldRouterStateChild !== undefined && oldSegmentChild !== undefined && (0, _matchsegments.matchSegment)(newSegmentChild, oldSegmentChild)) {\n            if (oldCacheNodeChild !== undefined && oldRouterStateChild !== undefined) {\n                // This segment exists in both the old and new trees. Recursively update\n                // the children.\n                taskChild = updateCacheNodeOnNavigation(oldCacheNodeChild, oldRouterStateChild, newRouterStateChild, didFindRootLayout, prefetchDataChild, prefetchHead, isPrefetchHeadPartial, isSamePageNavigation, newSegmentPathChild, scrollableSegmentsResult);\n            } else {\n                // There's no existing Cache Node for this segment. Switch to the\n                // \"create\" path.\n                taskChild = beginRenderingNewRouteTree(oldRouterStateChild, newRouterStateChild, didFindRootLayout, prefetchDataChild !== undefined ? prefetchDataChild : null, prefetchHead, isPrefetchHeadPartial, newSegmentPathChild, scrollableSegmentsResult);\n            }\n        } else {\n            // This is a new tree. Switch to the \"create\" path.\n            taskChild = beginRenderingNewRouteTree(oldRouterStateChild, newRouterStateChild, didFindRootLayout, prefetchDataChild !== undefined ? prefetchDataChild : null, prefetchHead, isPrefetchHeadPartial, newSegmentPathChild, scrollableSegmentsResult);\n        }\n        if (taskChild !== null) {\n            // Recursively propagate up the child tasks.\n            if (taskChild.route === null) {\n                // One of the child tasks discovered a change to the root layout.\n                // Immediately unwind from this recursive traversal.\n                return MPA_NAVIGATION_TASK;\n            }\n            if (taskChildren === null) {\n                taskChildren = new Map();\n            }\n            taskChildren.set(parallelRouteKey, taskChild);\n            const newCacheNodeChild = taskChild.node;\n            if (newCacheNodeChild !== null) {\n                const newSegmentMapChild = new Map(oldSegmentMapChild);\n                newSegmentMapChild.set(newSegmentKeyChild, newCacheNodeChild);\n                prefetchParallelRoutes.set(parallelRouteKey, newSegmentMapChild);\n            }\n            // The child tree's route state may be different from the prefetched\n            // route sent by the server. We need to clone it as we traverse back up\n            // the tree.\n            const taskChildRoute = taskChild.route;\n            patchedRouterStateChildren[parallelRouteKey] = taskChildRoute;\n            const dynamicRequestTreeChild = taskChild.dynamicRequestTree;\n            if (dynamicRequestTreeChild !== null) {\n                // Something in the child tree is dynamic.\n                needsDynamicRequest = true;\n                dynamicRequestTreeChildren[parallelRouteKey] = dynamicRequestTreeChild;\n            } else {\n                dynamicRequestTreeChildren[parallelRouteKey] = taskChildRoute;\n            }\n        } else {\n            // The child didn't change. We can use the prefetched router state.\n            patchedRouterStateChildren[parallelRouteKey] = newRouterStateChild;\n            dynamicRequestTreeChildren[parallelRouteKey] = newRouterStateChild;\n        }\n    }\n    if (taskChildren === null) {\n        // No new tasks were spawned.\n        return null;\n    }\n    const newCacheNode = {\n        lazyData: null,\n        rsc: oldCacheNode.rsc,\n        // We intentionally aren't updating the prefetchRsc field, since this node\n        // is already part of the current tree, because it would be weird for\n        // prefetch data to be newer than the final data. It probably won't ever be\n        // observable anyway, but it could happen if the segment is unmounted then\n        // mounted again, because LayoutRouter will momentarily switch to rendering\n        // prefetchRsc, via useDeferredValue.\n        prefetchRsc: oldCacheNode.prefetchRsc,\n        head: oldCacheNode.head,\n        prefetchHead: oldCacheNode.prefetchHead,\n        loading: oldCacheNode.loading,\n        // Everything is cloned except for the children, which we computed above.\n        parallelRoutes: prefetchParallelRoutes\n    };\n    return {\n        // Return a cloned copy of the router state with updated children.\n        route: patchRouterStateWithNewChildren(newRouterState, patchedRouterStateChildren),\n        node: newCacheNode,\n        dynamicRequestTree: needsDynamicRequest ? patchRouterStateWithNewChildren(newRouterState, dynamicRequestTreeChildren) : null,\n        children: taskChildren\n    };\n}\nfunction beginRenderingNewRouteTree(oldRouterState, newRouterState, didFindRootLayout, prefetchData, possiblyPartialPrefetchHead, isPrefetchHeadPartial, segmentPath, scrollableSegmentsResult) {\n    if (!didFindRootLayout) {\n        // The route tree changed before we reached a layout. (The highest-level\n        // layout in a route tree is referred to as the \"root\" layout.) This could\n        // mean that we're navigating between two different root layouts. When this\n        // happens, we perform a full-page (MPA-style) navigation.\n        //\n        // However, the algorithm for deciding where to start rendering a route\n        // (i.e. the one performed in order to reach this function) is stricter\n        // than the one used to detect a change in the root layout. So just because\n        // we're re-rendering a segment outside of the root layout does not mean we\n        // should trigger a full-page navigation.\n        //\n        // Specifically, we handle dynamic parameters differently: two segments are\n        // considered the same even if their parameter values are different.\n        //\n        // Refer to isNavigatingToNewRootLayout for details.\n        //\n        // Note that we only have to perform this extra traversal if we didn't\n        // already discover a root layout in the part of the tree that is unchanged.\n        // In the common case, this branch is skipped completely.\n        if (oldRouterState === undefined || (0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(oldRouterState, newRouterState)) {\n            // The root layout changed. Perform a full-page navigation.\n            return MPA_NAVIGATION_TASK;\n        }\n    }\n    return createCacheNodeOnNavigation(newRouterState, prefetchData, possiblyPartialPrefetchHead, isPrefetchHeadPartial, segmentPath, scrollableSegmentsResult);\n}\nfunction createCacheNodeOnNavigation(routerState, prefetchData, possiblyPartialPrefetchHead, isPrefetchHeadPartial, segmentPath, scrollableSegmentsResult) {\n    // Same traversal as updateCacheNodeNavigation, but we switch to this path\n    // once we reach the part of the tree that was not in the previous route. We\n    // don't need to diff against the old tree, we just need to create a new one.\n    if (prefetchData === null) {\n        // There's no prefetch for this segment. Everything from this point will be\n        // requested from the server, even if there are static children below it.\n        // Create a terminal task node that will later be fulfilled by\n        // server response.\n        return spawnPendingTask(routerState, null, possiblyPartialPrefetchHead, isPrefetchHeadPartial, segmentPath, scrollableSegmentsResult);\n    }\n    const routerStateChildren = routerState[1];\n    const isPrefetchRscPartial = prefetchData[4];\n    // The head is assigned to every leaf segment delivered by the server. Based\n    // on corresponding logic in fill-lazy-items-till-leaf-with-head.ts\n    const isLeafSegment = Object.keys(routerStateChildren).length === 0;\n    // If prefetch data is available for a segment, and it's fully static (i.e.\n    // does not contain any dynamic holes), we don't need to request it from\n    // the server.\n    if (isPrefetchRscPartial || // Check if the head is partial (only relevant if this is a leaf segment)\n    isPrefetchHeadPartial && isLeafSegment) {\n        // We only have partial data from this segment. Like missing segments, we\n        // must request the full data from the server.\n        return spawnPendingTask(routerState, prefetchData, possiblyPartialPrefetchHead, isPrefetchHeadPartial, segmentPath, scrollableSegmentsResult);\n    }\n    // The prefetched segment is fully static, so we don't need to request a new\n    // one from the server. Keep traversing down the tree until we reach something\n    // that requires a dynamic request.\n    const prefetchDataChildren = prefetchData[2];\n    const taskChildren = new Map();\n    const cacheNodeChildren = new Map();\n    let dynamicRequestTreeChildren = {};\n    let needsDynamicRequest = false;\n    if (isLeafSegment) {\n        // The segment path of every leaf segment (i.e. page) is collected into\n        // a result array. This is used by the LayoutRouter to scroll to ensure that\n        // new pages are visible after a navigation.\n        // TODO: We should use a string to represent the segment path instead of\n        // an array. We already use a string representation for the path when\n        // accessing the Segment Cache, so we can use the same one.\n        scrollableSegmentsResult.push(segmentPath);\n    } else {\n        for(let parallelRouteKey in routerStateChildren){\n            const routerStateChild = routerStateChildren[parallelRouteKey];\n            const prefetchDataChild = prefetchDataChildren !== null ? prefetchDataChildren[parallelRouteKey] : null;\n            const segmentChild = routerStateChild[0];\n            const segmentPathChild = segmentPath.concat([\n                parallelRouteKey,\n                segmentChild\n            ]);\n            const segmentKeyChild = (0, _createroutercachekey.createRouterCacheKey)(segmentChild);\n            const taskChild = createCacheNodeOnNavigation(routerStateChild, prefetchDataChild, possiblyPartialPrefetchHead, isPrefetchHeadPartial, segmentPathChild, scrollableSegmentsResult);\n            taskChildren.set(parallelRouteKey, taskChild);\n            const dynamicRequestTreeChild = taskChild.dynamicRequestTree;\n            if (dynamicRequestTreeChild !== null) {\n                // Something in the child tree is dynamic.\n                needsDynamicRequest = true;\n                dynamicRequestTreeChildren[parallelRouteKey] = dynamicRequestTreeChild;\n            } else {\n                dynamicRequestTreeChildren[parallelRouteKey] = routerStateChild;\n            }\n            const newCacheNodeChild = taskChild.node;\n            if (newCacheNodeChild !== null) {\n                const newSegmentMapChild = new Map();\n                newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild);\n                cacheNodeChildren.set(parallelRouteKey, newSegmentMapChild);\n            }\n        }\n    }\n    const rsc = prefetchData[1];\n    const loading = prefetchData[3];\n    return {\n        // Since we're inside a new route tree, unlike the\n        // `updateCacheNodeOnNavigation` path, the router state on the children\n        // tasks is always the same as the router state we pass in. So we don't need\n        // to clone/modify it.\n        route: routerState,\n        node: {\n            lazyData: null,\n            // Since this is a fully static segment, we don't need to use the\n            // `prefetchRsc` field.\n            rsc,\n            prefetchRsc: null,\n            head: isLeafSegment ? possiblyPartialPrefetchHead : null,\n            prefetchHead: null,\n            loading,\n            parallelRoutes: cacheNodeChildren\n        },\n        dynamicRequestTree: needsDynamicRequest ? patchRouterStateWithNewChildren(routerState, dynamicRequestTreeChildren) : null,\n        children: taskChildren\n    };\n}\nfunction patchRouterStateWithNewChildren(baseRouterState, newChildren) {\n    const clone = [\n        baseRouterState[0],\n        newChildren\n    ];\n    // Based on equivalent logic in apply-router-state-patch-to-tree, but should\n    // confirm whether we need to copy all of these fields. Not sure the server\n    // ever sends, e.g. the refetch marker.\n    if (2 in baseRouterState) {\n        clone[2] = baseRouterState[2];\n    }\n    if (3 in baseRouterState) {\n        clone[3] = baseRouterState[3];\n    }\n    if (4 in baseRouterState) {\n        clone[4] = baseRouterState[4];\n    }\n    return clone;\n}\nfunction spawnPendingTask(routerState, prefetchData, prefetchHead, isPrefetchHeadPartial, segmentPath, scrollableSegmentsResult) {\n    // Create a task that will later be fulfilled by data from the server.\n    // Clone the prefetched route tree and the `refetch` marker to it. We'll send\n    // this to the server so it knows where to start rendering.\n    const dynamicRequestTree = patchRouterStateWithNewChildren(routerState, routerState[1]);\n    dynamicRequestTree[3] = 'refetch';\n    const newTask = {\n        route: routerState,\n        // Corresponds to the part of the route that will be rendered on the server.\n        node: createPendingCacheNode(routerState, prefetchData, prefetchHead, isPrefetchHeadPartial, segmentPath, scrollableSegmentsResult),\n        // Because this is non-null, and it gets propagated up through the parent\n        // tasks, the root task will know that it needs to perform a server request.\n        dynamicRequestTree,\n        children: null\n    };\n    return newTask;\n}\nfunction spawnReusedTask(reusedRouterState) {\n    // Create a task that reuses an existing segment, e.g. when reusing\n    // the current active segment in place of a default route.\n    return {\n        route: reusedRouterState,\n        node: null,\n        dynamicRequestTree: null,\n        children: null\n    };\n}\nfunction listenForDynamicRequest(task, responsePromise) {\n    responsePromise.then((param)=>{\n        let { flightData } = param;\n        if (typeof flightData === 'string') {\n            // Happens when navigating to page in `pages` from `app`. We shouldn't\n            // get here because should have already handled this during\n            // the prefetch.\n            return;\n        }\n        for (const normalizedFlightData of flightData){\n            const { segmentPath, tree: serverRouterState, seedData: dynamicData, head: dynamicHead } = normalizedFlightData;\n            if (!dynamicData) {\n                continue;\n            }\n            writeDynamicDataIntoPendingTask(task, segmentPath, serverRouterState, dynamicData, dynamicHead);\n        }\n        // Now that we've exhausted all the data we received from the server, if\n        // there are any remaining pending tasks in the tree, abort them now.\n        // If there's any missing data, it will trigger a lazy fetch.\n        abortTask(task, null);\n    }, (error)=>{\n        // This will trigger an error during render\n        abortTask(task, error);\n    });\n}\nfunction writeDynamicDataIntoPendingTask(rootTask, segmentPath, serverRouterState, dynamicData, dynamicHead) {\n    // The data sent by the server represents only a subtree of the app. We need\n    // to find the part of the task tree that matches the server response, and\n    // fulfill it using the dynamic data.\n    //\n    // segmentPath represents the parent path of subtree. It's a repeating pattern\n    // of parallel route key and segment:\n    //\n    //   [string, Segment, string, Segment, string, Segment, ...]\n    //\n    // Iterate through the path and finish any tasks that match this payload.\n    let task = rootTask;\n    for(let i = 0; i < segmentPath.length; i += 2){\n        const parallelRouteKey = segmentPath[i];\n        const segment = segmentPath[i + 1];\n        const taskChildren = task.children;\n        if (taskChildren !== null) {\n            const taskChild = taskChildren.get(parallelRouteKey);\n            if (taskChild !== undefined) {\n                const taskSegment = taskChild.route[0];\n                if ((0, _matchsegments.matchSegment)(segment, taskSegment)) {\n                    // Found a match for this task. Keep traversing down the task tree.\n                    task = taskChild;\n                    continue;\n                }\n            }\n        }\n        // We didn't find a child task that matches the server data. Exit. We won't\n        // abort the task, though, because a different FlightDataPath may be able to\n        // fulfill it (see loop in listenForDynamicRequest). We only abort tasks\n        // once we've run out of data.\n        return;\n    }\n    finishTaskUsingDynamicDataPayload(task, serverRouterState, dynamicData, dynamicHead);\n}\nfunction finishTaskUsingDynamicDataPayload(task, serverRouterState, dynamicData, dynamicHead) {\n    if (task.dynamicRequestTree === null) {\n        // Everything in this subtree is already complete. Bail out.\n        return;\n    }\n    // dynamicData may represent a larger subtree than the task. Before we can\n    // finish the task, we need to line them up.\n    const taskChildren = task.children;\n    const taskNode = task.node;\n    if (taskChildren === null) {\n        // We've reached the leaf node of the pending task. The server data tree\n        // lines up the pending Cache Node tree. We can now switch to the\n        // normal algorithm.\n        if (taskNode !== null) {\n            finishPendingCacheNode(taskNode, task.route, serverRouterState, dynamicData, dynamicHead);\n            // Set this to null to indicate that this task is now complete.\n            task.dynamicRequestTree = null;\n        }\n        return;\n    }\n    // The server returned more data than we need to finish the task. Skip over\n    // the extra segments until we reach the leaf task node.\n    const serverChildren = serverRouterState[1];\n    const dynamicDataChildren = dynamicData[2];\n    for(const parallelRouteKey in serverRouterState){\n        const serverRouterStateChild = serverChildren[parallelRouteKey];\n        const dynamicDataChild = dynamicDataChildren[parallelRouteKey];\n        const taskChild = taskChildren.get(parallelRouteKey);\n        if (taskChild !== undefined) {\n            const taskSegment = taskChild.route[0];\n            if ((0, _matchsegments.matchSegment)(serverRouterStateChild[0], taskSegment) && dynamicDataChild !== null && dynamicDataChild !== undefined) {\n                // Found a match for this task. Keep traversing down the task tree.\n                return finishTaskUsingDynamicDataPayload(taskChild, serverRouterStateChild, dynamicDataChild, dynamicHead);\n            }\n        }\n    // We didn't find a child task that matches the server data. We won't abort\n    // the task, though, because a different FlightDataPath may be able to\n    // fulfill it (see loop in listenForDynamicRequest). We only abort tasks\n    // once we've run out of data.\n    }\n}\nfunction createPendingCacheNode(routerState, prefetchData, prefetchHead, isPrefetchHeadPartial, segmentPath, scrollableSegmentsResult) {\n    const routerStateChildren = routerState[1];\n    const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null;\n    const parallelRoutes = new Map();\n    for(let parallelRouteKey in routerStateChildren){\n        const routerStateChild = routerStateChildren[parallelRouteKey];\n        const prefetchDataChild = prefetchDataChildren !== null ? prefetchDataChildren[parallelRouteKey] : null;\n        const segmentChild = routerStateChild[0];\n        const segmentPathChild = segmentPath.concat([\n            parallelRouteKey,\n            segmentChild\n        ]);\n        const segmentKeyChild = (0, _createroutercachekey.createRouterCacheKey)(segmentChild);\n        const newCacheNodeChild = createPendingCacheNode(routerStateChild, prefetchDataChild === undefined ? null : prefetchDataChild, prefetchHead, isPrefetchHeadPartial, segmentPathChild, scrollableSegmentsResult);\n        const newSegmentMapChild = new Map();\n        newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild);\n        parallelRoutes.set(parallelRouteKey, newSegmentMapChild);\n    }\n    // The head is assigned to every leaf segment delivered by the server. Based\n    // on corresponding logic in fill-lazy-items-till-leaf-with-head.ts\n    const isLeafSegment = parallelRoutes.size === 0;\n    if (isLeafSegment) {\n        // The segment path of every leaf segment (i.e. page) is collected into\n        // a result array. This is used by the LayoutRouter to scroll to ensure that\n        // new pages are visible after a navigation.\n        // TODO: We should use a string to represent the segment path instead of\n        // an array. We already use a string representation for the path when\n        // accessing the Segment Cache, so we can use the same one.\n        scrollableSegmentsResult.push(segmentPath);\n    }\n    const maybePrefetchRsc = prefetchData !== null ? prefetchData[1] : null;\n    const maybePrefetchLoading = prefetchData !== null ? prefetchData[3] : null;\n    return {\n        lazyData: null,\n        parallelRoutes: parallelRoutes,\n        prefetchRsc: maybePrefetchRsc !== undefined ? maybePrefetchRsc : null,\n        prefetchHead: isLeafSegment ? prefetchHead : [\n            null,\n            null\n        ],\n        // TODO: Technically, a loading boundary could contain dynamic data. We must\n        // have separate `loading` and `prefetchLoading` fields to handle this, like\n        // we do for the segment data and head.\n        loading: maybePrefetchLoading !== undefined ? maybePrefetchLoading : null,\n        // Create a deferred promise. This will be fulfilled once the dynamic\n        // response is received from the server.\n        rsc: createDeferredRsc(),\n        head: isLeafSegment ? createDeferredRsc() : null\n    };\n}\nfunction finishPendingCacheNode(cacheNode, taskState, serverState, dynamicData, dynamicHead) {\n    // Writes a dynamic response into an existing Cache Node tree. This does _not_\n    // create a new tree, it updates the existing tree in-place. So it must follow\n    // the Suspense rules of cache safety — it can resolve pending promises, but\n    // it cannot overwrite existing data. It can add segments to the tree (because\n    // a missing segment will cause the layout router to suspend).\n    // but it cannot delete them.\n    //\n    // We must resolve every promise in the tree, or else it will suspend\n    // indefinitely. If we did not receive data for a segment, we will resolve its\n    // data promise to `null` to trigger a lazy fetch during render.\n    const taskStateChildren = taskState[1];\n    const serverStateChildren = serverState[1];\n    const dataChildren = dynamicData[2];\n    // The router state that we traverse the tree with (taskState) is the same one\n    // that we used to construct the pending Cache Node tree. That way we're sure\n    // to resolve all the pending promises.\n    const parallelRoutes = cacheNode.parallelRoutes;\n    for(let parallelRouteKey in taskStateChildren){\n        const taskStateChild = taskStateChildren[parallelRouteKey];\n        const serverStateChild = serverStateChildren[parallelRouteKey];\n        const dataChild = dataChildren[parallelRouteKey];\n        const segmentMapChild = parallelRoutes.get(parallelRouteKey);\n        const taskSegmentChild = taskStateChild[0];\n        const taskSegmentKeyChild = (0, _createroutercachekey.createRouterCacheKey)(taskSegmentChild);\n        const cacheNodeChild = segmentMapChild !== undefined ? segmentMapChild.get(taskSegmentKeyChild) : undefined;\n        if (cacheNodeChild !== undefined) {\n            if (serverStateChild !== undefined && (0, _matchsegments.matchSegment)(taskSegmentChild, serverStateChild[0])) {\n                if (dataChild !== undefined && dataChild !== null) {\n                    // This is the happy path. Recursively update all the children.\n                    finishPendingCacheNode(cacheNodeChild, taskStateChild, serverStateChild, dataChild, dynamicHead);\n                } else {\n                    // The server never returned data for this segment. Trigger a lazy\n                    // fetch during render. This shouldn't happen because the Route Tree\n                    // and the Seed Data tree sent by the server should always be the same\n                    // shape when part of the same server response.\n                    abortPendingCacheNode(taskStateChild, cacheNodeChild, null);\n                }\n            } else {\n                // The server never returned data for this segment. Trigger a lazy\n                // fetch during render.\n                abortPendingCacheNode(taskStateChild, cacheNodeChild, null);\n            }\n        } else {\n        // The server response matches what was expected to receive, but there's\n        // no matching Cache Node in the task tree. This is a bug in the\n        // implementation because we should have created a node for every\n        // segment in the tree that's associated with this task.\n        }\n    }\n    // Use the dynamic data from the server to fulfill the deferred RSC promise\n    // on the Cache Node.\n    const rsc = cacheNode.rsc;\n    const dynamicSegmentData = dynamicData[1];\n    if (rsc === null) {\n        // This is a lazy cache node. We can overwrite it. This is only safe\n        // because we know that the LayoutRouter suspends if `rsc` is `null`.\n        cacheNode.rsc = dynamicSegmentData;\n    } else if (isDeferredRsc(rsc)) {\n        // This is a deferred RSC promise. We can fulfill it with the data we just\n        // received from the server. If it was already resolved by a different\n        // navigation, then this does nothing because we can't overwrite data.\n        rsc.resolve(dynamicSegmentData);\n    } else {\n    // This is not a deferred RSC promise, nor is it empty, so it must have\n    // been populated by a different navigation. We must not overwrite it.\n    }\n    // Check if this is a leaf segment. If so, it will have a `head` property with\n    // a pending promise that needs to be resolved with the dynamic head from\n    // the server.\n    const head = cacheNode.head;\n    if (isDeferredRsc(head)) {\n        head.resolve(dynamicHead);\n    }\n}\nfunction abortTask(task, error) {\n    const cacheNode = task.node;\n    if (cacheNode === null) {\n        // This indicates the task is already complete.\n        return;\n    }\n    const taskChildren = task.children;\n    if (taskChildren === null) {\n        // Reached the leaf task node. This is the root of a pending cache\n        // node tree.\n        abortPendingCacheNode(task.route, cacheNode, error);\n    } else {\n        // This is an intermediate task node. Keep traversing until we reach a\n        // task node with no children. That will be the root of the cache node tree\n        // that needs to be resolved.\n        for (const taskChild of taskChildren.values()){\n            abortTask(taskChild, error);\n        }\n    }\n    // Set this to null to indicate that this task is now complete.\n    task.dynamicRequestTree = null;\n}\nfunction abortPendingCacheNode(routerState, cacheNode, error) {\n    // For every pending segment in the tree, resolve its `rsc` promise to `null`\n    // to trigger a lazy fetch during render.\n    //\n    // Or, if an error object is provided, it will error instead.\n    const routerStateChildren = routerState[1];\n    const parallelRoutes = cacheNode.parallelRoutes;\n    for(let parallelRouteKey in routerStateChildren){\n        const routerStateChild = routerStateChildren[parallelRouteKey];\n        const segmentMapChild = parallelRoutes.get(parallelRouteKey);\n        if (segmentMapChild === undefined) {\n            continue;\n        }\n        const segmentChild = routerStateChild[0];\n        const segmentKeyChild = (0, _createroutercachekey.createRouterCacheKey)(segmentChild);\n        const cacheNodeChild = segmentMapChild.get(segmentKeyChild);\n        if (cacheNodeChild !== undefined) {\n            abortPendingCacheNode(routerStateChild, cacheNodeChild, error);\n        } else {\n        // This shouldn't happen because we're traversing the same tree that was\n        // used to construct the cache nodes in the first place.\n        }\n    }\n    const rsc = cacheNode.rsc;\n    if (isDeferredRsc(rsc)) {\n        if (error === null) {\n            // This will trigger a lazy fetch during render.\n            rsc.resolve(null);\n        } else {\n            // This will trigger an error during rendering.\n            rsc.reject(error);\n        }\n    }\n    // Check if this is a leaf segment. If so, it will have a `head` property with\n    // a pending promise that needs to be resolved. If an error was provided, we\n    // will not resolve it with an error, since this is rendered at the root of\n    // the app. We want the segment to error, not the entire app.\n    const head = cacheNode.head;\n    if (isDeferredRsc(head)) {\n        head.resolve(null);\n    }\n}\nfunction updateCacheNodeOnPopstateRestoration(oldCacheNode, routerState) {\n    // A popstate navigation reads data from the local cache. It does not issue\n    // new network requests (unless the cache entries have been evicted). So, we\n    // update the cache to drop the prefetch data for any segment whose dynamic\n    // data was already received. This prevents an unnecessary flash back to PPR\n    // state during a back/forward navigation.\n    //\n    // This function clones the entire cache node tree and sets the `prefetchRsc`\n    // field to `null` to prevent it from being rendered. We can't mutate the node\n    // in place because this is a concurrent data structure.\n    const routerStateChildren = routerState[1];\n    const oldParallelRoutes = oldCacheNode.parallelRoutes;\n    const newParallelRoutes = new Map(oldParallelRoutes);\n    for(let parallelRouteKey in routerStateChildren){\n        const routerStateChild = routerStateChildren[parallelRouteKey];\n        const segmentChild = routerStateChild[0];\n        const segmentKeyChild = (0, _createroutercachekey.createRouterCacheKey)(segmentChild);\n        const oldSegmentMapChild = oldParallelRoutes.get(parallelRouteKey);\n        if (oldSegmentMapChild !== undefined) {\n            const oldCacheNodeChild = oldSegmentMapChild.get(segmentKeyChild);\n            if (oldCacheNodeChild !== undefined) {\n                const newCacheNodeChild = updateCacheNodeOnPopstateRestoration(oldCacheNodeChild, routerStateChild);\n                const newSegmentMapChild = new Map(oldSegmentMapChild);\n                newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild);\n                newParallelRoutes.set(parallelRouteKey, newSegmentMapChild);\n            }\n        }\n    }\n    // Only show prefetched data if the dynamic data is still pending.\n    //\n    // Tehnically, what we're actually checking is whether the dynamic network\n    // response was received. But since it's a streaming response, this does not\n    // mean that all the dynamic data has fully streamed in. It just means that\n    // _some_ of the dynamic data was received. But as a heuristic, we assume that\n    // the rest dynamic data will stream in quickly, so it's still better to skip\n    // the prefetch state.\n    const rsc = oldCacheNode.rsc;\n    const shouldUsePrefetch = isDeferredRsc(rsc) && rsc.status === 'pending';\n    return {\n        lazyData: null,\n        rsc,\n        head: oldCacheNode.head,\n        prefetchHead: shouldUsePrefetch ? oldCacheNode.prefetchHead : [\n            null,\n            null\n        ],\n        prefetchRsc: shouldUsePrefetch ? oldCacheNode.prefetchRsc : null,\n        loading: oldCacheNode.loading,\n        // These are the cloned children we computed above\n        parallelRoutes: newParallelRoutes\n    };\n}\nconst DEFERRED = Symbol();\n// This type exists to distinguish a DeferredRsc from a Flight promise. It's a\n// compromise to avoid adding an extra field on every Cache Node, which would be\n// awkward because the pre-PPR parts of codebase would need to account for it,\n// too. We can remove it once type Cache Node type is more settled.\nfunction isDeferredRsc(value) {\n    return value && value.tag === DEFERRED;\n}\nfunction createDeferredRsc() {\n    let resolve;\n    let reject;\n    const pendingRsc = new Promise((res, rej)=>{\n        resolve = res;\n        reject = rej;\n    });\n    pendingRsc.status = 'pending';\n    pendingRsc.resolve = (value)=>{\n        if (pendingRsc.status === 'pending') {\n            const fulfilledRsc = pendingRsc;\n            fulfilledRsc.status = 'fulfilled';\n            fulfilledRsc.value = value;\n            resolve(value);\n        }\n    };\n    pendingRsc.reject = (error)=>{\n        if (pendingRsc.status === 'pending') {\n            const rejectedRsc = pendingRsc;\n            rejectedRsc.status = 'rejected';\n            rejectedRsc.reason = error;\n            reject(error);\n        }\n    };\n    pendingRsc.tag = DEFERRED;\n    return pendingRsc;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=ppr-navigations.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/ppr-navigations.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    STATIC_STALETIME_MS: function() {\n        return STATIC_STALETIME_MS;\n    },\n    createSeededPrefetchCacheEntry: function() {\n        return createSeededPrefetchCacheEntry;\n    },\n    getOrCreatePrefetchCacheEntry: function() {\n        return getOrCreatePrefetchCacheEntry;\n    },\n    prunePrefetchCache: function() {\n        return prunePrefetchCache;\n    }\n});\nconst _fetchserverresponse = __webpack_require__(/*! ./fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst INTERCEPTION_CACHE_KEY_MARKER = '%';\n/**\n * Creates a cache key for the router prefetch cache\n *\n * @param url - The URL being navigated to\n * @param nextUrl - an internal URL, primarily used for handling rewrites. Defaults to '/'.\n * @return The generated prefetch cache key.\n */ function createPrefetchCacheKeyImpl(url, includeSearchParams, prefix) {\n    // Initially we only use the pathname as the cache key. We don't want to include\n    // search params so that multiple URLs with the same search parameter can re-use\n    // loading states.\n    let pathnameFromUrl = url.pathname;\n    // RSC responses can differ based on search params, specifically in the case where we aren't\n    // returning a partial response (ie with `PrefetchKind.AUTO`).\n    // In the auto case, since loading.js & layout.js won't have access to search params,\n    // we can safely re-use that cache entry. But for full prefetches, we should not\n    // re-use the cache entry as the response may differ.\n    if (includeSearchParams) {\n        // if we have a full prefetch, we can include the search param in the key,\n        // as we'll be getting back a full response. The server might have read the search\n        // params when generating the full response.\n        pathnameFromUrl += url.search;\n    }\n    if (prefix) {\n        return \"\" + prefix + INTERCEPTION_CACHE_KEY_MARKER + pathnameFromUrl;\n    }\n    return pathnameFromUrl;\n}\nfunction createPrefetchCacheKey(url, kind, nextUrl) {\n    return createPrefetchCacheKeyImpl(url, kind === _routerreducertypes.PrefetchKind.FULL, nextUrl);\n}\nfunction getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing) {\n    if (kind === void 0) kind = _routerreducertypes.PrefetchKind.TEMPORARY;\n    // We first check if there's a more specific interception route prefetch entry\n    // This is because when we detect a prefetch that corresponds with an interception route, we prefix it with nextUrl (see `createPrefetchCacheKey`)\n    // to avoid conflicts with other pages that may have the same URL but render different things depending on the `Next-URL` header.\n    for (const maybeNextUrl of [\n        nextUrl,\n        null\n    ]){\n        const cacheKeyWithParams = createPrefetchCacheKeyImpl(url, true, maybeNextUrl);\n        const cacheKeyWithoutParams = createPrefetchCacheKeyImpl(url, false, maybeNextUrl);\n        // First, we check if we have a cache entry that exactly matches the URL\n        const cacheKeyToUse = url.search ? cacheKeyWithParams : cacheKeyWithoutParams;\n        const existingEntry = prefetchCache.get(cacheKeyToUse);\n        if (existingEntry && allowAliasing) {\n            // We know we're returning an aliased entry when the pathname matches but the search params don't,\n            const isAliased = existingEntry.url.pathname === url.pathname && existingEntry.url.search !== url.search;\n            if (isAliased) {\n                return {\n                    ...existingEntry,\n                    aliased: true\n                };\n            }\n            return existingEntry;\n        }\n        // If the request contains search params, and we're not doing a full prefetch, we can return the\n        // param-less entry if it exists.\n        // This is technically covered by the check at the bottom of this function, which iterates over cache entries,\n        // but lets us arrive there quicker in the param-full case.\n        const entryWithoutParams = prefetchCache.get(cacheKeyWithoutParams);\n        if (false) {}\n    }\n    // If we've gotten to this point, we didn't find a specific cache entry that matched\n    // the request URL.\n    // We attempt a partial match by checking if there's a cache entry with the same pathname.\n    // Regardless of what we find, since it doesn't correspond with the requested URL, we'll mark it \"aliased\".\n    // This will signal to the router that it should only apply the loading state on the prefetched data.\n    if (false) {}\n    return undefined;\n}\nfunction getOrCreatePrefetchCacheEntry(param) {\n    let { url, nextUrl, tree, prefetchCache, kind, allowAliasing = true } = param;\n    const existingCacheEntry = getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing);\n    if (existingCacheEntry) {\n        // Grab the latest status of the cache entry and update it\n        existingCacheEntry.status = getPrefetchEntryCacheStatus(existingCacheEntry);\n        // when `kind` is provided, an explicit prefetch was requested.\n        // if the requested prefetch is \"full\" and the current cache entry wasn't, we want to re-prefetch with the new intent\n        const switchedToFullPrefetch = existingCacheEntry.kind !== _routerreducertypes.PrefetchKind.FULL && kind === _routerreducertypes.PrefetchKind.FULL;\n        if (switchedToFullPrefetch) {\n            // If we switched to a full prefetch, validate that the existing cache entry contained partial data.\n            // It's possible that the cache entry was seeded with full data but has a cache type of \"auto\" (ie when cache entries\n            // are seeded but without a prefetch intent)\n            existingCacheEntry.data.then((prefetchResponse)=>{\n                const isFullPrefetch = Array.isArray(prefetchResponse.flightData) && prefetchResponse.flightData.some((flightData)=>{\n                    // If we started rendering from the root and we returned RSC data (seedData), we already had a full prefetch.\n                    return flightData.isRootRender && flightData.seedData !== null;\n                });\n                if (!isFullPrefetch) {\n                    return createLazyPrefetchEntry({\n                        tree,\n                        url,\n                        nextUrl,\n                        prefetchCache,\n                        // If we didn't get an explicit prefetch kind, we want to set a temporary kind\n                        // rather than assuming the same intent as the previous entry, to be consistent with how we\n                        // lazily create prefetch entries when intent is left unspecified.\n                        kind: kind != null ? kind : _routerreducertypes.PrefetchKind.TEMPORARY\n                    });\n                }\n            });\n        }\n        // If the existing cache entry was marked as temporary, it means it was lazily created when attempting to get an entry,\n        // where we didn't have the prefetch intent. Now that we have the intent (in `kind`), we want to update the entry to the more accurate kind.\n        if (kind && existingCacheEntry.kind === _routerreducertypes.PrefetchKind.TEMPORARY) {\n            existingCacheEntry.kind = kind;\n        }\n        // We've determined that the existing entry we found is still valid, so we return it.\n        return existingCacheEntry;\n    }\n    // If we didn't return an entry, create a new one.\n    return createLazyPrefetchEntry({\n        tree,\n        url,\n        nextUrl,\n        prefetchCache,\n        kind: kind || _routerreducertypes.PrefetchKind.TEMPORARY\n    });\n}\n/*\n * Used to take an existing cache entry and prefix it with the nextUrl, if it exists.\n * This ensures that we don't have conflicting cache entries for the same URL (as is the case with route interception).\n */ function prefixExistingPrefetchCacheEntry(param) {\n    let { url, nextUrl, prefetchCache, existingCacheKey } = param;\n    const existingCacheEntry = prefetchCache.get(existingCacheKey);\n    if (!existingCacheEntry) {\n        // no-op -- there wasn't an entry to move\n        return;\n    }\n    const newCacheKey = createPrefetchCacheKey(url, existingCacheEntry.kind, nextUrl);\n    prefetchCache.set(newCacheKey, {\n        ...existingCacheEntry,\n        key: newCacheKey\n    });\n    prefetchCache.delete(existingCacheKey);\n    return newCacheKey;\n}\nfunction createSeededPrefetchCacheEntry(param) {\n    let { nextUrl, tree, prefetchCache, url, data, kind } = param;\n    // The initial cache entry technically includes full data, but it isn't explicitly prefetched -- we just seed the\n    // prefetch cache so that we can skip an extra prefetch request later, since we already have the data.\n    // if the prefetch corresponds with an interception route, we use the nextUrl to prefix the cache key\n    const prefetchCacheKey = data.couldBeIntercepted ? createPrefetchCacheKey(url, kind, nextUrl) : createPrefetchCacheKey(url, kind);\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data: Promise.resolve(data),\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: Date.now(),\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\n/**\n * Creates a prefetch entry entry and enqueues a fetch request to retrieve the data.\n */ function createLazyPrefetchEntry(param) {\n    let { url, kind, tree, nextUrl, prefetchCache } = param;\n    const prefetchCacheKey = createPrefetchCacheKey(url, kind);\n    // initiates the fetch request for the prefetch and attaches a listener\n    // to the promise to update the prefetch cache entry when the promise resolves (if necessary)\n    const data = _prefetchreducer.prefetchQueue.enqueue(()=>(0, _fetchserverresponse.fetchServerResponse)(url, {\n            flightRouterState: tree,\n            nextUrl,\n            prefetchKind: kind\n        }).then((prefetchResponse)=>{\n            // TODO: `fetchServerResponse` should be more tighly coupled to these prefetch cache operations\n            // to avoid drift between this cache key prefixing logic\n            // (which is currently directly influenced by the server response)\n            let newCacheKey;\n            if (prefetchResponse.couldBeIntercepted) {\n                // Determine if we need to prefix the cache key with the nextUrl\n                newCacheKey = prefixExistingPrefetchCacheEntry({\n                    url,\n                    existingCacheKey: prefetchCacheKey,\n                    nextUrl,\n                    prefetchCache\n                });\n            }\n            // If the prefetch was a cache hit, we want to update the existing cache entry to reflect that it was a full prefetch.\n            // This is because we know that a static response will contain the full RSC payload, and can be updated to respect the `static`\n            // staleTime.\n            if (prefetchResponse.prerendered) {\n                const existingCacheEntry = prefetchCache.get(newCacheKey != null ? newCacheKey : prefetchCacheKey);\n                if (existingCacheEntry) {\n                    existingCacheEntry.kind = _routerreducertypes.PrefetchKind.FULL;\n                    if (prefetchResponse.staleTime !== -1) {\n                        // This is the stale time that was collected by the server during\n                        // static generation. Use this in place of the default stale time.\n                        existingCacheEntry.staleTime = prefetchResponse.staleTime;\n                    }\n                }\n            }\n            return prefetchResponse;\n        }));\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data,\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: null,\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\nfunction prunePrefetchCache(prefetchCache) {\n    for (const [href, prefetchCacheEntry] of prefetchCache){\n        if (getPrefetchEntryCacheStatus(prefetchCacheEntry) === _routerreducertypes.PrefetchCacheEntryStatus.expired) {\n            prefetchCache.delete(href);\n        }\n    }\n}\n// These values are set by `define-env-plugin` (based on `nextConfig.experimental.staleTimes`)\n// and default to 5 minutes (static) / 0 seconds (dynamic)\nconst DYNAMIC_STALETIME_MS = Number(\"0\") * 1000;\nconst STATIC_STALETIME_MS = Number(\"300\") * 1000;\nfunction getPrefetchEntryCacheStatus(param) {\n    let { kind, prefetchTime, lastUsedTime, staleTime } = param;\n    if (staleTime !== -1) {\n        // `staleTime` is the value sent by the server during static generation.\n        // When this is available, it takes precedence over any of the heuristics\n        // that follow.\n        //\n        // TODO: When PPR is enabled, the server will *always* return a stale time\n        // when prefetching. We should never use a prefetch entry that hasn't yet\n        // received data from the server. So the only two cases should be 1) we use\n        // the server-generated stale time 2) the unresolved entry is discarded.\n        return Date.now() < prefetchTime + staleTime ? _routerreducertypes.PrefetchCacheEntryStatus.fresh : _routerreducertypes.PrefetchCacheEntryStatus.stale;\n    }\n    // We will re-use the cache entry data for up to the `dynamic` staletime window.\n    if (Date.now() < (lastUsedTime != null ? lastUsedTime : prefetchTime) + DYNAMIC_STALETIME_MS) {\n        return lastUsedTime ? _routerreducertypes.PrefetchCacheEntryStatus.reusable : _routerreducertypes.PrefetchCacheEntryStatus.fresh;\n    }\n    // For \"auto\" prefetching, we'll re-use only the loading boundary for up to `static` staletime window.\n    // A stale entry will only re-use the `loading` boundary, not the full data.\n    // This will trigger a \"lazy fetch\" for the full data.\n    if (kind === _routerreducertypes.PrefetchKind.AUTO) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.stale;\n        }\n    }\n    // for \"full\" prefetching, we'll re-use the cache entry data for up to `static` staletime window.\n    if (kind === _routerreducertypes.PrefetchKind.FULL) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.reusable;\n        }\n    }\n    return _routerreducertypes.PrefetchCacheEntryStatus.expired;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=prefetch-cache-utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js ***!
  \************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"findHeadInCache\", ({\n    enumerable: true,\n    get: function() {\n        return findHeadInCache;\n    }\n}));\nconst _createroutercachekey = __webpack_require__(/*! ../create-router-cache-key */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js\");\nfunction findHeadInCache(cache, parallelRoutes) {\n    return findHeadInCacheImpl(cache, parallelRoutes, '');\n}\nfunction findHeadInCacheImpl(cache, parallelRoutes, keyPrefix) {\n    const isLastItem = Object.keys(parallelRoutes).length === 0;\n    if (isLastItem) {\n        // Returns the entire Cache Node of the segment whose head we will render.\n        return [\n            cache,\n            keyPrefix\n        ];\n    }\n    // First try the 'children' parallel route if it exists\n    // when starting from the \"root\", this corresponds with the main page component\n    if (parallelRoutes.children) {\n        const [segment, childParallelRoutes] = parallelRoutes.children;\n        const childSegmentMap = cache.parallelRoutes.get('children');\n        if (childSegmentMap) {\n            const cacheKey = (0, _createroutercachekey.createRouterCacheKey)(segment);\n            const cacheNode = childSegmentMap.get(cacheKey);\n            if (cacheNode) {\n                const item = findHeadInCacheImpl(cacheNode, childParallelRoutes, keyPrefix + '/' + cacheKey);\n                if (item) return item;\n            }\n        }\n    }\n    // if we didn't find metadata in the page slot, check the other parallel routes\n    for(const key in parallelRoutes){\n        if (key === 'children') continue; // already checked above\n        const [segment, childParallelRoutes] = parallelRoutes[key];\n        const childSegmentMap = cache.parallelRoutes.get(key);\n        if (!childSegmentMap) {\n            continue;\n        }\n        const cacheKey = (0, _createroutercachekey.createRouterCacheKey)(segment);\n        const cacheNode = childSegmentMap.get(cacheKey);\n        if (!cacheNode) {\n            continue;\n        }\n        const item = findHeadInCacheImpl(cacheNode, childParallelRoutes, keyPrefix + '/' + cacheKey);\n        if (item) {\n            return item;\n        }\n    }\n    return null;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=find-head-in-cache.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js ***!
  \***********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getSegmentValue\", ({\n    enumerable: true,\n    get: function() {\n        return getSegmentValue;\n    }\n}));\nfunction getSegmentValue(segment) {\n    return Array.isArray(segment) ? segment[1] : segment;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-segment-value.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcmVkdWNlcnMvZ2V0LXNlZ21lbnQtdmFsdWUuanMiLCJtYXBwaW5ncyI6Ijs7OzttREFFZ0JBOzs7ZUFBQUE7OztBQUFULFNBQVNBLGdCQUFnQkMsT0FBZ0I7SUFDOUMsT0FBT0MsTUFBTUMsT0FBTyxDQUFDRixXQUFXQSxPQUFPLENBQUMsRUFBRSxHQUFHQTtBQUMvQyIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyb3V0ZXItcmVkdWNlclxccmVkdWNlcnNcXGdldC1zZWdtZW50LXZhbHVlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgU2VnbWVudCB9IGZyb20gJy4uLy4uLy4uLy4uL3NlcnZlci9hcHAtcmVuZGVyL3R5cGVzJ1xuXG5leHBvcnQgZnVuY3Rpb24gZ2V0U2VnbWVudFZhbHVlKHNlZ21lbnQ6IFNlZ21lbnQpIHtcbiAgcmV0dXJuIEFycmF5LmlzQXJyYXkoc2VnbWVudCkgPyBzZWdtZW50WzFdIDogc2VnbWVudFxufVxuIl0sIm5hbWVzIjpbImdldFNlZ21lbnRWYWx1ZSIsInNlZ21lbnQiLCJBcnJheSIsImlzQXJyYXkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js ***!
  \********************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hasInterceptionRouteInCurrentTree\", ({\n    enumerable: true,\n    get: function() {\n        return hasInterceptionRouteInCurrentTree;\n    }\n}));\nconst _interceptionroutes = __webpack_require__(/*! ../../../../shared/lib/router/utils/interception-routes */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/interception-routes.js\");\nfunction hasInterceptionRouteInCurrentTree(param) {\n    let [segment, parallelRoutes] = param;\n    // If we have a dynamic segment, it's marked as an interception route by the presence of the `i` suffix.\n    if (Array.isArray(segment) && (segment[2] === 'di' || segment[2] === 'ci')) {\n        return true;\n    }\n    // If segment is not an array, apply the existing string-based check\n    if (typeof segment === 'string' && (0, _interceptionroutes.isInterceptionRouteAppPath)(segment)) {\n        return true;\n    }\n    // Iterate through parallelRoutes if they exist\n    if (parallelRoutes) {\n        for(const key in parallelRoutes){\n            if (hasInterceptionRouteInCurrentTree(parallelRoutes[key])) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=has-interception-route-in-current-tree.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js ***!
  \*************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hmrRefreshReducer\", ({\n    enumerable: true,\n    get: function() {\n        return hmrRefreshReducer;\n    }\n}));\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(state, action) {\n    const { origin } = action;\n    const mutable = {};\n    const href = state.canonicalUrl;\n    mutable.preserveCustomHistoryState = false;\n    const cache = (0, _approuter.createEmptyCacheNode)();\n    // If the current tree was intercepted, the nextUrl should be included in the request.\n    // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n    const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree);\n    // TODO-APP: verify that `href` is not an external url.\n    // Fetch data from the root of the tree.\n    cache.lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(href, origin), {\n        flightRouterState: [\n            state.tree[0],\n            state.tree[1],\n            state.tree[2],\n            'refetch'\n        ],\n        nextUrl: includeNextUrl ? state.nextUrl : null,\n        isHmrRefresh: true\n    });\n    return cache.lazyData.then((param)=>{\n        let { flightData, canonicalUrl: canonicalUrlOverride } = param;\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === 'string') {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        // Remove cache.lazyData as it has been resolved at this point.\n        cache.lazyData = null;\n        let currentTree = state.tree;\n        let currentCache = state.cache;\n        for (const normalizedFlightData of flightData){\n            const { tree: treePatch, isRootRender } = normalizedFlightData;\n            if (!isRootRender) {\n                // TODO-APP: handle this case better\n                console.log('REFRESH FAILED');\n                return state;\n            }\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                ''\n            ], currentTree, treePatch, state.canonicalUrl);\n            if (newTree === null) {\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, href, state.pushRef.pendingPush);\n            }\n            const canonicalUrlOverrideHref = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : undefined;\n            if (canonicalUrlOverride) {\n                mutable.canonicalUrl = canonicalUrlOverrideHref;\n            }\n            const applied = (0, _applyflightdata.applyFlightData)(currentCache, cache, normalizedFlightData);\n            if (applied) {\n                mutable.cache = cache;\n                currentCache = cache;\n            }\n            mutable.patchedTree = newTree;\n            mutable.canonicalUrl = href;\n            currentTree = newTree;\n        }\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nfunction hmrRefreshReducerNoop(state, _action) {\n    return state;\n}\nconst hmrRefreshReducer =  false ? 0 : hmrRefreshReducerImpl;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hmr-refresh-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcmVkdWNlcnMvaG1yLXJlZnJlc2gtcmVkdWNlci5qcyIsIm1hcHBpbmdzIjoiOzs7O3FEQTZIYUE7OztlQUFBQTs7O2lEQTdIdUI7K0NBQ0Y7eURBQ1U7eURBQ0E7NkNBT1Y7MkNBQ0o7NkNBQ0U7dUNBRUs7bURBQ0M7K0RBQ1k7QUFFbEQsd0ZBQXdGO0FBQ3hGLFNBQVNDLHNCQUNQQyxLQUEyQixFQUMzQkMsTUFBd0I7SUFFeEIsTUFBTSxFQUFFQyxNQUFNLEVBQUUsR0FBR0Q7SUFDbkIsTUFBTUUsVUFBbUIsQ0FBQztJQUMxQixNQUFNQyxPQUFPSixNQUFNSyxZQUFZO0lBRS9CRixRQUFRRywwQkFBMEIsR0FBRztJQUVyQyxNQUFNQyxRQUFtQkMsQ0FBQUEsR0FBQUEsV0FBQUEsb0JBQUFBO0lBQ3pCLHNGQUFzRjtJQUN0RixzSEFBc0g7SUFDdEgsTUFBTUMsaUJBQWlCQyxDQUFBQSxHQUFBQSxtQ0FBQUEsaUNBQWlDLEVBQUNWLE1BQU1XLElBQUk7SUFFbkUsdURBQXVEO0lBQ3ZELHdDQUF3QztJQUN4Q0osTUFBTUssUUFBUSxHQUFHQyxDQUFBQSxHQUFBQSxxQkFBQUEsbUJBQUFBLEVBQW9CLElBQUlDLElBQUlWLE1BQU1GLFNBQVM7UUFDMURhLG1CQUFtQjtZQUFDZixNQUFNVyxJQUFJLENBQUMsRUFBRTtZQUFFWCxNQUFNVyxJQUFJLENBQUMsRUFBRTtZQUFFWCxNQUFNVyxJQUFJLENBQUMsRUFBRTtZQUFFO1NBQVU7UUFDM0VLLFNBQVNQLGlCQUFpQlQsTUFBTWdCLE9BQU8sR0FBRztRQUMxQ0MsY0FBYztJQUNoQjtJQUVBLE9BQU9WLE1BQU1LLFFBQVEsQ0FBQ00sSUFBSSxDQUN4QjtZQUFDLEVBQUVDLFVBQVUsRUFBRWQsY0FBY2Usb0JBQW9CLEVBQUU7UUFDakQsNERBQTREO1FBQzVELElBQUksT0FBT0QsZUFBZSxVQUFVO1lBQ2xDLE9BQU9FLENBQUFBLEdBQUFBLGlCQUFBQSxpQkFBQUEsRUFDTHJCLE9BQ0FHLFNBQ0FnQixZQUNBbkIsTUFBTXNCLE9BQU8sQ0FBQ0MsV0FBVztRQUU3QjtRQUVBLCtEQUErRDtRQUMvRGhCLE1BQU1LLFFBQVEsR0FBRztRQUVqQixJQUFJWSxjQUFjeEIsTUFBTVcsSUFBSTtRQUM1QixJQUFJYyxlQUFlekIsTUFBTU8sS0FBSztRQUU5QixLQUFLLE1BQU1tQix3QkFBd0JQLFdBQVk7WUFDN0MsTUFBTSxFQUFFUixNQUFNZ0IsU0FBUyxFQUFFQyxZQUFZLEVBQUUsR0FBR0Y7WUFDMUMsSUFBSSxDQUFDRSxjQUFjO2dCQUNqQixvQ0FBb0M7Z0JBQ3BDQyxRQUFRQyxHQUFHLENBQUM7Z0JBQ1osT0FBTzlCO1lBQ1Q7WUFFQSxNQUFNK0IsVUFBVUMsQ0FBQUEsR0FBQUEsNkJBQUFBLDJCQUFBQSxFQUNkO2dCQUNDO2FBQUcsRUFDSlIsYUFDQUcsV0FDQTNCLE1BQU1LLFlBQVk7WUFHcEIsSUFBSTBCLFlBQVksTUFBTTtnQkFDcEIsT0FBT0UsQ0FBQUEsR0FBQUEsdUJBQUFBLHFCQUFBQSxFQUFzQmpDLE9BQU9DLFFBQVEwQjtZQUM5QztZQUVBLElBQUlPLENBQUFBLEdBQUFBLDZCQUFBQSwyQkFBQUEsRUFBNEJWLGFBQWFPLFVBQVU7Z0JBQ3JELE9BQU9WLENBQUFBLEdBQUFBLGlCQUFBQSxpQkFBQUEsRUFDTHJCLE9BQ0FHLFNBQ0FDLE1BQ0FKLE1BQU1zQixPQUFPLENBQUNDLFdBQVc7WUFFN0I7WUFFQSxNQUFNWSwyQkFBMkJmLHVCQUM3QmdCLENBQUFBLEdBQUFBLG1CQUFBQSxpQkFBQUEsRUFBa0JoQix3QkFDbEJpQjtZQUVKLElBQUlqQixzQkFBc0I7Z0JBQ3hCakIsUUFBUUUsWUFBWSxHQUFHOEI7WUFDekI7WUFDQSxNQUFNRyxVQUFVQyxDQUFBQSxHQUFBQSxpQkFBQUEsZUFBQUEsRUFDZGQsY0FDQWxCLE9BQ0FtQjtZQUdGLElBQUlZLFNBQVM7Z0JBQ1huQyxRQUFRSSxLQUFLLEdBQUdBO2dCQUNoQmtCLGVBQWVsQjtZQUNqQjtZQUVBSixRQUFRcUMsV0FBVyxHQUFHVDtZQUN0QjVCLFFBQVFFLFlBQVksR0FBR0Q7WUFFdkJvQixjQUFjTztRQUNoQjtRQUNBLE9BQU9VLENBQUFBLEdBQUFBLGVBQUFBLGFBQUFBLEVBQWN6QyxPQUFPRztJQUM5QixHQUNBLElBQU1IO0FBRVY7QUFFQSxTQUFTMEMsc0JBQ1AxQyxLQUEyQixFQUMzQjJDLE9BQXlCO0lBRXpCLE9BQU8zQztBQUNUO0FBRU8sTUFBTUYsb0JBQ1g4QyxNQUFvQixHQUNoQkYsQ0FBcUJBLEdBQ3JCM0MiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xccm91dGVyLXJlZHVjZXJcXHJlZHVjZXJzXFxobXItcmVmcmVzaC1yZWR1Y2VyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZldGNoU2VydmVyUmVzcG9uc2UgfSBmcm9tICcuLi9mZXRjaC1zZXJ2ZXItcmVzcG9uc2UnXG5pbXBvcnQgeyBjcmVhdGVIcmVmRnJvbVVybCB9IGZyb20gJy4uL2NyZWF0ZS1ocmVmLWZyb20tdXJsJ1xuaW1wb3J0IHsgYXBwbHlSb3V0ZXJTdGF0ZVBhdGNoVG9UcmVlIH0gZnJvbSAnLi4vYXBwbHktcm91dGVyLXN0YXRlLXBhdGNoLXRvLXRyZWUnXG5pbXBvcnQgeyBpc05hdmlnYXRpbmdUb05ld1Jvb3RMYXlvdXQgfSBmcm9tICcuLi9pcy1uYXZpZ2F0aW5nLXRvLW5ldy1yb290LWxheW91dCdcbmltcG9ydCB0eXBlIHtcbiAgUmVhZG9ubHlSZWR1Y2VyU3RhdGUsXG4gIFJlZHVjZXJTdGF0ZSxcbiAgSG1yUmVmcmVzaEFjdGlvbixcbiAgTXV0YWJsZSxcbn0gZnJvbSAnLi4vcm91dGVyLXJlZHVjZXItdHlwZXMnXG5pbXBvcnQgeyBoYW5kbGVFeHRlcm5hbFVybCB9IGZyb20gJy4vbmF2aWdhdGUtcmVkdWNlcidcbmltcG9ydCB7IGhhbmRsZU11dGFibGUgfSBmcm9tICcuLi9oYW5kbGUtbXV0YWJsZSdcbmltcG9ydCB7IGFwcGx5RmxpZ2h0RGF0YSB9IGZyb20gJy4uL2FwcGx5LWZsaWdodC1kYXRhJ1xuaW1wb3J0IHR5cGUgeyBDYWNoZU5vZGUgfSBmcm9tICcuLi8uLi8uLi8uLi9zaGFyZWQvbGliL2FwcC1yb3V0ZXItY29udGV4dC5zaGFyZWQtcnVudGltZSdcbmltcG9ydCB7IGNyZWF0ZUVtcHR5Q2FjaGVOb2RlIH0gZnJvbSAnLi4vLi4vYXBwLXJvdXRlcidcbmltcG9ydCB7IGhhbmRsZVNlZ21lbnRNaXNtYXRjaCB9IGZyb20gJy4uL2hhbmRsZS1zZWdtZW50LW1pc21hdGNoJ1xuaW1wb3J0IHsgaGFzSW50ZXJjZXB0aW9uUm91dGVJbkN1cnJlbnRUcmVlIH0gZnJvbSAnLi9oYXMtaW50ZXJjZXB0aW9uLXJvdXRlLWluLWN1cnJlbnQtdHJlZSdcblxuLy8gQSB2ZXJzaW9uIG9mIHJlZnJlc2ggcmVkdWNlciB0aGF0IGtlZXBzIHRoZSBjYWNoZSBhcm91bmQgaW5zdGVhZCBvZiB3aXBpbmcgYWxsIG9mIGl0LlxuZnVuY3Rpb24gaG1yUmVmcmVzaFJlZHVjZXJJbXBsKFxuICBzdGF0ZTogUmVhZG9ubHlSZWR1Y2VyU3RhdGUsXG4gIGFjdGlvbjogSG1yUmVmcmVzaEFjdGlvblxuKTogUmVkdWNlclN0YXRlIHtcbiAgY29uc3QgeyBvcmlnaW4gfSA9IGFjdGlvblxuICBjb25zdCBtdXRhYmxlOiBNdXRhYmxlID0ge31cbiAgY29uc3QgaHJlZiA9IHN0YXRlLmNhbm9uaWNhbFVybFxuXG4gIG11dGFibGUucHJlc2VydmVDdXN0b21IaXN0b3J5U3RhdGUgPSBmYWxzZVxuXG4gIGNvbnN0IGNhY2hlOiBDYWNoZU5vZGUgPSBjcmVhdGVFbXB0eUNhY2hlTm9kZSgpXG4gIC8vIElmIHRoZSBjdXJyZW50IHRyZWUgd2FzIGludGVyY2VwdGVkLCB0aGUgbmV4dFVybCBzaG91bGQgYmUgaW5jbHVkZWQgaW4gdGhlIHJlcXVlc3QuXG4gIC8vIFRoaXMgaXMgdG8gZW5zdXJlIHRoYXQgdGhlIHJlZnJlc2ggcmVxdWVzdCBkb2Vzbid0IGdldCBpbnRlcmNlcHRlZCwgYWNjaWRlbnRhbGx5IHRyaWdnZXJpbmcgdGhlIGludGVyY2VwdGlvbiByb3V0ZS5cbiAgY29uc3QgaW5jbHVkZU5leHRVcmwgPSBoYXNJbnRlcmNlcHRpb25Sb3V0ZUluQ3VycmVudFRyZWUoc3RhdGUudHJlZSlcblxuICAvLyBUT0RPLUFQUDogdmVyaWZ5IHRoYXQgYGhyZWZgIGlzIG5vdCBhbiBleHRlcm5hbCB1cmwuXG4gIC8vIEZldGNoIGRhdGEgZnJvbSB0aGUgcm9vdCBvZiB0aGUgdHJlZS5cbiAgY2FjaGUubGF6eURhdGEgPSBmZXRjaFNlcnZlclJlc3BvbnNlKG5ldyBVUkwoaHJlZiwgb3JpZ2luKSwge1xuICAgIGZsaWdodFJvdXRlclN0YXRlOiBbc3RhdGUudHJlZVswXSwgc3RhdGUudHJlZVsxXSwgc3RhdGUudHJlZVsyXSwgJ3JlZmV0Y2gnXSxcbiAgICBuZXh0VXJsOiBpbmNsdWRlTmV4dFVybCA/IHN0YXRlLm5leHRVcmwgOiBudWxsLFxuICAgIGlzSG1yUmVmcmVzaDogdHJ1ZSxcbiAgfSlcblxuICByZXR1cm4gY2FjaGUubGF6eURhdGEudGhlbihcbiAgICAoeyBmbGlnaHREYXRhLCBjYW5vbmljYWxVcmw6IGNhbm9uaWNhbFVybE92ZXJyaWRlIH0pID0+IHtcbiAgICAgIC8vIEhhbmRsZSBjYXNlIHdoZW4gbmF2aWdhdGluZyB0byBwYWdlIGluIGBwYWdlc2AgZnJvbSBgYXBwYFxuICAgICAgaWYgKHR5cGVvZiBmbGlnaHREYXRhID09PSAnc3RyaW5nJykge1xuICAgICAgICByZXR1cm4gaGFuZGxlRXh0ZXJuYWxVcmwoXG4gICAgICAgICAgc3RhdGUsXG4gICAgICAgICAgbXV0YWJsZSxcbiAgICAgICAgICBmbGlnaHREYXRhLFxuICAgICAgICAgIHN0YXRlLnB1c2hSZWYucGVuZGluZ1B1c2hcbiAgICAgICAgKVxuICAgICAgfVxuXG4gICAgICAvLyBSZW1vdmUgY2FjaGUubGF6eURhdGEgYXMgaXQgaGFzIGJlZW4gcmVzb2x2ZWQgYXQgdGhpcyBwb2ludC5cbiAgICAgIGNhY2hlLmxhenlEYXRhID0gbnVsbFxuXG4gICAgICBsZXQgY3VycmVudFRyZWUgPSBzdGF0ZS50cmVlXG4gICAgICBsZXQgY3VycmVudENhY2hlID0gc3RhdGUuY2FjaGVcblxuICAgICAgZm9yIChjb25zdCBub3JtYWxpemVkRmxpZ2h0RGF0YSBvZiBmbGlnaHREYXRhKSB7XG4gICAgICAgIGNvbnN0IHsgdHJlZTogdHJlZVBhdGNoLCBpc1Jvb3RSZW5kZXIgfSA9IG5vcm1hbGl6ZWRGbGlnaHREYXRhXG4gICAgICAgIGlmICghaXNSb290UmVuZGVyKSB7XG4gICAgICAgICAgLy8gVE9ETy1BUFA6IGhhbmRsZSB0aGlzIGNhc2UgYmV0dGVyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1JFRlJFU0ggRkFJTEVEJylcbiAgICAgICAgICByZXR1cm4gc3RhdGVcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IG5ld1RyZWUgPSBhcHBseVJvdXRlclN0YXRlUGF0Y2hUb1RyZWUoXG4gICAgICAgICAgLy8gVE9ETy1BUFA6IHJlbW92ZSAnJ1xuICAgICAgICAgIFsnJ10sXG4gICAgICAgICAgY3VycmVudFRyZWUsXG4gICAgICAgICAgdHJlZVBhdGNoLFxuICAgICAgICAgIHN0YXRlLmNhbm9uaWNhbFVybFxuICAgICAgICApXG5cbiAgICAgICAgaWYgKG5ld1RyZWUgPT09IG51bGwpIHtcbiAgICAgICAgICByZXR1cm4gaGFuZGxlU2VnbWVudE1pc21hdGNoKHN0YXRlLCBhY3Rpb24sIHRyZWVQYXRjaClcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChpc05hdmlnYXRpbmdUb05ld1Jvb3RMYXlvdXQoY3VycmVudFRyZWUsIG5ld1RyZWUpKSB7XG4gICAgICAgICAgcmV0dXJuIGhhbmRsZUV4dGVybmFsVXJsKFxuICAgICAgICAgICAgc3RhdGUsXG4gICAgICAgICAgICBtdXRhYmxlLFxuICAgICAgICAgICAgaHJlZixcbiAgICAgICAgICAgIHN0YXRlLnB1c2hSZWYucGVuZGluZ1B1c2hcbiAgICAgICAgICApXG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBjYW5vbmljYWxVcmxPdmVycmlkZUhyZWYgPSBjYW5vbmljYWxVcmxPdmVycmlkZVxuICAgICAgICAgID8gY3JlYXRlSHJlZkZyb21VcmwoY2Fub25pY2FsVXJsT3ZlcnJpZGUpXG4gICAgICAgICAgOiB1bmRlZmluZWRcblxuICAgICAgICBpZiAoY2Fub25pY2FsVXJsT3ZlcnJpZGUpIHtcbiAgICAgICAgICBtdXRhYmxlLmNhbm9uaWNhbFVybCA9IGNhbm9uaWNhbFVybE92ZXJyaWRlSHJlZlxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFwcGxpZWQgPSBhcHBseUZsaWdodERhdGEoXG4gICAgICAgICAgY3VycmVudENhY2hlLFxuICAgICAgICAgIGNhY2hlLFxuICAgICAgICAgIG5vcm1hbGl6ZWRGbGlnaHREYXRhXG4gICAgICAgIClcblxuICAgICAgICBpZiAoYXBwbGllZCkge1xuICAgICAgICAgIG11dGFibGUuY2FjaGUgPSBjYWNoZVxuICAgICAgICAgIGN1cnJlbnRDYWNoZSA9IGNhY2hlXG4gICAgICAgIH1cblxuICAgICAgICBtdXRhYmxlLnBhdGNoZWRUcmVlID0gbmV3VHJlZVxuICAgICAgICBtdXRhYmxlLmNhbm9uaWNhbFVybCA9IGhyZWZcblxuICAgICAgICBjdXJyZW50VHJlZSA9IG5ld1RyZWVcbiAgICAgIH1cbiAgICAgIHJldHVybiBoYW5kbGVNdXRhYmxlKHN0YXRlLCBtdXRhYmxlKVxuICAgIH0sXG4gICAgKCkgPT4gc3RhdGVcbiAgKVxufVxuXG5mdW5jdGlvbiBobXJSZWZyZXNoUmVkdWNlck5vb3AoXG4gIHN0YXRlOiBSZWFkb25seVJlZHVjZXJTdGF0ZSxcbiAgX2FjdGlvbjogSG1yUmVmcmVzaEFjdGlvblxuKTogUmVkdWNlclN0YXRlIHtcbiAgcmV0dXJuIHN0YXRlXG59XG5cbmV4cG9ydCBjb25zdCBobXJSZWZyZXNoUmVkdWNlciA9XG4gIHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbidcbiAgICA/IGhtclJlZnJlc2hSZWR1Y2VyTm9vcFxuICAgIDogaG1yUmVmcmVzaFJlZHVjZXJJbXBsXG4iXSwibmFtZXMiOlsiaG1yUmVmcmVzaFJlZHVjZXIiLCJobXJSZWZyZXNoUmVkdWNlckltcGwiLCJzdGF0ZSIsImFjdGlvbiIsIm9yaWdpbiIsIm11dGFibGUiLCJocmVmIiwiY2Fub25pY2FsVXJsIiwicHJlc2VydmVDdXN0b21IaXN0b3J5U3RhdGUiLCJjYWNoZSIsImNyZWF0ZUVtcHR5Q2FjaGVOb2RlIiwiaW5jbHVkZU5leHRVcmwiLCJoYXNJbnRlcmNlcHRpb25Sb3V0ZUluQ3VycmVudFRyZWUiLCJ0cmVlIiwibGF6eURhdGEiLCJmZXRjaFNlcnZlclJlc3BvbnNlIiwiVVJMIiwiZmxpZ2h0Um91dGVyU3RhdGUiLCJuZXh0VXJsIiwiaXNIbXJSZWZyZXNoIiwidGhlbiIsImZsaWdodERhdGEiLCJjYW5vbmljYWxVcmxPdmVycmlkZSIsImhhbmRsZUV4dGVybmFsVXJsIiwicHVzaFJlZiIsInBlbmRpbmdQdXNoIiwiY3VycmVudFRyZWUiLCJjdXJyZW50Q2FjaGUiLCJub3JtYWxpemVkRmxpZ2h0RGF0YSIsInRyZWVQYXRjaCIsImlzUm9vdFJlbmRlciIsImNvbnNvbGUiLCJsb2ciLCJuZXdUcmVlIiwiYXBwbHlSb3V0ZXJTdGF0ZVBhdGNoVG9UcmVlIiwiaGFuZGxlU2VnbWVudE1pc21hdGNoIiwiaXNOYXZpZ2F0aW5nVG9OZXdSb290TGF5b3V0IiwiY2Fub25pY2FsVXJsT3ZlcnJpZGVIcmVmIiwiY3JlYXRlSHJlZkZyb21VcmwiLCJ1bmRlZmluZWQiLCJhcHBsaWVkIiwiYXBwbHlGbGlnaHREYXRhIiwicGF0Y2hlZFRyZWUiLCJoYW5kbGVNdXRhYmxlIiwiaG1yUmVmcmVzaFJlZHVjZXJOb29wIiwiX2FjdGlvbiIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js ***!
  \**********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    handleExternalUrl: function() {\n        return handleExternalUrl;\n    },\n    navigateReducer: function() {\n        return navigateReducer;\n    }\n});\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _invalidatecachebelowflightsegmentpath = __webpack_require__(/*! ../invalidate-cache-below-flight-segmentpath */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/invalidate-cache-below-flight-segmentpath.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _shouldhardnavigate = __webpack_require__(/*! ../should-hard-navigate */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/should-hard-navigate.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _segment = __webpack_require__(/*! ../../../../shared/lib/segment */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/segment.js\");\nconst _pprnavigations = __webpack_require__(/*! ../ppr-navigations */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/ppr-navigations.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ../prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst _clearcachenodedataforsegmentpath = __webpack_require__(/*! ../clear-cache-node-data-for-segment-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/clear-cache-node-data-for-segment-path.js\");\nconst _aliasedprefetchnavigations = __webpack_require__(/*! ../aliased-prefetch-navigations */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/aliased-prefetch-navigations.js\");\nconst _segmentcache = __webpack_require__(/*! ../../segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nfunction handleExternalUrl(state, mutable, url, pendingPush) {\n    mutable.mpaNavigation = true;\n    mutable.canonicalUrl = url;\n    mutable.pendingPush = pendingPush;\n    mutable.scrollableSegments = undefined;\n    return (0, _handlemutable.handleMutable)(state, mutable);\n}\nfunction generateSegmentsFromPatch(flightRouterPatch) {\n    const segments = [];\n    const [segment, parallelRoutes] = flightRouterPatch;\n    if (Object.keys(parallelRoutes).length === 0) {\n        return [\n            [\n                segment\n            ]\n        ];\n    }\n    for (const [parallelRouteKey, parallelRoute] of Object.entries(parallelRoutes)){\n        for (const childSegment of generateSegmentsFromPatch(parallelRoute)){\n            // If the segment is empty, it means we are at the root of the tree\n            if (segment === '') {\n                segments.push([\n                    parallelRouteKey,\n                    ...childSegment\n                ]);\n            } else {\n                segments.push([\n                    segment,\n                    parallelRouteKey,\n                    ...childSegment\n                ]);\n            }\n        }\n    }\n    return segments;\n}\nfunction triggerLazyFetchForLeafSegments(newCache, currentCache, flightSegmentPath, treePatch) {\n    let appliedPatch = false;\n    newCache.rsc = currentCache.rsc;\n    newCache.prefetchRsc = currentCache.prefetchRsc;\n    newCache.loading = currentCache.loading;\n    newCache.parallelRoutes = new Map(currentCache.parallelRoutes);\n    const segmentPathsToFill = generateSegmentsFromPatch(treePatch).map((segment)=>[\n            ...flightSegmentPath,\n            ...segment\n        ]);\n    for (const segmentPaths of segmentPathsToFill){\n        (0, _clearcachenodedataforsegmentpath.clearCacheNodeDataForSegmentPath)(newCache, currentCache, segmentPaths);\n        appliedPatch = true;\n    }\n    return appliedPatch;\n}\nfunction handleNavigationResult(url, state, mutable, pendingPush, result) {\n    switch(result.tag){\n        case _segmentcache.NavigationResultTag.MPA:\n            {\n                // Perform an MPA navigation.\n                const newUrl = result.data;\n                return handleExternalUrl(state, mutable, newUrl, pendingPush);\n            }\n        case _segmentcache.NavigationResultTag.NoOp:\n            {\n                // The server responded with no change to the current page. However, if\n                // the URL changed, we still need to update that.\n                const newCanonicalUrl = result.data.canonicalUrl;\n                mutable.canonicalUrl = newCanonicalUrl;\n                // Check if the only thing that changed was the hash fragment.\n                const oldUrl = new URL(state.canonicalUrl, url);\n                const onlyHashChange = // navigations are always same-origin.\n                url.pathname === oldUrl.pathname && url.search === oldUrl.search && url.hash !== oldUrl.hash;\n                if (onlyHashChange) {\n                    // The only updated part of the URL is the hash.\n                    mutable.onlyHashChange = true;\n                    mutable.shouldScroll = result.data.shouldScroll;\n                    mutable.hashFragment = url.hash;\n                    // Setting this to an empty array triggers a scroll for all new and\n                    // updated segments. See `ScrollAndFocusHandler` for more details.\n                    mutable.scrollableSegments = [];\n                }\n                return (0, _handlemutable.handleMutable)(state, mutable);\n            }\n        case _segmentcache.NavigationResultTag.Success:\n            {\n                // Received a new result.\n                mutable.cache = result.data.cacheNode;\n                mutable.patchedTree = result.data.flightRouterState;\n                mutable.canonicalUrl = result.data.canonicalUrl;\n                mutable.scrollableSegments = result.data.scrollableSegments;\n                mutable.shouldScroll = result.data.shouldScroll;\n                mutable.hashFragment = result.data.hash;\n                return (0, _handlemutable.handleMutable)(state, mutable);\n            }\n        case _segmentcache.NavigationResultTag.Async:\n            {\n                return result.data.then((asyncResult)=>handleNavigationResult(url, state, mutable, pendingPush, asyncResult), // TODO: This matches the current behavior but we need to do something\n                // better here if the network fails.\n                ()=>{\n                    return state;\n                });\n            }\n        default:\n            {\n                result;\n                return state;\n            }\n    }\n}\nfunction navigateReducer(state, action) {\n    const { url, isExternalUrl, navigateType, shouldScroll, allowAliasing } = action;\n    const mutable = {};\n    const { hash } = url;\n    const href = (0, _createhreffromurl.createHrefFromUrl)(url);\n    const pendingPush = navigateType === 'push';\n    // we want to prune the prefetch cache on every navigation to avoid it growing too large\n    (0, _prefetchcacheutils.prunePrefetchCache)(state.prefetchCache);\n    mutable.preserveCustomHistoryState = false;\n    mutable.pendingPush = pendingPush;\n    if (isExternalUrl) {\n        return handleExternalUrl(state, mutable, url.toString(), pendingPush);\n    }\n    // Handles case where `<meta http-equiv=\"refresh\">` tag is present,\n    // which will trigger an MPA navigation.\n    if (document.getElementById('__next-page-redirect')) {\n        return handleExternalUrl(state, mutable, href, pendingPush);\n    }\n    if (false) {}\n    const prefetchValues = (0, _prefetchcacheutils.getOrCreatePrefetchCacheEntry)({\n        url,\n        nextUrl: state.nextUrl,\n        tree: state.tree,\n        prefetchCache: state.prefetchCache,\n        allowAliasing\n    });\n    const { treeAtTimeOfPrefetch, data } = prefetchValues;\n    _prefetchreducer.prefetchQueue.bump(data);\n    return data.then((param)=>{\n        let { flightData, canonicalUrl: canonicalUrlOverride, postponed } = param;\n        let isFirstRead = false;\n        // we only want to mark this once\n        if (!prefetchValues.lastUsedTime) {\n            // important: we should only mark the cache node as dirty after we unsuspend from the call above\n            prefetchValues.lastUsedTime = Date.now();\n            isFirstRead = true;\n        }\n        if (prefetchValues.aliased) {\n            const result = (0, _aliasedprefetchnavigations.handleAliasedPrefetchEntry)(state, flightData, url, mutable);\n            // We didn't return new router state because we didn't apply the aliased entry for some reason.\n            // We'll re-invoke the navigation handler but ensure that we don't attempt to use the aliased entry. This\n            // will create an on-demand prefetch entry.\n            if (result === false) {\n                return navigateReducer(state, {\n                    ...action,\n                    allowAliasing: false\n                });\n            }\n            return result;\n        }\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === 'string') {\n            return handleExternalUrl(state, mutable, flightData, pendingPush);\n        }\n        const updatedCanonicalUrl = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : href;\n        const onlyHashChange = !!hash && state.canonicalUrl.split('#', 1)[0] === updatedCanonicalUrl.split('#', 1)[0];\n        // If only the hash has changed, the server hasn't sent us any new data. We can just update\n        // the mutable properties responsible for URL and scroll handling and return early.\n        if (onlyHashChange) {\n            mutable.onlyHashChange = true;\n            mutable.canonicalUrl = updatedCanonicalUrl;\n            mutable.shouldScroll = shouldScroll;\n            mutable.hashFragment = hash;\n            mutable.scrollableSegments = [];\n            return (0, _handlemutable.handleMutable)(state, mutable);\n        }\n        let currentTree = state.tree;\n        let currentCache = state.cache;\n        let scrollableSegments = [];\n        for (const normalizedFlightData of flightData){\n            const { pathToSegment: flightSegmentPath, seedData, head, isHeadPartial, isRootRender } = normalizedFlightData;\n            let treePatch = normalizedFlightData.tree;\n            // TODO-APP: remove ''\n            const flightSegmentPathWithLeadingEmpty = [\n                '',\n                ...flightSegmentPath\n            ];\n            // Create new tree based on the flightSegmentPath and router state patch\n            let newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)(flightSegmentPathWithLeadingEmpty, currentTree, treePatch, href);\n            // If the tree patch can't be applied to the current tree then we use the tree at time of prefetch\n            // TODO-APP: This should instead fill in the missing pieces in `currentTree` with the data from `treeAtTimeOfPrefetch`, then apply the patch.\n            if (newTree === null) {\n                newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)(flightSegmentPathWithLeadingEmpty, treeAtTimeOfPrefetch, treePatch, href);\n            }\n            if (newTree !== null) {\n                if (// will send back a static response that's rendered from\n                // the root. If for some reason it doesn't, we fall back to the\n                // non-PPR implementation.\n                // TODO: We should get rid of the else branch and do all navigations\n                // via startPPRNavigation. The current structure is just\n                // an incremental step.\n                seedData && isRootRender && postponed) {\n                    const task = (0, _pprnavigations.startPPRNavigation)(currentCache, currentTree, treePatch, seedData, head, isHeadPartial, false, scrollableSegments);\n                    if (task !== null) {\n                        if (task.route === null) {\n                            // Detected a change to the root layout. Perform an full-\n                            // page navigation.\n                            return handleExternalUrl(state, mutable, href, pendingPush);\n                        }\n                        // Use the tree computed by startPPRNavigation instead\n                        // of the one computed by applyRouterStatePatchToTree.\n                        // TODO: We should remove applyRouterStatePatchToTree\n                        // from the PPR path entirely.\n                        const patchedRouterState = task.route;\n                        newTree = patchedRouterState;\n                        const newCache = task.node;\n                        if (newCache !== null) {\n                            // We've created a new Cache Node tree that contains a prefetched\n                            // version of the next page. This can be rendered instantly.\n                            mutable.cache = newCache;\n                        }\n                        const dynamicRequestTree = task.dynamicRequestTree;\n                        if (dynamicRequestTree !== null) {\n                            // The prefetched tree has dynamic holes in it. We initiate a\n                            // dynamic request to fill them in.\n                            //\n                            // Do not block on the result. We'll immediately render the Cache\n                            // Node tree and suspend on the dynamic parts. When the request\n                            // comes in, we'll fill in missing data and ping React to\n                            // re-render. Unlike the lazy fetching model in the non-PPR\n                            // implementation, this is modeled as a single React update +\n                            // streaming, rather than multiple top-level updates. (However,\n                            // even in the new model, we'll still need to sometimes update the\n                            // root multiple times per navigation, like if the server sends us\n                            // a different response than we expected. For now, we revert back\n                            // to the lazy fetching mechanism in that case.)\n                            const dynamicRequest = (0, _fetchserverresponse.fetchServerResponse)(url, {\n                                flightRouterState: dynamicRequestTree,\n                                nextUrl: state.nextUrl\n                            });\n                            (0, _pprnavigations.listenForDynamicRequest)(task, dynamicRequest);\n                        // We store the dynamic request on the `lazyData` property of the CacheNode\n                        // because we're not going to await the dynamic request here. Since we're not blocking\n                        // on the dynamic request, `layout-router` will\n                        // task.node.lazyData = dynamicRequest\n                        } else {\n                        // The prefetched tree does not contain dynamic holes — it's\n                        // fully static. We can skip the dynamic request.\n                        }\n                    } else {\n                        // Nothing changed, so reuse the old cache.\n                        // TODO: What if the head changed but not any of the segment data?\n                        // Is that possible? If so, we should clone the whole tree and\n                        // update the head.\n                        newTree = treePatch;\n                    }\n                } else {\n                    // The static response does not include any dynamic holes, so\n                    // there's no need to do a second request.\n                    // TODO: As an incremental step this just reverts back to the\n                    // non-PPR implementation. We can simplify this branch further,\n                    // given that PPR prefetches are always static and return the whole\n                    // tree. Or in the meantime we could factor it out into a\n                    // separate function.\n                    if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                        return handleExternalUrl(state, mutable, href, pendingPush);\n                    }\n                    const cache = (0, _approuter.createEmptyCacheNode)();\n                    let applied = false;\n                    if (prefetchValues.status === _routerreducertypes.PrefetchCacheEntryStatus.stale && !isFirstRead) {\n                        // When we have a stale prefetch entry, we only want to re-use the loading state of the route we're navigating to, to support instant loading navigations\n                        // this will trigger a lazy fetch for the actual page data by nulling the `rsc` and `prefetchRsc` values for page data,\n                        // while copying over the `loading` for the segment that contains the page data.\n                        // We only do this on subsequent reads, as otherwise there'd be no loading data to re-use.\n                        // We skip this branch if only the hash fragment has changed, as we don't want to trigger a lazy fetch in that case\n                        applied = triggerLazyFetchForLeafSegments(cache, currentCache, flightSegmentPath, treePatch);\n                        // since we re-used the stale cache's loading state & refreshed the data,\n                        // update the `lastUsedTime` so that it can continue to be re-used for the next 30s\n                        prefetchValues.lastUsedTime = Date.now();\n                    } else {\n                        applied = (0, _applyflightdata.applyFlightData)(currentCache, cache, normalizedFlightData, prefetchValues);\n                    }\n                    const hardNavigate = (0, _shouldhardnavigate.shouldHardNavigate)(flightSegmentPathWithLeadingEmpty, currentTree);\n                    if (hardNavigate) {\n                        // Copy rsc for the root node of the cache.\n                        cache.rsc = currentCache.rsc;\n                        cache.prefetchRsc = currentCache.prefetchRsc;\n                        (0, _invalidatecachebelowflightsegmentpath.invalidateCacheBelowFlightSegmentPath)(cache, currentCache, flightSegmentPath);\n                        // Ensure the existing cache value is used when the cache was not invalidated.\n                        mutable.cache = cache;\n                    } else if (applied) {\n                        mutable.cache = cache;\n                        // If we applied the cache, we update the \"current cache\" value so any other\n                        // segments in the FlightDataPath will be able to reference the updated cache.\n                        currentCache = cache;\n                    }\n                    for (const subSegment of generateSegmentsFromPatch(treePatch)){\n                        const scrollableSegmentPath = [\n                            ...flightSegmentPath,\n                            ...subSegment\n                        ];\n                        // Filter out the __DEFAULT__ paths as they shouldn't be scrolled to in this case.\n                        if (scrollableSegmentPath[scrollableSegmentPath.length - 1] !== _segment.DEFAULT_SEGMENT_KEY) {\n                            scrollableSegments.push(scrollableSegmentPath);\n                        }\n                    }\n                }\n                currentTree = newTree;\n            }\n        }\n        mutable.patchedTree = currentTree;\n        mutable.canonicalUrl = updatedCanonicalUrl;\n        mutable.scrollableSegments = scrollableSegments;\n        mutable.hashFragment = hash;\n        mutable.shouldScroll = shouldScroll;\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=navigate-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js ***!
  \**********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    prefetchQueue: function() {\n        return prefetchQueue;\n    },\n    prefetchReducer: function() {\n        return prefetchReducer;\n    }\n});\nconst _promisequeue = __webpack_require__(/*! ../../promise-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ../prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst prefetchQueue = new _promisequeue.PromiseQueue(5);\nconst prefetchReducer =  false ? 0 : prefetchReducerImpl;\nfunction identityReducerWhenSegmentCacheIsEnabled(state) {\n    // Unlike the old implementation, the Segment Cache doesn't store its data in\n    // the router reducer state.\n    //\n    // This shouldn't be reachable because we wrap the prefetch API in a check,\n    // too, which prevents the action from being dispatched. But it's here for\n    // clarity + code elimination.\n    return state;\n}\nfunction prefetchReducerImpl(state, action) {\n    // let's prune the prefetch cache before we do anything else\n    (0, _prefetchcacheutils.prunePrefetchCache)(state.prefetchCache);\n    const { url } = action;\n    (0, _prefetchcacheutils.getOrCreatePrefetchCacheEntry)({\n        url,\n        nextUrl: state.nextUrl,\n        prefetchCache: state.prefetchCache,\n        kind: action.kind,\n        tree: state.tree,\n        allowAliasing: true\n    });\n    return state;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=prefetch-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/refresh-reducer.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/refresh-reducer.js ***!
  \*********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"refreshReducer\", ({\n    enumerable: true,\n    get: function() {\n        return refreshReducer;\n    }\n}));\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ../fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ../refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\nconst _segmentcache = __webpack_require__(/*! ../../segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nfunction refreshReducer(state, action) {\n    const { origin } = action;\n    const mutable = {};\n    const href = state.canonicalUrl;\n    let currentTree = state.tree;\n    mutable.preserveCustomHistoryState = false;\n    const cache = (0, _approuter.createEmptyCacheNode)();\n    // If the current tree was intercepted, the nextUrl should be included in the request.\n    // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n    const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree);\n    // TODO-APP: verify that `href` is not an external url.\n    // Fetch data from the root of the tree.\n    cache.lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(href, origin), {\n        flightRouterState: [\n            currentTree[0],\n            currentTree[1],\n            currentTree[2],\n            'refetch'\n        ],\n        nextUrl: includeNextUrl ? state.nextUrl : null\n    });\n    return cache.lazyData.then(async (param)=>{\n        let { flightData, canonicalUrl: canonicalUrlOverride } = param;\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === 'string') {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        // Remove cache.lazyData as it has been resolved at this point.\n        cache.lazyData = null;\n        for (const normalizedFlightData of flightData){\n            const { tree: treePatch, seedData: cacheNodeSeedData, head, isRootRender } = normalizedFlightData;\n            if (!isRootRender) {\n                // TODO-APP: handle this case better\n                console.log('REFRESH FAILED');\n                return state;\n            }\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                ''\n            ], currentTree, treePatch, state.canonicalUrl);\n            if (newTree === null) {\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, href, state.pushRef.pendingPush);\n            }\n            const canonicalUrlOverrideHref = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : undefined;\n            if (canonicalUrlOverride) {\n                mutable.canonicalUrl = canonicalUrlOverrideHref;\n            }\n            // Handles case where prefetch only returns the router tree patch without rendered components.\n            if (cacheNodeSeedData !== null) {\n                const rsc = cacheNodeSeedData[1];\n                const loading = cacheNodeSeedData[3];\n                cache.rsc = rsc;\n                cache.prefetchRsc = null;\n                cache.loading = loading;\n                (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(cache, undefined, treePatch, cacheNodeSeedData, head, undefined);\n                if (false) {} else {\n                    mutable.prefetchCache = new Map();\n                }\n            }\n            await (0, _refetchinactiveparallelsegments.refreshInactiveParallelSegments)({\n                state,\n                updatedTree: newTree,\n                updatedCache: cache,\n                includeNextUrl,\n                canonicalUrl: mutable.canonicalUrl || state.canonicalUrl\n            });\n            mutable.cache = cache;\n            mutable.patchedTree = newTree;\n            currentTree = newTree;\n        }\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=refresh-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/refresh-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/restore-reducer.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/restore-reducer.js ***!
  \*********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"restoreReducer\", ({\n    enumerable: true,\n    get: function() {\n        return restoreReducer;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _computechangedpath = __webpack_require__(/*! ../compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _pprnavigations = __webpack_require__(/*! ../ppr-navigations */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/ppr-navigations.js\");\nfunction restoreReducer(state, action) {\n    const { url, tree } = action;\n    const href = (0, _createhreffromurl.createHrefFromUrl)(url);\n    // This action is used to restore the router state from the history state.\n    // However, it's possible that the history state no longer contains the `FlightRouterState`.\n    // We will copy over the internal state on pushState/replaceState events, but if a history entry\n    // occurred before hydration, or if the user navigated to a hash using a regular anchor link,\n    // the history state will not contain the `FlightRouterState`.\n    // In this case, we'll continue to use the existing tree so the router doesn't get into an invalid state.\n    const treeToRestore = tree || state.tree;\n    const oldCache = state.cache;\n    const newCache =  false ? // prevents an unnecessary flash back to PPR state during a\n    // back/forward navigation.\n    0 : oldCache;\n    var _extractPathFromFlightRouterState;\n    return {\n        // Set canonical url\n        canonicalUrl: href,\n        pushRef: {\n            pendingPush: false,\n            mpaNavigation: false,\n            // Ensures that the custom history state that was set is preserved when applying this update.\n            preserveCustomHistoryState: true\n        },\n        focusAndScrollRef: state.focusAndScrollRef,\n        cache: newCache,\n        prefetchCache: state.prefetchCache,\n        // Restore provided tree\n        tree: treeToRestore,\n        nextUrl: (_extractPathFromFlightRouterState = (0, _computechangedpath.extractPathFromFlightRouterState)(treeToRestore)) != null ? _extractPathFromFlightRouterState : url.pathname\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=restore-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/restore-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/server-action-reducer.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/server-action-reducer.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"serverActionReducer\", ({\n    enumerable: true,\n    get: function() {\n        return serverActionReducer;\n    }\n}));\nconst _appcallserver = __webpack_require__(/*! ../../../app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ../../../app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _approuterheaders = __webpack_require__(/*! ../../app-router-headers */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _assignlocation = __webpack_require__(/*! ../../../assign-location */ \"(app-pages-browser)/./node_modules/next/dist/client/assign-location.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ../fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ../refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nconst _redirect = __webpack_require__(/*! ../../redirect */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect.js\");\nconst _redirecterror = __webpack_require__(/*! ../../redirect-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-error.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ../prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst _removebasepath = __webpack_require__(/*! ../../../remove-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _serverreferenceinfo = __webpack_require__(/*! ../../../../shared/lib/server-reference-info */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/server-reference-info.js\");\nconst _segmentcache = __webpack_require__(/*! ../../segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\n// // eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromFetch } from 'react-server-dom-webpack/client'\n// // eslint-disable-next-line import/no-extraneous-dependencies\n// import { encodeReply } from 'react-server-dom-webpack/client'\nconst { createFromFetch, createTemporaryReferenceSet, encodeReply } =  false ? 0 : __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nasync function fetchServerAction(state, nextUrl, param) {\n    let { actionId, actionArgs } = param;\n    const temporaryReferences = createTemporaryReferenceSet();\n    const info = (0, _serverreferenceinfo.extractInfoFromServerReferenceId)(actionId);\n    // TODO: Currently, we're only omitting unused args for the experimental \"use\n    // cache\" functions. Once the server reference info byte feature is stable, we\n    // should apply this to server actions as well.\n    const usedArgs = info.type === 'use-cache' ? (0, _serverreferenceinfo.omitUnusedArgs)(actionArgs, info) : actionArgs;\n    const body = await encodeReply(usedArgs, {\n        temporaryReferences\n    });\n    const res = await fetch('', {\n        method: 'POST',\n        headers: {\n            Accept: _approuterheaders.RSC_CONTENT_TYPE_HEADER,\n            [_approuterheaders.ACTION_HEADER]: actionId,\n            [_approuterheaders.NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(JSON.stringify(state.tree)),\n            ... false ? 0 : {},\n            ...nextUrl ? {\n                [_approuterheaders.NEXT_URL]: nextUrl\n            } : {}\n        },\n        body\n    });\n    const redirectHeader = res.headers.get('x-action-redirect');\n    const [location, _redirectType] = (redirectHeader == null ? void 0 : redirectHeader.split(';')) || [];\n    let redirectType;\n    switch(_redirectType){\n        case 'push':\n            redirectType = _redirecterror.RedirectType.push;\n            break;\n        case 'replace':\n            redirectType = _redirecterror.RedirectType.replace;\n            break;\n        default:\n            redirectType = undefined;\n    }\n    const isPrerender = !!res.headers.get(_approuterheaders.NEXT_IS_PRERENDER_HEADER);\n    let revalidatedParts;\n    try {\n        const revalidatedHeader = JSON.parse(res.headers.get('x-action-revalidated') || '[[],0,0]');\n        revalidatedParts = {\n            paths: revalidatedHeader[0] || [],\n            tag: !!revalidatedHeader[1],\n            cookie: revalidatedHeader[2]\n        };\n    } catch (e) {\n        revalidatedParts = {\n            paths: [],\n            tag: false,\n            cookie: false\n        };\n    }\n    const redirectLocation = location ? (0, _assignlocation.assignLocation)(location, new URL(state.canonicalUrl, window.location.href)) : undefined;\n    const contentType = res.headers.get('content-type');\n    if (contentType == null ? void 0 : contentType.startsWith(_approuterheaders.RSC_CONTENT_TYPE_HEADER)) {\n        const response = await createFromFetch(Promise.resolve(res), {\n            callServer: _appcallserver.callServer,\n            findSourceMapURL: _appfindsourcemapurl.findSourceMapURL,\n            temporaryReferences\n        });\n        if (location) {\n            // if it was a redirection, then result is just a regular RSC payload\n            return {\n                actionFlightData: (0, _flightdatahelpers.normalizeFlightData)(response.f),\n                redirectLocation,\n                redirectType,\n                revalidatedParts,\n                isPrerender\n            };\n        }\n        return {\n            actionResult: response.a,\n            actionFlightData: (0, _flightdatahelpers.normalizeFlightData)(response.f),\n            redirectLocation,\n            redirectType,\n            revalidatedParts,\n            isPrerender\n        };\n    }\n    // Handle invalid server action responses\n    if (res.status >= 400) {\n        // The server can respond with a text/plain error message, but we'll fallback to something generic\n        // if there isn't one.\n        const error = contentType === 'text/plain' ? await res.text() : 'An unexpected response was received from the server.';\n        throw Object.defineProperty(new Error(error), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return {\n        redirectLocation,\n        redirectType,\n        revalidatedParts,\n        isPrerender\n    };\n}\nfunction serverActionReducer(state, action) {\n    const { resolve, reject } = action;\n    const mutable = {};\n    let currentTree = state.tree;\n    mutable.preserveCustomHistoryState = false;\n    // only pass along the `nextUrl` param (used for interception routes) if the current route was intercepted.\n    // If the route has been intercepted, the action should be as well.\n    // Otherwise the server action might be intercepted with the wrong action id\n    // (ie, one that corresponds with the intercepted route)\n    const nextUrl = state.nextUrl && (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree) ? state.nextUrl : null;\n    return fetchServerAction(state, nextUrl, action).then(async (param)=>{\n        let { actionResult, actionFlightData: flightData, redirectLocation, redirectType, isPrerender, revalidatedParts } = param;\n        let redirectHref;\n        // honor the redirect type instead of defaulting to push in case of server actions.\n        if (redirectLocation) {\n            if (redirectType === _redirecterror.RedirectType.replace) {\n                state.pushRef.pendingPush = false;\n                mutable.pendingPush = false;\n            } else {\n                state.pushRef.pendingPush = true;\n                mutable.pendingPush = true;\n            }\n            redirectHref = (0, _createhreffromurl.createHrefFromUrl)(redirectLocation, false);\n            mutable.canonicalUrl = redirectHref;\n        }\n        if (!flightData) {\n            resolve(actionResult);\n            // If there is a redirect but no flight data we need to do a mpaNavigation.\n            if (redirectLocation) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, redirectLocation.href, state.pushRef.pendingPush);\n            }\n            return state;\n        }\n        if (typeof flightData === 'string') {\n            // Handle case when navigating to page in `pages` from `app`\n            resolve(actionResult);\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        const actionRevalidated = revalidatedParts.paths.length > 0 || revalidatedParts.tag || revalidatedParts.cookie;\n        for (const normalizedFlightData of flightData){\n            const { tree: treePatch, seedData: cacheNodeSeedData, head, isRootRender } = normalizedFlightData;\n            if (!isRootRender) {\n                // TODO-APP: handle this case better\n                console.log('SERVER ACTION APPLY FAILED');\n                resolve(actionResult);\n                return state;\n            }\n            // Given the path can only have two items the items are only the router state and rsc for the root.\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                ''\n            ], currentTree, treePatch, redirectHref ? redirectHref : state.canonicalUrl);\n            if (newTree === null) {\n                resolve(actionResult);\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                resolve(actionResult);\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, redirectHref || state.canonicalUrl, state.pushRef.pendingPush);\n            }\n            // The server sent back RSC data for the server action, so we need to apply it to the cache.\n            if (cacheNodeSeedData !== null) {\n                const rsc = cacheNodeSeedData[1];\n                const cache = (0, _approuter.createEmptyCacheNode)();\n                cache.rsc = rsc;\n                cache.prefetchRsc = null;\n                cache.loading = cacheNodeSeedData[3];\n                (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(cache, undefined, treePatch, cacheNodeSeedData, head, undefined);\n                mutable.cache = cache;\n                if (false) {} else {\n                    mutable.prefetchCache = new Map();\n                }\n                if (actionRevalidated) {\n                    await (0, _refetchinactiveparallelsegments.refreshInactiveParallelSegments)({\n                        state,\n                        updatedTree: newTree,\n                        updatedCache: cache,\n                        includeNextUrl: Boolean(nextUrl),\n                        canonicalUrl: mutable.canonicalUrl || state.canonicalUrl\n                    });\n                }\n            }\n            mutable.patchedTree = newTree;\n            currentTree = newTree;\n        }\n        if (redirectLocation && redirectHref) {\n            if ( true && !actionRevalidated) {\n                // Because the RedirectBoundary will trigger a navigation, we need to seed the prefetch cache\n                // with the FlightData that we got from the server action for the target page, so that it's\n                // available when the page is navigated to and doesn't need to be re-fetched.\n                // We only do this if the server action didn't revalidate any data, as in that case the\n                // client cache will be cleared and the data will be re-fetched anyway.\n                // NOTE: We don't do this in the Segment Cache implementation.\n                // Dynamic data should never be placed into the cache, unless it's\n                // \"converted\" to static data using <Link prefetch={true}>. What we\n                // do instead is re-prefetch links and forms whenever the cache is\n                // invalidated.\n                (0, _prefetchcacheutils.createSeededPrefetchCacheEntry)({\n                    url: redirectLocation,\n                    data: {\n                        flightData,\n                        canonicalUrl: undefined,\n                        couldBeIntercepted: false,\n                        prerendered: false,\n                        postponed: false,\n                        // TODO: We should be able to set this if the server action\n                        // returned a fully static response.\n                        staleTime: -1\n                    },\n                    tree: state.tree,\n                    prefetchCache: state.prefetchCache,\n                    nextUrl: state.nextUrl,\n                    kind: isPrerender ? _routerreducertypes.PrefetchKind.FULL : _routerreducertypes.PrefetchKind.AUTO\n                });\n                mutable.prefetchCache = state.prefetchCache;\n            }\n            // If the action triggered a redirect, the action promise will be rejected with\n            // a redirect so that it's handled by RedirectBoundary as we won't have a valid\n            // action result to resolve the promise with. This will effectively reset the state of\n            // the component that called the action as the error boundary will remount the tree.\n            // The status code doesn't matter here as the action handler will have already sent\n            // a response with the correct status code.\n            reject((0, _redirect.getRedirectError)((0, _hasbasepath.hasBasePath)(redirectHref) ? (0, _removebasepath.removeBasePath)(redirectHref) : redirectHref, redirectType || _redirecterror.RedirectType.push));\n        } else {\n            resolve(actionResult);\n        }\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, (e)=>{\n        // When the server action is rejected we don't update the state and instead call the reject handler of the promise.\n        reject(e);\n        return state;\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=server-action-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/server-action-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/server-patch-reducer.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/server-patch-reducer.js ***!
  \**************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"serverPatchReducer\", ({\n    enumerable: true,\n    get: function() {\n        return serverPatchReducer;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nfunction serverPatchReducer(state, action) {\n    const { serverResponse: { flightData, canonicalUrl: canonicalUrlOverride } } = action;\n    const mutable = {};\n    mutable.preserveCustomHistoryState = false;\n    // Handle case when navigating to page in `pages` from `app`\n    if (typeof flightData === 'string') {\n        return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n    }\n    let currentTree = state.tree;\n    let currentCache = state.cache;\n    for (const normalizedFlightData of flightData){\n        const { segmentPath: flightSegmentPath, tree: treePatch } = normalizedFlightData;\n        const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n            '',\n            ...flightSegmentPath\n        ], currentTree, treePatch, state.canonicalUrl);\n        // `applyRouterStatePatchToTree` returns `null` when it determined that the server response is not applicable to the current tree.\n        // In other words, the server responded with a tree that doesn't match what the client is currently rendering.\n        // This can happen if the server patch action took longer to resolve than a subsequent navigation which would have changed the tree.\n        // Previously this case triggered an MPA navigation but it should be safe to simply discard the server response rather than forcing\n        // the entire page to reload.\n        if (newTree === null) {\n            return state;\n        }\n        if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, state.canonicalUrl, state.pushRef.pendingPush);\n        }\n        const canonicalUrlOverrideHref = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : undefined;\n        if (canonicalUrlOverrideHref) {\n            mutable.canonicalUrl = canonicalUrlOverrideHref;\n        }\n        const cache = (0, _approuter.createEmptyCacheNode)();\n        (0, _applyflightdata.applyFlightData)(currentCache, cache, normalizedFlightData);\n        mutable.patchedTree = newTree;\n        mutable.cache = cache;\n        currentCache = cache;\n        currentTree = newTree;\n    }\n    return (0, _handlemutable.handleMutable)(state, mutable);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=server-patch-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/server-patch-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js ***!
  \*******************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    addRefreshMarkerToActiveParallelSegments: function() {\n        return addRefreshMarkerToActiveParallelSegments;\n    },\n    refreshInactiveParallelSegments: function() {\n        return refreshInactiveParallelSegments;\n    }\n});\nconst _applyflightdata = __webpack_require__(/*! ./apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _fetchserverresponse = __webpack_require__(/*! ./fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _segment = __webpack_require__(/*! ../../../shared/lib/segment */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/segment.js\");\nasync function refreshInactiveParallelSegments(options) {\n    const fetchedSegments = new Set();\n    await refreshInactiveParallelSegmentsImpl({\n        ...options,\n        rootTree: options.updatedTree,\n        fetchedSegments\n    });\n}\nasync function refreshInactiveParallelSegmentsImpl(param) {\n    let { state, updatedTree, updatedCache, includeNextUrl, fetchedSegments, rootTree = updatedTree, canonicalUrl } = param;\n    const [, parallelRoutes, refetchPath, refetchMarker] = updatedTree;\n    const fetchPromises = [];\n    if (refetchPath && refetchPath !== canonicalUrl && refetchMarker === 'refresh' && // it's possible for the tree to contain multiple segments that contain data at the same URL\n    // we keep track of them so we can dedupe the requests\n    !fetchedSegments.has(refetchPath)) {\n        fetchedSegments.add(refetchPath) // Mark this URL as fetched\n        ;\n        // Eagerly kick off the fetch for the refetch path & the parallel routes. This should be fine to do as they each operate\n        // independently on their own cache nodes, and `applyFlightData` will copy anything it doesn't care about from the existing cache.\n        const fetchPromise = (0, _fetchserverresponse.fetchServerResponse)(new URL(refetchPath, location.origin), {\n            // refetch from the root of the updated tree, otherwise it will be scoped to the current segment\n            // and might not contain the data we need to patch in interception route data (such as dynamic params from a previous segment)\n            flightRouterState: [\n                rootTree[0],\n                rootTree[1],\n                rootTree[2],\n                'refetch'\n            ],\n            nextUrl: includeNextUrl ? state.nextUrl : null\n        }).then((param)=>{\n            let { flightData } = param;\n            if (typeof flightData !== 'string') {\n                for (const flightDataPath of flightData){\n                    // we only pass the new cache as this function is called after clearing the router cache\n                    // and filling in the new page data from the server. Meaning the existing cache is actually the cache that's\n                    // just been created & has been written to, but hasn't been \"committed\" yet.\n                    (0, _applyflightdata.applyFlightData)(updatedCache, updatedCache, flightDataPath);\n                }\n            } else {\n            // When flightData is a string, it suggests that the server response should have triggered an MPA navigation\n            // I'm not 100% sure of this decision, but it seems unlikely that we'd want to introduce a redirect side effect\n            // when refreshing on-screen data, so handling this has been ommitted.\n            }\n        });\n        fetchPromises.push(fetchPromise);\n    }\n    for(const key in parallelRoutes){\n        const parallelFetchPromise = refreshInactiveParallelSegmentsImpl({\n            state,\n            updatedTree: parallelRoutes[key],\n            updatedCache,\n            includeNextUrl,\n            fetchedSegments,\n            rootTree,\n            canonicalUrl\n        });\n        fetchPromises.push(parallelFetchPromise);\n    }\n    await Promise.all(fetchPromises);\n}\nfunction addRefreshMarkerToActiveParallelSegments(tree, path) {\n    const [segment, parallelRoutes, , refetchMarker] = tree;\n    // a page segment might also contain concatenated search params, so we do a partial match on the key\n    if (segment.includes(_segment.PAGE_SEGMENT_KEY) && refetchMarker !== 'refresh') {\n        tree[2] = path;\n        tree[3] = 'refresh';\n    }\n    for(const key in parallelRoutes){\n        addRefreshMarkerToActiveParallelSegments(parallelRoutes[key], path);\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=refetch-inactive-parallel-segments.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_HMR_REFRESH: function() {\n        return ACTION_HMR_REFRESH;\n    },\n    ACTION_NAVIGATE: function() {\n        return ACTION_NAVIGATE;\n    },\n    ACTION_PREFETCH: function() {\n        return ACTION_PREFETCH;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_RESTORE: function() {\n        return ACTION_RESTORE;\n    },\n    ACTION_SERVER_ACTION: function() {\n        return ACTION_SERVER_ACTION;\n    },\n    ACTION_SERVER_PATCH: function() {\n        return ACTION_SERVER_PATCH;\n    },\n    PrefetchCacheEntryStatus: function() {\n        return PrefetchCacheEntryStatus;\n    },\n    PrefetchKind: function() {\n        return PrefetchKind;\n    }\n});\nconst ACTION_REFRESH = 'refresh';\nconst ACTION_NAVIGATE = 'navigate';\nconst ACTION_RESTORE = 'restore';\nconst ACTION_SERVER_PATCH = 'server-patch';\nconst ACTION_PREFETCH = 'prefetch';\nconst ACTION_HMR_REFRESH = 'hmr-refresh';\nconst ACTION_SERVER_ACTION = 'server-action';\nvar PrefetchKind = /*#__PURE__*/ function(PrefetchKind) {\n    PrefetchKind[\"AUTO\"] = \"auto\";\n    PrefetchKind[\"FULL\"] = \"full\";\n    PrefetchKind[\"TEMPORARY\"] = \"temporary\";\n    return PrefetchKind;\n}({});\nvar PrefetchCacheEntryStatus = /*#__PURE__*/ function(PrefetchCacheEntryStatus) {\n    PrefetchCacheEntryStatus[\"fresh\"] = \"fresh\";\n    PrefetchCacheEntryStatus[\"reusable\"] = \"reusable\";\n    PrefetchCacheEntryStatus[\"expired\"] = \"expired\";\n    PrefetchCacheEntryStatus[\"stale\"] = \"stale\";\n    return PrefetchCacheEntryStatus;\n}({});\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=router-reducer-types.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/router-reducer.js ***!
  \***********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"reducer\", ({\n    enumerable: true,\n    get: function() {\n        return reducer;\n    }\n}));\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _navigatereducer = __webpack_require__(/*! ./reducers/navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _serverpatchreducer = __webpack_require__(/*! ./reducers/server-patch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/server-patch-reducer.js\");\nconst _restorereducer = __webpack_require__(/*! ./reducers/restore-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/restore-reducer.js\");\nconst _refreshreducer = __webpack_require__(/*! ./reducers/refresh-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/refresh-reducer.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _hmrrefreshreducer = __webpack_require__(/*! ./reducers/hmr-refresh-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js\");\nconst _serveractionreducer = __webpack_require__(/*! ./reducers/server-action-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/server-action-reducer.js\");\n/**\n * Reducer that handles the app-router state updates.\n */ function clientReducer(state, action) {\n    switch(action.type){\n        case _routerreducertypes.ACTION_NAVIGATE:\n            {\n                return (0, _navigatereducer.navigateReducer)(state, action);\n            }\n        case _routerreducertypes.ACTION_SERVER_PATCH:\n            {\n                return (0, _serverpatchreducer.serverPatchReducer)(state, action);\n            }\n        case _routerreducertypes.ACTION_RESTORE:\n            {\n                return (0, _restorereducer.restoreReducer)(state, action);\n            }\n        case _routerreducertypes.ACTION_REFRESH:\n            {\n                return (0, _refreshreducer.refreshReducer)(state, action);\n            }\n        case _routerreducertypes.ACTION_HMR_REFRESH:\n            {\n                return (0, _hmrrefreshreducer.hmrRefreshReducer)(state, action);\n            }\n        case _routerreducertypes.ACTION_PREFETCH:\n            {\n                return (0, _prefetchreducer.prefetchReducer)(state, action);\n            }\n        case _routerreducertypes.ACTION_SERVER_ACTION:\n            {\n                return (0, _serveractionreducer.serverActionReducer)(state, action);\n            }\n        // This case should never be hit as dispatch is strongly typed.\n        default:\n            throw Object.defineProperty(new Error('Unknown action'), \"__NEXT_ERROR_CODE\", {\n                value: \"E295\",\n                enumerable: false,\n                configurable: true\n            });\n    }\n}\nfunction serverReducer(state, _action) {\n    return state;\n}\nconst reducer =  false ? 0 : clientReducer;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=router-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/set-cache-busting-search-param.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/set-cache-busting-search-param.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"setCacheBustingSearchParam\", ({\n    enumerable: true,\n    get: function() {\n        return setCacheBustingSearchParam;\n    }\n}));\nconst _hash = __webpack_require__(/*! ../../../shared/lib/hash */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hash.js\");\nconst _approuterheaders = __webpack_require__(/*! ../app-router-headers */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\");\nconst setCacheBustingSearchParam = (url, headers)=>{\n    const uniqueCacheKey = (0, _hash.hexHash)([\n        headers[_approuterheaders.NEXT_ROUTER_PREFETCH_HEADER] || '0',\n        headers[_approuterheaders.NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] || '0',\n        headers[_approuterheaders.NEXT_ROUTER_STATE_TREE_HEADER],\n        headers[_approuterheaders.NEXT_URL]\n    ].join(','));\n    /**\n   * Note that we intentionally do not use `url.searchParams.set` here:\n   *\n   * const url = new URL('https://example.com/search?q=custom%20spacing');\n   * url.searchParams.set('_rsc', 'abc123');\n   * console.log(url.toString()); // Outputs: https://example.com/search?q=custom+spacing&_rsc=abc123\n   *                                                                             ^ <--- this is causing confusion\n   * This is in fact intended based on https://url.spec.whatwg.org/#interface-urlsearchparams, but\n   * we want to preserve the %20 as %20 if that's what the user passed in, hence the custom\n   * logic below.\n   */ const existingSearch = url.search;\n    const rawQuery = existingSearch.startsWith('?') ? existingSearch.slice(1) : existingSearch;\n    const pairs = rawQuery.split('&').filter(Boolean);\n    pairs.push(_approuterheaders.NEXT_RSC_UNION_QUERY + \"=\" + uniqueCacheKey);\n    url.search = pairs.length ? \"?\" + pairs.join('&') : '';\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=set-cache-busting-search-param.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/set-cache-busting-search-param.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/should-hard-navigate.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/should-hard-navigate.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"shouldHardNavigate\", ({\n    enumerable: true,\n    get: function() {\n        return shouldHardNavigate;\n    }\n}));\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nconst _matchsegments = __webpack_require__(/*! ../match-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/match-segments.js\");\nfunction shouldHardNavigate(flightSegmentPath, flightRouterState) {\n    const [segment, parallelRoutes] = flightRouterState;\n    // TODO-APP: Check if `as` can be replaced.\n    const [currentSegment, parallelRouteKey] = flightSegmentPath;\n    // Check if current segment matches the existing segment.\n    if (!(0, _matchsegments.matchSegment)(currentSegment, segment)) {\n        // If dynamic parameter in tree doesn't match up with segment path a hard navigation is triggered.\n        if (Array.isArray(currentSegment)) {\n            return true;\n        }\n        // If the existing segment did not match soft navigation is triggered.\n        return false;\n    }\n    const lastSegment = flightSegmentPath.length <= 2;\n    if (lastSegment) {\n        return false;\n    }\n    return shouldHardNavigate((0, _flightdatahelpers.getNextFlightSegmentPath)(flightSegmentPath), parallelRoutes[parallelRouteKey]);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=should-hard-navigate.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/should-hard-navigate.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/client/components/segment-cache.js ***!
  \*******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/**\n * Entry point to the Segment Cache implementation.\n *\n * All code related to the Segment Cache lives `segment-cache-impl` directory.\n * Callers access it through this indirection.\n *\n * This is to ensure the code is dead code eliminated from the bundle if the\n * flag is disabled.\n *\n * TODO: This is super tedious. Since experimental flags are an essential part\n * of our workflow, we should establish a better pattern for dead code\n * elimination. Ideally it would be done at the bundler level, like how React's\n * build process works. In the React repo, you don't even need to add any extra\n * configuration per experiment — if the code is not reachable, it gets stripped\n * from the build automatically by Rollup. Or, shorter term, we could stub out\n * experimental modules at build time by updating the build config, i.e. a more\n * automated version of what I'm doing manually in this file.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    NavigationResultTag: function() {\n        return NavigationResultTag;\n    },\n    PrefetchPriority: function() {\n        return PrefetchPriority;\n    },\n    bumpPrefetchTask: function() {\n        return bumpPrefetchTask;\n    },\n    cancelPrefetchTask: function() {\n        return cancelPrefetchTask;\n    },\n    createCacheKey: function() {\n        return createCacheKey;\n    },\n    getCurrentCacheVersion: function() {\n        return getCurrentCacheVersion;\n    },\n    navigate: function() {\n        return navigate;\n    },\n    prefetch: function() {\n        return prefetch;\n    },\n    revalidateEntireCache: function() {\n        return revalidateEntireCache;\n    },\n    schedulePrefetchTask: function() {\n        return schedulePrefetchTask;\n    }\n});\nconst notEnabled = ()=>{\n    throw Object.defineProperty(new Error('Segment Cache experiment is not enabled. This is a bug in Next.js.'), \"__NEXT_ERROR_CODE\", {\n        value: \"E654\",\n        enumerable: false,\n        configurable: true\n    });\n};\nconst prefetch =  false ? 0 : notEnabled;\nconst navigate =  false ? 0 : notEnabled;\nconst revalidateEntireCache =  false ? 0 : notEnabled;\nconst getCurrentCacheVersion =  false ? 0 : notEnabled;\nconst schedulePrefetchTask =  false ? 0 : notEnabled;\nconst cancelPrefetchTask =  false ? 0 : notEnabled;\nconst bumpPrefetchTask =  false ? 0 : notEnabled;\nconst createCacheKey =  false ? 0 : notEnabled;\nvar NavigationResultTag = /*#__PURE__*/ function(NavigationResultTag) {\n    NavigationResultTag[NavigationResultTag[\"MPA\"] = 0] = \"MPA\";\n    NavigationResultTag[NavigationResultTag[\"Success\"] = 1] = \"Success\";\n    NavigationResultTag[NavigationResultTag[\"NoOp\"] = 2] = \"NoOp\";\n    NavigationResultTag[NavigationResultTag[\"Async\"] = 3] = \"Async\";\n    return NavigationResultTag;\n}({});\nvar PrefetchPriority = /*#__PURE__*/ function(PrefetchPriority) {\n    /**\n   * Assigned to any visible link that was hovered/touched at some point. This\n   * is not removed on mouse exit, because a link that was momentarily\n   * hovered is more likely to to be interacted with than one that was not.\n   */ PrefetchPriority[PrefetchPriority[\"Intent\"] = 2] = \"Intent\";\n    /**\n   * The default priority for prefetch tasks.\n   */ PrefetchPriority[PrefetchPriority[\"Default\"] = 1] = \"Default\";\n    /**\n   * Assigned to tasks when they spawn non-blocking background work, like\n   * revalidating a partially cached entry to see if more data is available.\n   */ PrefetchPriority[PrefetchPriority[\"Background\"] = 0] = \"Background\";\n    return PrefetchPriority;\n}({});\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=segment-cache.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/unauthorized.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/client/components/unauthorized.js ***!
  \******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"unauthorized\", ({\n    enumerable: true,\n    get: function() {\n        return unauthorized;\n    }\n}));\nconst _httpaccessfallback = __webpack_require__(/*! ./http-access-fallback/http-access-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\");\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */ const DIGEST = \"\" + _httpaccessfallback.HTTP_ERROR_FALLBACK_ERROR_CODE + \";401\";\nfunction unauthorized() {\n    if (true) {\n        throw Object.defineProperty(new Error(\"`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E411\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // eslint-disable-next-line no-throw-literal\n    const error = Object.defineProperty(new Error(DIGEST), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = DIGEST;\n    throw error;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=unauthorized.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7Z0RBc0JnQkE7OztlQUFBQTs7O2dEQW5CVDtBQUVQLGdDQUFnQztBQUNoQzs7Ozs7Ozs7Ozs7O0NBWUMsR0FFRCxNQUFNQyxTQUFVLEtBQUVDLG9CQUFBQSw4QkFBOEIsR0FBQztBQUUxQyxTQUFTRjtJQUNkLElBQUksSUFBZ0QsRUFBRTtRQUNwRCxNQUFNLHFCQUVMLENBRkssSUFBSU0sTUFDUCxnSEFERzttQkFBQTt3QkFBQTswQkFBQTtRQUVOO0lBQ0Y7SUFFQSw0Q0FBNEM7SUFDNUMsTUFBTUMsUUFBUSxxQkFBaUIsQ0FBakIsSUFBSUQsTUFBTUwsU0FBVjtlQUFBO29CQUFBO3NCQUFBO0lBQWdCO0lBQzVCTSxNQUFrQ0MsTUFBTSxHQUFHUDtJQUM3QyxNQUFNTTtBQUNSIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHVuYXV0aG9yaXplZC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBIVFRQX0VSUk9SX0ZBTExCQUNLX0VSUk9SX0NPREUsXG4gIHR5cGUgSFRUUEFjY2Vzc0ZhbGxiYWNrRXJyb3IsXG59IGZyb20gJy4vaHR0cC1hY2Nlc3MtZmFsbGJhY2svaHR0cC1hY2Nlc3MtZmFsbGJhY2snXG5cbi8vIFRPRE86IEFkZCBgdW5hdXRob3JpemVkYCBkb2NzXG4vKipcbiAqIEBleHBlcmltZW50YWxcbiAqIFRoaXMgZnVuY3Rpb24gYWxsb3dzIHlvdSB0byByZW5kZXIgdGhlIFt1bmF1dGhvcml6ZWQuanMgZmlsZV0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2FwaS1yZWZlcmVuY2UvZmlsZS1jb252ZW50aW9ucy91bmF1dGhvcml6ZWQpXG4gKiB3aXRoaW4gYSByb3V0ZSBzZWdtZW50IGFzIHdlbGwgYXMgaW5qZWN0IGEgdGFnLlxuICpcbiAqIGB1bmF1dGhvcml6ZWQoKWAgY2FuIGJlIHVzZWQgaW5cbiAqIFtTZXJ2ZXIgQ29tcG9uZW50c10oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vcmVuZGVyaW5nL3NlcnZlci1jb21wb25lbnRzKSxcbiAqIFtSb3V0ZSBIYW5kbGVyc10oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vcm91dGluZy9yb3V0ZS1oYW5kbGVycyksIGFuZFxuICogW1NlcnZlciBBY3Rpb25zXShodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYnVpbGRpbmcteW91ci1hcHBsaWNhdGlvbi9kYXRhLWZldGNoaW5nL3NlcnZlci1hY3Rpb25zLWFuZC1tdXRhdGlvbnMpLlxuICpcbiAqXG4gKiBSZWFkIG1vcmU6IFtOZXh0LmpzIERvY3M6IGB1bmF1dGhvcml6ZWRgXShodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYXBpLXJlZmVyZW5jZS9mdW5jdGlvbnMvdW5hdXRob3JpemVkKVxuICovXG5cbmNvbnN0IERJR0VTVCA9IGAke0hUVFBfRVJST1JfRkFMTEJBQ0tfRVJST1JfQ09ERX07NDAxYFxuXG5leHBvcnQgZnVuY3Rpb24gdW5hdXRob3JpemVkKCk6IG5ldmVyIHtcbiAgaWYgKCFwcm9jZXNzLmVudi5fX05FWFRfRVhQRVJJTUVOVEFMX0FVVEhfSU5URVJSVVBUUykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgIGBcXGB1bmF1dGhvcml6ZWQoKVxcYCBpcyBleHBlcmltZW50YWwgYW5kIG9ubHkgYWxsb3dlZCB0byBiZSB1c2VkIHdoZW4gXFxgZXhwZXJpbWVudGFsLmF1dGhJbnRlcnJ1cHRzXFxgIGlzIGVuYWJsZWQuYFxuICAgIClcbiAgfVxuXG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby10aHJvdy1saXRlcmFsXG4gIGNvbnN0IGVycm9yID0gbmV3IEVycm9yKERJR0VTVCkgYXMgSFRUUEFjY2Vzc0ZhbGxiYWNrRXJyb3JcbiAgOyhlcnJvciBhcyBIVFRQQWNjZXNzRmFsbGJhY2tFcnJvcikuZGlnZXN0ID0gRElHRVNUXG4gIHRocm93IGVycm9yXG59XG4iXSwibmFtZXMiOlsidW5hdXRob3JpemVkIiwiRElHRVNUIiwiSFRUUF9FUlJPUl9GQUxMQkFDS19FUlJPUl9DT0RFIiwicHJvY2VzcyIsImVudiIsIl9fTkVYVF9FWFBFUklNRU5UQUxfQVVUSF9JTlRFUlJVUFRTIiwiRXJyb3IiLCJlcnJvciIsImRpZ2VzdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/unauthorized.js\n"));

/***/ })

}]);