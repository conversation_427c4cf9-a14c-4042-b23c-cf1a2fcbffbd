"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPanelPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell-ring.js\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _components_admin_SessionManager__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/admin/SessionManager */ \"(app-pages-browser)/./src/components/admin/SessionManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPanelPage() {\n    _s();\n    const { appSettings, dispatch, botSystemStatus } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_10__.useTradingContext)();\n    const [localSettings, setLocalSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(appSettings);\n    const [apiKey, setApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('iVIOLOnigRM31Qzm4UoLYsJo4QYIsd1XeXKztnwHfcijpWiAaWQKRsmx3NO7LrLA');\n    const [apiSecret, setApiSecret] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('jzAnpgIFFv3Ypdhf4jEXljjbkBpfJE5W2aN0zrtypmD3RAjoh2vdQXMr66LOv5fp');\n    const [showApiKey, setShowApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showApiSecret, setShowApiSecret] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [telegramToken, setTelegramToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [telegramChatId, setTelegramChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanelPage.useEffect\": ()=>{\n            setLocalSettings(appSettings);\n        }\n    }[\"AdminPanelPage.useEffect\"], [\n        appSettings\n    ]);\n    const handleSettingsChange = (key, value)=>{\n        setLocalSettings((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const handleSaveAppSettings = ()=>{\n        dispatch({\n            type: 'SET_APP_SETTINGS',\n            payload: localSettings\n        });\n        toast({\n            title: \"App Settings Saved\",\n            description: \"Global application settings have been updated.\"\n        });\n    };\n    const handleSaveApiKeys = async ()=>{\n        try {\n            // Store API keys securely (in a real implementation, these would be encrypted)\n            localStorage.setItem('binance_api_key', apiKey);\n            localStorage.setItem('binance_api_secret', apiSecret);\n            console.log(\"API Keys Saved:\", {\n                apiKey: apiKey.substring(0, 10) + '...',\n                apiSecret: apiSecret.substring(0, 10) + '...'\n            });\n            toast({\n                title: \"API Keys Saved\",\n                description: \"Binance API keys have been saved securely.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save API keys.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleTestApiConnection = async ()=>{\n        try {\n            // Test connection to Binance API\n            const response = await fetch('https://api.binance.com/api/v3/ping');\n            if (response.ok) {\n                toast({\n                    title: \"API Connection Test\",\n                    description: \"Successfully connected to Binance API!\"\n                });\n            } else {\n                toast({\n                    title: \"Connection Failed\",\n                    description: \"Unable to connect to Binance API.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            toast({\n                title: \"Connection Error\",\n                description: \"Network error while testing API connection.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleSaveTelegramConfig = ()=>{\n        try {\n            localStorage.setItem('telegram_bot_token', telegramToken);\n            localStorage.setItem('telegram_chat_id', telegramChatId);\n            console.log(\"Telegram Config Saved:\", {\n                telegramToken: telegramToken.substring(0, 10) + '...',\n                telegramChatId\n            });\n            toast({\n                title: \"Telegram Config Saved\",\n                description: \"Telegram settings have been saved successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save Telegram configuration.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleTestTelegram = async ()=>{\n        if (!telegramToken || !telegramChatId) {\n            toast({\n                title: \"Missing Configuration\",\n                description: \"Please enter both Telegram bot token and chat ID.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    chat_id: telegramChatId,\n                    text: '🤖 Test message from Pluto Trading Bot! Your Telegram integration is working correctly.'\n                })\n            });\n            if (response.ok) {\n                toast({\n                    title: \"Telegram Test Successful\",\n                    description: \"Test message sent successfully!\"\n                });\n            } else {\n                toast({\n                    title: \"Telegram Test Failed\",\n                    description: \"Failed to send test message. Check your token and chat ID.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            toast({\n                title: \"Telegram Error\",\n                description: \"Network error while testing Telegram integration.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const adminTabs = [\n        {\n            value: \"systemTools\",\n            label: \"System Tools\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 58\n            }, this)\n        },\n        {\n            value: \"appSettings\",\n            label: \"App Settings\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 58\n            }, this)\n        },\n        {\n            value: \"apiKeys\",\n            label: \"Exchange API Keys\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 59\n            }, this)\n        },\n        {\n            value: \"telegram\",\n            label: \"Telegram Integration\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 63\n            }, this)\n        },\n        {\n            value: \"sessionManager\",\n            label: \"Session Manager\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 64\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n            className: \"border-2 border-border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                    className: \"flex flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                    className: \"text-3xl font-bold text-primary\",\n                                    children: \"Admin Panel\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                    children: \"Manage global settings and tools for Pluto Trading Bot.\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>router.push('/dashboard'),\n                            className: \"btn-outline-neo\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                \"Return to Dashboard\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                        defaultValue: \"systemTools\",\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_13__.ScrollArea, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                    className: \"bg-card border-border border-2 p-1\",\n                                    children: adminTabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: tab.value,\n                                            className: \"px-4 py-2 text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    tab.icon,\n                                                    \" \",\n                                                    tab.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 22\n                                            }, this)\n                                        }, tab.value, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 20\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"systemTools\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BackendStatus, {\n                                            showDetails: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                            className: \"bg-card-foreground/5 border-border border-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                        children: \"System Tools\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 31\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"Database Editor, Clean Duplicates, Export/Import, Backup/Restore, Diagnostics - (Placeholders for future implementation).\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    className: \"btn-outline-neo\",\n                                                                    onClick: ()=>toast({\n                                                                            title: \"DB Editor Clicked\"\n                                                                        }),\n                                                                    children: \"View Database (Read-Only)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    className: \"btn-outline-neo\",\n                                                                    onClick: ()=>toast({\n                                                                            title: \"Export Orders Clicked\"\n                                                                        }),\n                                                                    children: \"Export Orders to Excel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    className: \"btn-outline-neo\",\n                                                                    onClick: ()=>toast({\n                                                                            title: \"Export History Clicked\"\n                                                                        }),\n                                                                    children: \"Export History to Excel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    className: \"btn-outline-neo\",\n                                                                    onClick: ()=>toast({\n                                                                            title: \"Backup DB Clicked\"\n                                                                        }),\n                                                                    children: \"Backup Database\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    className: \"btn-outline-neo\",\n                                                                    onClick: ()=>toast({\n                                                                            title: \"Restore DB Clicked\"\n                                                                        }),\n                                                                    disabled: true,\n                                                                    children: \"Restore Database\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 159,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    className: \"btn-outline-neo\",\n                                                                    onClick: ()=>toast({\n                                                                            title: \"Diagnostics Clicked\"\n                                                                        }),\n                                                                    children: \"Run System Diagnostics\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"appSettings\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    className: \"bg-card-foreground/5 border-border border-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                children: \"Application Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 29\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"preferredStablecoin\",\n                                                            children: \"Preferred Stablecoin (for Swap Mode)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                            value: localSettings.preferredStablecoin,\n                                                            onValueChange: (val)=>handleSettingsChange('preferredStablecoin', val),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                    id: \"preferredStablecoin\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                        placeholder: \"Select Stablecoin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 177,\n                                                                        columnNumber: 63\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                    children: _lib_types__WEBPACK_IMPORTED_MODULE_11__.AVAILABLE_STABLECOINS.map((sc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                            value: sc,\n                                                                            children: sc\n                                                                        }, sc, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 179,\n                                                                            columnNumber: 58\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 178,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"priceUpdateIntervalMs\",\n                                                            children: \"Price Update Interval (ms)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"priceUpdateIntervalMs\",\n                                                            type: \"number\",\n                                                            value: localSettings.priceUpdateIntervalMs || 1000,\n                                                            onChange: (e)=>handleSettingsChange('priceUpdateIntervalMs', parseInt(e.target.value) || 1000)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    onClick: handleSaveAppSettings,\n                                                    className: \"btn-neo\",\n                                                    children: \"Save App Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"apiKeys\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    className: \"bg-card-foreground/5 border-border border-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                children: \"Exchange API Keys (Binance)\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 29\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Configure your Binance API keys for real trading. Keys are stored securely in browser storage.\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"apiKey\",\n                                                            children: \"API Key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"apiKey\",\n                                                                    type: showApiKey ? \"text\" : \"password\",\n                                                                    value: apiKey,\n                                                                    onChange: (e)=>setApiKey(e.target.value),\n                                                                    placeholder: \"Enter your Binance API key\",\n                                                                    className: \"pr-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                    onClick: ()=>setShowApiKey(!showApiKey),\n                                                                    children: showApiKey ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 39\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 72\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"apiSecret\",\n                                                            children: \"API Secret\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"apiSecret\",\n                                                                    type: showApiSecret ? \"text\" : \"password\",\n                                                                    value: apiSecret,\n                                                                    onChange: (e)=>setApiSecret(e.target.value),\n                                                                    placeholder: \"Enter your Binance API secret\",\n                                                                    className: \"pr-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                    onClick: ()=>setShowApiSecret(!showApiSecret),\n                                                                    children: showApiSecret ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 42\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 75\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: handleSaveApiKeys,\n                                                            className: \"btn-neo\",\n                                                            children: \"Save API Keys\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: handleTestApiConnection,\n                                                            variant: \"outline\",\n                                                            className: \"btn-outline-neo\",\n                                                            children: \"Test Connection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"telegram\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    className: \"bg-card-foreground/5 border-border border-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                children: \"Telegram Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 29\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Configure Telegram bot for real-time trading notifications.\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"telegramToken\",\n                                                            children: \"Telegram Bot Token\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"telegramToken\",\n                                                            type: \"password\",\n                                                            value: telegramToken,\n                                                            onChange: (e)=>setTelegramToken(e.target.value),\n                                                            placeholder: \"Enter your Telegram bot token\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"telegramChatId\",\n                                                            children: \"Telegram Chat ID\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"telegramChatId\",\n                                                            value: telegramChatId,\n                                                            onChange: (e)=>setTelegramChatId(e.target.value),\n                                                            placeholder: \"Enter your Telegram chat ID\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                            id: \"notifyOnOrder\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"notifyOnOrder\",\n                                                            children: \"Notify on Order Execution\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                            id: \"notifyOnErrors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"notifyOnErrors\",\n                                                            children: \"Notify on Errors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: handleSaveTelegramConfig,\n                                                            className: \"btn-neo\",\n                                                            children: \"Save Telegram Config\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: handleTestTelegram,\n                                                            variant: \"outline\",\n                                                            className: \"btn-outline-neo\",\n                                                            children: \"Test Telegram\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6 bg-blue-500/10 border border-blue-500/20 rounded-md p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-blue-600 mb-2\",\n                                                            children: \"\\uD83D\\uDCF1 Quick Setup Guide\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                            className: \"text-sm space-y-1 list-decimal list-inside\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Search for @BotFather on Telegram\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Send /newbot and follow instructions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Copy the bot token and paste above\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Send a message to your bot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Visit: api.telegram.org/bot[TOKEN]/getUpdates\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Copy your chat ID and paste above\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"sessionManager\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SessionManager__WEBPACK_IMPORTED_MODULE_14__.SessionManager, {}, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPanelPage, \"tbnDcI/g4v3KjLPgVB/4mL2wCfY=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_10__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminPanelPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPanelPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});