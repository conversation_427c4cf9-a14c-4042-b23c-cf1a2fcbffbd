"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-body.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-body.js ***!
  \********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DialogBody\", ({\n    enumerable: true,\n    get: function() {\n        return DialogBody;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst DialogBody = function DialogBody(param) {\n    let { children, className } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        \"data-nextjs-dialog-body\": true,\n        className: className,\n        children: children\n    });\n};\n_c = DialogBody;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dialog-body.js.map\nvar _c;\n$RefreshReg$(_c, \"DialogBody\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9kaWFsb2cvZGlhbG9nLWJvZHkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs4Q0FrQlNBOzs7ZUFBQUE7Ozs7OzZFQWxCYztBQU92QixtQkFBOEMsU0FBU0EsV0FBVyxLQUdqRTtJQUhpRSxNQUNoRUMsUUFBUSxFQUNSQyxTQUFTLEVBQ1YsR0FIaUU7SUFJaEUscUJBQ0UscUJBQUNDLE9BQUFBO1FBQUlDLHlCQUF1QjtRQUFDRixXQUFXQTtrQkFDckNEOztBQUdQO0tBVE1EIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcY29tcG9uZW50c1xcZGlhbG9nXFxkaWFsb2ctYm9keS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmV4cG9ydCB0eXBlIERpYWxvZ0JvZHlQcm9wcyA9IHtcbiAgY2hpbGRyZW4/OiBSZWFjdC5SZWFjdE5vZGVcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmNvbnN0IERpYWxvZ0JvZHk6IFJlYWN0LkZDPERpYWxvZ0JvZHlQcm9wcz4gPSBmdW5jdGlvbiBEaWFsb2dCb2R5KHtcbiAgY2hpbGRyZW4sXG4gIGNsYXNzTmFtZSxcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGRhdGEtbmV4dGpzLWRpYWxvZy1ib2R5IGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG5leHBvcnQgeyBEaWFsb2dCb2R5IH1cbiJdLCJuYW1lcyI6WyJEaWFsb2dCb2R5IiwiY2hpbGRyZW4iLCJjbGFzc05hbWUiLCJkaXYiLCJkYXRhLW5leHRqcy1kaWFsb2ctYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-body.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-content.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-content.js ***!
  \***********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DialogContent\", ({\n    enumerable: true,\n    get: function() {\n        return DialogContent;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst DialogContent = function DialogContent(param) {\n    let { children, className } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        \"data-nextjs-dialog-content\": true,\n        className: className,\n        children: children\n    });\n};\n_c = DialogContent;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dialog-content.js.map\nvar _c;\n$RefreshReg$(_c, \"DialogContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9kaWFsb2cvZGlhbG9nLWNvbnRlbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztpREFrQlNBOzs7ZUFBQUE7Ozs7OzZFQWxCYztBQU92QixzQkFBb0QsU0FBU0EsY0FBYyxLQUcxRTtJQUgwRSxNQUN6RUMsUUFBUSxFQUNSQyxTQUFTLEVBQ1YsR0FIMEU7SUFJekUscUJBQ0UscUJBQUNDLE9BQUFBO1FBQUlDLDRCQUEwQjtRQUFDRixXQUFXQTtrQkFDeENEOztBQUdQO0tBVE1EIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcY29tcG9uZW50c1xcZGlhbG9nXFxkaWFsb2ctY29udGVudC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmV4cG9ydCB0eXBlIERpYWxvZ0NvbnRlbnRQcm9wcyA9IHtcbiAgY2hpbGRyZW4/OiBSZWFjdC5SZWFjdE5vZGVcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmNvbnN0IERpYWxvZ0NvbnRlbnQ6IFJlYWN0LkZDPERpYWxvZ0NvbnRlbnRQcm9wcz4gPSBmdW5jdGlvbiBEaWFsb2dDb250ZW50KHtcbiAgY2hpbGRyZW4sXG4gIGNsYXNzTmFtZSxcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGRhdGEtbmV4dGpzLWRpYWxvZy1jb250ZW50IGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG5leHBvcnQgeyBEaWFsb2dDb250ZW50IH1cbiJdLCJuYW1lcyI6WyJEaWFsb2dDb250ZW50IiwiY2hpbGRyZW4iLCJjbGFzc05hbWUiLCJkaXYiLCJkYXRhLW5leHRqcy1kaWFsb2ctY29udGVudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-content.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-footer.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-footer.js ***!
  \**********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DialogFooter\", ({\n    enumerable: true,\n    get: function() {\n        return DialogFooter;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nfunction DialogFooter(param) {\n    let { children, className } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        \"data-nextjs-dialog-footer\": true,\n        className: className,\n        children: children\n    });\n}\n_c = DialogFooter;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dialog-footer.js.map\nvar _c;\n$RefreshReg$(_c, \"DialogFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9kaWFsb2cvZGlhbG9nLWZvb3Rlci5qcyIsIm1hcHBpbmdzIjoiOzs7O2dEQUtnQkE7OztlQUFBQTs7OztBQUFULHNCQUFzQixLQUEwQztJQUExQyxNQUFFQyxRQUFRLEVBQUVDLFNBQVMsRUFBcUIsR0FBMUM7SUFDM0IscUJBQ0UscUJBQUNDLE9BQUFBO1FBQUlDLDJCQUF5QjtRQUFDRixXQUFXQTtrQkFDdkNEOztBQUdQO0tBTmdCRCIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXGNvbXBvbmVudHNcXGRpYWxvZ1xcZGlhbG9nLWZvb3Rlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHR5cGUgRGlhbG9nRm9vdGVyUHJvcHMgPSB7XG4gIGNoaWxkcmVuPzogUmVhY3QuUmVhY3ROb2RlXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xufVxuXG5leHBvcnQgZnVuY3Rpb24gRGlhbG9nRm9vdGVyKHsgY2hpbGRyZW4sIGNsYXNzTmFtZSB9OiBEaWFsb2dGb290ZXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXYgZGF0YS1uZXh0anMtZGlhbG9nLWZvb3RlciBjbGFzc05hbWU9e2NsYXNzTmFtZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJEaWFsb2dGb290ZXIiLCJjaGlsZHJlbiIsImNsYXNzTmFtZSIsImRpdiIsImRhdGEtbmV4dGpzLWRpYWxvZy1mb290ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-footer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-header.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-header.js ***!
  \**********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DialogHeader\", ({\n    enumerable: true,\n    get: function() {\n        return DialogHeader;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst DialogHeader = function DialogHeader(param) {\n    let { children, className } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        \"data-nextjs-dialog-header\": true,\n        className: className,\n        children: children\n    });\n};\n_c = DialogHeader;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dialog-header.js.map\nvar _c;\n$RefreshReg$(_c, \"DialogHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9kaWFsb2cvZGlhbG9nLWhlYWRlci5qcyIsIm1hcHBpbmdzIjoiOzs7O2dEQWtCU0E7OztlQUFBQTs7Ozs7NkVBbEJjO0FBT3ZCLHFCQUFrRCxTQUFTQSxhQUFhLEtBR3ZFO0lBSHVFLE1BQ3RFQyxRQUFRLEVBQ1JDLFNBQVMsRUFDVixHQUh1RTtJQUl0RSxxQkFDRSxxQkFBQ0MsT0FBQUE7UUFBSUMsMkJBQXlCO1FBQUNGLFdBQVdBO2tCQUN2Q0Q7O0FBR1A7S0FUTUQiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xccmVhY3QtZGV2LW92ZXJsYXlcXHVpXFxjb21wb25lbnRzXFxkaWFsb2dcXGRpYWxvZy1oZWFkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuXG5leHBvcnQgdHlwZSBEaWFsb2dIZWFkZXJQcm9wcyA9IHtcbiAgY2hpbGRyZW4/OiBSZWFjdC5SZWFjdE5vZGVcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmNvbnN0IERpYWxvZ0hlYWRlcjogUmVhY3QuRkM8RGlhbG9nSGVhZGVyUHJvcHM+ID0gZnVuY3Rpb24gRGlhbG9nSGVhZGVyKHtcbiAgY2hpbGRyZW4sXG4gIGNsYXNzTmFtZSxcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGRhdGEtbmV4dGpzLWRpYWxvZy1oZWFkZXIgY2xhc3NOYW1lPXtjbGFzc05hbWV9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvZGl2PlxuICApXG59XG5cbmV4cG9ydCB7IERpYWxvZ0hlYWRlciB9XG4iXSwibmFtZXMiOlsiRGlhbG9nSGVhZGVyIiwiY2hpbGRyZW4iLCJjbGFzc05hbWUiLCJkaXYiLCJkYXRhLW5leHRqcy1kaWFsb2ctaGVhZGVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-header.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Dialog\", ({\n    enumerable: true,\n    get: function() {\n        return Dialog;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _useonclickoutside = __webpack_require__(/*! ../../hooks/use-on-click-outside */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-on-click-outside.js\");\nconst _usemeasureheight = __webpack_require__(/*! ../../hooks/use-measure-height */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-measure-height.js\");\nconst CSS_SELECTORS_TO_EXCLUDE_ON_CLICK_OUTSIDE = [\n    '[data-next-mark]',\n    '[data-issues-open]',\n    '#nextjs-dev-tools-menu',\n    '[data-nextjs-error-overlay-nav]',\n    '[data-info-popover]'\n];\nconst Dialog = function Dialog(param) {\n    _s();\n    let { children, type, className, onClose, 'aria-labelledby': ariaLabelledBy, 'aria-describedby': ariaDescribedBy, dialogResizerRef, ...props } = param;\n    const dialogRef = _react.useRef(null);\n    const [role, setRole] = _react.useState(typeof document !== 'undefined' && document.hasFocus() ? 'dialog' : undefined);\n    const ref = _react.useRef(null);\n    const [height, pristine] = (0, _usemeasureheight.useMeasureHeight)(ref);\n    (0, _useonclickoutside.useOnClickOutside)(dialogRef.current, CSS_SELECTORS_TO_EXCLUDE_ON_CLICK_OUTSIDE, (e)=>{\n        e.preventDefault();\n        return onClose == null ? void 0 : onClose();\n    });\n    _react.useEffect({\n        \"Dialog.useEffect\": ()=>{\n            if (dialogRef.current == null) {\n                return;\n            }\n            function handleFocus() {\n                // safari will force itself as the active application when a background page triggers any sort of autofocus\n                // this is a workaround to only set the dialog role if the document has focus\n                setRole(document.hasFocus() ? 'dialog' : undefined);\n            }\n            window.addEventListener('focus', handleFocus);\n            window.addEventListener('blur', handleFocus);\n            return ({\n                \"Dialog.useEffect\": ()=>{\n                    window.removeEventListener('focus', handleFocus);\n                    window.removeEventListener('blur', handleFocus);\n                }\n            })[\"Dialog.useEffect\"];\n        }\n    }[\"Dialog.useEffect\"], []);\n    _react.useEffect({\n        \"Dialog.useEffect\": ()=>{\n            const dialog = dialogRef.current;\n            const root = dialog == null ? void 0 : dialog.getRootNode();\n            const initialActiveElement = root instanceof ShadowRoot ? root == null ? void 0 : root.activeElement : null;\n            // Trap focus within the dialog\n            dialog == null ? void 0 : dialog.focus();\n            return ({\n                \"Dialog.useEffect\": ()=>{\n                    // Blur first to avoid getting stuck, in case `activeElement` is missing\n                    dialog == null ? void 0 : dialog.blur();\n                    // Restore focus to the previously active element\n                    initialActiveElement == null ? void 0 : initialActiveElement.focus();\n                }\n            })[\"Dialog.useEffect\"];\n        }\n    }[\"Dialog.useEffect\"], []);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        ref: dialogRef,\n        tabIndex: -1,\n        \"data-nextjs-dialog\": true,\n        role: role,\n        \"aria-labelledby\": ariaLabelledBy,\n        \"aria-describedby\": ariaDescribedBy,\n        \"aria-modal\": \"true\",\n        className: className,\n        onKeyDown: (e)=>{\n            if (e.key === 'Escape') {\n                onClose == null ? void 0 : onClose();\n            }\n        },\n        ...props,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n            ref: dialogResizerRef,\n            \"data-nextjs-dialog-sizer\": true,\n            // [x] Don't animate on initial load\n            // [x] No duplicate elements\n            // [x] Responds to content growth\n            style: {\n                height,\n                transition: pristine ? undefined : 'height 250ms var(--timing-swift)'\n            },\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                ref: ref,\n                children: children\n            })\n        })\n    });\n};\n_s(Dialog, \"fUJNA+MBJ7/yoZvHPU9jbZNPJXA=\");\n_c = Dialog;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dialog.js.map\nvar _c;\n$RefreshReg$(_c, \"Dialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/index.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/index.js ***!
  \**************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Dialog: function() {\n        return _dialog.Dialog;\n    },\n    DialogBody: function() {\n        return _dialogbody.DialogBody;\n    },\n    DialogContent: function() {\n        return _dialogcontent.DialogContent;\n    },\n    DialogFooter: function() {\n        return _dialogfooter.DialogFooter;\n    },\n    DialogHeader: function() {\n        return _dialogheader.DialogHeader;\n    },\n    styles: function() {\n        return _styles.styles;\n    }\n});\nconst _dialog = __webpack_require__(/*! ./dialog */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog.js\");\nconst _dialogbody = __webpack_require__(/*! ./dialog-body */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-body.js\");\nconst _dialogcontent = __webpack_require__(/*! ./dialog-content */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-content.js\");\nconst _dialogheader = __webpack_require__(/*! ./dialog-header */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-header.js\");\nconst _dialogfooter = __webpack_require__(/*! ./dialog-footer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-footer.js\");\nconst _styles = __webpack_require__(/*! ./styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/styles.js\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9kaWFsb2cvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBQVNBLE1BQU07ZUFBTkEsUUFBQUEsTUFBTTs7SUFDTkMsVUFBVTtlQUFWQSxZQUFBQSxVQUFVOztJQUNWQyxhQUFhO2VBQWJBLGVBQUFBLGFBQWE7O0lBRWJDLFlBQVk7ZUFBWkEsY0FBQUEsWUFBWTs7SUFEWkMsWUFBWTtlQUFaQSxjQUFBQSxZQUFZOztJQUVaQyxNQUFNO2VBQU5BLFFBQUFBLE1BQU07OztvQ0FMUTt3Q0FDSTsyQ0FDRzswQ0FDRDswQ0FDQTtvQ0FDTiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXGNvbXBvbmVudHNcXGRpYWxvZ1xcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgRGlhbG9nIH0gZnJvbSAnLi9kaWFsb2cnXG5leHBvcnQgeyBEaWFsb2dCb2R5IH0gZnJvbSAnLi9kaWFsb2ctYm9keSdcbmV4cG9ydCB7IERpYWxvZ0NvbnRlbnQgfSBmcm9tICcuL2RpYWxvZy1jb250ZW50J1xuZXhwb3J0IHsgRGlhbG9nSGVhZGVyIH0gZnJvbSAnLi9kaWFsb2ctaGVhZGVyJ1xuZXhwb3J0IHsgRGlhbG9nRm9vdGVyIH0gZnJvbSAnLi9kaWFsb2ctZm9vdGVyJ1xuZXhwb3J0IHsgc3R5bGVzIH0gZnJvbSAnLi9zdHlsZXMnXG4iXSwibmFtZXMiOlsiRGlhbG9nIiwiRGlhbG9nQm9keSIsIkRpYWxvZ0NvbnRlbnQiLCJEaWFsb2dGb290ZXIiLCJEaWFsb2dIZWFkZXIiLCJzdHlsZXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/styles.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/styles.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst styles = \"\\n  [data-nextjs-dialog-root] {\\n    --next-dialog-radius: var(--rounded-xl);\\n    --next-dialog-footer-height: var(--size-48);\\n    --next-dialog-max-width: 960px;\\n    --next-dialog-row-padding: 16px;\\n    --next-dialog-container-padding: 12px;\\n\\n    display: flex;\\n    flex-direction: column-reverse;\\n    width: 100%;\\n    max-height: calc(100% - 56px);\\n    max-width: var(--next-dialog-max-width);\\n    margin-right: auto;\\n    margin-left: auto;\\n    scale: 0.98;\\n    opacity: 0;\\n    transition-property: scale, opacity;\\n    transition-duration: var(--transition-duration);\\n    transition-timing-function: var(--timing-overlay);\\n\\n    &[data-rendered='true'] {\\n      opacity: 1;\\n      scale: 1;\\n    }\\n  }\\n\\n  [data-nextjs-dialog] {\\n    outline: none;\\n    overflow: hidden;\\n  }\\n  [data-nextjs-dialog]::-webkit-scrollbar {\\n    width: 6px;\\n    border-radius: 0 0 1rem 1rem;\\n    margin-bottom: 1rem;\\n  }\\n  [data-nextjs-dialog]::-webkit-scrollbar-button {\\n    display: none;\\n  }\\n  [data-nextjs-dialog]::-webkit-scrollbar-track {\\n    border-radius: 0 0 1rem 1rem;\\n    background-color: var(--color-background-100);\\n  }\\n  [data-nextjs-dialog]::-webkit-scrollbar-thumb {\\n    border-radius: 1rem;\\n    background-color: var(--color-gray-500);\\n  }\\n\\n  \\n  [data-nextjs-dialog-sizer] {\\n    overflow: hidden;\\n    border-radius: inherit;\\n  }\\n\\n  [data-nextjs-dialog-backdrop] {\\n    opacity: 0;\\n    transition: opacity var(--transition-duration) var(--timing-overlay);\\n  }\\n\\n  [data-nextjs-dialog-overlay][data-rendered='true']\\n    [data-nextjs-dialog-backdrop] {\\n    opacity: 1;\\n  }\\n\\n  [data-nextjs-dialog-content] {\\n    overflow-y: auto;\\n    border: none;\\n    margin: 0;\\n    display: flex;\\n    flex-direction: column;\\n    position: relative;\\n    padding: 16px 12px;\\n  }\\n\\n  /* Account for the footer height, when present */\\n  [data-nextjs-dialog][data-has-footer='true'] [data-nextjs-dialog-body] {\\n    margin-bottom: var(--next-dialog-footer-height);\\n  }\\n\\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-header] {\\n    flex-shrink: 0;\\n    margin-bottom: 8px;\\n  }\\n\\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-body] {\\n    position: relative;\\n    flex: 1 1 auto;\\n  }\\n\\n  [data-nextjs-dialog-footer] {\\n    /* Subtract border width */\\n    width: calc(100% - 2px);\\n    /* \\n      We make this element fixed to anchor it to the bottom during the height transition.\\n      If you make this relative it will jump during the transition and not collapse or expand smoothly.\\n      If you make this absolute it will remain stuck at its initial position when scrolling the dialog.\\n    */\\n    position: fixed;\\n    bottom: 1px;\\n    min-height: var(--next-dialog-footer-height);\\n    border-radius: 0 0 var(--next-dialog-radius) var(--next-dialog-radius);\\n    overflow: hidden;\\n\\n    > * {\\n      min-height: var(--next-dialog-footer-height);\\n    }\\n  }\\n\\n  @media (max-height: 812px) {\\n    [data-nextjs-dialog-overlay] {\\n      max-height: calc(100% - 15px);\\n    }\\n  }\\n\\n  @media (min-width: 576px) {\\n    [data-nextjs-dialog-root] {\\n      --next-dialog-max-width: 540px;\\n    }\\n  }\\n\\n  @media (min-width: 768px) {\\n    [data-nextjs-dialog-root] {\\n      --next-dialog-max-width: 720px;\\n    }\\n  }\\n\\n  @media (min-width: 992px) {\\n    [data-nextjs-dialog-root] {\\n      --next-dialog-max-width: 960px;\\n    }\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/styles.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-body.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-body.js ***!
  \********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DialogBody\", ({\n    enumerable: true,\n    get: function() {\n        return DialogBody;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst DialogBody = function DialogBody(param) {\n    let { children, className } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        \"data-nextjs-dialog-body\": true,\n        className: className,\n        children: children\n    });\n};\n_c = DialogBody;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dialog-body.js.map\nvar _c;\n$RefreshReg$(_c, \"DialogBody\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9kaWFsb2cvZGlhbG9nLWJvZHkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs4Q0FrQlNBOzs7ZUFBQUE7Ozs7OzZFQWxCYztBQU92QixtQkFBOEMsU0FBU0EsV0FBVyxLQUdqRTtJQUhpRSxNQUNoRUMsUUFBUSxFQUNSQyxTQUFTLEVBQ1YsR0FIaUU7SUFJaEUscUJBQ0UscUJBQUNDLE9BQUFBO1FBQUlDLHlCQUF1QjtRQUFDRixXQUFXQTtrQkFDckNEOztBQUdQO0tBVE1EIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcY29tcG9uZW50c1xcZGlhbG9nXFxkaWFsb2ctYm9keS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmV4cG9ydCB0eXBlIERpYWxvZ0JvZHlQcm9wcyA9IHtcbiAgY2hpbGRyZW4/OiBSZWFjdC5SZWFjdE5vZGVcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmNvbnN0IERpYWxvZ0JvZHk6IFJlYWN0LkZDPERpYWxvZ0JvZHlQcm9wcz4gPSBmdW5jdGlvbiBEaWFsb2dCb2R5KHtcbiAgY2hpbGRyZW4sXG4gIGNsYXNzTmFtZSxcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGRhdGEtbmV4dGpzLWRpYWxvZy1ib2R5IGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG5leHBvcnQgeyBEaWFsb2dCb2R5IH1cbiJdLCJuYW1lcyI6WyJEaWFsb2dCb2R5IiwiY2hpbGRyZW4iLCJjbGFzc05hbWUiLCJkaXYiLCJkYXRhLW5leHRqcy1kaWFsb2ctYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-body.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-content.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-content.js ***!
  \***********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DialogContent\", ({\n    enumerable: true,\n    get: function() {\n        return DialogContent;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst DialogContent = function DialogContent(param) {\n    let { children, className } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        \"data-nextjs-dialog-content\": true,\n        className: className,\n        children: children\n    });\n};\n_c = DialogContent;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dialog-content.js.map\nvar _c;\n$RefreshReg$(_c, \"DialogContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9kaWFsb2cvZGlhbG9nLWNvbnRlbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztpREFrQlNBOzs7ZUFBQUE7Ozs7OzZFQWxCYztBQU92QixzQkFBb0QsU0FBU0EsY0FBYyxLQUcxRTtJQUgwRSxNQUN6RUMsUUFBUSxFQUNSQyxTQUFTLEVBQ1YsR0FIMEU7SUFJekUscUJBQ0UscUJBQUNDLE9BQUFBO1FBQUlDLDRCQUEwQjtRQUFDRixXQUFXQTtrQkFDeENEOztBQUdQO0tBVE1EIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcY29tcG9uZW50c1xcZGlhbG9nXFxkaWFsb2ctY29udGVudC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmV4cG9ydCB0eXBlIERpYWxvZ0NvbnRlbnRQcm9wcyA9IHtcbiAgY2hpbGRyZW4/OiBSZWFjdC5SZWFjdE5vZGVcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmNvbnN0IERpYWxvZ0NvbnRlbnQ6IFJlYWN0LkZDPERpYWxvZ0NvbnRlbnRQcm9wcz4gPSBmdW5jdGlvbiBEaWFsb2dDb250ZW50KHtcbiAgY2hpbGRyZW4sXG4gIGNsYXNzTmFtZSxcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGRhdGEtbmV4dGpzLWRpYWxvZy1jb250ZW50IGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG5leHBvcnQgeyBEaWFsb2dDb250ZW50IH1cbiJdLCJuYW1lcyI6WyJEaWFsb2dDb250ZW50IiwiY2hpbGRyZW4iLCJjbGFzc05hbWUiLCJkaXYiLCJkYXRhLW5leHRqcy1kaWFsb2ctY29udGVudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-content.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-footer.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-footer.js ***!
  \**********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DialogFooter\", ({\n    enumerable: true,\n    get: function() {\n        return DialogFooter;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nfunction DialogFooter(param) {\n    let { children, className } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        \"data-nextjs-dialog-footer\": true,\n        className: className,\n        children: children\n    });\n}\n_c = DialogFooter;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dialog-footer.js.map\nvar _c;\n$RefreshReg$(_c, \"DialogFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9kaWFsb2cvZGlhbG9nLWZvb3Rlci5qcyIsIm1hcHBpbmdzIjoiOzs7O2dEQUtnQkE7OztlQUFBQTs7OztBQUFULHNCQUFzQixLQUEwQztJQUExQyxNQUFFQyxRQUFRLEVBQUVDLFNBQVMsRUFBcUIsR0FBMUM7SUFDM0IscUJBQ0UscUJBQUNDLE9BQUFBO1FBQUlDLDJCQUF5QjtRQUFDRixXQUFXQTtrQkFDdkNEOztBQUdQO0tBTmdCRCIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXGNvbXBvbmVudHNcXGRpYWxvZ1xcZGlhbG9nLWZvb3Rlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHR5cGUgRGlhbG9nRm9vdGVyUHJvcHMgPSB7XG4gIGNoaWxkcmVuPzogUmVhY3QuUmVhY3ROb2RlXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xufVxuXG5leHBvcnQgZnVuY3Rpb24gRGlhbG9nRm9vdGVyKHsgY2hpbGRyZW4sIGNsYXNzTmFtZSB9OiBEaWFsb2dGb290ZXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXYgZGF0YS1uZXh0anMtZGlhbG9nLWZvb3RlciBjbGFzc05hbWU9e2NsYXNzTmFtZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJEaWFsb2dGb290ZXIiLCJjaGlsZHJlbiIsImNsYXNzTmFtZSIsImRpdiIsImRhdGEtbmV4dGpzLWRpYWxvZy1mb290ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-footer.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-header.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-header.js ***!
  \**********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DialogHeader\", ({\n    enumerable: true,\n    get: function() {\n        return DialogHeader;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst DialogHeader = function DialogHeader(param) {\n    let { children, className } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        \"data-nextjs-dialog-header\": true,\n        className: className,\n        children: children\n    });\n};\n_c = DialogHeader;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dialog-header.js.map\nvar _c;\n$RefreshReg$(_c, \"DialogHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9kaWFsb2cvZGlhbG9nLWhlYWRlci5qcyIsIm1hcHBpbmdzIjoiOzs7O2dEQWtCU0E7OztlQUFBQTs7Ozs7NkVBbEJjO0FBT3ZCLHFCQUFrRCxTQUFTQSxhQUFhLEtBR3ZFO0lBSHVFLE1BQ3RFQyxRQUFRLEVBQ1JDLFNBQVMsRUFDVixHQUh1RTtJQUl0RSxxQkFDRSxxQkFBQ0MsT0FBQUE7UUFBSUMsMkJBQXlCO1FBQUNGLFdBQVdBO2tCQUN2Q0Q7O0FBR1A7S0FUTUQiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xccmVhY3QtZGV2LW92ZXJsYXlcXHVpXFxjb21wb25lbnRzXFxkaWFsb2dcXGRpYWxvZy1oZWFkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuXG5leHBvcnQgdHlwZSBEaWFsb2dIZWFkZXJQcm9wcyA9IHtcbiAgY2hpbGRyZW4/OiBSZWFjdC5SZWFjdE5vZGVcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmNvbnN0IERpYWxvZ0hlYWRlcjogUmVhY3QuRkM8RGlhbG9nSGVhZGVyUHJvcHM+ID0gZnVuY3Rpb24gRGlhbG9nSGVhZGVyKHtcbiAgY2hpbGRyZW4sXG4gIGNsYXNzTmFtZSxcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGRhdGEtbmV4dGpzLWRpYWxvZy1oZWFkZXIgY2xhc3NOYW1lPXtjbGFzc05hbWV9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvZGl2PlxuICApXG59XG5cbmV4cG9ydCB7IERpYWxvZ0hlYWRlciB9XG4iXSwibmFtZXMiOlsiRGlhbG9nSGVhZGVyIiwiY2hpbGRyZW4iLCJjbGFzc05hbWUiLCJkaXYiLCJkYXRhLW5leHRqcy1kaWFsb2ctaGVhZGVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-header.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Dialog\", ({\n    enumerable: true,\n    get: function() {\n        return Dialog;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _useonclickoutside = __webpack_require__(/*! ../../hooks/use-on-click-outside */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-on-click-outside.js\");\nconst _usemeasureheight = __webpack_require__(/*! ../../hooks/use-measure-height */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-measure-height.js\");\nconst CSS_SELECTORS_TO_EXCLUDE_ON_CLICK_OUTSIDE = [\n    '[data-next-mark]',\n    '[data-issues-open]',\n    '#nextjs-dev-tools-menu',\n    '[data-nextjs-error-overlay-nav]',\n    '[data-info-popover]'\n];\nconst Dialog = function Dialog(param) {\n    _s();\n    let { children, type, className, onClose, 'aria-labelledby': ariaLabelledBy, 'aria-describedby': ariaDescribedBy, dialogResizerRef, ...props } = param;\n    const dialogRef = _react.useRef(null);\n    const [role, setRole] = _react.useState(typeof document !== 'undefined' && document.hasFocus() ? 'dialog' : undefined);\n    const ref = _react.useRef(null);\n    const [height, pristine] = (0, _usemeasureheight.useMeasureHeight)(ref);\n    (0, _useonclickoutside.useOnClickOutside)(dialogRef.current, CSS_SELECTORS_TO_EXCLUDE_ON_CLICK_OUTSIDE, (e)=>{\n        e.preventDefault();\n        return onClose == null ? void 0 : onClose();\n    });\n    _react.useEffect({\n        \"Dialog.useEffect\": ()=>{\n            if (dialogRef.current == null) {\n                return;\n            }\n            function handleFocus() {\n                // safari will force itself as the active application when a background page triggers any sort of autofocus\n                // this is a workaround to only set the dialog role if the document has focus\n                setRole(document.hasFocus() ? 'dialog' : undefined);\n            }\n            window.addEventListener('focus', handleFocus);\n            window.addEventListener('blur', handleFocus);\n            return ({\n                \"Dialog.useEffect\": ()=>{\n                    window.removeEventListener('focus', handleFocus);\n                    window.removeEventListener('blur', handleFocus);\n                }\n            })[\"Dialog.useEffect\"];\n        }\n    }[\"Dialog.useEffect\"], []);\n    _react.useEffect({\n        \"Dialog.useEffect\": ()=>{\n            const dialog = dialogRef.current;\n            const root = dialog == null ? void 0 : dialog.getRootNode();\n            const initialActiveElement = root instanceof ShadowRoot ? root == null ? void 0 : root.activeElement : null;\n            // Trap focus within the dialog\n            dialog == null ? void 0 : dialog.focus();\n            return ({\n                \"Dialog.useEffect\": ()=>{\n                    // Blur first to avoid getting stuck, in case `activeElement` is missing\n                    dialog == null ? void 0 : dialog.blur();\n                    // Restore focus to the previously active element\n                    initialActiveElement == null ? void 0 : initialActiveElement.focus();\n                }\n            })[\"Dialog.useEffect\"];\n        }\n    }[\"Dialog.useEffect\"], []);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        ref: dialogRef,\n        tabIndex: -1,\n        \"data-nextjs-dialog\": true,\n        role: role,\n        \"aria-labelledby\": ariaLabelledBy,\n        \"aria-describedby\": ariaDescribedBy,\n        \"aria-modal\": \"true\",\n        className: className,\n        onKeyDown: (e)=>{\n            if (e.key === 'Escape') {\n                onClose == null ? void 0 : onClose();\n            }\n        },\n        ...props,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n            ref: dialogResizerRef,\n            \"data-nextjs-dialog-sizer\": true,\n            // [x] Don't animate on initial load\n            // [x] No duplicate elements\n            // [x] Responds to content growth\n            style: {\n                height,\n                transition: pristine ? undefined : 'height 250ms var(--timing-swift)'\n            },\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                ref: ref,\n                children: children\n            })\n        })\n    });\n};\n_s(Dialog, \"fUJNA+MBJ7/yoZvHPU9jbZNPJXA=\");\n_c = Dialog;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dialog.js.map\nvar _c;\n$RefreshReg$(_c, \"Dialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/index.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/index.js ***!
  \**************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Dialog: function() {\n        return _dialog.Dialog;\n    },\n    DialogBody: function() {\n        return _dialogbody.DialogBody;\n    },\n    DialogContent: function() {\n        return _dialogcontent.DialogContent;\n    },\n    DialogFooter: function() {\n        return _dialogfooter.DialogFooter;\n    },\n    DialogHeader: function() {\n        return _dialogheader.DialogHeader;\n    },\n    styles: function() {\n        return _styles.styles;\n    }\n});\nconst _dialog = __webpack_require__(/*! ./dialog */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog.js\");\nconst _dialogbody = __webpack_require__(/*! ./dialog-body */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-body.js\");\nconst _dialogcontent = __webpack_require__(/*! ./dialog-content */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-content.js\");\nconst _dialogheader = __webpack_require__(/*! ./dialog-header */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-header.js\");\nconst _dialogfooter = __webpack_require__(/*! ./dialog-footer */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-footer.js\");\nconst _styles = __webpack_require__(/*! ./styles */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/styles.js\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9kaWFsb2cvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBQVNBLE1BQU07ZUFBTkEsUUFBQUEsTUFBTTs7SUFDTkMsVUFBVTtlQUFWQSxZQUFBQSxVQUFVOztJQUNWQyxhQUFhO2VBQWJBLGVBQUFBLGFBQWE7O0lBRWJDLFlBQVk7ZUFBWkEsY0FBQUEsWUFBWTs7SUFEWkMsWUFBWTtlQUFaQSxjQUFBQSxZQUFZOztJQUVaQyxNQUFNO2VBQU5BLFFBQUFBLE1BQU07OztvQ0FMUTt3Q0FDSTsyQ0FDRzswQ0FDRDswQ0FDQTtvQ0FDTiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXGNvbXBvbmVudHNcXGRpYWxvZ1xcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgRGlhbG9nIH0gZnJvbSAnLi9kaWFsb2cnXG5leHBvcnQgeyBEaWFsb2dCb2R5IH0gZnJvbSAnLi9kaWFsb2ctYm9keSdcbmV4cG9ydCB7IERpYWxvZ0NvbnRlbnQgfSBmcm9tICcuL2RpYWxvZy1jb250ZW50J1xuZXhwb3J0IHsgRGlhbG9nSGVhZGVyIH0gZnJvbSAnLi9kaWFsb2ctaGVhZGVyJ1xuZXhwb3J0IHsgRGlhbG9nRm9vdGVyIH0gZnJvbSAnLi9kaWFsb2ctZm9vdGVyJ1xuZXhwb3J0IHsgc3R5bGVzIH0gZnJvbSAnLi9zdHlsZXMnXG4iXSwibmFtZXMiOlsiRGlhbG9nIiwiRGlhbG9nQm9keSIsIkRpYWxvZ0NvbnRlbnQiLCJEaWFsb2dGb290ZXIiLCJEaWFsb2dIZWFkZXIiLCJzdHlsZXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/styles.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/styles.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst styles = \"\\n  [data-nextjs-dialog-root] {\\n    --next-dialog-radius: var(--rounded-xl);\\n    --next-dialog-footer-height: var(--size-48);\\n    --next-dialog-max-width: 960px;\\n    --next-dialog-row-padding: 16px;\\n    --next-dialog-container-padding: 12px;\\n\\n    display: flex;\\n    flex-direction: column-reverse;\\n    width: 100%;\\n    max-height: calc(100% - 56px);\\n    max-width: var(--next-dialog-max-width);\\n    margin-right: auto;\\n    margin-left: auto;\\n    scale: 0.98;\\n    opacity: 0;\\n    transition-property: scale, opacity;\\n    transition-duration: var(--transition-duration);\\n    transition-timing-function: var(--timing-overlay);\\n\\n    &[data-rendered='true'] {\\n      opacity: 1;\\n      scale: 1;\\n    }\\n  }\\n\\n  [data-nextjs-dialog] {\\n    outline: none;\\n    overflow: hidden;\\n  }\\n  [data-nextjs-dialog]::-webkit-scrollbar {\\n    width: 6px;\\n    border-radius: 0 0 1rem 1rem;\\n    margin-bottom: 1rem;\\n  }\\n  [data-nextjs-dialog]::-webkit-scrollbar-button {\\n    display: none;\\n  }\\n  [data-nextjs-dialog]::-webkit-scrollbar-track {\\n    border-radius: 0 0 1rem 1rem;\\n    background-color: var(--color-background-100);\\n  }\\n  [data-nextjs-dialog]::-webkit-scrollbar-thumb {\\n    border-radius: 1rem;\\n    background-color: var(--color-gray-500);\\n  }\\n\\n  \\n  [data-nextjs-dialog-sizer] {\\n    overflow: hidden;\\n    border-radius: inherit;\\n  }\\n\\n  [data-nextjs-dialog-backdrop] {\\n    opacity: 0;\\n    transition: opacity var(--transition-duration) var(--timing-overlay);\\n  }\\n\\n  [data-nextjs-dialog-overlay][data-rendered='true']\\n    [data-nextjs-dialog-backdrop] {\\n    opacity: 1;\\n  }\\n\\n  [data-nextjs-dialog-content] {\\n    overflow-y: auto;\\n    border: none;\\n    margin: 0;\\n    display: flex;\\n    flex-direction: column;\\n    position: relative;\\n    padding: 16px 12px;\\n  }\\n\\n  /* Account for the footer height, when present */\\n  [data-nextjs-dialog][data-has-footer='true'] [data-nextjs-dialog-body] {\\n    margin-bottom: var(--next-dialog-footer-height);\\n  }\\n\\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-header] {\\n    flex-shrink: 0;\\n    margin-bottom: 8px;\\n  }\\n\\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-body] {\\n    position: relative;\\n    flex: 1 1 auto;\\n  }\\n\\n  [data-nextjs-dialog-footer] {\\n    /* Subtract border width */\\n    width: calc(100% - 2px);\\n    /* \\n      We make this element fixed to anchor it to the bottom during the height transition.\\n      If you make this relative it will jump during the transition and not collapse or expand smoothly.\\n      If you make this absolute it will remain stuck at its initial position when scrolling the dialog.\\n    */\\n    position: fixed;\\n    bottom: 1px;\\n    min-height: var(--next-dialog-footer-height);\\n    border-radius: 0 0 var(--next-dialog-radius) var(--next-dialog-radius);\\n    overflow: hidden;\\n\\n    > * {\\n      min-height: var(--next-dialog-footer-height);\\n    }\\n  }\\n\\n  @media (max-height: 812px) {\\n    [data-nextjs-dialog-overlay] {\\n      max-height: calc(100% - 15px);\\n    }\\n  }\\n\\n  @media (min-width: 576px) {\\n    [data-nextjs-dialog-root] {\\n      --next-dialog-max-width: 540px;\\n    }\\n  }\\n\\n  @media (min-width: 768px) {\\n    [data-nextjs-dialog-root] {\\n      --next-dialog-max-width: 720px;\\n    }\\n  }\\n\\n  @media (min-width: 992px) {\\n    [data-nextjs-dialog-root] {\\n      --next-dialog-max-width: 960px;\\n    }\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9kaWFsb2cvc3R5bGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7MENBdUlTQTs7O2VBQUFBOzs7QUF2SVQsTUFBTUEsU0FBVSIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXGNvbXBvbmVudHNcXGRpYWxvZ1xcc3R5bGVzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHN0eWxlcyA9IGBcbiAgW2RhdGEtbmV4dGpzLWRpYWxvZy1yb290XSB7XG4gICAgLS1uZXh0LWRpYWxvZy1yYWRpdXM6IHZhcigtLXJvdW5kZWQteGwpO1xuICAgIC0tbmV4dC1kaWFsb2ctZm9vdGVyLWhlaWdodDogdmFyKC0tc2l6ZS00OCk7XG4gICAgLS1uZXh0LWRpYWxvZy1tYXgtd2lkdGg6IDk2MHB4O1xuICAgIC0tbmV4dC1kaWFsb2ctcm93LXBhZGRpbmc6IDE2cHg7XG4gICAgLS1uZXh0LWRpYWxvZy1jb250YWluZXItcGFkZGluZzogMTJweDtcblxuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbi1yZXZlcnNlO1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIG1heC1oZWlnaHQ6IGNhbGMoMTAwJSAtIDU2cHgpO1xuICAgIG1heC13aWR0aDogdmFyKC0tbmV4dC1kaWFsb2ctbWF4LXdpZHRoKTtcbiAgICBtYXJnaW4tcmlnaHQ6IGF1dG87XG4gICAgbWFyZ2luLWxlZnQ6IGF1dG87XG4gICAgc2NhbGU6IDAuOTg7XG4gICAgb3BhY2l0eTogMDtcbiAgICB0cmFuc2l0aW9uLXByb3BlcnR5OiBzY2FsZSwgb3BhY2l0eTtcbiAgICB0cmFuc2l0aW9uLWR1cmF0aW9uOiB2YXIoLS10cmFuc2l0aW9uLWR1cmF0aW9uKTtcbiAgICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogdmFyKC0tdGltaW5nLW92ZXJsYXkpO1xuXG4gICAgJltkYXRhLXJlbmRlcmVkPSd0cnVlJ10ge1xuICAgICAgb3BhY2l0eTogMTtcbiAgICAgIHNjYWxlOiAxO1xuICAgIH1cbiAgfVxuXG4gIFtkYXRhLW5leHRqcy1kaWFsb2ddIHtcbiAgICBvdXRsaW5lOiBub25lO1xuICAgIG92ZXJmbG93OiBoaWRkZW47XG4gIH1cbiAgW2RhdGEtbmV4dGpzLWRpYWxvZ106Oi13ZWJraXQtc2Nyb2xsYmFyIHtcbiAgICB3aWR0aDogNnB4O1xuICAgIGJvcmRlci1yYWRpdXM6IDAgMCAxcmVtIDFyZW07XG4gICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbiAgfVxuICBbZGF0YS1uZXh0anMtZGlhbG9nXTo6LXdlYmtpdC1zY3JvbGxiYXItYnV0dG9uIHtcbiAgICBkaXNwbGF5OiBub25lO1xuICB9XG4gIFtkYXRhLW5leHRqcy1kaWFsb2ddOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XG4gICAgYm9yZGVyLXJhZGl1czogMCAwIDFyZW0gMXJlbTtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1jb2xvci1iYWNrZ3JvdW5kLTEwMCk7XG4gIH1cbiAgW2RhdGEtbmV4dGpzLWRpYWxvZ106Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcbiAgICBib3JkZXItcmFkaXVzOiAxcmVtO1xuICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWNvbG9yLWdyYXktNTAwKTtcbiAgfVxuXG4gICR7XG4gICAgJycgLyogUGxhY2Ugb3ZlcmZsb3c6IGhpZGRlbiBvbiB0aGlzIHNvIHdlIGNhbiBicmVhayBvdXQgZnJvbSBbZGF0YS1uZXh0anMtZGlhbG9nXSAqL1xuICB9XG4gIFtkYXRhLW5leHRqcy1kaWFsb2ctc2l6ZXJdIHtcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgIGJvcmRlci1yYWRpdXM6IGluaGVyaXQ7XG4gIH1cblxuICBbZGF0YS1uZXh0anMtZGlhbG9nLWJhY2tkcm9wXSB7XG4gICAgb3BhY2l0eTogMDtcbiAgICB0cmFuc2l0aW9uOiBvcGFjaXR5IHZhcigtLXRyYW5zaXRpb24tZHVyYXRpb24pIHZhcigtLXRpbWluZy1vdmVybGF5KTtcbiAgfVxuXG4gIFtkYXRhLW5leHRqcy1kaWFsb2ctb3ZlcmxheV1bZGF0YS1yZW5kZXJlZD0ndHJ1ZSddXG4gICAgW2RhdGEtbmV4dGpzLWRpYWxvZy1iYWNrZHJvcF0ge1xuICAgIG9wYWNpdHk6IDE7XG4gIH1cblxuICBbZGF0YS1uZXh0anMtZGlhbG9nLWNvbnRlbnRdIHtcbiAgICBvdmVyZmxvdy15OiBhdXRvO1xuICAgIGJvcmRlcjogbm9uZTtcbiAgICBtYXJnaW46IDA7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICBwYWRkaW5nOiAxNnB4IDEycHg7XG4gIH1cblxuICAvKiBBY2NvdW50IGZvciB0aGUgZm9vdGVyIGhlaWdodCwgd2hlbiBwcmVzZW50ICovXG4gIFtkYXRhLW5leHRqcy1kaWFsb2ddW2RhdGEtaGFzLWZvb3Rlcj0ndHJ1ZSddIFtkYXRhLW5leHRqcy1kaWFsb2ctYm9keV0ge1xuICAgIG1hcmdpbi1ib3R0b206IHZhcigtLW5leHQtZGlhbG9nLWZvb3Rlci1oZWlnaHQpO1xuICB9XG5cbiAgW2RhdGEtbmV4dGpzLWRpYWxvZy1jb250ZW50XSA+IFtkYXRhLW5leHRqcy1kaWFsb2ctaGVhZGVyXSB7XG4gICAgZmxleC1zaHJpbms6IDA7XG4gICAgbWFyZ2luLWJvdHRvbTogOHB4O1xuICB9XG5cbiAgW2RhdGEtbmV4dGpzLWRpYWxvZy1jb250ZW50XSA+IFtkYXRhLW5leHRqcy1kaWFsb2ctYm9keV0ge1xuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICBmbGV4OiAxIDEgYXV0bztcbiAgfVxuXG4gIFtkYXRhLW5leHRqcy1kaWFsb2ctZm9vdGVyXSB7XG4gICAgLyogU3VidHJhY3QgYm9yZGVyIHdpZHRoICovXG4gICAgd2lkdGg6IGNhbGMoMTAwJSAtIDJweCk7XG4gICAgLyogXG4gICAgICBXZSBtYWtlIHRoaXMgZWxlbWVudCBmaXhlZCB0byBhbmNob3IgaXQgdG8gdGhlIGJvdHRvbSBkdXJpbmcgdGhlIGhlaWdodCB0cmFuc2l0aW9uLlxuICAgICAgSWYgeW91IG1ha2UgdGhpcyByZWxhdGl2ZSBpdCB3aWxsIGp1bXAgZHVyaW5nIHRoZSB0cmFuc2l0aW9uIGFuZCBub3QgY29sbGFwc2Ugb3IgZXhwYW5kIHNtb290aGx5LlxuICAgICAgSWYgeW91IG1ha2UgdGhpcyBhYnNvbHV0ZSBpdCB3aWxsIHJlbWFpbiBzdHVjayBhdCBpdHMgaW5pdGlhbCBwb3NpdGlvbiB3aGVuIHNjcm9sbGluZyB0aGUgZGlhbG9nLlxuICAgICovXG4gICAgcG9zaXRpb246IGZpeGVkO1xuICAgIGJvdHRvbTogMXB4O1xuICAgIG1pbi1oZWlnaHQ6IHZhcigtLW5leHQtZGlhbG9nLWZvb3Rlci1oZWlnaHQpO1xuICAgIGJvcmRlci1yYWRpdXM6IDAgMCB2YXIoLS1uZXh0LWRpYWxvZy1yYWRpdXMpIHZhcigtLW5leHQtZGlhbG9nLXJhZGl1cyk7XG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcblxuICAgID4gKiB7XG4gICAgICBtaW4taGVpZ2h0OiB2YXIoLS1uZXh0LWRpYWxvZy1mb290ZXItaGVpZ2h0KTtcbiAgICB9XG4gIH1cblxuICBAbWVkaWEgKG1heC1oZWlnaHQ6IDgxMnB4KSB7XG4gICAgW2RhdGEtbmV4dGpzLWRpYWxvZy1vdmVybGF5XSB7XG4gICAgICBtYXgtaGVpZ2h0OiBjYWxjKDEwMCUgLSAxNXB4KTtcbiAgICB9XG4gIH1cblxuICBAbWVkaWEgKG1pbi13aWR0aDogNTc2cHgpIHtcbiAgICBbZGF0YS1uZXh0anMtZGlhbG9nLXJvb3RdIHtcbiAgICAgIC0tbmV4dC1kaWFsb2ctbWF4LXdpZHRoOiA1NDBweDtcbiAgICB9XG4gIH1cblxuICBAbWVkaWEgKG1pbi13aWR0aDogNzY4cHgpIHtcbiAgICBbZGF0YS1uZXh0anMtZGlhbG9nLXJvb3RdIHtcbiAgICAgIC0tbmV4dC1kaWFsb2ctbWF4LXdpZHRoOiA3MjBweDtcbiAgICB9XG4gIH1cblxuICBAbWVkaWEgKG1pbi13aWR0aDogOTkycHgpIHtcbiAgICBbZGF0YS1uZXh0anMtZGlhbG9nLXJvb3RdIHtcbiAgICAgIC0tbmV4dC1kaWFsb2ctbWF4LXdpZHRoOiA5NjBweDtcbiAgICB9XG4gIH1cbmBcblxuZXhwb3J0IHsgc3R5bGVzIH1cbiJdLCJuYW1lcyI6WyJzdHlsZXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/styles.js\n"));

/***/ })

}]);