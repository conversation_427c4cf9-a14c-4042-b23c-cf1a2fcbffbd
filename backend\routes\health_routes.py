from flask import Blueprint, jsonify

health_bp = Blueprint('health', __name__, url_prefix='/health')

@health_bp.route('/', methods=['GET', 'HEAD'])
def health():
    return jsonify({"status": "ok"}), 200

# Add API health endpoint for frontend compatibility
api_health_bp = Blueprint('api_health', __name__, url_prefix='/api')

@api_health_bp.route('/health', methods=['GET', 'HEAD'])
def api_health():
    return jsonify({"status": "ok"}), 200