"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useTradingContext } from '@/contexts/TradingContext';
import { SessionManager } from '@/lib/session-manager';
import { useToast } from '@/hooks/use-toast';
import { Trash2, FileSpreadsheet, Clock, TrendingUp } from 'lucide-react';
import type { SessionMetadata, OrderHistoryEntry } from '@/lib/types';
import HistoryTable from './HistoryTable';
import { format } from 'date-fns';

export default function SessionAwareHistory() {
  const { dispatch, orderHistory, config } = useTradingContext();
  const { toast } = useToast();
  const [sessions, setSessions] = useState<SessionMetadata[]>([]);
  const [selectedSessionId, setSelectedSessionId] = useState<string>('current');
  const [selectedSessionHistory, setSelectedSessionHistory] = useState<OrderHistoryEntry[]>([]);
  const sessionManager = SessionManager.getInstance();

  useEffect(() => {
    loadSessions();
  }, []);

  useEffect(() => {
    if (selectedSessionId === 'current') {
      setSelectedSessionHistory(orderHistory);
    } else {
      const sessionHistory = sessionManager.getSessionHistory(selectedSessionId);
      setSelectedSessionHistory(sessionHistory);
    }
  }, [selectedSessionId, orderHistory]);

  const loadSessions = () => {
    const allSessions = sessionManager.getAllSessions();
    setSessions(allSessions.sort((a, b) => b.lastModified - a.lastModified));
  };

  const handleClearHistory = () => {
    if (selectedSessionId === 'current') {
      dispatch({ type: 'CLEAR_ORDER_HISTORY' });
      toast({
        title: "History Cleared",
        description: "Current session trade history has been cleared.",
      });
    } else {
      // For past sessions, we would need to implement session history clearing
      toast({
        title: "Cannot Clear",
        description: "Cannot clear history for past sessions. Use current session to clear history.",
        variant: "destructive"
      });
    }
  };

  const handleExportHistory = () => {
    if (selectedSessionHistory.length === 0) {
      toast({
        title: "No Data to Export",
        description: "There is no trade history to export for the selected session.",
        variant: "destructive",
      });
      return;
    }

    let csvContent: string;
    let filename: string;

    if (selectedSessionId === 'current') {
      // Use existing export logic for current session
      const headers = [
        'Date', 'Time', 'Pair', 'Crypto', 'Order Type', 'Amount', 'Avg Price', 'Value',
        'Price 1', 'Crypto 1', 'Price 2', 'Crypto 2', 'Profit/Loss (Crypto1)', 'Profit/Loss (Crypto2)'
      ];

      csvContent = [
        headers.join(','),
        ...selectedSessionHistory.map(entry => [
          new Date(entry.timestamp).toISOString().split('T')[0],
          new Date(entry.timestamp).toTimeString().split(' ')[0],
          entry.pair,
          entry.crypto1Symbol,
          entry.orderType,
          entry.amountCrypto1?.toFixed(config.numDigits) || '',
          entry.avgPrice?.toFixed(config.numDigits) || '',
          entry.valueCrypto2?.toFixed(config.numDigits) || '',
          entry.price1?.toFixed(config.numDigits) || '',
          entry.crypto1Symbol,
          entry.price2?.toFixed(config.numDigits) || '',
          entry.crypto2Symbol,
          entry.realizedProfitLossCrypto1?.toFixed(config.numDigits) || '',
          entry.realizedProfitLossCrypto2?.toFixed(config.numDigits) || ''
        ].join(','))
      ].join('\n');

      filename = `current_session_history_${new Date().toISOString().split('T')[0]}.csv`;
    } else {
      // Use session manager export for past sessions
      csvContent = sessionManager.exportSessionToCSV(selectedSessionId);
      const session = sessionManager.loadSession(selectedSessionId);
      filename = `${session?.name || 'session'}_${new Date().toISOString().split('T')[0]}.csv`;
    }

    if (!csvContent) {
      toast({
        title: "Export Failed",
        description: "Failed to generate CSV content.",
        variant: "destructive"
      });
      return;
    }

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Export Complete",
      description: "Trade history has been exported to CSV file.",
    });
  };

  const getSelectedSessionInfo = () => {
    if (selectedSessionId === 'current') {
      return {
        name: 'Current Session',
        pair: `${config.crypto1}/${config.crypto2}`,
        totalTrades: orderHistory.length,
        totalProfitLoss: orderHistory
          .filter(trade => trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined)
          .reduce((sum, trade) => sum + (trade.realizedProfitLossCrypto2 || 0), 0),
        lastModified: Date.now(),
        isActive: true
      };
    }

    return sessions.find(s => s.id === selectedSessionId);
  };

  const selectedSession = getSelectedSessionInfo();

  return (
    <div className="space-y-6">
      {/* Session Selection */}
      <Card className="border-2 border-border">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-primary">Session History</CardTitle>
          <CardDescription>View trading history for current and past sessions.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="flex-1">
              <label className="text-sm font-medium mb-2 block">Select Session:</label>
              <Select value={selectedSessionId} onValueChange={setSelectedSessionId}>
                <SelectTrigger className="w-full sm:w-[300px]">
                  <SelectValue placeholder="Select a session" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="current">
                    <div className="flex items-center gap-2">
                      <Badge variant="default" className="text-xs">Current</Badge>
                      <span>Current Session ({config.crypto1}/{config.crypto2})</span>
                    </div>
                  </SelectItem>
                  {sessions.length > 0 && (
                    <>
                      <Separator className="my-1" />
                      {sessions.map((session) => (
                        <SelectItem key={session.id} value={session.id}>
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary" className="text-xs">Past</Badge>
                            <span>{session.name} ({session.pair})</span>
                          </div>
                        </SelectItem>
                      ))}
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleClearHistory} 
                className="btn-outline-neo"
                disabled={selectedSessionId !== 'current'}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Clear History
              </Button>
              <Button variant="outline" size="sm" onClick={handleExportHistory} className="btn-outline-neo">
                <FileSpreadsheet className="mr-2 h-4 w-4" />
                Export
              </Button>
            </div>
          </div>

          {/* Session Info */}
          {selectedSession && (
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Session:</span>
                  <div className="font-medium">{selectedSession.name}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Pair:</span>
                  <div className="font-medium">{selectedSession.pair}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Total Trades:</span>
                  <div className="font-medium">{selectedSession.totalTrades}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Total P/L:</span>
                  <div className={`font-medium ${selectedSession.totalProfitLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {selectedSession.totalProfitLoss.toFixed(4)}
                  </div>
                </div>
              </div>
              {selectedSessionId !== 'current' && (
                <div className="mt-2 text-xs text-muted-foreground">
                  Last modified: {format(new Date(selectedSession.lastModified), 'MMM dd, yyyy HH:mm')}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* History Table */}
      <Card className="border-2 border-border">
        <CardHeader>
          <CardTitle className="text-lg font-bold text-primary">
            Trade History - {selectedSession?.name || 'Unknown Session'}
          </CardTitle>
          <CardDescription>
            {selectedSessionHistory.length === 0 
              ? "No trades recorded for this session yet."
              : `Showing ${selectedSessionHistory.length} trades for the selected session.`
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* We'll need to create a modified HistoryTable that accepts custom history */}
          <SessionHistoryTable history={selectedSessionHistory} config={config} />
        </CardContent>
      </Card>
    </div>
  );
}

// Custom history table component for session-specific history
function SessionHistoryTable({ history, config }: { history: OrderHistoryEntry[], config: any }) {
  const formatNum = (num?: number) => num?.toFixed(config.numDigits) ?? '-';

  const columns = [
    { key: "date", label: "Date" },
    { key: "hour", label: "Hour" },
    { key: "pair", label: "Couple" },
    { key: "crypto", label: `Crypto (${config.crypto1})` },
    { key: "orderType", label: "Order Type" },
    { key: "amount", label: "Amount" },
    { key: "avgPrice", label: "Avg Price" },
    { key: "value", label: `Value (${config.crypto2})` },
    { key: "price1", label: "Price 1" },
    { key: "crypto1Symbol", label: `Crypto (${config.crypto1})` },
    { key: "price2", label: "Price 2" },
    { key: "crypto2Symbol", label: `Crypto (${config.crypto2})` },
    { key: "profitCrypto1", label: `Profit/Loss (${config.crypto1})` },
    { key: "profitCrypto2", label: `Profit/Loss (${config.crypto2})` },
  ];

  if (history.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
        <p>No trading history for this session yet.</p>
      </div>
    );
  }

  return (
    <div className="border-2 border-border rounded-sm">
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="bg-card hover:bg-card border-b">
              {columns.map((col) => (
                <th key={col.key} className="font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm text-left">{col.label}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {history.map((entry: OrderHistoryEntry) => (
              <tr key={entry.id} className="hover:bg-card/80 border-b">
                <td className="px-3 py-2 text-xs">{format(new Date(entry.timestamp), 'yyyy-MM-dd')}</td>
                <td className="px-3 py-2 text-xs">{format(new Date(entry.timestamp), 'HH:mm:ss')}</td>
                <td className="px-3 py-2 text-xs">{entry.pair}</td>
                <td className="px-3 py-2 text-xs">{entry.crypto1Symbol}</td>
                <td className={`px-3 py-2 text-xs font-semibold ${entry.orderType === "BUY" ? "text-green-400" : "text-destructive"}`}>
                  {entry.orderType}
                </td>
                <td className="px-3 py-2 text-xs">{formatNum(entry.amountCrypto1)}</td>
                <td className="px-3 py-2 text-xs">{formatNum(entry.avgPrice)}</td>
                <td className="px-3 py-2 text-xs">{formatNum(entry.valueCrypto2)}</td>
                <td className="px-3 py-2 text-xs">{formatNum(entry.price1)}</td>
                <td className="px-3 py-2 text-xs">{entry.crypto1Symbol}</td>
                <td className="px-3 py-2 text-xs">{formatNum(entry.price2) ?? "-"}</td>
                <td className="px-3 py-2 text-xs">{entry.crypto2Symbol}</td>
                <td className={`px-3 py-2 text-xs ${entry.realizedProfitLossCrypto1 && entry.realizedProfitLossCrypto1 > 0 ? "text-green-400" : entry.realizedProfitLossCrypto1 && entry.realizedProfitLossCrypto1 < 0 ? "text-destructive" : ""}`}>
                  {formatNum(entry.realizedProfitLossCrypto1)}
                </td>
                <td className={`px-3 py-2 text-xs ${entry.realizedProfitLossCrypto2 && entry.realizedProfitLossCrypto2 > 0 ? "text-green-400" : entry.realizedProfitLossCrypto2 && entry.realizedProfitLossCrypto2 < 0 ? "text-destructive" : ""}`}>
                  {formatNum(entry.realizedProfitLossCrypto2)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
