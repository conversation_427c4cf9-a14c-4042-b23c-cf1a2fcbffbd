"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/build-error.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/container/build-error.js ***!
  \************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    BuildError: function() {\n        return BuildError;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _stripansi = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/strip-ansi */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/strip-ansi/index.js\"));\nconst _terminal = __webpack_require__(/*! ../components/terminal */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/index.js\");\nconst _erroroverlaylayout = __webpack_require__(/*! ../components/errors/error-overlay-layout/error-overlay-layout */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js\");\nconst getErrorTextFromBuildErrorMessage = (multiLineMessage)=>{\n    const lines = multiLineMessage.split('\\n');\n    // The multi-line build error message looks like:\n    // <file path>:<line number>:<column number>\n    // <error message>\n    // <error code frame of compiler or bundler>\n    // e.g.\n    // ./path/to/file.js:1:1\n    // SyntaxError: ...\n    // > 1 | con st foo =\n    // ...\n    return (0, _stripansi.default)(lines[1] || '');\n};\nconst BuildError = function BuildError(param) {\n    let { message, ...props } = param;\n    const noop = (0, _react.useCallback)(()=>{}, []);\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    const formattedMessage = (0, _react.useMemo)(()=>getErrorTextFromBuildErrorMessage(message) || 'Failed to compile', [\n        message\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_erroroverlaylayout.ErrorOverlayLayout, {\n        errorType: \"Build Error\",\n        errorMessage: formattedMessage,\n        onClose: noop,\n        error: error,\n        footerMessage: \"This error occurred during the build process and can only be dismissed by fixing the error.\",\n        ...props,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_terminal.Terminal, {\n            content: message\n        })\n    });\n};\n_c = BuildError;\nconst styles = \"\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=build-error.js.map\nvar _c;\n$RefreshReg$(_c, \"BuildError\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/build-error.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/errors.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/container/errors.js ***!
  \*******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Errors: function() {\n        return Errors;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _overlay = __webpack_require__(/*! ../components/overlay */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js\");\nconst _runtimeerror = __webpack_require__(/*! ./runtime-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/index.js\");\nconst _errorsource = __webpack_require__(/*! ../../../../../shared/lib/error-source */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/error-source.js\");\nconst _hotlinkedtext = __webpack_require__(/*! ../components/hot-linked-text */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js\");\nconst _componentstackpseudohtml = __webpack_require__(/*! ./runtime-error/component-stack-pseudo-html */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/component-stack-pseudo-html.js\");\nconst _hydrationerrorinfo = __webpack_require__(/*! ../../../errors/hydration-error-info */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js\");\nconst _consoleerror = __webpack_require__(/*! ../../../errors/console-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/errors/console-error.js\");\nconst _errortelemetryutils = __webpack_require__(/*! ../../../../../lib/error-telemetry-utils */ \"(pages-dir-browser)/./node_modules/next/dist/lib/error-telemetry-utils.js\");\nconst _erroroverlaylayout = __webpack_require__(/*! ../components/errors/error-overlay-layout/error-overlay-layout */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js\");\nconst _ishydrationerror = __webpack_require__(/*! ../../../is-hydration-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\");\nfunction isNextjsLink(text) {\n    return text.startsWith('https://nextjs.org');\n}\nfunction ErrorDescription(param) {\n    let { error, hydrationWarning } = param;\n    const isUnhandledOrReplayError = (0, _consoleerror.isUnhandledConsoleOrRejection)(error);\n    const unhandledErrorType = isUnhandledOrReplayError ? (0, _consoleerror.getUnhandledErrorType)(error) : null;\n    const isConsoleErrorStringMessage = unhandledErrorType === 'string';\n    // If the error is:\n    // - hydration warning\n    // - captured console error or unhandled rejection\n    // skip displaying the error name\n    const title = isUnhandledOrReplayError && isConsoleErrorStringMessage || hydrationWarning ? '' : error.name + ': ';\n    const environmentName = 'environmentName' in error ? error.environmentName : '';\n    const envPrefix = environmentName ? \"[ \" + environmentName + \" ] \" : '';\n    // The environment name will be displayed as a label, so remove it\n    // from the message (e.g. \"[ Server ] hello world\" -> \"hello world\").\n    let message = error.message;\n    if (message.startsWith(envPrefix)) {\n        message = message.slice(envPrefix.length);\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            title,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                text: hydrationWarning || message,\n                matcher: isNextjsLink\n            })\n        ]\n    });\n}\n_c = ErrorDescription;\nfunction Errors(param) {\n    let { runtimeErrors, debugInfo, onClose, ...props } = param;\n    var _activeError_componentStackFrames;\n    const dialogResizerRef = (0, _react.useRef)(null);\n    (0, _react.useEffect)(()=>{\n        // Close the error overlay when pressing escape\n        function handleKeyDown(event) {\n            if (event.key === 'Escape') {\n                onClose();\n            }\n        }\n        document.addEventListener('keydown', handleKeyDown);\n        return ()=>document.removeEventListener('keydown', handleKeyDown);\n    }, [\n        onClose\n    ]);\n    const isLoading = (0, _react.useMemo)(()=>{\n        return runtimeErrors.length < 1;\n    }, [\n        runtimeErrors.length\n    ]);\n    const [activeIdx, setActiveIndex] = (0, _react.useState)(0);\n    const activeError = (0, _react.useMemo)(()=>{\n        var _runtimeErrors_activeIdx;\n        return (_runtimeErrors_activeIdx = runtimeErrors[activeIdx]) != null ? _runtimeErrors_activeIdx : null;\n    }, [\n        activeIdx,\n        runtimeErrors\n    ]);\n    if (isLoading) {\n        // TODO: better loading state\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_overlay.Overlay, {});\n    }\n    if (!activeError) {\n        return null;\n    }\n    const error = activeError.error;\n    const isServerError = [\n        'server',\n        'edge-server'\n    ].includes((0, _errorsource.getErrorSource)(error) || '');\n    const isUnhandledError = (0, _consoleerror.isUnhandledConsoleOrRejection)(error);\n    const errorDetails = error.details || {};\n    const notes = errorDetails.notes || '';\n    const [warningTemplate, serverContent, clientContent] = errorDetails.warning || [\n        null,\n        '',\n        ''\n    ];\n    const hydrationErrorType = (0, _hydrationerrorinfo.getHydrationWarningType)(warningTemplate);\n    const hydrationWarning = warningTemplate ? warningTemplate.replace('%s', serverContent).replace('%s', clientContent).replace('%s', '') // remove the %s for stack\n    .replace(/%s$/, '') // If there's still a %s at the end, remove it\n    .replace(/^Warning: /, '').replace(/^Error: /, '') : null;\n    const errorCode = (0, _errortelemetryutils.extractNextErrorCode)(error);\n    const footerMessage = isServerError ? 'This error happened while generating the page. Any console logs will be displayed in the terminal window.' : undefined;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_erroroverlaylayout.ErrorOverlayLayout, {\n        errorCode: errorCode,\n        errorType: isServerError ? 'Runtime Error' : isUnhandledError ? 'Console Error' : 'Unhandled Runtime Error',\n        errorMessage: /*#__PURE__*/ (0, _jsxruntime.jsx)(ErrorDescription, {\n            error: error,\n            hydrationWarning: hydrationWarning\n        }),\n        onClose: isServerError ? undefined : onClose,\n        debugInfo: debugInfo,\n        error: error,\n        runtimeErrors: runtimeErrors,\n        activeIdx: activeIdx,\n        setActiveIndex: setActiveIndex,\n        footerMessage: footerMessage,\n        dialogResizerRef: dialogResizerRef,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                className: \"error-overlay-notes-container\",\n                children: [\n                    notes ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                            id: \"nextjs__container_errors__notes\",\n                            className: \"nextjs__container_errors__notes\",\n                            children: notes\n                        })\n                    }) : null,\n                    hydrationWarning ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                        id: \"nextjs__container_errors__link\",\n                        className: \"nextjs__container_errors__link\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                            text: \"See more info here: \" + _ishydrationerror.NEXTJS_HYDRATION_ERROR_LINK\n                        })\n                    }) : null\n                ]\n            }),\n            hydrationWarning && (((_activeError_componentStackFrames = activeError.componentStackFrames) == null ? void 0 : _activeError_componentStackFrames.length) || !!errorDetails.reactOutputComponentDiff) ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_componentstackpseudohtml.PseudoHtmlDiff, {\n                className: \"nextjs__container_errors__component-stack\",\n                hydrationMismatchType: hydrationErrorType,\n                firstContent: serverContent,\n                secondContent: clientContent,\n                reactOutputComponentDiff: errorDetails.reactOutputComponentDiff || ''\n            }) : null,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n                fallback: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                    \"data-nextjs-error-suspended\": true\n                }),\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_runtimeerror.RuntimeError, {\n                    error: activeError,\n                    dialogResizerRef: dialogResizerRef\n                }, activeError.id.toString())\n            })\n        ]\n    });\n}\n_c1 = Errors;\nconst styles = \"\\n  .nextjs-error-with-static {\\n    bottom: calc(16px * 4.5);\\n  }\\n  p.nextjs__container_errors__link {\\n    font-size: var(--size-14);\\n  }\\n  p.nextjs__container_errors__notes {\\n    color: var(--color-stack-notes);\\n    font-size: var(--size-14);\\n    line-height: 1.5;\\n  }\\n  .nextjs-container-errors-body > h2:not(:first-child) {\\n    margin-top: calc(16px + 8px);\\n  }\\n  .nextjs-container-errors-body > h2 {\\n    color: var(--color-title-color);\\n    margin-bottom: 8px;\\n    font-size: var(--size-20);\\n  }\\n  .nextjs-toast-errors-parent {\\n    cursor: pointer;\\n    transition: transform 0.2s ease;\\n  }\\n  .nextjs-toast-errors-parent:hover {\\n    transform: scale(1.1);\\n  }\\n  .nextjs-toast-errors {\\n    display: flex;\\n    align-items: center;\\n    justify-content: flex-start;\\n  }\\n  .nextjs-toast-errors > svg {\\n    margin-right: 8px;\\n  }\\n  .nextjs-toast-hide-button {\\n    margin-left: 24px;\\n    border: none;\\n    background: none;\\n    color: var(--color-ansi-bright-white);\\n    padding: 0;\\n    transition: opacity 0.25s ease;\\n    opacity: 0.7;\\n  }\\n  .nextjs-toast-hide-button:hover {\\n    opacity: 1;\\n  }\\n  .nextjs__container_errors_inspect_copy_button {\\n    cursor: pointer;\\n    background: none;\\n    border: none;\\n    color: var(--color-ansi-bright-white);\\n    font-size: var(--size-24);\\n    padding: 0;\\n    margin: 0;\\n    margin-left: 8px;\\n    transition: opacity 0.25s ease;\\n  }\\n  .nextjs__container_errors__error_title {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    margin-bottom: 14px;\\n  }\\n  .error-overlay-notes-container {\\n    margin: 8px 2px;\\n  }\\n  .error-overlay-notes-container p {\\n    white-space: pre-wrap;\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=errors.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"ErrorDescription\");\n$RefreshReg$(_c1, \"Errors\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/errors.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/root-layout-missing-tags-error.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/container/root-layout-missing-tags-error.js ***!
  \*******************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RootLayoutMissingTagsError\", ({\n    enumerable: true,\n    get: function() {\n        return RootLayoutMissingTagsError;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _hotlinkedtext = __webpack_require__(/*! ../components/hot-linked-text */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js\");\nconst _erroroverlaylayout = __webpack_require__(/*! ../components/errors/error-overlay-layout/error-overlay-layout */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js\");\nfunction RootLayoutMissingTagsError(param) {\n    let { missingTags, ...props } = param;\n    const noop = (0, _react.useCallback)(()=>{}, []);\n    const error = Object.defineProperty(new Error(\"The following tags are missing in the Root Layout: \" + missingTags.map((tagName)=>\"<\" + tagName + \">\").join(', ') + \".\\nRead more at https://nextjs.org/docs/messages/missing-root-layout-tags\"), \"__NEXT_ERROR_CODE\", {\n        value: \"E638\",\n        enumerable: false,\n        configurable: true\n    });\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_erroroverlaylayout.ErrorOverlayLayout, {\n        errorType: \"Missing Required HTML Tag\",\n        error: error,\n        errorMessage: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n            text: error.message\n        }),\n        onClose: noop,\n        ...props\n    });\n}\n_c = RootLayoutMissingTagsError;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=root-layout-missing-tags-error.js.map\nvar _c;\n$RefreshReg$(_c, \"RootLayoutMissingTagsError\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/root-layout-missing-tags-error.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/component-stack-pseudo-html.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/component-stack-pseudo-html.js ***!
  \******************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PSEUDO_HTML_DIFF_STYLES: function() {\n        return PSEUDO_HTML_DIFF_STYLES;\n    },\n    PseudoHtmlDiff: function() {\n        return _diffview.PseudoHtmlDiff;\n    }\n});\nconst _diffview = __webpack_require__(/*! ../../components/hydration-diff/diff-view */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/hydration-diff/diff-view.js\");\nconst PSEUDO_HTML_DIFF_STYLES = \"\\n  [data-nextjs-container-errors-pseudo-html] {\\n    padding: 8px 0;\\n    margin: 8px 0;\\n    border: 1px solid var(--color-gray-400);\\n    background: var(--color-background-200);\\n    color: var(--color-syntax-constant);\\n    font-family: var(--font-stack-monospace);\\n    font-size: var(--size-12);\\n    line-height: 1.33em; /* 16px in 12px font size */\\n    border-radius: var(--rounded-md-2);\\n  }\\n  [data-nextjs-container-errors-pseudo-html-line] {\\n    display: inline-block;\\n    width: 100%;\\n    padding-left: 40px;\\n    line-height: calc(5 / 3);\\n  }\\n  [data-nextjs-container-errors-pseudo-html--diff='error'] {\\n    background: var(--color-amber-100);\\n    font-weight: bold;\\n  }\\n  [data-nextjs-container-errors-pseudo-html-collapse-button] {\\n    all: unset;\\n    margin-left: 12px;\\n    &:focus {\\n      outline: none;\\n    }\\n  }\\n  [data-nextjs-container-errors-pseudo-html--diff='add'] {\\n    background: var(--color-green-300);\\n  }\\n  [data-nextjs-container-errors-pseudo-html-line-sign] {\\n    margin-left: calc(24px * -1);\\n    margin-right: 24px;\\n  }\\n  [data-nextjs-container-errors-pseudo-html--diff='add']\\n    [data-nextjs-container-errors-pseudo-html-line-sign] {\\n    color: var(--color-green-900);\\n  }\\n  [data-nextjs-container-errors-pseudo-html--diff='remove'] {\\n    background: var(--color-red-300);\\n  }\\n  [data-nextjs-container-errors-pseudo-html--diff='remove']\\n    [data-nextjs-container-errors-pseudo-html-line-sign] {\\n    color: var(--color-red-900);\\n    margin-left: calc(24px * -1);\\n    margin-right: 24px;\\n  }\\n  [data-nextjs-container-errors-pseudo-html--diff='error']\\n    [data-nextjs-container-errors-pseudo-html-line-sign] {\\n    color: var(--color-amber-900);\\n  }\\n  \\n  [data-nextjs-container-errors-pseudo-html--hint] {\\n    display: inline-block;\\n    font-size: 0;\\n    height: 0;\\n  }\\n  [data-nextjs-container-errors-pseudo-html--tag-adjacent='false'] {\\n    color: var(--color-accents-1);\\n  }\\n  .nextjs__container_errors__component-stack {\\n    margin: 0;\\n  }\\n  [data-nextjs-container-errors-pseudo-html-collapse='true']\\n    .nextjs__container_errors__component-stack\\n    code {\\n    max-height: 120px;\\n    mask-image: linear-gradient(to bottom,rgba(0,0,0,0) 0%,black 10%);\\n    padding-bottom: 40px;\\n  }\\n  .nextjs__container_errors__component-stack code {\\n    display: block;\\n    width: 100%;\\n    white-space: pre-wrap;\\n    scroll-snap-type: y mandatory;\\n    overflow-y: hidden;\\n  }\\n  [data-nextjs-container-errors-pseudo-html--diff] {\\n    scroll-snap-align: center;\\n  }\\n  .error-overlay-hydration-error-diff-plus-icon {\\n    color: var(--color-green-900);\\n  }\\n  .error-overlay-hydration-error-diff-minus-icon {\\n    color: var(--color-red-900);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=component-stack-pseudo-html.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/component-stack-pseudo-html.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/index.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/index.js ***!
  \********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    RuntimeError: function() {\n        return RuntimeError;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _codeframe = __webpack_require__(/*! ../../components/code-frame/code-frame */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/code-frame/code-frame.js\");\nconst _callstack = __webpack_require__(/*! ../../components/errors/call-stack/call-stack */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js\");\nconst _componentstackpseudohtml = __webpack_require__(/*! ./component-stack-pseudo-html */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/component-stack-pseudo-html.js\");\nconst _geterrorbytype = __webpack_require__(/*! ../../../utils/get-error-by-type */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-error-by-type.js\");\nfunction RuntimeError(param) {\n    let { error, dialogResizerRef } = param;\n    const frames = (0, _geterrorbytype.useFrames)(error);\n    const firstFrame = (0, _react.useMemo)(()=>{\n        const firstFirstPartyFrameIndex = frames.findIndex((entry)=>!entry.ignored && Boolean(entry.originalCodeFrame) && Boolean(entry.originalStackFrame));\n        var _frames_firstFirstPartyFrameIndex;\n        return (_frames_firstFirstPartyFrameIndex = frames[firstFirstPartyFrameIndex]) != null ? _frames_firstFirstPartyFrameIndex : null;\n    }, [\n        frames\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            firstFrame && /*#__PURE__*/ (0, _jsxruntime.jsx)(_codeframe.CodeFrame, {\n                stackFrame: firstFrame.originalStackFrame,\n                codeFrame: firstFrame.originalCodeFrame\n            }),\n            frames.length > 0 && /*#__PURE__*/ (0, _jsxruntime.jsx)(_callstack.CallStack, {\n                dialogResizerRef: dialogResizerRef,\n                frames: frames\n            })\n        ]\n    });\n}\n_c = RuntimeError;\nconst styles = \"\\n  \" + _componentstackpseudohtml.PSEUDO_HTML_DIFF_STYLES + \"\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\nvar _c;\n$RefreshReg$(_c, \"RuntimeError\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29udGFpbmVyL3J1bnRpbWUtZXJyb3IvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBY2dCQSxZQUFZO2VBQVpBOztJQThCSEMsTUFBTTtlQUFOQTs7OzttQ0E1Q1c7dUNBQ0U7dUNBQ0E7c0RBQ2M7NENBSWpDO0FBT0Esc0JBQXNCLEtBQThDO0lBQTlDLE1BQUVDLEtBQUssRUFBRUMsZ0JBQWdCLEVBQXFCLEdBQTlDO0lBQzNCLE1BQU1DLFNBQVNDLENBQUFBLEdBQUFBLGdCQUFBQSxTQUFBQSxFQUFVSDtJQUV6QixNQUFNSSxhQUFhQyxDQUFBQSxHQUFBQSxPQUFBQSxPQUFBQSxFQUFRO1FBQ3pCLE1BQU1DLDRCQUE0QkosT0FBT0ssU0FBUyxDQUNoRCxDQUFDQyxRQUNDLENBQUNBLE1BQU1DLE9BQU8sSUFDZEMsUUFBUUYsTUFBTUcsaUJBQWlCLEtBQy9CRCxRQUFRRixNQUFNSSxrQkFBa0I7WUFHN0JWO1FBQVAsT0FBT0EsQ0FBQUEsb0NBQUFBLE1BQU0sQ0FBQ0ksMEJBQUFBLEtBQTBCLE9BQWpDSixvQ0FBcUM7SUFDOUMsR0FBRztRQUFDQTtLQUFPO0lBRVgsT0FDRSxXQURGLEdBQ0U7O1lBQ0dFLGNBQ0MsV0FEREEsR0FDQyxxQkFBQ1MsV0FBQUEsU0FBUztnQkFDUkMsWUFBWVYsV0FBV1Esa0JBQWtCO2dCQUN6Q0csV0FBV1gsV0FBV08saUJBQWlCOztZQUkxQ1QsT0FBT2MsTUFBTSxHQUFHLG1CQUNmLHFCQUFDQyxXQUFBQSxTQUFTO2dCQUFDaEIsa0JBQWtCQTtnQkFBa0JDLFFBQVFBOzs7O0FBSS9EO0tBNUJnQko7QUE4QlQsTUFBTUMsU0FBVSxTQUNuQm1CLDBCQUFBQSx1QkFBdUIsR0FBQyIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXGNvbnRhaW5lclxccnVudGltZS1lcnJvclxcaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZU1lbW8gfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IENvZGVGcmFtZSB9IGZyb20gJy4uLy4uL2NvbXBvbmVudHMvY29kZS1mcmFtZS9jb2RlLWZyYW1lJ1xuaW1wb3J0IHsgQ2FsbFN0YWNrIH0gZnJvbSAnLi4vLi4vY29tcG9uZW50cy9lcnJvcnMvY2FsbC1zdGFjay9jYWxsLXN0YWNrJ1xuaW1wb3J0IHsgUFNFVURPX0hUTUxfRElGRl9TVFlMRVMgfSBmcm9tICcuL2NvbXBvbmVudC1zdGFjay1wc2V1ZG8taHRtbCdcbmltcG9ydCB7XG4gIHVzZUZyYW1lcyxcbiAgdHlwZSBSZWFkeVJ1bnRpbWVFcnJvcixcbn0gZnJvbSAnLi4vLi4vLi4vdXRpbHMvZ2V0LWVycm9yLWJ5LXR5cGUnXG5cbmV4cG9ydCB0eXBlIFJ1bnRpbWVFcnJvclByb3BzID0ge1xuICBlcnJvcjogUmVhZHlSdW50aW1lRXJyb3JcbiAgZGlhbG9nUmVzaXplclJlZjogUmVhY3QuUmVmT2JqZWN0PEhUTUxEaXZFbGVtZW50IHwgbnVsbD5cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFJ1bnRpbWVFcnJvcih7IGVycm9yLCBkaWFsb2dSZXNpemVyUmVmIH06IFJ1bnRpbWVFcnJvclByb3BzKSB7XG4gIGNvbnN0IGZyYW1lcyA9IHVzZUZyYW1lcyhlcnJvcilcblxuICBjb25zdCBmaXJzdEZyYW1lID0gdXNlTWVtbygoKSA9PiB7XG4gICAgY29uc3QgZmlyc3RGaXJzdFBhcnR5RnJhbWVJbmRleCA9IGZyYW1lcy5maW5kSW5kZXgoXG4gICAgICAoZW50cnkpID0+XG4gICAgICAgICFlbnRyeS5pZ25vcmVkICYmXG4gICAgICAgIEJvb2xlYW4oZW50cnkub3JpZ2luYWxDb2RlRnJhbWUpICYmXG4gICAgICAgIEJvb2xlYW4oZW50cnkub3JpZ2luYWxTdGFja0ZyYW1lKVxuICAgIClcblxuICAgIHJldHVybiBmcmFtZXNbZmlyc3RGaXJzdFBhcnR5RnJhbWVJbmRleF0gPz8gbnVsbFxuICB9LCBbZnJhbWVzXSlcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICB7Zmlyc3RGcmFtZSAmJiAoXG4gICAgICAgIDxDb2RlRnJhbWVcbiAgICAgICAgICBzdGFja0ZyYW1lPXtmaXJzdEZyYW1lLm9yaWdpbmFsU3RhY2tGcmFtZSF9XG4gICAgICAgICAgY29kZUZyYW1lPXtmaXJzdEZyYW1lLm9yaWdpbmFsQ29kZUZyYW1lIX1cbiAgICAgICAgLz5cbiAgICAgICl9XG5cbiAgICAgIHtmcmFtZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgIDxDYWxsU3RhY2sgZGlhbG9nUmVzaXplclJlZj17ZGlhbG9nUmVzaXplclJlZn0gZnJhbWVzPXtmcmFtZXN9IC8+XG4gICAgICApfVxuICAgIDwvPlxuICApXG59XG5cbmV4cG9ydCBjb25zdCBzdHlsZXMgPSBgXG4gICR7UFNFVURPX0hUTUxfRElGRl9TVFlMRVN9XG5gXG4iXSwibmFtZXMiOlsiUnVudGltZUVycm9yIiwic3R5bGVzIiwiZXJyb3IiLCJkaWFsb2dSZXNpemVyUmVmIiwiZnJhbWVzIiwidXNlRnJhbWVzIiwiZmlyc3RGcmFtZSIsInVzZU1lbW8iLCJmaXJzdEZpcnN0UGFydHlGcmFtZUluZGV4IiwiZmluZEluZGV4IiwiZW50cnkiLCJpZ25vcmVkIiwiQm9vbGVhbiIsIm9yaWdpbmFsQ29kZUZyYW1lIiwib3JpZ2luYWxTdGFja0ZyYW1lIiwiQ29kZUZyYW1lIiwic3RhY2tGcmFtZSIsImNvZGVGcmFtZSIsImxlbmd0aCIsIkNhbGxTdGFjayIsIlBTRVVET19IVE1MX0RJRkZfU1RZTEVTIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/render-error.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/render-error.js ***!
  \***************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RenderError\", ({\n    enumerable: true,\n    get: function() {\n        return RenderError;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _shared = __webpack_require__(/*! ../../../shared */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _geterrorbytype = __webpack_require__(/*! ../../../utils/get-error-by-type */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-error-by-type.js\");\nfunction getErrorSignature(ev) {\n    const { event } = ev;\n    // eslint-disable-next-line default-case -- TypeScript checks this\n    switch(event.type){\n        case _shared.ACTION_UNHANDLED_ERROR:\n        case _shared.ACTION_UNHANDLED_REJECTION:\n            {\n                return event.reason.name + \"::\" + event.reason.message + \"::\" + event.reason.stack;\n            }\n    }\n}\nconst RenderError = (props)=>{\n    var _state_rootLayoutMissingTags;\n    const { state } = props;\n    const isBuildError = !!((_state_rootLayoutMissingTags = state.rootLayoutMissingTags) == null ? void 0 : _state_rootLayoutMissingTags.length) || !!state.buildError;\n    if (isBuildError) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(RenderBuildError, {\n            ...props\n        });\n    } else {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(RenderRuntimeError, {\n            ...props\n        });\n    }\n};\n_c = RenderError;\nconst RenderRuntimeError = (param)=>{\n    let { children, state, isAppDir } = param;\n    const { errors } = state;\n    const [lookups, setLookups] = (0, _react.useState)({});\n    const [runtimeErrors, nextError] = (0, _react.useMemo)(()=>{\n        let ready = [];\n        let next = null;\n        // Ensure errors are displayed in the order they occurred in:\n        for(let idx = 0; idx < errors.length; ++idx){\n            const e = errors[idx];\n            const { id } = e;\n            if (id in lookups) {\n                ready.push(lookups[id]);\n                continue;\n            }\n            // Check for duplicate errors\n            if (idx > 0) {\n                const prev = errors[idx - 1];\n                if (getErrorSignature(prev) === getErrorSignature(e)) {\n                    continue;\n                }\n            }\n            next = e;\n            break;\n        }\n        return [\n            ready,\n            next\n        ];\n    }, [\n        errors,\n        lookups\n    ]);\n    (0, _react.useEffect)(()=>{\n        if (nextError == null) {\n            return;\n        }\n        let mounted = true;\n        (0, _geterrorbytype.getErrorByType)(nextError, isAppDir).then((resolved)=>{\n            if (mounted) {\n                // We don't care if the desired error changed while we were resolving,\n                // thus we're not tracking it using a ref. Once the work has been done,\n                // we'll store it.\n                setLookups((m)=>({\n                        ...m,\n                        [resolved.id]: resolved\n                    }));\n            }\n        });\n        return ()=>{\n            mounted = false;\n        };\n    }, [\n        nextError,\n        isAppDir\n    ]);\n    const totalErrorCount = runtimeErrors.length;\n    return children({\n        runtimeErrors,\n        totalErrorCount\n    });\n};\n_c1 = RenderRuntimeError;\nconst RenderBuildError = (param)=>{\n    let { children } = param;\n    return children({\n        runtimeErrors: [],\n        // Build errors and missing root layout tags persist until fixed,\n        // so we can set a fixed error count of 1\n        totalErrorCount: 1\n    });\n};\n_c2 = RenderBuildError;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=render-error.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"RenderError\");\n$RefreshReg$(_c1, \"RenderRuntimeError\");\n$RefreshReg$(_c2, \"RenderBuildError\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/render-error.js\n"));

/***/ })

});