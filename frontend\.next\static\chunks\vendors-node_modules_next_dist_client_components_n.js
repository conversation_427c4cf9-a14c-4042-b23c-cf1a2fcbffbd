"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_n"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/not-found-error.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/not-found-error.js ***!
  \*********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return NotFound;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _errorfallback = __webpack_require__(/*! ./http-access-fallback/error-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-fallback.js\");\nfunction NotFound() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorfallback.HTTPAccessErrorFallback, {\n        status: 404,\n        message: \"This page could not be found.\"\n    });\n}\n_c = NotFound;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=not-found-error.js.map\nvar _c;\n$RefreshReg$(_c, \"NotFound\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7MkNBRUE7OztlQUF3QkE7Ozs7MkNBRmdCO0FBRXpCO0lBQ2IscUJBQ0UscUJBQUNDLGVBQUFBLHVCQUF1QjtRQUN0QkMsUUFBUTtRQUNSQyxTQUFROztBQUdkO0tBUHdCSCIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxub3QtZm91bmQtZXJyb3IudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEhUVFBBY2Nlc3NFcnJvckZhbGxiYWNrIH0gZnJvbSAnLi9odHRwLWFjY2Vzcy1mYWxsYmFjay9lcnJvci1mYWxsYmFjaydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTm90Rm91bmQoKSB7XG4gIHJldHVybiAoXG4gICAgPEhUVFBBY2Nlc3NFcnJvckZhbGxiYWNrXG4gICAgICBzdGF0dXM9ezQwNH1cbiAgICAgIG1lc3NhZ2U9XCJUaGlzIHBhZ2UgY291bGQgbm90IGJlIGZvdW5kLlwiXG4gICAgLz5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIk5vdEZvdW5kIiwiSFRUUEFjY2Vzc0Vycm9yRmFsbGJhY2siLCJzdGF0dXMiLCJtZXNzYWdlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/not-found.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/client/components/not-found.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"notFound\", ({\n    enumerable: true,\n    get: function() {\n        return notFound;\n    }\n}));\nconst _httpaccessfallback = __webpack_require__(/*! ./http-access-fallback/http-access-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\");\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */ const DIGEST = \"\" + _httpaccessfallback.HTTP_ERROR_FALLBACK_ERROR_CODE + \";404\";\nfunction notFound() {\n    // eslint-disable-next-line no-throw-literal\n    const error = Object.defineProperty(new Error(DIGEST), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = DIGEST;\n    throw error;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=not-found.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/client/components/promise-queue.js ***!
  \*******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/*\n    This is a simple promise queue that allows you to limit the number of concurrent promises\n    that are running at any given time. It's used to limit the number of concurrent\n    prefetch requests that are being made to the server but could be used for other\n    things as well.\n*/ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PromiseQueue\", ({\n    enumerable: true,\n    get: function() {\n        return PromiseQueue;\n    }\n}));\nconst _class_private_field_loose_base = __webpack_require__(/*! @swc/helpers/_/_class_private_field_loose_base */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_loose_base.js\");\nconst _class_private_field_loose_key = __webpack_require__(/*! @swc/helpers/_/_class_private_field_loose_key */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_class_private_field_loose_key.js\");\nvar _maxConcurrency = /*#__PURE__*/ _class_private_field_loose_key._(\"_maxConcurrency\"), _runningCount = /*#__PURE__*/ _class_private_field_loose_key._(\"_runningCount\"), _queue = /*#__PURE__*/ _class_private_field_loose_key._(\"_queue\"), _processNext = /*#__PURE__*/ _class_private_field_loose_key._(\"_processNext\");\nclass PromiseQueue {\n    enqueue(promiseFn) {\n        let taskResolve;\n        let taskReject;\n        const taskPromise = new Promise((resolve, reject)=>{\n            taskResolve = resolve;\n            taskReject = reject;\n        });\n        const task = async ()=>{\n            try {\n                _class_private_field_loose_base._(this, _runningCount)[_runningCount]++;\n                const result = await promiseFn();\n                taskResolve(result);\n            } catch (error) {\n                taskReject(error);\n            } finally{\n                _class_private_field_loose_base._(this, _runningCount)[_runningCount]--;\n                _class_private_field_loose_base._(this, _processNext)[_processNext]();\n            }\n        };\n        const enqueueResult = {\n            promiseFn: taskPromise,\n            task\n        };\n        // wonder if we should take a LIFO approach here\n        _class_private_field_loose_base._(this, _queue)[_queue].push(enqueueResult);\n        _class_private_field_loose_base._(this, _processNext)[_processNext]();\n        return taskPromise;\n    }\n    bump(promiseFn) {\n        const index = _class_private_field_loose_base._(this, _queue)[_queue].findIndex((item)=>item.promiseFn === promiseFn);\n        if (index > -1) {\n            const bumpedItem = _class_private_field_loose_base._(this, _queue)[_queue].splice(index, 1)[0];\n            _class_private_field_loose_base._(this, _queue)[_queue].unshift(bumpedItem);\n            _class_private_field_loose_base._(this, _processNext)[_processNext](true);\n        }\n    }\n    constructor(maxConcurrency = 5){\n        Object.defineProperty(this, _processNext, {\n            value: processNext\n        });\n        Object.defineProperty(this, _maxConcurrency, {\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, _runningCount, {\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, _queue, {\n            writable: true,\n            value: void 0\n        });\n        _class_private_field_loose_base._(this, _maxConcurrency)[_maxConcurrency] = maxConcurrency;\n        _class_private_field_loose_base._(this, _runningCount)[_runningCount] = 0;\n        _class_private_field_loose_base._(this, _queue)[_queue] = [];\n    }\n}\nfunction processNext(forced) {\n    if (forced === void 0) forced = false;\n    if ((_class_private_field_loose_base._(this, _runningCount)[_runningCount] < _class_private_field_loose_base._(this, _maxConcurrency)[_maxConcurrency] || forced) && _class_private_field_loose_base._(this, _queue)[_queue].length > 0) {\n        var _class_private_field_loose_base__queue_shift;\n        (_class_private_field_loose_base__queue_shift = _class_private_field_loose_base._(this, _queue)[_queue].shift()) == null ? void 0 : _class_private_field_loose_base__queue_shift.task();\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=promise-queue.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js ***!
  \**********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AppDevOverlayErrorBoundary\", ({\n    enumerable: true,\n    get: function() {\n        return AppDevOverlayErrorBoundary;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _runtimeerrorhandler = __webpack_require__(/*! ../../errors/runtime-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/runtime-error-handler.js\");\nconst _errorboundary = __webpack_require__(/*! ../../error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nfunction ErroredHtml(param) {\n    let { globalError: [GlobalError, globalErrorStyles], error } = param;\n    if (!error) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"html\", {\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"head\", {}),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"body\", {})\n            ]\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_errorboundary.ErrorBoundary, {\n        errorComponent: _errorboundary.GlobalError,\n        children: [\n            globalErrorStyles,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(GlobalError, {\n                error: error\n            })\n        ]\n    });\n}\n_c = ErroredHtml;\nclass AppDevOverlayErrorBoundary extends _react.PureComponent {\n    static getDerivedStateFromError(error) {\n        if (!error.stack) {\n            return {\n                isReactError: false,\n                reactError: null\n            };\n        }\n        _runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError = true;\n        return {\n            isReactError: true,\n            reactError: error\n        };\n    }\n    componentDidCatch() {\n        this.props.onError(this.state.isReactError);\n    }\n    render() {\n        const { children, globalError } = this.props;\n        const { isReactError, reactError } = this.state;\n        const fallback = /*#__PURE__*/ (0, _jsxruntime.jsx)(ErroredHtml, {\n            globalError: globalError,\n            error: reactError\n        });\n        return isReactError ? fallback : children;\n    }\n    constructor(...args){\n        super(...args), this.state = {\n            isReactError: false,\n            reactError: null\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dev-overlay-error-boundary.js.map\nvar _c;\n$RefreshReg$(_c, \"ErroredHtml\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js ***!
  \*******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AppDevOverlay\", ({\n    enumerable: true,\n    get: function() {\n        return AppDevOverlay;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _appdevoverlayerrorboundary = __webpack_require__(/*! ./app-dev-overlay-error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\");\nconst _fontstyles = __webpack_require__(/*! ../font/font-styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js\");\nconst _devoverlay = __webpack_require__(/*! ../ui/dev-overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/dev-overlay.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nfunction readSsrError() {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    const ssrErrorTemplateTag = document.querySelector('template[data-next-error-message]');\n    if (ssrErrorTemplateTag) {\n        const message = ssrErrorTemplateTag.getAttribute('data-next-error-message');\n        const stack = ssrErrorTemplateTag.getAttribute('data-next-error-stack');\n        const digest = ssrErrorTemplateTag.getAttribute('data-next-error-digest');\n        const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n        if (digest) {\n            ;\n            error.digest = digest;\n        }\n        // Skip Next.js SSR'd internal errors that which will be handled by the error boundaries.\n        if ((0, _isnextroutererror.isNextRouterError)(error)) {\n            return null;\n        }\n        error.stack = stack || '';\n        return error;\n    }\n    return null;\n}\n// Needs to be in the same error boundary as the shell.\n// If it commits, we know we recovered from an SSR error.\n// If it doesn't commit, we errored again and React will take care of error reporting.\nfunction ReplaySsrOnlyErrors() {\n    if (true) {\n        // Need to read during render. The attributes will be gone after commit.\n        const ssrError = readSsrError();\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            if (ssrError !== null) {\n                // TODO(veil): Produces wrong Owner Stack\n                // TODO(veil): Mark as recoverable error\n                // TODO(veil): console.error\n                (0, _useerrorhandler.handleClientError)(ssrError, []);\n            }\n        }, [\n            ssrError\n        ]);\n    }\n    return null;\n}\n_c = ReplaySsrOnlyErrors;\nfunction AppDevOverlay(param) {\n    let { state, globalError, children } = param;\n    const [isErrorOverlayOpen, setIsErrorOverlayOpen] = (0, _react.useState)(false);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_appdevoverlayerrorboundary.AppDevOverlayErrorBoundary, {\n                globalError: globalError,\n                onError: setIsErrorOverlayOpen,\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(ReplaySsrOnlyErrors, {}),\n                    children\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_fontstyles.FontStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_devoverlay.DevOverlay, {\n                state: state,\n                isErrorOverlayOpen: isErrorOverlayOpen,\n                setIsErrorOverlayOpen: setIsErrorOverlayOpen\n            })\n        ]\n    });\n}\n_c1 = AppDevOverlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dev-overlay.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"ReplaySsrOnlyErrors\");\n$RefreshReg$(_c1, \"AppDevOverlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/client-entry.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/client-entry.js ***!
  \****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createRootLevelDevOverlayElement\", ({\n    enumerable: true,\n    get: function() {\n        return createRootLevelDevOverlayElement;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _appdevoverlay = __webpack_require__(/*! ./app-dev-overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js\");\nconst _getsocketurl = __webpack_require__(/*! ../utils/get-socket-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-socket-url.js\");\nconst _shared = __webpack_require__(/*! ../shared */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _hotreloadertypes = __webpack_require__(/*! ../../../../server/dev/hot-reloader-types */ \"(app-pages-browser)/./node_modules/next/dist/server/dev/hot-reloader-types.js\");\nconst _errorboundary = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\"));\nfunction createRootLevelDevOverlayElement(reactEl) {\n    const rootLayoutMissingTags = window.__next_root_layout_missing_tags;\n    const hasMissingTags = !!(rootLayoutMissingTags == null ? void 0 : rootLayoutMissingTags.length);\n    const socketUrl = (0, _getsocketurl.getSocketUrl)( false || '');\n    const socket = new window.WebSocket(\"\" + socketUrl + \"/_next/webpack-hmr\");\n    // add minimal \"hot reload\" support for RSC errors\n    const handler = (event)=>{\n        let obj;\n        try {\n            obj = JSON.parse(event.data);\n        } catch (e) {}\n        if (!obj || !('action' in obj)) {\n            return;\n        }\n        if (obj.action === _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES) {\n            window.location.reload();\n        }\n    };\n    socket.addEventListener('message', handler);\n    const FallbackLayout = hasMissingTags ? (param)=>{\n        let { children } = param;\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"html\", {\n            id: \"__next_error__\",\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"body\", {\n                children: children\n            })\n        });\n    } : _react.default.Fragment;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(FallbackLayout, {\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_appdevoverlay.AppDevOverlay, {\n            state: {\n                ..._shared.INITIAL_OVERLAY_STATE,\n                rootLayoutMissingTags,\n                routerType: 'app'\n            },\n            globalError: [\n                _errorboundary.default,\n                null\n            ],\n            children: reactEl\n        })\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=client-entry.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/client-entry.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js ***!
  \***********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return HotReload;\n    },\n    waitForWebpackRuntimeHotUpdate: function() {\n        return waitForWebpackRuntimeHotUpdate;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _stripansi = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/strip-ansi */ \"(app-pages-browser)/./node_modules/next/dist/compiled/strip-ansi/index.js\"));\nconst _formatwebpackmessages = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../utils/format-webpack-messages */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/format-webpack-messages.js\"));\nconst _navigation = __webpack_require__(/*! ../../navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\nconst _shared = __webpack_require__(/*! ../shared */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _parsestack = __webpack_require__(/*! ../utils/parse-stack */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-stack.js\");\nconst _appdevoverlay = __webpack_require__(/*! ./app-dev-overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _runtimeerrorhandler = __webpack_require__(/*! ../../errors/runtime-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/runtime-error-handler.js\");\nconst _usewebsocket = __webpack_require__(/*! ../utils/use-websocket */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/use-websocket.js\");\nconst _parsecomponentstack = __webpack_require__(/*! ../utils/parse-component-stack */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-component-stack.js\");\nconst _hotreloadertypes = __webpack_require__(/*! ../../../../server/dev/hot-reloader-types */ \"(app-pages-browser)/./node_modules/next/dist/server/dev/hot-reloader-types.js\");\nconst _extractmodulesfromturbopackmessage = __webpack_require__(/*! ../../../../server/dev/extract-modules-from-turbopack-message */ \"(app-pages-browser)/./node_modules/next/dist/server/dev/extract-modules-from-turbopack-message.js\");\nconst _navigationuntracked = __webpack_require__(/*! ../../navigation-untracked */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation-untracked.js\");\nconst _stitchederror = __webpack_require__(/*! ../../errors/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst _iserrorthrownwhilerenderingrsc = __webpack_require__(/*! ../../../lib/is-error-thrown-while-rendering-rsc */ \"(app-pages-browser)/./node_modules/next/dist/client/lib/is-error-thrown-while-rendering-rsc.js\");\nconst _handledevbuildindicatorhmrevents = __webpack_require__(/*! ../../../dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js\");\nlet mostRecentCompilationHash = null;\nlet __nextDevClientId = Math.round(Math.random() * 100 + Date.now());\nlet reloading = false;\nlet startLatency = null;\nlet turbopackLastUpdateLatency = null;\nlet turbopackUpdatedModules = new Set();\nlet pendingHotUpdateWebpack = Promise.resolve();\nlet resolvePendingHotUpdateWebpack = ()=>{};\nfunction setPendingHotUpdateWebpack() {\n    pendingHotUpdateWebpack = new Promise((resolve)=>{\n        resolvePendingHotUpdateWebpack = ()=>{\n            resolve();\n        };\n    });\n}\nfunction waitForWebpackRuntimeHotUpdate() {\n    return pendingHotUpdateWebpack;\n}\nfunction handleBeforeHotUpdateWebpack(dispatcher, hasUpdates) {\n    if (hasUpdates) {\n        dispatcher.onBeforeRefresh();\n    }\n}\nfunction handleSuccessfulHotUpdateWebpack(dispatcher, sendMessage, updatedModules) {\n    resolvePendingHotUpdateWebpack();\n    dispatcher.onBuildOk();\n    reportHmrLatency(sendMessage, updatedModules);\n    dispatcher.onRefresh();\n}\nfunction reportHmrLatency(sendMessage, updatedModules) {\n    if (!startLatency) return;\n    // turbopack has a debounce for the \"built\" event which we don't want to\n    // incorrectly show in this number, use the last TURBOPACK_MESSAGE time\n    let endLatency = turbopackLastUpdateLatency != null ? turbopackLastUpdateLatency : Date.now();\n    const latency = endLatency - startLatency;\n    console.log(\"[Fast Refresh] done in \" + latency + \"ms\");\n    sendMessage(JSON.stringify({\n        event: 'client-hmr-latency',\n        id: window.__nextDevClientId,\n        startTime: startLatency,\n        endTime: endLatency,\n        page: window.location.pathname,\n        updatedModules,\n        // Whether the page (tab) was hidden at the time the event occurred.\n        // This can impact the accuracy of the event's timing.\n        isPageHidden: document.visibilityState === 'hidden'\n    }));\n}\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash) {\n    // Update last known compilation hash.\n    mostRecentCompilationHash = hash;\n}\n/**\n * Is there a newer version of this code available?\n * For webpack: Check if the hash changed compared to __webpack_hash__\n * For Turbopack: Always true because it doesn't have __webpack_hash__\n */ function isUpdateAvailable() {\n    if (false) {}\n    /* globals __webpack_hash__ */ // __webpack_hash__ is the hash of the current compilation.\n    // It's a global variable injected by Webpack.\n    return mostRecentCompilationHash !== __webpack_require__.h();\n}\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n    // @ts-expect-error module.hot exists\n    return module.hot.status() === 'idle';\n}\nfunction afterApplyUpdates(fn) {\n    if (canApplyUpdates()) {\n        fn();\n    } else {\n        function handler(status) {\n            if (status === 'idle') {\n                // @ts-expect-error module.hot exists\n                module.hot.removeStatusHandler(handler);\n                fn();\n            }\n        }\n        // @ts-expect-error module.hot exists\n        module.hot.addStatusHandler(handler);\n    }\n}\nfunction performFullReload(err, sendMessage) {\n    const stackTrace = err && (err.stack && err.stack.split('\\n').slice(0, 5).join('\\n') || err.message || err + '');\n    sendMessage(JSON.stringify({\n        event: 'client-full-reload',\n        stackTrace,\n        hadRuntimeError: !!_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError,\n        dependencyChain: err ? err.dependencyChain : undefined\n    }));\n    if (reloading) return;\n    reloading = true;\n    window.location.reload();\n}\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdates(onBeforeUpdate, onHotUpdateSuccess, sendMessage, dispatcher) {\n    if (!isUpdateAvailable() || !canApplyUpdates()) {\n        resolvePendingHotUpdateWebpack();\n        dispatcher.onBuildOk();\n        reportHmrLatency(sendMessage, []);\n        return;\n    }\n    function handleApplyUpdates(err, updatedModules) {\n        if (err || _runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError || !updatedModules) {\n            if (err) {\n                console.warn('[Fast Refresh] performing full reload\\n\\n' + \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" + 'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' + 'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' + 'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' + 'Fast Refresh requires at least one parent function component in your React tree.');\n            } else if (_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError) {\n                console.warn(_shared.REACT_REFRESH_FULL_RELOAD_FROM_ERROR);\n            }\n            performFullReload(err, sendMessage);\n            return;\n        }\n        const hasUpdates = Boolean(updatedModules.length);\n        if (typeof onHotUpdateSuccess === 'function') {\n            // Maybe we want to do something.\n            onHotUpdateSuccess(updatedModules);\n        }\n        if (isUpdateAvailable()) {\n            // While we were updating, there was a new update! Do it again.\n            tryApplyUpdates(hasUpdates ? ()=>{} : onBeforeUpdate, hasUpdates ? ()=>dispatcher.onBuildOk() : onHotUpdateSuccess, sendMessage, dispatcher);\n        } else {\n            dispatcher.onBuildOk();\n            if (false) {}\n        }\n    }\n    // https://webpack.js.org/api/hot-module-replacement/#check\n    // @ts-expect-error module.hot exists\n    module.hot.check(/* autoApply */ false).then((updatedModules)=>{\n        if (!updatedModules) {\n            return null;\n        }\n        if (typeof onBeforeUpdate === 'function') {\n            const hasUpdates = Boolean(updatedModules.length);\n            onBeforeUpdate(hasUpdates);\n        }\n        // https://webpack.js.org/api/hot-module-replacement/#apply\n        // @ts-expect-error module.hot exists\n        return module.hot.apply();\n    }).then((updatedModules)=>{\n        handleApplyUpdates(null, updatedModules);\n    }, (err)=>{\n        handleApplyUpdates(err, null);\n    });\n}\n/** Handles messages from the sevrer for the App Router. */ function processMessage(obj, sendMessage, processTurbopackMessage, router, dispatcher, appIsrManifestRef, pathnameRef) {\n    if (!('action' in obj)) {\n        return;\n    }\n    function handleErrors(errors) {\n        // \"Massage\" webpack messages.\n        const formatted = (0, _formatwebpackmessages.default)({\n            errors: errors,\n            warnings: []\n        });\n        // Only show the first error.\n        dispatcher.onBuildError(formatted.errors[0]);\n        // Also log them to the console.\n        for(let i = 0; i < formatted.errors.length; i++){\n            console.error((0, _stripansi.default)(formatted.errors[i]));\n        }\n        // Do not attempt to reload now.\n        // We will reload on next success instead.\n        if (false) {}\n    }\n    function handleHotUpdate() {\n        if (false) {} else {\n            tryApplyUpdates(function onBeforeHotUpdate(hasUpdates) {\n                handleBeforeHotUpdateWebpack(dispatcher, hasUpdates);\n            }, function onSuccessfulHotUpdate(webpackUpdatedModules) {\n                // Only dismiss it when we're sure it's a hot update.\n                // Otherwise it would flicker right before the reload.\n                handleSuccessfulHotUpdateWebpack(dispatcher, sendMessage, webpackUpdatedModules);\n            }, sendMessage, dispatcher);\n        }\n    }\n    switch(obj.action){\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST:\n            {\n                if (true) {\n                    if (appIsrManifestRef) {\n                        appIsrManifestRef.current = obj.data;\n                        // handle initial status on receiving manifest\n                        // navigation is handled in useEffect for pathname changes\n                        // as we'll receive the updated manifest before usePathname\n                        // triggers for new value\n                        if (pathnameRef.current in obj.data) {\n                            dispatcher.onStaticIndicator(true);\n                        } else {\n                            dispatcher.onStaticIndicator(false);\n                        }\n                    }\n                }\n                break;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILDING:\n            {\n                startLatency = Date.now();\n                turbopackLastUpdateLatency = null;\n                turbopackUpdatedModules.clear();\n                if (true) {\n                    setPendingHotUpdateWebpack();\n                }\n                console.log('[Fast Refresh] rebuilding');\n                break;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SYNC:\n            {\n                if (obj.hash) {\n                    handleAvailableHash(obj.hash);\n                }\n                const { errors, warnings } = obj;\n                // Is undefined when it's a 'built' event\n                if ('versionInfo' in obj) dispatcher.onVersionInfo(obj.versionInfo);\n                if ('debug' in obj && obj.debug) dispatcher.onDebugInfo(obj.debug);\n                if ('devIndicator' in obj) dispatcher.onDevIndicator(obj.devIndicator);\n                const hasErrors = Boolean(errors && errors.length);\n                // Compilation with errors (e.g. syntax error or missing modules).\n                if (hasErrors) {\n                    sendMessage(JSON.stringify({\n                        event: 'client-error',\n                        errorCount: errors.length,\n                        clientId: __nextDevClientId\n                    }));\n                    handleErrors(errors);\n                    return;\n                }\n                const hasWarnings = Boolean(warnings && warnings.length);\n                if (hasWarnings) {\n                    sendMessage(JSON.stringify({\n                        event: 'client-warning',\n                        warningCount: warnings.length,\n                        clientId: __nextDevClientId\n                    }));\n                    // Print warnings to the console.\n                    const formattedMessages = (0, _formatwebpackmessages.default)({\n                        warnings: warnings,\n                        errors: []\n                    });\n                    for(let i = 0; i < formattedMessages.warnings.length; i++){\n                        if (i === 5) {\n                            console.warn('There were more warnings in other files.\\n' + 'You can find a complete log in the terminal.');\n                            break;\n                        }\n                        console.warn((0, _stripansi.default)(formattedMessages.warnings[i]));\n                    }\n                // No early return here as we need to apply modules in the same way between warnings only and compiles without warnings\n                }\n                sendMessage(JSON.stringify({\n                    event: 'client-success',\n                    clientId: __nextDevClientId\n                }));\n                if (obj.action === _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILT) {\n                    // Handle hot updates\n                    handleHotUpdate();\n                }\n                return;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED:\n            {\n                processTurbopackMessage({\n                    type: _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n                    data: {\n                        sessionId: obj.data.sessionId\n                    }\n                });\n                break;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE:\n            {\n                dispatcher.onBeforeRefresh();\n                processTurbopackMessage({\n                    type: _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n                    data: obj.data\n                });\n                dispatcher.onRefresh();\n                if (_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError) {\n                    console.warn(_shared.REACT_REFRESH_FULL_RELOAD_FROM_ERROR);\n                    performFullReload(null, sendMessage);\n                }\n                for (const module1 of (0, _extractmodulesfromturbopackmessage.extractModulesFromTurbopackMessage)(obj.data)){\n                    turbopackUpdatedModules.add(module1);\n                }\n                turbopackLastUpdateLatency = Date.now();\n                break;\n            }\n        // TODO-APP: make server component change more granular\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES:\n            {\n                sendMessage(JSON.stringify({\n                    event: 'server-component-reload-page',\n                    clientId: __nextDevClientId,\n                    hash: obj.hash\n                }));\n                // Store the latest hash in a session cookie so that it's sent back to the\n                // server with any subsequent requests.\n                document.cookie = \"__next_hmr_refresh_hash__=\" + obj.hash;\n                if (_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError) {\n                    if (reloading) return;\n                    reloading = true;\n                    return window.location.reload();\n                }\n                (0, _react.startTransition)(()=>{\n                    router.hmrRefresh();\n                    dispatcher.onRefresh();\n                });\n                if (false) {}\n                return;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE:\n            {\n                sendMessage(JSON.stringify({\n                    event: 'client-reload-page',\n                    clientId: __nextDevClientId\n                }));\n                if (reloading) return;\n                reloading = true;\n                return window.location.reload();\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE:\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE:\n            {\n                // TODO-APP: potentially only refresh if the currently viewed page was added/removed.\n                return router.hmrRefresh();\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR:\n            {\n                const { errorJSON } = obj;\n                if (errorJSON) {\n                    const { message, stack } = JSON.parse(errorJSON);\n                    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                        value: \"E394\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                    error.stack = stack;\n                    handleErrors([\n                        error\n                    ]);\n                }\n                return;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE:\n            {\n                return;\n            }\n        default:\n            {}\n    }\n}\nfunction HotReload(param) {\n    let { assetPrefix, children, globalError } = param;\n    const [state, dispatch] = (0, _shared.useErrorOverlayReducer)('app');\n    const dispatcher = (0, _react.useMemo)(()=>{\n        return {\n            onBuildOk () {\n                dispatch({\n                    type: _shared.ACTION_BUILD_OK\n                });\n            },\n            onBuildError (message) {\n                dispatch({\n                    type: _shared.ACTION_BUILD_ERROR,\n                    message\n                });\n            },\n            onBeforeRefresh () {\n                dispatch({\n                    type: _shared.ACTION_BEFORE_REFRESH\n                });\n            },\n            onRefresh () {\n                dispatch({\n                    type: _shared.ACTION_REFRESH\n                });\n            },\n            onVersionInfo (versionInfo) {\n                dispatch({\n                    type: _shared.ACTION_VERSION_INFO,\n                    versionInfo\n                });\n            },\n            onStaticIndicator (status) {\n                dispatch({\n                    type: _shared.ACTION_STATIC_INDICATOR,\n                    staticIndicator: status\n                });\n            },\n            onDebugInfo (debugInfo) {\n                dispatch({\n                    type: _shared.ACTION_DEBUG_INFO,\n                    debugInfo\n                });\n            },\n            onDevIndicator (devIndicator) {\n                dispatch({\n                    type: _shared.ACTION_DEV_INDICATOR,\n                    devIndicator\n                });\n            }\n        };\n    }, [\n        dispatch\n    ]);\n    //  We render a separate error overlay at the root when an error is thrown from rendering RSC, so\n    //  we should not render an additional error overlay in the descendent. However, we need to\n    //  keep rendering these hooks to ensure HMR works when the error is addressed.\n    const shouldRenderErrorOverlay = (0, _react.useSyncExternalStore)(()=>()=>{}, ()=>!(0, _iserrorthrownwhilerenderingrsc.shouldRenderRootLevelErrorOverlay)(), ()=>true);\n    const handleOnUnhandledError = (0, _react.useCallback)((error)=>{\n        const errorDetails = error.details;\n        // Component stack is added to the error in use-error-handler in case there was a hydration error\n        const componentStackTrace = error._componentStack || (errorDetails == null ? void 0 : errorDetails.componentStack);\n        const warning = errorDetails == null ? void 0 : errorDetails.warning;\n        dispatch({\n            type: _shared.ACTION_UNHANDLED_ERROR,\n            reason: error,\n            frames: (0, _parsestack.parseStack)(error.stack || ''),\n            componentStackFrames: typeof componentStackTrace === 'string' ? (0, _parsecomponentstack.parseComponentStack)(componentStackTrace) : undefined,\n            warning\n        });\n    }, [\n        dispatch\n    ]);\n    const handleOnUnhandledRejection = (0, _react.useCallback)((reason)=>{\n        const stitchedError = (0, _stitchederror.getReactStitchedError)(reason);\n        dispatch({\n            type: _shared.ACTION_UNHANDLED_REJECTION,\n            reason: stitchedError,\n            frames: (0, _parsestack.parseStack)(stitchedError.stack || '')\n        });\n    }, [\n        dispatch\n    ]);\n    (0, _useerrorhandler.useErrorHandler)(handleOnUnhandledError, handleOnUnhandledRejection);\n    const webSocketRef = (0, _usewebsocket.useWebsocket)(assetPrefix);\n    (0, _usewebsocket.useWebsocketPing)(webSocketRef);\n    const sendMessage = (0, _usewebsocket.useSendMessage)(webSocketRef);\n    const processTurbopackMessage = (0, _usewebsocket.useTurbopack)(sendMessage, (err)=>performFullReload(err, sendMessage));\n    const router = (0, _navigation.useRouter)();\n    // We don't want access of the pathname for the dev tools to trigger a dynamic\n    // access (as the dev overlay will never be present in production).\n    const pathname = (0, _navigationuntracked.useUntrackedPathname)();\n    const appIsrManifestRef = (0, _react.useRef)({});\n    const pathnameRef = (0, _react.useRef)(pathname);\n    if (true) {\n        // this conditional is only for dead-code elimination which\n        // isn't a runtime conditional only build-time so ignore hooks rule\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            pathnameRef.current = pathname;\n            const appIsrManifest = appIsrManifestRef.current;\n            if (appIsrManifest) {\n                if (pathname && pathname in appIsrManifest) {\n                    try {\n                        dispatcher.onStaticIndicator(true);\n                    } catch (reason) {\n                        let message = '';\n                        if (reason instanceof DOMException) {\n                            var _reason_stack;\n                            // Most likely a SecurityError, because of an unavailable localStorage\n                            message = (_reason_stack = reason.stack) != null ? _reason_stack : reason.message;\n                        } else if (reason instanceof Error) {\n                            var _reason_stack1;\n                            message = 'Error: ' + reason.message + '\\n' + ((_reason_stack1 = reason.stack) != null ? _reason_stack1 : '');\n                        } else {\n                            message = 'Unexpected Exception: ' + reason;\n                        }\n                        console.warn('[HMR] ' + message);\n                    }\n                } else {\n                    dispatcher.onStaticIndicator(false);\n                }\n            }\n        }, [\n            pathname,\n            dispatcher\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        const websocket = webSocketRef.current;\n        if (!websocket) return;\n        const handler = (event)=>{\n            try {\n                const obj = JSON.parse(event.data);\n                (0, _handledevbuildindicatorhmrevents.handleDevBuildIndicatorHmrEvents)(obj);\n                processMessage(obj, sendMessage, processTurbopackMessage, router, dispatcher, appIsrManifestRef, pathnameRef);\n            } catch (err) {\n                var _err_stack;\n                console.warn('[HMR] Invalid message: ' + JSON.stringify(event.data) + '\\n' + ((_err_stack = err == null ? void 0 : err.stack) != null ? _err_stack : ''));\n            }\n        };\n        websocket.addEventListener('message', handler);\n        return ()=>websocket.removeEventListener('message', handler);\n    }, [\n        sendMessage,\n        router,\n        webSocketRef,\n        dispatcher,\n        processTurbopackMessage,\n        appIsrManifestRef\n    ]);\n    if (shouldRenderErrorOverlay) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_appdevoverlay.AppDevOverlay, {\n            state: state,\n            globalError: globalError,\n            children: children\n        });\n    }\n    return children;\n}\n_c = HotReload;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hot-reloader-client.js.map\nvar _c;\n$RefreshReg$(_c, \"HotReload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js ***!
  \****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"FontStyles\", ({\n    enumerable: true,\n    get: function() {\n        return FontStyles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _css = __webpack_require__(/*! ../utils/css */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n      /* latin-ext */\\n      @font-face {\\n        font-family: '__nextjs-Geist';\\n        font-style: normal;\\n        font-weight: 400 600;\\n        font-display: swap;\\n        src: url(/__nextjs_font/geist-latin-ext.woff2) format('woff2');\\n        unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7,\\n          U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F,\\n          U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F,\\n          U+A720-A7FF;\\n      }\\n      /* latin-ext */\\n      @font-face {\\n        font-family: '__nextjs-Geist Mono';\\n        font-style: normal;\\n        font-weight: 400 600;\\n        font-display: swap;\\n        src: url(/__nextjs_font/geist-mono-latin-ext.woff2) format('woff2');\\n        unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7,\\n          U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F,\\n          U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F,\\n          U+A720-A7FF;\\n      }\\n      /* latin */\\n      @font-face {\\n        font-family: '__nextjs-Geist';\\n        font-style: normal;\\n        font-weight: 400 600;\\n        font-display: swap;\\n        src: url(/__nextjs_font/geist-latin.woff2) format('woff2');\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n      /* latin */\\n      @font-face {\\n        font-family: '__nextjs-Geist Mono';\\n        font-style: normal;\\n        font-weight: 400 600;\\n        font-display: swap;\\n        src: url(/__nextjs_font/geist-mono-latin.woff2) format('woff2');\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n    \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst FontStyles = ()=>{\n    (0, _react.useInsertionEffect)(()=>{\n        const style = document.createElement('style');\n        style.textContent = (0, _css.css)(_templateObject());\n        document.head.appendChild(style);\n        return ()=>{\n            document.head.removeChild(style);\n        };\n    }, []);\n    return null;\n};\n_c = FontStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=font-styles.js.map\nvar _c;\n$RefreshReg$(_c, \"FontStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js ***!
  \****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"FontStyles\", ({\n    enumerable: true,\n    get: function() {\n        return FontStyles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _css = __webpack_require__(/*! ../utils/css */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n      /* latin-ext */\\n      @font-face {\\n        font-family: '__nextjs-Geist';\\n        font-style: normal;\\n        font-weight: 400 600;\\n        font-display: swap;\\n        src: url(/__nextjs_font/geist-latin-ext.woff2) format('woff2');\\n        unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7,\\n          U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F,\\n          U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F,\\n          U+A720-A7FF;\\n      }\\n      /* latin-ext */\\n      @font-face {\\n        font-family: '__nextjs-Geist Mono';\\n        font-style: normal;\\n        font-weight: 400 600;\\n        font-display: swap;\\n        src: url(/__nextjs_font/geist-mono-latin-ext.woff2) format('woff2');\\n        unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7,\\n          U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F,\\n          U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F,\\n          U+A720-A7FF;\\n      }\\n      /* latin */\\n      @font-face {\\n        font-family: '__nextjs-Geist';\\n        font-style: normal;\\n        font-weight: 400 600;\\n        font-display: swap;\\n        src: url(/__nextjs_font/geist-latin.woff2) format('woff2');\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n      /* latin */\\n      @font-face {\\n        font-family: '__nextjs-Geist Mono';\\n        font-style: normal;\\n        font-weight: 400 600;\\n        font-display: swap;\\n        src: url(/__nextjs_font/geist-mono-latin.woff2) format('woff2');\\n        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,\\n          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,\\n          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\\n      }\\n    \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst FontStyles = ()=>{\n    (0, _react.useInsertionEffect)(()=>{\n        const style = document.createElement('style');\n        style.textContent = (0, _css.css)(_templateObject());\n        document.head.appendChild(style);\n        return ()=>{\n            document.head.removeChild(style);\n        };\n    }, []);\n    return null;\n};\n_c = FontStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=font-styles.js.map\nvar _c;\n$RefreshReg$(_c, \"FontStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/bus.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/pages/bus.js ***!
  \*********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    emit: function() {\n        return emit;\n    },\n    off: function() {\n        return off;\n    },\n    on: function() {\n        return on;\n    }\n});\nlet handlers = new Set();\nlet queue = [];\nfunction drain() {\n    // Draining should never happen synchronously in case multiple handlers are\n    // registered.\n    setTimeout(function() {\n        while(Boolean(queue.length) && // Or, if all handlers removed themselves as a result of handling the\n        // event(s)\n        Boolean(handlers.size)){\n            const ev = queue.shift();\n            handlers.forEach((handler)=>handler(ev));\n        }\n    }, 1);\n}\nfunction emit(ev) {\n    queue.push(Object.freeze({\n        ...ev\n    }));\n    drain();\n}\nfunction on(fn) {\n    if (handlers.has(fn)) {\n        return false;\n    }\n    handlers.add(fn);\n    drain();\n    return true;\n}\nfunction off(fn) {\n    if (handlers.has(fn)) {\n        handlers.delete(fn);\n        return true;\n    }\n    return false;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=bus.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/bus.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/client.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/pages/client.js ***!
  \************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getErrorByType: function() {\n        return _geterrorbytype.getErrorByType;\n    },\n    getServerError: function() {\n        return _nodestackframes.getServerError;\n    },\n    onBeforeRefresh: function() {\n        return onBeforeRefresh;\n    },\n    onBuildError: function() {\n        return onBuildError;\n    },\n    onBuildOk: function() {\n        return onBuildOk;\n    },\n    onDevIndicator: function() {\n        return onDevIndicator;\n    },\n    onRefresh: function() {\n        return onRefresh;\n    },\n    onStaticIndicator: function() {\n        return onStaticIndicator;\n    },\n    onVersionInfo: function() {\n        return onVersionInfo;\n    },\n    register: function() {\n        return register;\n    },\n    unregister: function() {\n        return unregister;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _bus = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./bus */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/bus.js\"));\nconst _parsestack = __webpack_require__(/*! ../utils/parse-stack */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-stack.js\");\nconst _parsecomponentstack = __webpack_require__(/*! ../utils/parse-component-stack */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-component-stack.js\");\nconst _hydrationerrorinfo = __webpack_require__(/*! ../../errors/hydration-error-info */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/errors/hydration-error-info.js\");\nconst _shared = __webpack_require__(/*! ../shared */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _attachhydrationerrorstate = __webpack_require__(/*! ../../errors/attach-hydration-error-state */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/errors/attach-hydration-error-state.js\");\nconst _geterrorbytype = __webpack_require__(/*! ../utils/get-error-by-type */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-error-by-type.js\");\nconst _nodestackframes = __webpack_require__(/*! ../utils/node-stack-frames */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/node-stack-frames.js\");\nlet isRegistered = false;\nlet stackTraceLimit = undefined;\nfunction handleError(error) {\n    if (!error || !(error instanceof Error) || typeof error.stack !== 'string') {\n        // A non-error was thrown, we don't have anything to show. :-(\n        return;\n    }\n    (0, _attachhydrationerrorstate.attachHydrationErrorState)(error);\n    const componentStackTrace = error._componentStack || _hydrationerrorinfo.hydrationErrorState.componentStack;\n    const componentStackFrames = typeof componentStackTrace === 'string' ? (0, _parsecomponentstack.parseComponentStack)(componentStackTrace) : undefined;\n    // Skip ModuleBuildError and ModuleNotFoundError, as it will be sent through onBuildError callback.\n    // This is to avoid same error as different type showing up on client to cause flashing.\n    if (error.name !== 'ModuleBuildError' && error.name !== 'ModuleNotFoundError') {\n        _bus.emit({\n            type: _shared.ACTION_UNHANDLED_ERROR,\n            reason: error,\n            frames: (0, _parsestack.parseStack)(error.stack),\n            componentStackFrames\n        });\n    }\n}\nlet origConsoleError = console.error;\nfunction nextJsHandleConsoleError() {\n    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n        args[_key] = arguments[_key];\n    }\n    // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n    const error =  true ? args[1] : 0;\n    (0, _hydrationerrorinfo.storeHydrationErrorStateFromConsoleArgs)(...args);\n    handleError(error);\n    origConsoleError.apply(window.console, args);\n}\nfunction onUnhandledError(event) {\n    const error = event == null ? void 0 : event.error;\n    handleError(error);\n}\nfunction onUnhandledRejection(ev) {\n    const reason = ev == null ? void 0 : ev.reason;\n    if (!reason || !(reason instanceof Error) || typeof reason.stack !== 'string') {\n        // A non-error was thrown, we don't have anything to show. :-(\n        return;\n    }\n    const e = reason;\n    _bus.emit({\n        type: _shared.ACTION_UNHANDLED_REJECTION,\n        reason: reason,\n        frames: (0, _parsestack.parseStack)(e.stack)\n    });\n}\nfunction register() {\n    if (isRegistered) {\n        return;\n    }\n    isRegistered = true;\n    try {\n        const limit = Error.stackTraceLimit;\n        Error.stackTraceLimit = 50;\n        stackTraceLimit = limit;\n    } catch (e) {}\n    window.addEventListener('error', onUnhandledError);\n    window.addEventListener('unhandledrejection', onUnhandledRejection);\n    window.console.error = nextJsHandleConsoleError;\n}\nfunction unregister() {\n    if (!isRegistered) {\n        return;\n    }\n    isRegistered = false;\n    if (stackTraceLimit !== undefined) {\n        try {\n            Error.stackTraceLimit = stackTraceLimit;\n        } catch (e) {}\n        stackTraceLimit = undefined;\n    }\n    window.removeEventListener('error', onUnhandledError);\n    window.removeEventListener('unhandledrejection', onUnhandledRejection);\n    window.console.error = origConsoleError;\n}\nfunction onBuildOk() {\n    _bus.emit({\n        type: _shared.ACTION_BUILD_OK\n    });\n}\nfunction onBuildError(message) {\n    _bus.emit({\n        type: _shared.ACTION_BUILD_ERROR,\n        message\n    });\n}\nfunction onRefresh() {\n    _bus.emit({\n        type: _shared.ACTION_REFRESH\n    });\n}\nfunction onBeforeRefresh() {\n    _bus.emit({\n        type: _shared.ACTION_BEFORE_REFRESH\n    });\n}\nfunction onVersionInfo(versionInfo) {\n    _bus.emit({\n        type: _shared.ACTION_VERSION_INFO,\n        versionInfo\n    });\n}\nfunction onStaticIndicator(isStatic) {\n    _bus.emit({\n        type: _shared.ACTION_STATIC_INDICATOR,\n        staticIndicator: isStatic\n    });\n}\nfunction onDevIndicator(devIndicatorsState) {\n    _bus.emit({\n        type: _shared.ACTION_DEV_INDICATOR,\n        devIndicator: devIndicatorsState\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=client.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/client.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/hooks.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/pages/hooks.js ***!
  \***********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"usePagesDevOverlay\", ({\n    enumerable: true,\n    get: function() {\n        return usePagesDevOverlay;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _bus = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./bus */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/bus.js\"));\nconst _shared = __webpack_require__(/*! ../shared */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _router = __webpack_require__(/*! ../../../router */ \"(pages-dir-browser)/./node_modules/next/dist/client/router.js\");\nconst usePagesDevOverlay = ()=>{\n    _s();\n    const [state, dispatch] = (0, _shared.useErrorOverlayReducer)('pages');\n    _react.default.useEffect({\n        \"usePagesDevOverlay.useEffect\": ()=>{\n            _bus.on(dispatch);\n            const { handleStaticIndicator } = __webpack_require__(/*! ./hot-reloader-client */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/hot-reloader-client.js\");\n            _router.Router.events.on('routeChangeComplete', handleStaticIndicator);\n            return ({\n                \"usePagesDevOverlay.useEffect\": function() {\n                    _router.Router.events.off('routeChangeComplete', handleStaticIndicator);\n                    _bus.off(dispatch);\n                }\n            })[\"usePagesDevOverlay.useEffect\"];\n        }\n    }[\"usePagesDevOverlay.useEffect\"], [\n        dispatch\n    ]);\n    const onComponentError = _react.default.useCallback({\n        \"usePagesDevOverlay.useCallback[onComponentError]\": (_error, _componentStack)=>{\n        // TODO: special handling\n        }\n    }[\"usePagesDevOverlay.useCallback[onComponentError]\"], []);\n    return {\n        state,\n        onComponentError\n    };\n};\n_s(usePagesDevOverlay, \"yiU6D4sMPUxEaIlbYMKpxnTQY+U=\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hooks.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/hooks.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/hot-reloader-client.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/pages/hot-reloader-client.js ***!
  \*************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// TODO: Remove use of `any` type. Fix no-use-before-define violations.\n/* eslint-disable @typescript-eslint/no-use-before-define */ /**\n * MIT License\n *\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */ // This file is a modified version of the Create React App HMR dev client that\n// can be found here:\n// https://github.com/facebook/create-react-app/blob/v3.4.1/packages/react-dev-utils/webpackHotDevClient.js\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return connect;\n    },\n    handleStaticIndicator: function() {\n        return handleStaticIndicator;\n    },\n    performFullReload: function() {\n        return performFullReload;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _client = __webpack_require__(/*! ./client */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/client.js\");\nconst _stripansi = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/strip-ansi */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/strip-ansi/index.js\"));\nconst _websocket = __webpack_require__(/*! ./websocket */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js\");\nconst _formatwebpackmessages = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../utils/format-webpack-messages */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/format-webpack-messages.js\"));\nconst _hotreloadertypes = __webpack_require__(/*! ../../../../server/dev/hot-reloader-types */ \"(pages-dir-browser)/./node_modules/next/dist/server/dev/hot-reloader-types.js\");\nconst _extractmodulesfromturbopackmessage = __webpack_require__(/*! ../../../../server/dev/extract-modules-from-turbopack-message */ \"(pages-dir-browser)/./node_modules/next/dist/server/dev/extract-modules-from-turbopack-message.js\");\nconst _shared = __webpack_require__(/*! ../shared */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _runtimeerrorhandler = __webpack_require__(/*! ../../errors/runtime-error-handler */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/errors/runtime-error-handler.js\");\nwindow.__nextDevClientId = Math.round(Math.random() * 100 + Date.now());\nlet customHmrEventHandler;\nlet turbopackMessageListeners = [];\nlet MODE = 'webpack';\nfunction connect(mode) {\n    MODE = mode;\n    (0, _client.register)();\n    (0, _websocket.addMessageListener)((payload)=>{\n        if (!('action' in payload)) {\n            return;\n        }\n        try {\n            processMessage(payload);\n        } catch (err) {\n            var _err_stack;\n            console.warn('[HMR] Invalid message: ' + JSON.stringify(payload) + '\\n' + ((_err_stack = err == null ? void 0 : err.stack) != null ? _err_stack : ''));\n        }\n    });\n    return {\n        subscribeToHmrEvent (handler) {\n            customHmrEventHandler = handler;\n        },\n        onUnrecoverableError () {\n            _runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError = true;\n        },\n        addTurbopackMessageListener (cb) {\n            turbopackMessageListeners.push(cb);\n        },\n        sendTurbopackMessage (msg) {\n            (0, _websocket.sendMessage)(msg);\n        },\n        handleUpdateError (err) {\n            performFullReload(err);\n        }\n    };\n}\n// Remember some state related to hot module replacement.\nvar isFirstCompilation = true;\nvar mostRecentCompilationHash = null;\nvar hasCompileErrors = false;\nfunction clearOutdatedErrors() {\n    // Clean up outdated compile errors, if any.\n    if (typeof console !== 'undefined' && typeof console.clear === 'function') {\n        if (hasCompileErrors) {\n            console.clear();\n        }\n    }\n}\n// Successful compilation.\nfunction handleSuccess() {\n    clearOutdatedErrors();\n    if (MODE === 'webpack') {\n        const isHotUpdate = !isFirstCompilation || window.__NEXT_DATA__.page !== '/_error' && isUpdateAvailable();\n        isFirstCompilation = false;\n        hasCompileErrors = false;\n        // Attempt to apply hot updates or reload.\n        if (isHotUpdate) {\n            tryApplyUpdates(onBeforeFastRefresh, onFastRefresh);\n        }\n    } else {\n        reportHmrLatency([\n            ...turbopackUpdatedModules\n        ]);\n        (0, _client.onBuildOk)();\n    }\n}\n// Compilation with warnings (e.g. ESLint).\nfunction handleWarnings(warnings) {\n    clearOutdatedErrors();\n    const isHotUpdate = !isFirstCompilation;\n    isFirstCompilation = false;\n    hasCompileErrors = false;\n    function printWarnings() {\n        // Print warnings to the console.\n        const formatted = (0, _formatwebpackmessages.default)({\n            warnings: warnings,\n            errors: []\n        });\n        if (typeof console !== 'undefined' && typeof console.warn === 'function') {\n            var _formatted_warnings;\n            for(let i = 0; i < ((_formatted_warnings = formatted.warnings) == null ? void 0 : _formatted_warnings.length); i++){\n                if (i === 5) {\n                    console.warn('There were more warnings in other files.\\n' + 'You can find a complete log in the terminal.');\n                    break;\n                }\n                console.warn((0, _stripansi.default)(formatted.warnings[i]));\n            }\n        }\n    }\n    printWarnings();\n    // Attempt to apply hot updates or reload.\n    if (isHotUpdate) {\n        tryApplyUpdates(onBeforeFastRefresh, onFastRefresh);\n    }\n}\n// Compilation with errors (e.g. syntax error or missing modules).\nfunction handleErrors(errors) {\n    clearOutdatedErrors();\n    isFirstCompilation = false;\n    hasCompileErrors = true;\n    // \"Massage\" webpack messages.\n    var formatted = (0, _formatwebpackmessages.default)({\n        errors: errors,\n        warnings: []\n    });\n    // Only show the first error.\n    (0, _client.onBuildError)(formatted.errors[0]);\n    // Also log them to the console.\n    if (typeof console !== 'undefined' && typeof console.error === 'function') {\n        for(var i = 0; i < formatted.errors.length; i++){\n            console.error((0, _stripansi.default)(formatted.errors[i]));\n        }\n    }\n    // Do not attempt to reload now.\n    // We will reload on next success instead.\n    if (false) {}\n}\nlet startLatency = null;\nlet turbopackLastUpdateLatency = null;\nlet turbopackUpdatedModules = new Set();\nlet isrManifest = {};\nfunction onBeforeFastRefresh(updatedModules) {\n    if (updatedModules.length > 0) {\n        // Only trigger a pending state if we have updates to apply\n        // (cf. onFastRefresh)\n        (0, _client.onBeforeRefresh)();\n    }\n}\nfunction onFastRefresh(updatedModules) {\n    if (updatedModules === void 0) updatedModules = [];\n    (0, _client.onBuildOk)();\n    if (updatedModules.length === 0) {\n        return;\n    }\n    (0, _client.onRefresh)();\n    reportHmrLatency();\n}\nfunction reportHmrLatency(updatedModules) {\n    if (updatedModules === void 0) updatedModules = [];\n    if (!startLatency) return;\n    // turbopack has a debounce for the BUILT event which we don't want to\n    // incorrectly show in this number, use the last TURBOPACK_MESSAGE time\n    let endLatency = turbopackLastUpdateLatency != null ? turbopackLastUpdateLatency : Date.now();\n    const latency = endLatency - startLatency;\n    console.log(\"[Fast Refresh] done in \" + latency + \"ms\");\n    (0, _websocket.sendMessage)(JSON.stringify({\n        event: 'client-hmr-latency',\n        id: window.__nextDevClientId,\n        startTime: startLatency,\n        endTime: endLatency,\n        page: window.location.pathname,\n        updatedModules,\n        // Whether the page (tab) was hidden at the time the event occurred.\n        // This can impact the accuracy of the event's timing.\n        isPageHidden: document.visibilityState === 'hidden'\n    }));\n    if (self.__NEXT_HMR_LATENCY_CB) {\n        self.__NEXT_HMR_LATENCY_CB(latency);\n    }\n}\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash) {\n    // Update last known compilation hash.\n    mostRecentCompilationHash = hash;\n}\nfunction handleStaticIndicator() {\n    if (true) {\n        var _window_next_router_components__app;\n        const routeInfo = window.next.router.components[window.next.router.pathname];\n        const pageComponent = routeInfo == null ? void 0 : routeInfo.Component;\n        const appComponent = (_window_next_router_components__app = window.next.router.components['/_app']) == null ? void 0 : _window_next_router_components__app.Component;\n        const isDynamicPage = Boolean(pageComponent == null ? void 0 : pageComponent.getInitialProps) || Boolean(routeInfo.__N_SSP);\n        const hasAppGetInitialProps = Boolean(appComponent == null ? void 0 : appComponent.getInitialProps) && (appComponent == null ? void 0 : appComponent.getInitialProps) !== (appComponent == null ? void 0 : appComponent.origGetInitialProps);\n        const isPageStatic = window.location.pathname in isrManifest || !isDynamicPage && !hasAppGetInitialProps;\n        (0, _client.onStaticIndicator)(isPageStatic);\n    }\n}\n/** Handles messages from the sevrer for the Pages Router. */ function processMessage(obj) {\n    if (!('action' in obj)) {\n        return;\n    }\n    // Use turbopack message for analytics, (still need built for webpack)\n    switch(obj.action){\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST:\n            {\n                isrManifest = obj.data;\n                handleStaticIndicator();\n                break;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILDING:\n            {\n                startLatency = Date.now();\n                turbopackLastUpdateLatency = null;\n                turbopackUpdatedModules.clear();\n                console.log('[Fast Refresh] rebuilding');\n                break;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SYNC:\n            {\n                if (obj.hash) handleAvailableHash(obj.hash);\n                const { errors, warnings } = obj;\n                // Is undefined when it's a 'built' event\n                if ('versionInfo' in obj) (0, _client.onVersionInfo)(obj.versionInfo);\n                if ('devIndicator' in obj) (0, _client.onDevIndicator)(obj.devIndicator);\n                const hasErrors = Boolean(errors && errors.length);\n                if (hasErrors) {\n                    (0, _websocket.sendMessage)(JSON.stringify({\n                        event: 'client-error',\n                        errorCount: errors.length,\n                        clientId: window.__nextDevClientId\n                    }));\n                    return handleErrors(errors);\n                }\n                const hasWarnings = Boolean(warnings && warnings.length);\n                if (hasWarnings) {\n                    (0, _websocket.sendMessage)(JSON.stringify({\n                        event: 'client-warning',\n                        warningCount: warnings.length,\n                        clientId: window.__nextDevClientId\n                    }));\n                    return handleWarnings(warnings);\n                }\n                (0, _websocket.sendMessage)(JSON.stringify({\n                    event: 'client-success',\n                    clientId: window.__nextDevClientId\n                }));\n                return handleSuccess();\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES:\n            {\n                if (hasCompileErrors || _runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError) {\n                    window.location.reload();\n                }\n                return;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR:\n            {\n                const { errorJSON } = obj;\n                if (errorJSON) {\n                    const { message, stack } = JSON.parse(errorJSON);\n                    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                        value: \"E394\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                    error.stack = stack;\n                    handleErrors([\n                        error\n                    ]);\n                }\n                return;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED:\n            {\n                for (const listener of turbopackMessageListeners){\n                    listener({\n                        type: _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n                        data: obj.data\n                    });\n                }\n                break;\n            }\n        case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE:\n            {\n                const updatedModules = (0, _extractmodulesfromturbopackmessage.extractModulesFromTurbopackMessage)(obj.data);\n                onBeforeFastRefresh([\n                    ...updatedModules\n                ]);\n                for (const listener of turbopackMessageListeners){\n                    listener({\n                        type: _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n                        data: obj.data\n                    });\n                }\n                if (_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError) {\n                    console.warn(_shared.REACT_REFRESH_FULL_RELOAD_FROM_ERROR);\n                    performFullReload(null);\n                }\n                (0, _client.onRefresh)();\n                for (const module1 of updatedModules){\n                    turbopackUpdatedModules.add(module1);\n                }\n                turbopackLastUpdateLatency = Date.now();\n                break;\n            }\n        default:\n            {\n                if (customHmrEventHandler) {\n                    customHmrEventHandler(obj);\n                    break;\n                }\n                break;\n            }\n    }\n}\n// Is there a newer version of this code available?\nfunction isUpdateAvailable() {\n    /* globals __webpack_hash__ */ // __webpack_hash__ is the hash of the current compilation.\n    // It's a global variable injected by Webpack.\n    return mostRecentCompilationHash !== __webpack_require__.h();\n}\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n    // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n    return module.hot.status() === 'idle';\n}\nfunction afterApplyUpdates(fn) {\n    if (canApplyUpdates()) {\n        fn();\n    } else {\n        function handler(status) {\n            if (status === 'idle') {\n                // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n                module.hot.removeStatusHandler(handler);\n                fn();\n            }\n        }\n        // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n        module.hot.addStatusHandler(handler);\n    }\n}\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdates(onBeforeHotUpdate, onHotUpdateSuccess) {\n    // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n    if (false) {}\n    if (!isUpdateAvailable() || !canApplyUpdates()) {\n        (0, _client.onBuildOk)();\n        return;\n    }\n    function handleApplyUpdates(err, updatedModules) {\n        if (err || _runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError || !updatedModules) {\n            if (err) {\n                console.warn('[Fast Refresh] performing full reload\\n\\n' + \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" + 'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' + 'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' + 'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' + 'Fast Refresh requires at least one parent function component in your React tree.');\n            } else if (_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError) {\n                console.warn('[Fast Refresh] performing full reload because your application had an unrecoverable error');\n            }\n            performFullReload(err);\n            return;\n        }\n        if (typeof onHotUpdateSuccess === 'function') {\n            // Maybe we want to do something.\n            onHotUpdateSuccess(updatedModules);\n        }\n        if (isUpdateAvailable()) {\n            // While we were updating, there was a new update! Do it again.\n            // However, this time, don't trigger a pending refresh state.\n            tryApplyUpdates(updatedModules.length > 0 ? undefined : onBeforeHotUpdate, updatedModules.length > 0 ? _client.onBuildOk : onHotUpdateSuccess);\n        } else {\n            (0, _client.onBuildOk)();\n            if (false) {}\n        }\n    }\n    // https://webpack.js.org/api/hot-module-replacement/#check\n    // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n    module.hot.check(/* autoApply */ false).then((updatedModules)=>{\n        if (!updatedModules) {\n            return null;\n        }\n        if (typeof onBeforeHotUpdate === 'function') {\n            onBeforeHotUpdate(updatedModules);\n        }\n        // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n        return module.hot.apply();\n    }).then((updatedModules)=>{\n        handleApplyUpdates(null, updatedModules);\n    }, (err)=>{\n        handleApplyUpdates(err, null);\n    });\n}\nfunction performFullReload(err) {\n    const stackTrace = err && (err.stack && err.stack.split('\\n').slice(0, 5).join('\\n') || err.message || err + '');\n    (0, _websocket.sendMessage)(JSON.stringify({\n        event: 'client-full-reload',\n        stackTrace,\n        hadRuntimeError: !!_runtimeerrorhandler.RuntimeErrorHandler.hadRuntimeError,\n        dependencyChain: err ? err.dependencyChain : undefined\n    }));\n    window.location.reload();\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hot-reloader-client.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/hot-reloader-client.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/pages-dev-overlay-error-boundary.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/pages/pages-dev-overlay-error-boundary.js ***!
  \**************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PagesDevOverlayErrorBoundary\", ({\n    enumerable: true,\n    get: function() {\n        return PagesDevOverlayErrorBoundary;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nclass PagesDevOverlayErrorBoundary extends _react.default.PureComponent {\n    static getDerivedStateFromError(error) {\n        return {\n            error\n        };\n    }\n    componentDidCatch(error, // accidentally excluded in some versions.\n    errorInfo) {\n        this.props.onError(error, (errorInfo == null ? void 0 : errorInfo.componentStack) || null);\n        this.setState({\n            error\n        });\n    }\n    // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n    render() {\n        // The component has to be unmounted or else it would continue to error\n        return this.state.error ? null : this.props.children;\n    }\n    constructor(...args){\n        super(...args), this.state = {\n            error: null\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=pages-dev-overlay-error-boundary.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/pages-dev-overlay-error-boundary.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/pages-dev-overlay.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/pages/pages-dev-overlay.js ***!
  \***********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PagesDevOverlay\", ({\n    enumerable: true,\n    get: function() {\n        return PagesDevOverlay;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _pagesdevoverlayerrorboundary = __webpack_require__(/*! ./pages-dev-overlay-error-boundary */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/pages-dev-overlay-error-boundary.js\");\nconst _hooks = __webpack_require__(/*! ./hooks */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/hooks.js\");\nconst _fontstyles = __webpack_require__(/*! ../font/font-styles */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js\");\nconst _devoverlay = __webpack_require__(/*! ../ui/dev-overlay */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/dev-overlay.js\");\nfunction PagesDevOverlay(param) {\n    let { children } = param;\n    const { state, onComponentError } = (0, _hooks.usePagesDevOverlay)();\n    const [isErrorOverlayOpen, setIsErrorOverlayOpen] = (0, _react.useState)(true);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_pagesdevoverlayerrorboundary.PagesDevOverlayErrorBoundary, {\n                onError: onComponentError,\n                children: children != null ? children : null\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_fontstyles.FontStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_devoverlay.DevOverlay, {\n                state: state,\n                isErrorOverlayOpen: isErrorOverlayOpen,\n                setIsErrorOverlayOpen: setIsErrorOverlayOpen\n            })\n        ]\n    });\n}\n_c = PagesDevOverlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=pages-dev-overlay.js.map\nvar _c;\n$RefreshReg$(_c, \"PagesDevOverlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/pages-dev-overlay.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js ***!
  \***************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    addMessageListener: function() {\n        return addMessageListener;\n    },\n    connectHMR: function() {\n        return connectHMR;\n    },\n    sendMessage: function() {\n        return sendMessage;\n    }\n});\nconst _hotreloadertypes = __webpack_require__(/*! ../../../../server/dev/hot-reloader-types */ \"(pages-dir-browser)/./node_modules/next/dist/server/dev/hot-reloader-types.js\");\nconst _getsocketurl = __webpack_require__(/*! ../utils/get-socket-url */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-socket-url.js\");\nlet source;\nconst eventCallbacks = [];\nfunction addMessageListener(callback) {\n    eventCallbacks.push(callback);\n}\nfunction sendMessage(data) {\n    if (!source || source.readyState !== source.OPEN) return;\n    return source.send(data);\n}\nlet reconnections = 0;\nlet reloading = false;\nlet serverSessionId = null;\nfunction connectHMR(options) {\n    function init() {\n        if (source) source.close();\n        function handleOnline() {\n            reconnections = 0;\n            window.console.log('[HMR] connected');\n        }\n        function handleMessage(event) {\n            // While the page is reloading, don't respond to any more messages.\n            // On reconnect, the server may send an empty list of changes if it was restarted.\n            if (reloading) {\n                return;\n            }\n            // Coerce into HMR_ACTION_TYPES as that is the format.\n            const msg = JSON.parse(event.data);\n            if ('action' in msg && msg.action === _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED) {\n                if (serverSessionId !== null && serverSessionId !== msg.data.sessionId) {\n                    // Either the server's session id has changed and it's a new server, or\n                    // it's been too long since we disconnected and we should reload the page.\n                    // There could be 1) unhandled server errors and/or 2) stale content.\n                    // Perform a hard reload of the page.\n                    window.location.reload();\n                    reloading = true;\n                    return;\n                }\n                serverSessionId = msg.data.sessionId;\n            }\n            for (const eventCallback of eventCallbacks){\n                eventCallback(msg);\n            }\n        }\n        let timer;\n        function handleDisconnect() {\n            source.onerror = null;\n            source.onclose = null;\n            source.close();\n            reconnections++;\n            // After 25 reconnects we'll want to reload the page as it indicates the dev server is no longer running.\n            if (reconnections > 25) {\n                reloading = true;\n                window.location.reload();\n                return;\n            }\n            clearTimeout(timer);\n            // Try again after 5 seconds\n            timer = setTimeout(init, reconnections > 5 ? 5000 : 1000);\n        }\n        const url = (0, _getsocketurl.getSocketUrl)(options.assetPrefix);\n        source = new window.WebSocket(\"\" + url + options.path);\n        source.onopen = handleOnline;\n        source.onerror = handleDisconnect;\n        source.onclose = handleDisconnect;\n        source.onmessage = handleMessage;\n    }\n    init();\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=websocket.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvcGFnZXMvd2Vic29ja2V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQVlnQkEsa0JBQWtCO2VBQWxCQTs7SUFhQUMsVUFBVTtlQUFWQTs7SUFUQUMsV0FBVztlQUFYQTs7OzhDQWJUOzBDQUNzQjtBQUU3QixJQUFJQztBQUlKLE1BQU1DLGlCQUF3QyxFQUFFO0FBRXpDLFNBQVNKLG1CQUFtQkssUUFBd0I7SUFDekRELGVBQWVFLElBQUksQ0FBQ0Q7QUFDdEI7QUFFTyxTQUFTSCxZQUFZSyxJQUFZO0lBQ3RDLElBQUksQ0FBQ0osVUFBVUEsT0FBT0ssVUFBVSxLQUFLTCxPQUFPTSxJQUFJLEVBQUU7SUFDbEQsT0FBT04sT0FBT08sSUFBSSxDQUFDSDtBQUNyQjtBQUVBLElBQUlJLGdCQUFnQjtBQUNwQixJQUFJQyxZQUFZO0FBQ2hCLElBQUlDLGtCQUFpQztBQUU5QixTQUFTWixXQUFXYSxPQUE4QztJQUN2RSxTQUFTQztRQUNQLElBQUlaLFFBQVFBLE9BQU9hLEtBQUs7UUFFeEIsU0FBU0M7WUFDUE4sZ0JBQWdCO1lBQ2hCTyxPQUFPQyxPQUFPLENBQUNDLEdBQUcsQ0FBQztRQUNyQjtRQUVBLFNBQVNDLGNBQWNDLEtBQTJCO1lBQ2hELG1FQUFtRTtZQUNuRSxrRkFBa0Y7WUFDbEYsSUFBSVYsV0FBVztnQkFDYjtZQUNGO1lBRUEsc0RBQXNEO1lBQ3RELE1BQU1XLE1BQXdCQyxLQUFLQyxLQUFLLENBQUNILE1BQU1mLElBQUk7WUFFbkQsSUFDRSxZQUFZZ0IsT0FDWkEsSUFBSUcsTUFBTSxLQUFLQyxrQkFBQUEsMkJBQTJCLENBQUNDLG1CQUFtQixFQUM5RDtnQkFDQSxJQUNFZixvQkFBb0IsUUFDcEJBLG9CQUFvQlUsSUFBSWhCLElBQUksQ0FBQ3NCLFNBQVMsRUFDdEM7b0JBQ0EsdUVBQXVFO29CQUN2RSwwRUFBMEU7b0JBQzFFLHFFQUFxRTtvQkFDckUscUNBQXFDO29CQUNyQ1gsT0FBT1ksUUFBUSxDQUFDQyxNQUFNO29CQUV0Qm5CLFlBQVk7b0JBQ1o7Z0JBQ0Y7Z0JBRUFDLGtCQUFrQlUsSUFBSWhCLElBQUksQ0FBQ3NCLFNBQVM7WUFDdEM7WUFFQSxLQUFLLE1BQU1HLGlCQUFpQjVCLGVBQWdCO2dCQUMxQzRCLGNBQWNUO1lBQ2hCO1FBQ0Y7UUFFQSxJQUFJVTtRQUNKLFNBQVNDO1lBQ1AvQixPQUFPZ0MsT0FBTyxHQUFHO1lBQ2pCaEMsT0FBT2lDLE9BQU8sR0FBRztZQUNqQmpDLE9BQU9hLEtBQUs7WUFDWkw7WUFDQSx5R0FBeUc7WUFDekcsSUFBSUEsZ0JBQWdCLElBQUk7Z0JBQ3RCQyxZQUFZO2dCQUNaTSxPQUFPWSxRQUFRLENBQUNDLE1BQU07Z0JBQ3RCO1lBQ0Y7WUFFQU0sYUFBYUo7WUFDYiw0QkFBNEI7WUFDNUJBLFFBQVFLLFdBQVd2QixNQUFNSixnQkFBZ0IsSUFBSSxPQUFPO1FBQ3REO1FBRUEsTUFBTTRCLE1BQU1DLENBQUFBLEdBQUFBLGNBQUFBLFlBQUFBLEVBQWExQixRQUFRMkIsV0FBVztRQUU1Q3RDLFNBQVMsSUFBSWUsT0FBT3dCLFNBQVMsQ0FBRSxLQUFFSCxNQUFNekIsUUFBUTZCLElBQUk7UUFDbkR4QyxPQUFPeUMsTUFBTSxHQUFHM0I7UUFDaEJkLE9BQU9nQyxPQUFPLEdBQUdEO1FBQ2pCL0IsT0FBT2lDLE9BQU8sR0FBR0Y7UUFDakIvQixPQUFPMEMsU0FBUyxHQUFHeEI7SUFDckI7SUFFQU47QUFDRiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxccGFnZXNcXHdlYnNvY2tldC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBITVJfQUNUSU9OU19TRU5UX1RPX0JST1dTRVIsXG4gIHR5cGUgSE1SX0FDVElPTl9UWVBFUyxcbn0gZnJvbSAnLi4vLi4vLi4vLi4vc2VydmVyL2Rldi9ob3QtcmVsb2FkZXItdHlwZXMnXG5pbXBvcnQgeyBnZXRTb2NrZXRVcmwgfSBmcm9tICcuLi91dGlscy9nZXQtc29ja2V0LXVybCdcblxubGV0IHNvdXJjZTogV2ViU29ja2V0XG5cbnR5cGUgQWN0aW9uQ2FsbGJhY2sgPSAoYWN0aW9uOiBITVJfQUNUSU9OX1RZUEVTKSA9PiB2b2lkXG5cbmNvbnN0IGV2ZW50Q2FsbGJhY2tzOiBBcnJheTxBY3Rpb25DYWxsYmFjaz4gPSBbXVxuXG5leHBvcnQgZnVuY3Rpb24gYWRkTWVzc2FnZUxpc3RlbmVyKGNhbGxiYWNrOiBBY3Rpb25DYWxsYmFjaykge1xuICBldmVudENhbGxiYWNrcy5wdXNoKGNhbGxiYWNrKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gc2VuZE1lc3NhZ2UoZGF0YTogc3RyaW5nKSB7XG4gIGlmICghc291cmNlIHx8IHNvdXJjZS5yZWFkeVN0YXRlICE9PSBzb3VyY2UuT1BFTikgcmV0dXJuXG4gIHJldHVybiBzb3VyY2Uuc2VuZChkYXRhKVxufVxuXG5sZXQgcmVjb25uZWN0aW9ucyA9IDBcbmxldCByZWxvYWRpbmcgPSBmYWxzZVxubGV0IHNlcnZlclNlc3Npb25JZDogbnVtYmVyIHwgbnVsbCA9IG51bGxcblxuZXhwb3J0IGZ1bmN0aW9uIGNvbm5lY3RITVIob3B0aW9uczogeyBwYXRoOiBzdHJpbmc7IGFzc2V0UHJlZml4OiBzdHJpbmcgfSkge1xuICBmdW5jdGlvbiBpbml0KCkge1xuICAgIGlmIChzb3VyY2UpIHNvdXJjZS5jbG9zZSgpXG5cbiAgICBmdW5jdGlvbiBoYW5kbGVPbmxpbmUoKSB7XG4gICAgICByZWNvbm5lY3Rpb25zID0gMFxuICAgICAgd2luZG93LmNvbnNvbGUubG9nKCdbSE1SXSBjb25uZWN0ZWQnKVxuICAgIH1cblxuICAgIGZ1bmN0aW9uIGhhbmRsZU1lc3NhZ2UoZXZlbnQ6IE1lc3NhZ2VFdmVudDxzdHJpbmc+KSB7XG4gICAgICAvLyBXaGlsZSB0aGUgcGFnZSBpcyByZWxvYWRpbmcsIGRvbid0IHJlc3BvbmQgdG8gYW55IG1vcmUgbWVzc2FnZXMuXG4gICAgICAvLyBPbiByZWNvbm5lY3QsIHRoZSBzZXJ2ZXIgbWF5IHNlbmQgYW4gZW1wdHkgbGlzdCBvZiBjaGFuZ2VzIGlmIGl0IHdhcyByZXN0YXJ0ZWQuXG4gICAgICBpZiAocmVsb2FkaW5nKSB7XG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICAvLyBDb2VyY2UgaW50byBITVJfQUNUSU9OX1RZUEVTIGFzIHRoYXQgaXMgdGhlIGZvcm1hdC5cbiAgICAgIGNvbnN0IG1zZzogSE1SX0FDVElPTl9UWVBFUyA9IEpTT04ucGFyc2UoZXZlbnQuZGF0YSlcblxuICAgICAgaWYgKFxuICAgICAgICAnYWN0aW9uJyBpbiBtc2cgJiZcbiAgICAgICAgbXNnLmFjdGlvbiA9PT0gSE1SX0FDVElPTlNfU0VOVF9UT19CUk9XU0VSLlRVUkJPUEFDS19DT05ORUNURURcbiAgICAgICkge1xuICAgICAgICBpZiAoXG4gICAgICAgICAgc2VydmVyU2Vzc2lvbklkICE9PSBudWxsICYmXG4gICAgICAgICAgc2VydmVyU2Vzc2lvbklkICE9PSBtc2cuZGF0YS5zZXNzaW9uSWRcbiAgICAgICAgKSB7XG4gICAgICAgICAgLy8gRWl0aGVyIHRoZSBzZXJ2ZXIncyBzZXNzaW9uIGlkIGhhcyBjaGFuZ2VkIGFuZCBpdCdzIGEgbmV3IHNlcnZlciwgb3JcbiAgICAgICAgICAvLyBpdCdzIGJlZW4gdG9vIGxvbmcgc2luY2Ugd2UgZGlzY29ubmVjdGVkIGFuZCB3ZSBzaG91bGQgcmVsb2FkIHRoZSBwYWdlLlxuICAgICAgICAgIC8vIFRoZXJlIGNvdWxkIGJlIDEpIHVuaGFuZGxlZCBzZXJ2ZXIgZXJyb3JzIGFuZC9vciAyKSBzdGFsZSBjb250ZW50LlxuICAgICAgICAgIC8vIFBlcmZvcm0gYSBoYXJkIHJlbG9hZCBvZiB0aGUgcGFnZS5cbiAgICAgICAgICB3aW5kb3cubG9jYXRpb24ucmVsb2FkKClcblxuICAgICAgICAgIHJlbG9hZGluZyA9IHRydWVcbiAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuXG4gICAgICAgIHNlcnZlclNlc3Npb25JZCA9IG1zZy5kYXRhLnNlc3Npb25JZFxuICAgICAgfVxuXG4gICAgICBmb3IgKGNvbnN0IGV2ZW50Q2FsbGJhY2sgb2YgZXZlbnRDYWxsYmFja3MpIHtcbiAgICAgICAgZXZlbnRDYWxsYmFjayhtc2cpXG4gICAgICB9XG4gICAgfVxuXG4gICAgbGV0IHRpbWVyOiBSZXR1cm5UeXBlPHR5cGVvZiBzZXRUaW1lb3V0PlxuICAgIGZ1bmN0aW9uIGhhbmRsZURpc2Nvbm5lY3QoKSB7XG4gICAgICBzb3VyY2Uub25lcnJvciA9IG51bGxcbiAgICAgIHNvdXJjZS5vbmNsb3NlID0gbnVsbFxuICAgICAgc291cmNlLmNsb3NlKClcbiAgICAgIHJlY29ubmVjdGlvbnMrK1xuICAgICAgLy8gQWZ0ZXIgMjUgcmVjb25uZWN0cyB3ZSdsbCB3YW50IHRvIHJlbG9hZCB0aGUgcGFnZSBhcyBpdCBpbmRpY2F0ZXMgdGhlIGRldiBzZXJ2ZXIgaXMgbm8gbG9uZ2VyIHJ1bm5pbmcuXG4gICAgICBpZiAocmVjb25uZWN0aW9ucyA+IDI1KSB7XG4gICAgICAgIHJlbG9hZGluZyA9IHRydWVcbiAgICAgICAgd2luZG93LmxvY2F0aW9uLnJlbG9hZCgpXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICBjbGVhclRpbWVvdXQodGltZXIpXG4gICAgICAvLyBUcnkgYWdhaW4gYWZ0ZXIgNSBzZWNvbmRzXG4gICAgICB0aW1lciA9IHNldFRpbWVvdXQoaW5pdCwgcmVjb25uZWN0aW9ucyA+IDUgPyA1MDAwIDogMTAwMClcbiAgICB9XG5cbiAgICBjb25zdCB1cmwgPSBnZXRTb2NrZXRVcmwob3B0aW9ucy5hc3NldFByZWZpeClcblxuICAgIHNvdXJjZSA9IG5ldyB3aW5kb3cuV2ViU29ja2V0KGAke3VybH0ke29wdGlvbnMucGF0aH1gKVxuICAgIHNvdXJjZS5vbm9wZW4gPSBoYW5kbGVPbmxpbmVcbiAgICBzb3VyY2Uub25lcnJvciA9IGhhbmRsZURpc2Nvbm5lY3RcbiAgICBzb3VyY2Uub25jbG9zZSA9IGhhbmRsZURpc2Nvbm5lY3RcbiAgICBzb3VyY2Uub25tZXNzYWdlID0gaGFuZGxlTWVzc2FnZVxuICB9XG5cbiAgaW5pdCgpXG59XG4iXSwibmFtZXMiOlsiYWRkTWVzc2FnZUxpc3RlbmVyIiwiY29ubmVjdEhNUiIsInNlbmRNZXNzYWdlIiwic291cmNlIiwiZXZlbnRDYWxsYmFja3MiLCJjYWxsYmFjayIsInB1c2giLCJkYXRhIiwicmVhZHlTdGF0ZSIsIk9QRU4iLCJzZW5kIiwicmVjb25uZWN0aW9ucyIsInJlbG9hZGluZyIsInNlcnZlclNlc3Npb25JZCIsIm9wdGlvbnMiLCJpbml0IiwiY2xvc2UiLCJoYW5kbGVPbmxpbmUiLCJ3aW5kb3ciLCJjb25zb2xlIiwibG9nIiwiaGFuZGxlTWVzc2FnZSIsImV2ZW50IiwibXNnIiwiSlNPTiIsInBhcnNlIiwiYWN0aW9uIiwiSE1SX0FDVElPTlNfU0VOVF9UT19CUk9XU0VSIiwiVFVSQk9QQUNLX0NPTk5FQ1RFRCIsInNlc3Npb25JZCIsImxvY2F0aW9uIiwicmVsb2FkIiwiZXZlbnRDYWxsYmFjayIsInRpbWVyIiwiaGFuZGxlRGlzY29ubmVjdCIsIm9uZXJyb3IiLCJvbmNsb3NlIiwiY2xlYXJUaW1lb3V0Iiwic2V0VGltZW91dCIsInVybCIsImdldFNvY2tldFVybCIsImFzc2V0UHJlZml4IiwiV2ViU29ja2V0IiwicGF0aCIsIm9ub3BlbiIsIm9ubWVzc2FnZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js\n"));

/***/ })

}]);