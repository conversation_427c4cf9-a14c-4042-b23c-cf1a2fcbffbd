"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-c82b02ac"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.js ***!
  \************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INDICATOR_STYLES: function() {\n        return DEV_TOOLS_INDICATOR_STYLES;\n    },\n    DevToolsIndicator: function() {\n        return DevToolsIndicator;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _shared = __webpack_require__(/*! ../../../../shared */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _toast = __webpack_require__(/*! ../../toast */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/index.js\");\nconst _nextlogo = __webpack_require__(/*! ./next-logo */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.js\");\nconst _initialize = __webpack_require__(/*! ../../../../../../dev/dev-build-indicator/internal/initialize */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js\");\nconst _devrenderindicator = __webpack_require__(/*! ../../../../utils/dev-indicator/dev-render-indicator */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/dev-render-indicator.js\");\nconst _usedelayedrender = __webpack_require__(/*! ../../../hooks/use-delayed-render */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-delayed-render.js\");\nconst _turbopackinfo = __webpack_require__(/*! ./dev-tools-info/turbopack-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js\");\nconst _routeinfo = __webpack_require__(/*! ./dev-tools-info/route-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js\");\nconst _gearicon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../icons/gear-icon */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/gear-icon.js\"));\nconst _userpreferences = __webpack_require__(/*! ./dev-tools-info/user-preferences */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js\");\n// TODO: add E2E tests to cover different scenarios\nconst INDICATOR_POSITION = \"bottom-left\" || 0;\nfunction DevToolsIndicator(param) {\n    let { state, errorCount, isBuildError, setIsErrorOverlayOpen } = param;\n    const [isDevToolsIndicatorVisible, setIsDevToolsIndicatorVisible] = (0, _react.useState)(true);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(DevToolsPopover, {\n        routerType: state.routerType,\n        semver: state.versionInfo.installed,\n        issueCount: errorCount,\n        isStaticRoute: state.staticIndicator,\n        hide: ()=>{\n            setIsDevToolsIndicatorVisible(false);\n            fetch('/__nextjs_disable_dev_indicator', {\n                method: 'POST'\n            });\n        },\n        setIsErrorOverlayOpen: setIsErrorOverlayOpen,\n        isTurbopack: !!false,\n        disabled: state.disableDevIndicator || !isDevToolsIndicatorVisible,\n        isBuildError: isBuildError\n    });\n}\n_c = DevToolsIndicator;\nconst Context = /*#__PURE__*/ (0, _react.createContext)({});\nfunction getInitialPosition() {\n    if (typeof localStorage !== 'undefined' && localStorage.getItem(_shared.STORAGE_KEY_POSITION)) {\n        return localStorage.getItem(_shared.STORAGE_KEY_POSITION);\n    }\n    return INDICATOR_POSITION;\n}\nconst OVERLAYS = {\n    Root: 'root',\n    Turbo: 'turbo',\n    Route: 'route',\n    Preferences: 'preferences'\n};\nfunction DevToolsPopover(param) {\n    let { routerType, disabled, issueCount, isStaticRoute, isTurbopack, isBuildError, hide, setIsErrorOverlayOpen } = param;\n    const menuRef = (0, _react.useRef)(null);\n    const triggerRef = (0, _react.useRef)(null);\n    const [open, setOpen] = (0, _react.useState)(null);\n    const [position, setPosition] = (0, _react.useState)(getInitialPosition());\n    const [selectedIndex, setSelectedIndex] = (0, _react.useState)(-1);\n    const isMenuOpen = open === OVERLAYS.Root;\n    const isTurbopackInfoOpen = open === OVERLAYS.Turbo;\n    const isRouteInfoOpen = open === OVERLAYS.Route;\n    const isPreferencesOpen = open === OVERLAYS.Preferences;\n    const { mounted: menuMounted, rendered: menuRendered } = (0, _usedelayedrender.useDelayedRender)(isMenuOpen, {\n        // Intentionally no fade in, makes the UI feel more immediate\n        enterDelay: 0,\n        // Graceful fade out to confirm that the UI did not break\n        exitDelay: _utils.MENU_DURATION_MS\n    });\n    // Features to make the menu accessible\n    (0, _utils.useFocusTrap)(menuRef, triggerRef, isMenuOpen);\n    (0, _utils.useClickOutside)(menuRef, triggerRef, isMenuOpen, closeMenu);\n    (0, _react.useEffect)(()=>{\n        if (open === null) {\n            // Avoid flashing selected state\n            const id = setTimeout(()=>{\n                setSelectedIndex(-1);\n            }, _utils.MENU_DURATION_MS);\n            return ()=>clearTimeout(id);\n        }\n    }, [\n        open\n    ]);\n    function select(index) {\n        var _menuRef_current;\n        if (index === 'first') {\n            setTimeout(()=>{\n                var _menuRef_current;\n                const all = (_menuRef_current = menuRef.current) == null ? void 0 : _menuRef_current.querySelectorAll('[role=\"menuitem\"]');\n                if (all) {\n                    const firstIndex = all[0].getAttribute('data-index');\n                    select(Number(firstIndex));\n                }\n            });\n            return;\n        }\n        if (index === 'last') {\n            setTimeout(()=>{\n                var _menuRef_current;\n                const all = (_menuRef_current = menuRef.current) == null ? void 0 : _menuRef_current.querySelectorAll('[role=\"menuitem\"]');\n                if (all) {\n                    const lastIndex = all.length - 1;\n                    select(lastIndex);\n                }\n            });\n            return;\n        }\n        const el = (_menuRef_current = menuRef.current) == null ? void 0 : _menuRef_current.querySelector('[data-index=\"' + index + '\"]');\n        if (el) {\n            setSelectedIndex(index);\n            el == null ? void 0 : el.focus();\n        }\n    }\n    function onMenuKeydown(e) {\n        e.preventDefault();\n        switch(e.key){\n            case 'ArrowDown':\n                const next = selectedIndex + 1;\n                select(next);\n                break;\n            case 'ArrowUp':\n                const prev = selectedIndex - 1;\n                select(prev);\n                break;\n            case 'Home':\n                select('first');\n                break;\n            case 'End':\n                select('last');\n                break;\n            default:\n                break;\n        }\n    }\n    function openErrorOverlay() {\n        setOpen(null);\n        if (issueCount > 0) {\n            setIsErrorOverlayOpen(true);\n        }\n    }\n    function toggleErrorOverlay() {\n        setIsErrorOverlayOpen((prev)=>!prev);\n    }\n    function openRootMenu() {\n        setOpen((prevOpen)=>{\n            if (prevOpen === null) select('first');\n            return OVERLAYS.Root;\n        });\n    }\n    function onTriggerClick() {\n        if (open === OVERLAYS.Root) {\n            setOpen(null);\n        } else {\n            openRootMenu();\n            setTimeout(()=>{\n                select('first');\n            });\n        }\n    }\n    function closeMenu() {\n        // Only close when we were on `Root`,\n        // otherwise it will close other overlays\n        setOpen((prevOpen)=>{\n            if (prevOpen === OVERLAYS.Root) {\n                return null;\n            }\n            return prevOpen;\n        });\n    }\n    function handleHideDevtools() {\n        setOpen(null);\n        hide();\n    }\n    const [vertical, horizontal] = position.split('-', 2);\n    const popover = {\n        [vertical]: 'calc(100% + 8px)',\n        [horizontal]: 0\n    };\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_toast.Toast, {\n        \"data-nextjs-toast\": true,\n        style: {\n            '--animate-out-duration-ms': \"\" + _utils.MENU_DURATION_MS + \"ms\",\n            '--animate-out-timing-function': _utils.MENU_CURVE,\n            boxShadow: 'none',\n            zIndex: 2147483647,\n            // Reset the toast component's default positions.\n            bottom: 'initial',\n            left: 'initial',\n            [vertical]: '20px',\n            [horizontal]: '20px'\n        },\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_nextlogo.NextLogo, {\n                ref: triggerRef,\n                \"aria-haspopup\": \"menu\",\n                \"aria-expanded\": isMenuOpen,\n                \"aria-controls\": \"nextjs-dev-tools-menu\",\n                \"aria-label\": \"\" + (isMenuOpen ? 'Close' : 'Open') + \" Next.js Dev Tools\",\n                \"data-nextjs-dev-tools-button\": true,\n                disabled: disabled,\n                issueCount: issueCount,\n                onTriggerClick: onTriggerClick,\n                toggleErrorOverlay: toggleErrorOverlay,\n                isDevBuilding: (0, _initialize.useIsDevBuilding)(),\n                isDevRendering: (0, _devrenderindicator.useIsDevRendering)(),\n                isBuildError: isBuildError\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_routeinfo.RouteInfo, {\n                isOpen: isRouteInfoOpen,\n                close: openRootMenu,\n                triggerRef: triggerRef,\n                style: popover,\n                routerType: routerType,\n                routeType: isStaticRoute ? 'Static' : 'Dynamic'\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_turbopackinfo.TurbopackInfo, {\n                isOpen: isTurbopackInfoOpen,\n                close: openRootMenu,\n                triggerRef: triggerRef,\n                style: popover\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_userpreferences.UserPreferences, {\n                isOpen: isPreferencesOpen,\n                close: openRootMenu,\n                triggerRef: triggerRef,\n                style: popover,\n                hide: handleHideDevtools,\n                setPosition: setPosition,\n                position: position\n            }),\n            menuMounted && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                ref: menuRef,\n                id: \"nextjs-dev-tools-menu\",\n                role: \"menu\",\n                dir: \"ltr\",\n                \"aria-orientation\": \"vertical\",\n                \"aria-label\": \"Next.js Dev Tools Items\",\n                tabIndex: -1,\n                className: \"dev-tools-indicator-menu\",\n                onKeyDown: onMenuKeydown,\n                \"data-rendered\": menuRendered,\n                style: popover,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(Context.Provider, {\n                    value: {\n                        closeMenu,\n                        selectedIndex,\n                        setSelectedIndex\n                    },\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"dev-tools-indicator-inner\",\n                            children: [\n                                issueCount > 0 && /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                    title: issueCount + \" \" + (issueCount === 1 ? 'issue' : 'issues') + \" found. Click to view details in the dev overlay.\",\n                                    index: 0,\n                                    label: \"Issues\",\n                                    value: /*#__PURE__*/ (0, _jsxruntime.jsx)(IssueCount, {\n                                        children: issueCount\n                                    }),\n                                    onClick: openErrorOverlay\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                    title: \"Current route is \" + (isStaticRoute ? 'static' : 'dynamic') + \".\",\n                                    label: \"Route\",\n                                    index: 1,\n                                    value: isStaticRoute ? 'Static' : 'Dynamic',\n                                    onClick: ()=>setOpen(OVERLAYS.Route),\n                                    \"data-nextjs-route-type\": isStaticRoute ? 'static' : 'dynamic'\n                                }),\n                                isTurbopack ? /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                    title: \"Turbopack is enabled.\",\n                                    label: \"Turbopack\",\n                                    value: \"Enabled\"\n                                }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                    index: 2,\n                                    title: \"Learn about Turbopack and how to enable it in your application.\",\n                                    label: \"Try Turbopack\",\n                                    value: /*#__PURE__*/ (0, _jsxruntime.jsx)(ChevronRight, {}),\n                                    onClick: ()=>setOpen(OVERLAYS.Turbo)\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                            className: \"dev-tools-indicator-footer\",\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                \"data-preferences\": true,\n                                label: \"Preferences\",\n                                value: /*#__PURE__*/ (0, _jsxruntime.jsx)(_gearicon.default, {}),\n                                onClick: ()=>setOpen(OVERLAYS.Preferences),\n                                index: isTurbopack ? 2 : 3\n                            })\n                        })\n                    ]\n                })\n            })\n        ]\n    });\n}\n_c1 = DevToolsPopover;\nfunction ChevronRight() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"none\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fill: \"#666\",\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M5.50011 1.93945L6.03044 2.46978L10.8537 7.293C11.2442 7.68353 11.2442 8.31669 10.8537 8.70722L6.03044 13.5304L5.50011 14.0608L4.43945 13.0001L4.96978 12.4698L9.43945 8.00011L4.96978 3.53044L4.43945 3.00011L5.50011 1.93945Z\"\n        })\n    });\n}\n_c2 = ChevronRight;\nfunction MenuItem(param) {\n    let { index, label, value, onClick, href, ...props } = param;\n    const isInteractive = typeof onClick === 'function' || typeof href === 'string';\n    const { closeMenu, selectedIndex, setSelectedIndex } = (0, _react.useContext)(Context);\n    const selected = selectedIndex === index;\n    function click() {\n        if (isInteractive) {\n            onClick == null ? void 0 : onClick();\n            closeMenu();\n            if (href) {\n                window.open(href, '_blank', 'noopener, noreferrer');\n            }\n        }\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        className: \"dev-tools-indicator-item\",\n        \"data-index\": index,\n        \"data-selected\": selected,\n        onClick: click,\n        // Needs `onMouseMove` instead of enter to work together\n        // with keyboard and mouse input\n        onMouseMove: ()=>{\n            if (isInteractive && index !== undefined && selectedIndex !== index) {\n                setSelectedIndex(index);\n            }\n        },\n        onMouseLeave: ()=>setSelectedIndex(-1),\n        onKeyDown: (e)=>{\n            if (e.key === 'Enter' || e.key === ' ') {\n                click();\n            }\n        },\n        role: isInteractive ? 'menuitem' : undefined,\n        tabIndex: selected ? 0 : -1,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                className: \"dev-tools-indicator-label\",\n                children: label\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                className: \"dev-tools-indicator-value\",\n                children: value\n            })\n        ]\n    });\n}\n_c3 = MenuItem;\nfunction IssueCount(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n        className: \"dev-tools-indicator-issue-count\",\n        \"data-has-issues\": children > 0,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                className: \"dev-tools-indicator-issue-count-indicator\"\n            }),\n            children\n        ]\n    });\n}\n_c4 = IssueCount;\nconst DEV_TOOLS_INDICATOR_STYLES = \"\\n  .dev-tools-indicator-menu {\\n    -webkit-font-smoothing: antialiased;\\n    display: flex;\\n    flex-direction: column;\\n    align-items: flex-start;\\n    background: var(--color-background-100);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background-clip: padding-box;\\n    box-shadow: var(--shadow-menu);\\n    border-radius: var(--rounded-xl);\\n    position: absolute;\\n    font-family: var(--font-stack-sans);\\n    z-index: 1000;\\n    overflow: hidden;\\n    opacity: 0;\\n    outline: 0;\\n    min-width: 248px;\\n    transition: opacity var(--animate-out-duration-ms)\\n      var(--animate-out-timing-function);\\n\\n    &[data-rendered='true'] {\\n      opacity: 1;\\n      scale: 1;\\n    }\\n  }\\n\\n  .dev-tools-indicator-inner {\\n    padding: 6px;\\n    width: 100%;\\n  }\\n\\n  .dev-tools-indicator-item {\\n    display: flex;\\n    align-items: center;\\n    padding: 8px 6px;\\n    height: var(--size-36);\\n    border-radius: 6px;\\n    text-decoration: none !important;\\n    user-select: none;\\n    white-space: nowrap;\\n\\n    svg {\\n      width: var(--size-16);\\n      height: var(--size-16);\\n    }\\n\\n    &:focus-visible {\\n      outline: 0;\\n    }\\n  }\\n\\n  .dev-tools-indicator-footer {\\n    background: var(--color-background-200);\\n    padding: 6px;\\n    border-top: 1px solid var(--color-gray-400);\\n    width: 100%;\\n  }\\n\\n  .dev-tools-indicator-item[data-selected='true'] {\\n    cursor: pointer;\\n    background-color: var(--color-gray-200);\\n  }\\n\\n  .dev-tools-indicator-label {\\n    font-size: var(--size-14);\\n    line-height: var(--size-20);\\n    color: var(--color-gray-1000);\\n  }\\n\\n  .dev-tools-indicator-value {\\n    font-size: var(--size-14);\\n    line-height: var(--size-20);\\n    color: var(--color-gray-900);\\n    margin-left: auto;\\n  }\\n\\n  .dev-tools-indicator-issue-count {\\n    --color-primary: var(--color-gray-800);\\n    --color-secondary: var(--color-gray-100);\\n    display: flex;\\n    flex-direction: row;\\n    align-items: center;\\n    justify-content: center;\\n    gap: 8px;\\n    min-width: var(--size-40);\\n    height: var(--size-24);\\n    background: var(--color-background-100);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background-clip: padding-box;\\n    box-shadow: var(--shadow-small);\\n    padding: 2px;\\n    color: var(--color-gray-1000);\\n    border-radius: 128px;\\n    font-weight: 500;\\n    font-size: var(--size-13);\\n    font-variant-numeric: tabular-nums;\\n\\n    &[data-has-issues='true'] {\\n      --color-primary: var(--color-red-800);\\n      --color-secondary: var(--color-red-100);\\n    }\\n\\n    .dev-tools-indicator-issue-count-indicator {\\n      width: var(--size-8);\\n      height: var(--size-8);\\n      background: var(--color-primary);\\n      box-shadow: 0 0 0 2px var(--color-secondary);\\n      border-radius: 50%;\\n    }\\n  }\\n\\n  .dev-tools-indicator-shortcut {\\n    display: flex;\\n    gap: 4px;\\n\\n    kbd {\\n      width: var(--size-20);\\n      height: var(--size-20);\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      border-radius: var(--rounded-md);\\n      border: 1px solid var(--color-gray-400);\\n      font-family: var(--font-stack-sans);\\n      background: var(--color-background-100);\\n      color: var(--color-gray-1000);\\n      text-align: center;\\n      font-size: var(--size-12);\\n      line-height: var(--size-16);\\n    }\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-tools-indicator.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"DevToolsIndicator\");\n$RefreshReg$(_c1, \"DevToolsPopover\");\n$RefreshReg$(_c2, \"ChevronRight\");\n$RefreshReg$(_c3, \"MenuItem\");\n$RefreshReg$(_c4, \"IssueCount\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js ***!
  \**********************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INFO_STYLES: function() {\n        return DEV_TOOLS_INFO_STYLES;\n    },\n    DevToolsInfo: function() {\n        return DevToolsInfo;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _utils = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js\");\nconst _usedelayedrender = __webpack_require__(/*! ../../../../hooks/use-delayed-render */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-delayed-render.js\");\nfunction DevToolsInfo(param) {\n    let { title, children, learnMoreLink, isOpen, triggerRef, close, ...props } = param;\n    const ref = (0, _react.useRef)(null);\n    const closeButtonRef = (0, _react.useRef)(null);\n    const { mounted, rendered } = (0, _usedelayedrender.useDelayedRender)(isOpen, {\n        // Intentionally no fade in, makes the UI feel more immediate\n        enterDelay: 0,\n        // Graceful fade out to confirm that the UI did not break\n        exitDelay: _utils.MENU_DURATION_MS\n    });\n    (0, _utils.useFocusTrap)(ref, triggerRef, isOpen, ()=>{\n        var _closeButtonRef_current;\n        (_closeButtonRef_current = closeButtonRef.current) == null ? void 0 : _closeButtonRef_current.focus();\n    });\n    (0, _utils.useClickOutside)(ref, triggerRef, isOpen, close);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        tabIndex: -1,\n        role: \"dialog\",\n        ref: ref,\n        \"data-info-popover\": true,\n        ...props,\n        \"data-rendered\": rendered,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n            className: \"dev-tools-info-container\",\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h1\", {\n                    className: \"dev-tools-info-title\",\n                    children: title\n                }),\n                children,\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"dev-tools-info-button-container\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                            ref: closeButtonRef,\n                            className: \"dev-tools-info-close-button\",\n                            onClick: close,\n                            children: \"Close\"\n                        }),\n                        learnMoreLink && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                            className: \"dev-tools-info-learn-more-button\",\n                            href: learnMoreLink,\n                            target: \"_blank\",\n                            rel: \"noreferrer noopener\",\n                            children: \"Learn More\"\n                        })\n                    ]\n                })\n            ]\n        })\n    });\n}\n_c = DevToolsInfo;\nconst DEV_TOOLS_INFO_STYLES = \"\\n  [data-info-popover] {\\n    -webkit-font-smoothing: antialiased;\\n    display: flex;\\n    flex-direction: column;\\n    align-items: flex-start;\\n    background: var(--color-background-100);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background-clip: padding-box;\\n    box-shadow: var(--shadow-menu);\\n    border-radius: var(--rounded-xl);\\n    position: absolute;\\n    font-family: var(--font-stack-sans);\\n    z-index: 1000;\\n    overflow: hidden;\\n    opacity: 0;\\n    outline: 0;\\n    min-width: 350px;\\n    transition: opacity var(--animate-out-duration-ms)\\n      var(--animate-out-timing-function);\\n\\n    &[data-rendered='true'] {\\n      opacity: 1;\\n      scale: 1;\\n    }\\n\\n    button:focus-visible {\\n      outline: var(--focus-ring);\\n    }\\n  }\\n\\n  .dev-tools-info-container {\\n    padding: 12px;\\n  }\\n\\n  .dev-tools-info-title {\\n    padding: 8px 6px;\\n    color: var(--color-gray-1000);\\n    font-size: var(--size-16);\\n    font-weight: 600;\\n    line-height: var(--size-20);\\n    margin: 0;\\n  }\\n\\n  .dev-tools-info-article {\\n    padding: 8px 6px;\\n    color: var(--color-gray-1000);\\n    font-size: var(--size-14);\\n    line-height: var(--size-20);\\n    margin: 0;\\n  }\\n  .dev-tools-info-paragraph {\\n    &:last-child {\\n      margin-bottom: 0;\\n    }\\n  }\\n\\n  .dev-tools-info-button-container {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    padding: 8px 6px;\\n  }\\n\\n  .dev-tools-info-close-button {\\n    padding: 0 8px;\\n    height: var(--size-28);\\n    font-size: var(--size-14);\\n    font-weight: 500;\\n    line-height: var(--size-20);\\n    transition: background var(--duration-short) ease;\\n    color: var(--color-gray-1000);\\n    border-radius: var(--rounded-md-2);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background: var(--color-background-200);\\n  }\\n\\n  .dev-tools-info-close-button:hover {\\n    background: var(--color-gray-400);\\n  }\\n\\n  .dev-tools-info-learn-more-button {\\n    align-content: center;\\n    padding: 0 8px;\\n    height: var(--size-28);\\n    font-size: var(--size-14);\\n    font-weight: 500;\\n    line-height: var(--size-20);\\n    transition: background var(--duration-short) ease;\\n    color: var(--color-background-100);\\n    border-radius: var(--rounded-md-2);\\n    background: var(--color-gray-1000);\\n  }\\n\\n  .dev-tools-info-learn-more-button:hover {\\n    text-decoration: none;\\n    color: var(--color-background-100);\\n    opacity: 0.9;\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-tools-info.js.map\nvar _c;\n$RefreshReg$(_c, \"DevToolsInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js ***!
  \******************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INFO_ROUTE_INFO_STYLES: function() {\n        return DEV_TOOLS_INFO_ROUTE_INFO_STYLES;\n    },\n    RouteInfo: function() {\n        return RouteInfo;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _devtoolsinfo = __webpack_require__(/*! ./dev-tools-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js\");\nfunction StaticRouteContent(param) {\n    let { routerType } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"article\", {\n        className: \"dev-tools-info-article\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: [\n                    \"The path\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                        className: \"dev-tools-info-code\",\n                        children: window.location.pathname\n                    }),\n                    ' ',\n                    'is marked as \"static\" since it will be prerendered during the build time.'\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: [\n                    \"With Static Rendering, routes are rendered at build time, or in the background after\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        className: \"dev-tools-info-link\",\n                        href: routerType === 'pages' ? 'https://nextjs.org/docs/pages/building-your-application/data-fetching/incremental-static-regeneration' : \"https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: \"data revalidation\"\n                    }),\n                    \".\"\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: \"Static rendering is useful when a route has data that is not personalized to the user and can be known at build time, such as a static blog post or a product page.\"\n            })\n        ]\n    });\n}\n_c = StaticRouteContent;\nfunction DynamicRouteContent(param) {\n    let { routerType } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"article\", {\n        className: \"dev-tools-info-article\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: [\n                    \"The path\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                        className: \"dev-tools-info-code\",\n                        children: window.location.pathname\n                    }),\n                    ' ',\n                    'is marked as \"dynamic\" since it will be rendered for each user at',\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"strong\", {\n                        children: \"request time\"\n                    }),\n                    \".\"\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: \"Dynamic rendering is useful when a route has data that is personalized to the user or has information that can only be known at request time, such as cookies or the URL's search params.\"\n            }),\n            routerType === 'pages' ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-pagraph\",\n                children: [\n                    \"Exporting the\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        className: \"dev-tools-info-link\",\n                        href: \"https://nextjs.org/docs/pages/building-your-application/data-fetching/get-server-side-props\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: \"getServerSideProps\"\n                    }),\n                    ' ',\n                    \"function will opt the route into dynamic rendering. This function will be called by the server on every request.\"\n                ]\n            }) : /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: [\n                    \"During rendering, if a\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        className: \"dev-tools-info-link\",\n                        href: \"https://nextjs.org/docs/app/building-your-application/rendering/server-components#dynamic-apis\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: \"Dynamic API\"\n                    }),\n                    ' ',\n                    \"or a\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        className: \"dev-tools-info-link\",\n                        href: \"https://nextjs.org/docs/app/api-reference/functions/fetch\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: \"fetch\"\n                    }),\n                    ' ',\n                    \"option of\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                        className: \"dev-tools-info-code\",\n                        children: \"{ cache: 'no-store' }\"\n                    }),\n                    ' ',\n                    \"is discovered, Next.js will switch to dynamically rendering the whole route.\"\n                ]\n            })\n        ]\n    });\n}\n_c1 = DynamicRouteContent;\nconst learnMoreLink = {\n    pages: {\n        static: 'https://nextjs.org/docs/pages/building-your-application/rendering/static-site-generation',\n        dynamic: 'https://nextjs.org/docs/pages/building-your-application/rendering/server-side-rendering'\n    },\n    app: {\n        static: 'https://nextjs.org/docs/app/building-your-application/rendering/server-components#static-rendering-default',\n        dynamic: 'https://nextjs.org/docs/app/building-your-application/rendering/server-components#dynamic-rendering'\n    }\n};\nfunction RouteInfo(param) {\n    let { routeType, routerType, ...props } = param;\n    const isStaticRoute = routeType === 'Static';\n    const learnMore = isStaticRoute ? learnMoreLink[routerType].static : learnMoreLink[routerType].dynamic;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_devtoolsinfo.DevToolsInfo, {\n        title: \"\" + routeType + \" Route\",\n        learnMoreLink: learnMore,\n        ...props,\n        children: isStaticRoute ? /*#__PURE__*/ (0, _jsxruntime.jsx)(StaticRouteContent, {\n            routerType: routerType\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(DynamicRouteContent, {\n            routerType: routerType\n        })\n    });\n}\n_c2 = RouteInfo;\nconst DEV_TOOLS_INFO_ROUTE_INFO_STYLES = \"\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=route-info.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"StaticRouteContent\");\n$RefreshReg$(_c1, \"DynamicRouteContent\");\n$RefreshReg$(_c2, \"RouteInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js ***!
  \**********************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES: function() {\n        return DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES;\n    },\n    TurbopackInfo: function() {\n        return TurbopackInfo;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _devtoolsinfo = __webpack_require__(/*! ./dev-tools-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js\");\nconst _copybutton = __webpack_require__(/*! ../../../copy-button */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/copy-button/index.js\");\nfunction TurbopackInfo(props) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_devtoolsinfo.DevToolsInfo, {\n        title: \"Turbopack\",\n        learnMoreLink: \"https://nextjs.org/docs/app/api-reference/turbopack\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"article\", {\n                className: \"dev-tools-info-article\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        className: \"dev-tools-info-paragraph\",\n                        children: [\n                            \"Turbopack is an incremental bundler optimized for JavaScript and TypeScript, written in Rust, and built into Next.js. Turbopack can be used in Next.js in both the\",\n                            ' ',\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                className: \"dev-tools-info-code\",\n                                children: \"pages\"\n                            }),\n                            \" and\",\n                            ' ',\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                className: \"dev-tools-info-code\",\n                                children: \"app\"\n                            }),\n                            \" directories for faster local development.\"\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        className: \"dev-tools-info-paragraph\",\n                        children: [\n                            \"To enable Turbopack, use the\",\n                            ' ',\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                className: \"dev-tools-info-code\",\n                                children: \"--turbopack\"\n                            }),\n                            \" flag when running the Next.js development server.\"\n                        ]\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                className: \"dev-tools-info-code-block-container\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"dev-tools-info-code-block\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_copybutton.CopyButton, {\n                            actionLabel: \"Copy Next.js Turbopack Command\",\n                            successLabel: \"Next.js Turbopack Command Copied\",\n                            content: '--turbopack',\n                            className: \"dev-tools-info-copy-button\"\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"pre\", {\n                            className: \"dev-tools-info-code-block-pre\",\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"code\", {\n                                children: [\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '  '\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '{'\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: [\n                                            '  ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"scripts\"'\n                                            }),\n                                            \": \",\n                                            '{'\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line dev-tools-info-highlight\",\n                                        children: [\n                                            '    ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"dev\"'\n                                            }),\n                                            \":\",\n                                            ' ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-value\",\n                                                children: '\"next dev --turbopack\"'\n                                            }),\n                                            \",\"\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: [\n                                            '    ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"build\"'\n                                            }),\n                                            \":\",\n                                            ' ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-value\",\n                                                children: '\"next build\"'\n                                            }),\n                                            \",\"\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: [\n                                            '    ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"start\"'\n                                            }),\n                                            \":\",\n                                            ' ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-value\",\n                                                children: '\"next start\"'\n                                            }),\n                                            \",\"\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: [\n                                            '    ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"lint\"'\n                                            }),\n                                            \":\",\n                                            ' ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-value\",\n                                                children: '\"next lint\"'\n                                            })\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '  }'\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '}'\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '  '\n                                    })\n                                ]\n                            })\n                        })\n                    ]\n                })\n            })\n        ]\n    });\n}\n_c = TurbopackInfo;\nconst DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES = \"\\n  .dev-tools-info-code {\\n    background: var(--color-gray-400);\\n    color: var(--color-gray-1000);\\n    font-family: var(--font-stack-monospace);\\n    padding: 2px 4px;\\n    margin: 0;\\n    font-size: var(--size-13);\\n    white-space: break-spaces;\\n    border-radius: var(--rounded-md-2);\\n  }\\n\\n  .dev-tools-info-code-block-container {\\n    padding: 6px;\\n  }\\n\\n  .dev-tools-info-code-block {\\n    position: relative;\\n    background: var(--color-background-200);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    border-radius: var(--rounded-md-2);\\n    min-width: 326px;\\n  }\\n\\n  .dev-tools-info-code-block-pre {\\n    margin: 0;\\n    font-family: var(--font-stack-monospace);\\n    font-size: var(--size-12);\\n  }\\n\\n  .dev-tools-info-copy-button {\\n    position: absolute;\\n\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    right: 8px;\\n    top: 8px;\\n    padding: 4px;\\n    height: var(--size-24);\\n    width: var(--size-24);\\n    border-radius: var(--rounded-md-2);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background: var(--color-background-100);\\n  }\\n\\n  .dev-tools-info-code-block-line {\\n    display: block;\\n    line-height: 1.5;\\n    padding: 0 16px;\\n  }\\n\\n  .dev-tools-info-code-block-line.dev-tools-info-highlight {\\n    border-left: 2px solid var(--color-blue-900);\\n    background: var(--color-blue-400);\\n  }\\n\\n  .dev-tools-info-code-block-json-key {\\n    color: var(--color-syntax-keyword);\\n  }\\n\\n  .dev-tools-info-code-block-json-value {\\n    color: var(--color-syntax-link);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=turbopack-info.js.map\nvar _c;\n$RefreshReg$(_c, \"TurbopackInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js ***!
  \************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INFO_USER_PREFERENCES_STYLES: function() {\n        return DEV_TOOLS_INFO_USER_PREFERENCES_STYLES;\n    },\n    UserPreferences: function() {\n        return UserPreferences;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _css = __webpack_require__(/*! ../../../../../utils/css */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nconst _eyeicon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../../icons/eye-icon */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/eye-icon.js\"));\nconst _shared = __webpack_require__(/*! ../../../../../shared */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _lighticon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../../icons/light-icon */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/light-icon.js\"));\nconst _darkicon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../../icons/dark-icon */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/dark-icon.js\"));\nconst _systemicon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../../icons/system-icon */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/system-icon.js\"));\nconst _devtoolsinfo = __webpack_require__(/*! ./dev-tools-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  .preferences-container {\\n    padding: 8px 6px;\\n    width: 100%;\\n  }\\n\\n  @media (min-width: 576px) {\\n    .preferences-container {\\n      width: 480px;\\n    }\\n  }\\n\\n  .preference-section:first-child {\\n    padding-top: 0;\\n  }\\n\\n  .preference-section {\\n    padding: 12px 0;\\n    border-bottom: 1px solid var(--color-gray-400);\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    gap: 24px;\\n  }\\n\\n  .preference-section:last-child {\\n    border-bottom: none;\\n  }\\n\\n  .preference-header {\\n    margin-bottom: 0;\\n    flex: 1;\\n  }\\n\\n  .preference-header label {\\n    font-size: var(--size-14);\\n    font-weight: 500;\\n    color: var(--color-gray-1000);\\n    margin: 0;\\n  }\\n\\n  .preference-description {\\n    color: var(--color-gray-900);\\n    font-size: var(--size-14);\\n    margin: 0;\\n  }\\n\\n  .preference-icon {\\n    display: flex;\\n    align-items: center;\\n    width: 16px;\\n    height: 16px;\\n  }\\n\\n  .select-button,\\n  .action-button {\\n    display: flex;\\n    align-items: center;\\n    gap: 8px;\\n    background: var(--color-background-100);\\n    border: 1px solid var(--color-gray-400);\\n    border-radius: var(--rounded-lg);\\n    font-weight: 400;\\n    font-size: var(--size-14);\\n    color: var(--color-gray-1000);\\n    padding: 6px 8px;\\n\\n    &:hover {\\n      background: var(--color-gray-100);\\n    }\\n  }\\n\\n  .preference-control-select {\\n    padding: 6px 8px;\\n    display: flex;\\n    align-items: center;\\n    gap: 8px;\\n    border-radius: var(--rounded-lg);\\n    border: 1px solid var(--color-gray-400);\\n\\n    &:hover {\\n      background: var(--color-gray-100);\\n    }\\n\\n    &:focus-within {\\n      outline: var(--focus-ring);\\n    }\\n  }\\n\\n  .preference-control-select select {\\n    font-size: var(--size-14);\\n    font-weight: 400;\\n    border: none;\\n    padding: 0 6px 0 0;\\n    border-radius: 0;\\n    outline: none;\\n    background: none;\\n  }\\n\\n  :global(.icon) {\\n    width: 18px;\\n    height: 18px;\\n    color: #666;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction getInitialPreference() {\n    if (typeof localStorage === 'undefined') {\n        return 'system';\n    }\n    const theme = localStorage.getItem(_shared.STORAGE_KEY_THEME);\n    return theme === 'dark' || theme === 'light' ? theme : 'system';\n}\nfunction UserPreferences(param) {\n    let { setPosition, position, hide, ...props } = param;\n    // derive initial theme from system preference\n    const [theme, setTheme] = (0, _react.useState)(getInitialPreference());\n    const handleThemeChange = (e)=>{\n        const portal = document.querySelector('nextjs-portal');\n        if (!portal) return;\n        setTheme(e.target.value);\n        if (e.target.value === 'system') {\n            portal.classList.remove('dark');\n            portal.classList.remove('light');\n            localStorage.removeItem(_shared.STORAGE_KEY_THEME);\n            return;\n        }\n        if (e.target.value === 'dark') {\n            portal.classList.add('dark');\n            portal.classList.remove('light');\n            localStorage.setItem(_shared.STORAGE_KEY_THEME, 'dark');\n        } else {\n            portal.classList.remove('dark');\n            portal.classList.add('light');\n            localStorage.setItem(_shared.STORAGE_KEY_THEME, 'light');\n        }\n    };\n    function handlePositionChange(e) {\n        setPosition(e.target.value);\n        localStorage.setItem(_shared.STORAGE_KEY_POSITION, e.target.value);\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_devtoolsinfo.DevToolsInfo, {\n        title: \"Preferences\",\n        ...props,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n            className: \"preferences-container\",\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"preference-section\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"preference-header\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"label\", {\n                                    htmlFor: \"theme\",\n                                    children: \"Theme\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    className: \"preference-description\",\n                                    children: \"Select your theme preference.\"\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"preference-control-select\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                    className: \"preference-icon\",\n                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(ThemeIcon, {\n                                        theme: theme\n                                    })\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"select\", {\n                                    id: \"theme\",\n                                    name: \"theme\",\n                                    className: \"select-button\",\n                                    value: theme,\n                                    onChange: handleThemeChange,\n                                    children: [\n                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                            value: \"system\",\n                                            children: \"System\"\n                                        }),\n                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                            value: \"light\",\n                                            children: \"Light\"\n                                        }),\n                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                            value: \"dark\",\n                                            children: \"Dark\"\n                                        })\n                                    ]\n                                })\n                            ]\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"preference-section\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"preference-header\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"label\", {\n                                    htmlFor: \"position\",\n                                    children: \"Position\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    className: \"preference-description\",\n                                    children: \"Adjust the placement of your dev tools.\"\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                            className: \"preference-control-select\",\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"select\", {\n                                id: \"position\",\n                                name: \"position\",\n                                className: \"select-button\",\n                                value: position,\n                                onChange: handlePositionChange,\n                                children: [\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                        value: \"bottom-left\",\n                                        children: \"Bottom Left\"\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                        value: \"bottom-right\",\n                                        children: \"Bottom Right\"\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                        value: \"top-left\",\n                                        children: \"Top Left\"\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                        value: \"top-right\",\n                                        children: \"Top Right\"\n                                    })\n                                ]\n                            })\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"preference-section\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"preference-header\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"label\", {\n                                    htmlFor: \"hide-dev-tools\",\n                                    children: \"Hide Dev Tools for this session\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    className: \"preference-description\",\n                                    children: \"Hide Dev Tools until you restart your dev server, or 1 day.\"\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                            className: \"preference-control\",\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"button\", {\n                                id: \"hide-dev-tools\",\n                                name: \"hide-dev-tools\",\n                                \"data-hide-dev-tools\": true,\n                                className: \"action-button\",\n                                onClick: hide,\n                                children: [\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"preference-icon\",\n                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_eyeicon.default, {})\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                        children: \"Hide\"\n                                    })\n                                ]\n                            })\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                    className: \"preference-section\",\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                        className: \"preference-header\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"label\", {\n                                children: \"Disable Dev Tools for this project\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                                className: \"preference-description\",\n                                children: [\n                                    \"To disable this UI completely, set\",\n                                    ' ',\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                        className: \"dev-tools-info-code\",\n                                        children: \"devIndicators: false\"\n                                    }),\n                                    ' ',\n                                    \"in your \",\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                        className: \"dev-tools-info-code\",\n                                        children: \"next.config\"\n                                    }),\n                                    ' ',\n                                    \"file.\"\n                                ]\n                            })\n                        ]\n                    })\n                })\n            ]\n        })\n    });\n}\n_c = UserPreferences;\nfunction ThemeIcon(param) {\n    let { theme } = param;\n    switch(theme){\n        case 'system':\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_systemicon.default, {});\n        case 'dark':\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_darkicon.default, {});\n        case 'light':\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lighticon.default, {});\n        default:\n            return null;\n    }\n}\n_c1 = ThemeIcon;\nconst DEV_TOOLS_INFO_USER_PREFERENCES_STYLES = (0, _css.css)(_templateObject());\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=user-preferences.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"UserPreferences\");\n$RefreshReg$(_c1, \"ThemeIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.js ***!
  \**************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Cross: function() {\n        return Cross;\n    },\n    NextLogo: function() {\n        return NextLogo;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _css = __webpack_require__(/*! ../../../../utils/css */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nconst _mergerefs = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../utils/merge-refs */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/merge-refs.js\"));\nconst _useminimumloadingtimemultiple = __webpack_require__(/*! ./use-minimum-loading-time-multiple */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n          [data-next-badge-root] {\\n            --timing: cubic-bezier(0.23, 0.88, 0.26, 0.92);\\n            --duration-long: 250ms;\\n            --color-outer-border: #171717;\\n            --color-inner-border: hsla(0, 0%, 100%, 0.14);\\n            --color-hover-alpha-subtle: hsla(0, 0%, 100%, 0.13);\\n            --color-hover-alpha-error: hsla(0, 0%, 100%, 0.2);\\n            --color-hover-alpha-error-2: hsla(0, 0%, 100%, 0.25);\\n            --mark-size: calc(var(--size) - var(--size-2) * 2);\\n\\n            --focus-color: var(--color-blue-800);\\n            --focus-ring: 2px solid var(--focus-color);\\n\\n            &:has([data-next-badge][data-error='true']) {\\n              --focus-color: #fff;\\n            }\\n          }\\n\\n          [data-disabled-icon] {\\n            display: flex;\\n            align-items: center;\\n            justify-content: center;\\n            padding-right: 4px;\\n          }\\n\\n          [data-next-badge] {\\n            -webkit-font-smoothing: antialiased;\\n            width: var(--size);\\n            height: var(--size);\\n            display: flex;\\n            align-items: center;\\n            position: relative;\\n            background: rgba(0, 0, 0, 0.8);\\n            box-shadow:\\n              0 0 0 1px var(--color-outer-border),\\n              inset 0 0 0 1px var(--color-inner-border),\\n              0px 16px 32px -8px rgba(0, 0, 0, 0.24);\\n            backdrop-filter: blur(48px);\\n            border-radius: var(--rounded-full);\\n            user-select: none;\\n            cursor: pointer;\\n            scale: 1;\\n            overflow: hidden;\\n            will-change: scale, box-shadow, width, background;\\n            transition:\\n              scale var(--duration-short) var(--timing),\\n              width var(--duration-long) var(--timing),\\n              box-shadow var(--duration-long) var(--timing),\\n              background var(--duration-short) ease;\\n\\n            &:active[data-error='false'] {\\n              scale: 0.95;\\n            }\\n\\n            &[data-animate='true']:not(:hover) {\\n              scale: 1.02;\\n            }\\n\\n            &[data-error='false']:has([data-next-mark]:focus-visible) {\\n              outline: var(--focus-ring);\\n              outline-offset: 3px;\\n            }\\n\\n            &[data-error='true'] {\\n              background: #ca2a30;\\n              --color-inner-border: #e5484d;\\n\\n              [data-next-mark] {\\n                background: var(--color-hover-alpha-error);\\n                outline-offset: 0px;\\n\\n                &:focus-visible {\\n                  outline: var(--focus-ring);\\n                  outline-offset: -1px;\\n                }\\n\\n                &:hover {\\n                  background: var(--color-hover-alpha-error-2);\\n                }\\n              }\\n            }\\n\\n            &[data-error-expanded='false'][data-error='true'] ~ [data-dot] {\\n              scale: 1;\\n            }\\n\\n            > div {\\n              display: flex;\\n            }\\n          }\\n\\n          [data-issues-collapse]:focus-visible {\\n            outline: var(--focus-ring);\\n          }\\n\\n          [data-issues]:has([data-issues-open]:focus-visible) {\\n            outline: var(--focus-ring);\\n            outline-offset: -1px;\\n          }\\n\\n          [data-dot] {\\n            content: '';\\n            width: var(--size-8);\\n            height: var(--size-8);\\n            background: #fff;\\n            box-shadow: 0 0 0 1px var(--color-outer-border);\\n            border-radius: 50%;\\n            position: absolute;\\n            top: 2px;\\n            right: 0px;\\n            scale: 0;\\n            pointer-events: none;\\n            transition: scale 200ms var(--timing);\\n            transition-delay: var(--duration-short);\\n          }\\n\\n          [data-issues] {\\n            --padding-left: 8px;\\n            display: flex;\\n            gap: 2px;\\n            align-items: center;\\n            padding-left: 8px;\\n            padding-right: 8px;\\n            height: var(--size-32);\\n            margin: 0 2px;\\n            border-radius: var(--rounded-full);\\n            transition: background var(--duration-short) ease;\\n\\n            &:has([data-issues-open]:hover) {\\n              background: var(--color-hover-alpha-error);\\n            }\\n\\n            &:has([data-issues-collapse]) {\\n              padding-right: calc(var(--padding-left) / 2);\\n            }\\n\\n            [data-cross] {\\n              translate: 0px -1px;\\n            }\\n          }\\n\\n          [data-issues-open] {\\n            font-size: var(--size-13);\\n            color: white;\\n            width: fit-content;\\n            height: 100%;\\n            display: flex;\\n            gap: 2px;\\n            align-items: center;\\n            margin: 0;\\n            line-height: var(--size-36);\\n            font-weight: 500;\\n            z-index: 2;\\n            white-space: nowrap;\\n\\n            &:focus-visible {\\n              outline: 0;\\n            }\\n          }\\n\\n          [data-issues-collapse] {\\n            width: var(--size-24);\\n            height: var(--size-24);\\n            border-radius: var(--rounded-full);\\n            transition: background var(--duration-short) ease;\\n\\n            &:hover {\\n              background: var(--color-hover-alpha-error);\\n            }\\n          }\\n\\n          [data-cross] {\\n            color: #fff;\\n            width: var(--size-12);\\n            height: var(--size-12);\\n          }\\n\\n          [data-next-mark] {\\n            width: var(--mark-size);\\n            height: var(--mark-size);\\n            margin-left: 2px;\\n            display: flex;\\n            align-items: center;\\n            border-radius: var(--rounded-full);\\n            transition: background var(--duration-long) var(--timing);\\n\\n            &:focus-visible {\\n              outline: 0;\\n            }\\n\\n            &:hover {\\n              background: var(--color-hover-alpha-subtle);\\n            }\\n\\n            svg {\\n              flex-shrink: 0;\\n              width: var(--size-40);\\n              height: var(--size-40);\\n            }\\n          }\\n\\n          [data-issues-count-animation] {\\n            display: grid;\\n            place-items: center center;\\n            font-variant-numeric: tabular-nums;\\n\\n            &[data-animate='false'] {\\n              [data-issues-count-exit],\\n              [data-issues-count-enter] {\\n                animation-duration: 0ms;\\n              }\\n            }\\n\\n            > * {\\n              grid-area: 1 / 1;\\n            }\\n\\n            [data-issues-count-exit] {\\n              animation: fadeOut 300ms var(--timing) forwards;\\n            }\\n\\n            [data-issues-count-enter] {\\n              animation: fadeIn 300ms var(--timing) forwards;\\n            }\\n          }\\n\\n          [data-issues-count-plural] {\\n            display: inline-block;\\n            &[data-animate='true'] {\\n              animation: fadeIn 300ms var(--timing) forwards;\\n            }\\n          }\\n\\n          .path0 {\\n            animation: draw0 1.5s ease-in-out infinite;\\n          }\\n\\n          .path1 {\\n            animation: draw1 1.5s ease-out infinite;\\n            animation-delay: 0.3s;\\n          }\\n\\n          .paused {\\n            stroke-dashoffset: 0;\\n          }\\n\\n          @keyframes fadeIn {\\n            0% {\\n              opacity: 0;\\n              filter: blur(2px);\\n              transform: translateY(8px);\\n            }\\n            100% {\\n              opacity: 1;\\n              filter: blur(0px);\\n              transform: translateY(0);\\n            }\\n          }\\n\\n          @keyframes fadeOut {\\n            0% {\\n              opacity: 1;\\n              filter: blur(0px);\\n              transform: translateY(0);\\n            }\\n            100% {\\n              opacity: 0;\\n              transform: translateY(-12px);\\n              filter: blur(2px);\\n            }\\n          }\\n\\n          @keyframes draw0 {\\n            0%,\\n            25% {\\n              stroke-dashoffset: -29.6;\\n            }\\n            25%,\\n            50% {\\n              stroke-dashoffset: 0;\\n            }\\n            50%,\\n            75% {\\n              stroke-dashoffset: 0;\\n            }\\n            75%,\\n            100% {\\n              stroke-dashoffset: 29.6;\\n            }\\n          }\\n\\n          @keyframes draw1 {\\n            0%,\\n            20% {\\n              stroke-dashoffset: -11.6;\\n            }\\n            20%,\\n            50% {\\n              stroke-dashoffset: 0;\\n            }\\n            50%,\\n            75% {\\n              stroke-dashoffset: 0;\\n            }\\n            75%,\\n            100% {\\n              stroke-dashoffset: 11.6;\\n            }\\n          }\\n\\n          @media (prefers-reduced-motion) {\\n            [data-issues-count-exit],\\n            [data-issues-count-enter],\\n            [data-issues-count-plural] {\\n              animation-duration: 0ms !important;\\n            }\\n          }\\n        \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst SIZE = '2.25rem' // 36px in 16px base\n;\nconst SIZE_PX = 36;\nconst SHORT_DURATION_MS = 150;\nconst NextLogo = /*#__PURE__*/ (0, _react.forwardRef)(_s(function NextLogo(param, propRef) {\n    _s();\n    let { disabled, issueCount, isDevBuilding, isDevRendering, isBuildError, onTriggerClick, toggleErrorOverlay, ...props } = param;\n    const hasError = issueCount > 0;\n    const [isErrorExpanded, setIsErrorExpanded] = (0, _react.useState)(hasError);\n    const [dismissed, setDismissed] = (0, _react.useState)(false);\n    const newErrorDetected = useUpdateAnimation(issueCount, SHORT_DURATION_MS);\n    const triggerRef = (0, _react.useRef)(null);\n    const ref = (0, _react.useRef)(null);\n    const [measuredWidth, pristine] = useMeasureWidth(ref);\n    const isLoading = (0, _useminimumloadingtimemultiple.useMinimumLoadingTimeMultiple)(isDevBuilding || isDevRendering);\n    const isExpanded = isErrorExpanded || disabled;\n    const style = (0, _react.useMemo)(()=>{\n        let width = SIZE;\n        // Animates the badge, if expanded\n        if (measuredWidth > SIZE_PX) width = measuredWidth;\n        // No animations on page load, assume the intrinsic width immediately\n        if (pristine && hasError) width = 'auto';\n        // Default state, collapsed\n        return {\n            width\n        };\n    }, [\n        measuredWidth,\n        pristine,\n        hasError\n    ]);\n    (0, _react.useEffect)(()=>{\n        setIsErrorExpanded(hasError);\n    }, [\n        hasError\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-next-badge-root\": true,\n        style: {\n            '--size': SIZE,\n            '--duration-short': \"\" + SHORT_DURATION_MS + \"ms\",\n            // if the indicator is disabled, hide the badge\n            // also allow the \"disabled\" state be dismissed, as long as there are no build errors\n            display: disabled && (!hasError || dismissed) ? 'none' : 'block'\n        },\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                children: (0, _css.css)(_templateObject())\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"data-next-badge\": true,\n                \"data-error\": hasError,\n                \"data-error-expanded\": isExpanded,\n                \"data-animate\": newErrorDetected,\n                style: style,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    ref: ref,\n                    children: [\n                        !disabled && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                            ref: (0, _mergerefs.default)(triggerRef, propRef),\n                            \"data-next-mark\": true,\n                            \"data-next-mark-loading\": isLoading,\n                            onClick: onTriggerClick,\n                            ...props,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(NextMark, {\n                                isLoading: isLoading,\n                                isDevBuilding: isDevBuilding\n                            })\n                        }),\n                        isExpanded && /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            \"data-issues\": true,\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"button\", {\n                                    \"data-issues-open\": true,\n                                    \"aria-label\": \"Open issues overlay\",\n                                    onClick: toggleErrorOverlay,\n                                    children: [\n                                        disabled && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                            \"data-disabled-icon\": true,\n                                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Warning, {})\n                                        }),\n                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(AnimateCount, {\n                                            animate: newErrorDetected,\n                                            \"data-issues-count-animation\": true,\n                                            children: issueCount\n                                        }, issueCount),\n                                        ' ',\n                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                            children: [\n                                                \"Issue\",\n                                                issueCount > 1 && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                    \"aria-hidden\": true,\n                                                    \"data-issues-count-plural\": true,\n                                                    // This only needs to animate once the count changes from 1 -> 2,\n                                                    // otherwise it should stay static between re-renders.\n                                                    \"data-animate\": newErrorDetected && issueCount === 2,\n                                                    children: \"s\"\n                                                })\n                                            ]\n                                        })\n                                    ]\n                                }),\n                                !isBuildError && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                                    \"data-issues-collapse\": true,\n                                    \"aria-label\": \"Collapse issues badge\",\n                                    onClick: ()=>{\n                                        var _triggerRef_current;\n                                        if (disabled) {\n                                            setDismissed(true);\n                                        } else {\n                                            setIsErrorExpanded(false);\n                                        }\n                                        (_triggerRef_current = triggerRef.current) == null ? void 0 : _triggerRef_current.focus();\n                                    },\n                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Cross, {\n                                        \"data-cross\": true\n                                    })\n                                })\n                            ]\n                        })\n                    ]\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"aria-hidden\": true,\n                \"data-dot\": true\n            })\n        ]\n    });\n}, \"jkUnATqmh1rTMIYx3BahGx7X0Qc=\", false, function() {\n    return [\n        useUpdateAnimation,\n        useMeasureWidth\n    ];\n}));\nfunction AnimateCount(param) {\n    let { children: count, animate = true, ...props } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        ...props,\n        \"data-animate\": animate,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"aria-hidden\": true,\n                \"data-issues-count-exit\": true,\n                children: count - 1\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"data-issues-count\": true,\n                \"data-issues-count-enter\": true,\n                children: count\n            })\n        ]\n    });\n}\n_c = AnimateCount;\nfunction useMeasureWidth(ref) {\n    const [width, setWidth] = (0, _react.useState)(0);\n    const [pristine, setPristine] = (0, _react.useState)(true);\n    (0, _react.useEffect)(()=>{\n        const el = ref.current;\n        if (!el) {\n            return;\n        }\n        const observer = new ResizeObserver(()=>{\n            const { width: w } = el.getBoundingClientRect();\n            setWidth((prevWidth)=>{\n                if (prevWidth !== 0) {\n                    setPristine(false);\n                }\n                return w;\n            });\n        });\n        observer.observe(el);\n        return ()=>observer.disconnect();\n    }, [\n        ref\n    ]);\n    return [\n        width,\n        pristine\n    ];\n}\nfunction useUpdateAnimation(issueCount, animationDurationMs) {\n    if (animationDurationMs === void 0) animationDurationMs = 0;\n    const lastUpdatedTimeStamp = (0, _react.useRef)(null);\n    const [animate, setAnimate] = (0, _react.useState)(false);\n    (0, _react.useEffect)(()=>{\n        if (issueCount > 0) {\n            const deltaMs = lastUpdatedTimeStamp.current ? Date.now() - lastUpdatedTimeStamp.current : -1;\n            lastUpdatedTimeStamp.current = Date.now();\n            // We don't animate if `issueCount` changes too quickly\n            if (deltaMs <= animationDurationMs) {\n                return;\n            }\n            setAnimate(true);\n            // It is important to use a CSS transitioned state, not a CSS keyframed animation\n            // because if the issue count increases faster than the animation duration, it\n            // will abruptly stop and not transition smoothly back to its original state.\n            const timeoutId = window.setTimeout(()=>{\n                setAnimate(false);\n            }, animationDurationMs);\n            return ()=>{\n                clearTimeout(timeoutId);\n            };\n        }\n    }, [\n        issueCount,\n        animationDurationMs\n    ]);\n    return animate;\n}\nfunction NextMark(param) {\n    let { isLoading, isDevBuilding } = param;\n    const strokeColor = isDevBuilding ? 'rgba(255,255,255,0.7)' : 'white';\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n        width: \"40\",\n        height: \"40\",\n        viewBox: \"0 0 40 40\",\n        fill: \"none\",\n        \"data-next-mark-loading\": isLoading,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"g\", {\n                transform: \"translate(8.5, 13)\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        className: isLoading ? 'path0' : 'paused',\n                        d: \"M13.3 15.2 L2.34 1 V12.6\",\n                        fill: \"none\",\n                        stroke: \"url(#next_logo_paint0_linear_1357_10853)\",\n                        strokeWidth: \"1.86\",\n                        mask: \"url(#next_logo_mask0)\",\n                        strokeDasharray: \"29.6\",\n                        strokeDashoffset: \"29.6\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        className: isLoading ? 'path1' : 'paused',\n                        d: \"M11.825 1.5 V13.1\",\n                        strokeWidth: \"1.86\",\n                        stroke: \"url(#next_logo_paint1_linear_1357_10853)\",\n                        strokeDasharray: \"11.6\",\n                        strokeDashoffset: \"11.6\"\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"defs\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"next_logo_paint0_linear_1357_10853\",\n                        x1: \"9.95555\",\n                        y1: \"11.1226\",\n                        x2: \"15.4778\",\n                        y2: \"17.9671\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                stopColor: strokeColor\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"0.604072\",\n                                stopColor: strokeColor,\n                                stopOpacity: \"0\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: strokeColor,\n                                stopOpacity: \"0\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"next_logo_paint1_linear_1357_10853\",\n                        x1: \"11.8222\",\n                        y1: \"1.40039\",\n                        x2: \"11.791\",\n                        y2: \"9.62542\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                stopColor: strokeColor\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: strokeColor,\n                                stopOpacity: \"0\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"mask\", {\n                        id: \"next_logo_mask0\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"rect\", {\n                                width: \"100%\",\n                                height: \"100%\",\n                                fill: \"white\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"rect\", {\n                                width: \"5\",\n                                height: \"1.5\",\n                                fill: \"black\"\n                            })\n                        ]\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c1 = NextMark;\nfunction Warning() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"12\",\n        height: \"12\",\n        viewBox: \"0 0 12 12\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M3.98071 1.125L1.125 3.98071L1.125 8.01929L3.98071 10.875H8.01929L10.875 8.01929V3.98071L8.01929 1.125H3.98071ZM3.82538 0C3.62647 0 3.4357 0.0790176 3.29505 0.21967L0.21967 3.29505C0.0790176 3.4357 0 3.62647 0 3.82538V8.17462C0 8.37353 0.0790178 8.5643 0.21967 8.70495L3.29505 11.7803C3.4357 11.921 3.62647 12 3.82538 12H8.17462C8.37353 12 8.5643 11.921 8.70495 11.7803L11.7803 8.70495C11.921 8.5643 12 8.37353 12 8.17462V3.82538C12 3.62647 11.921 3.4357 11.7803 3.29505L8.70495 0.21967C8.5643 0.0790177 8.37353 0 8.17462 0H3.82538ZM6.5625 2.8125V3.375V6V6.5625H5.4375V6V3.375V2.8125H6.5625ZM6 9C6.41421 9 6.75 8.66421 6.75 8.25C6.75 7.83579 6.41421 7.5 6 7.5C5.58579 7.5 5.25 7.83579 5.25 8.25C5.25 8.66421 5.58579 9 6 9Z\",\n            fill: \"#EAEAEA\"\n        })\n    });\n}\n_c2 = Warning;\nfunction Cross(props) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"12\",\n        height: \"12\",\n        viewBox: \"0 0 14 14\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M3.08889 11.8384L2.62486 12.3024L1.69678 11.3744L2.16082 10.9103L6.07178 6.99937L2.16082 3.08841L1.69678 2.62437L2.62486 1.69629L3.08889 2.16033L6.99986 6.07129L10.9108 2.16033L11.3749 1.69629L12.3029 2.62437L11.8389 3.08841L7.92793 6.99937L11.8389 10.9103L12.3029 11.3744L11.3749 12.3024L10.9108 11.8384L6.99986 7.92744L3.08889 11.8384Z\",\n            fill: \"currentColor\"\n        })\n    });\n}\n_c3 = Cross;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=next-logo.js.map\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"AnimateCount\");\n$RefreshReg$(_c1, \"NextMark\");\n$RefreshReg$(_c2, \"Warning\");\n$RefreshReg$(_c3, \"Cross\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.js ***!
  \************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INDICATOR_STYLES: function() {\n        return DEV_TOOLS_INDICATOR_STYLES;\n    },\n    DevToolsIndicator: function() {\n        return DevToolsIndicator;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _shared = __webpack_require__(/*! ../../../../shared */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _toast = __webpack_require__(/*! ../../toast */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/index.js\");\nconst _nextlogo = __webpack_require__(/*! ./next-logo */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.js\");\nconst _initialize = __webpack_require__(/*! ../../../../../../dev/dev-build-indicator/internal/initialize */ \"(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js\");\nconst _devrenderindicator = __webpack_require__(/*! ../../../../utils/dev-indicator/dev-render-indicator */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/dev-render-indicator.js\");\nconst _usedelayedrender = __webpack_require__(/*! ../../../hooks/use-delayed-render */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-delayed-render.js\");\nconst _turbopackinfo = __webpack_require__(/*! ./dev-tools-info/turbopack-info */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js\");\nconst _routeinfo = __webpack_require__(/*! ./dev-tools-info/route-info */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js\");\nconst _gearicon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../icons/gear-icon */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/gear-icon.js\"));\nconst _userpreferences = __webpack_require__(/*! ./dev-tools-info/user-preferences */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js\");\n// TODO: add E2E tests to cover different scenarios\nconst INDICATOR_POSITION = \"bottom-left\" || 0;\nfunction DevToolsIndicator(param) {\n    let { state, errorCount, isBuildError, setIsErrorOverlayOpen } = param;\n    const [isDevToolsIndicatorVisible, setIsDevToolsIndicatorVisible] = (0, _react.useState)(true);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(DevToolsPopover, {\n        routerType: state.routerType,\n        semver: state.versionInfo.installed,\n        issueCount: errorCount,\n        isStaticRoute: state.staticIndicator,\n        hide: ()=>{\n            setIsDevToolsIndicatorVisible(false);\n            fetch('/__nextjs_disable_dev_indicator', {\n                method: 'POST'\n            });\n        },\n        setIsErrorOverlayOpen: setIsErrorOverlayOpen,\n        isTurbopack: !!false,\n        disabled: state.disableDevIndicator || !isDevToolsIndicatorVisible,\n        isBuildError: isBuildError\n    });\n}\n_c = DevToolsIndicator;\nconst Context = /*#__PURE__*/ (0, _react.createContext)({});\nfunction getInitialPosition() {\n    if (typeof localStorage !== 'undefined' && localStorage.getItem(_shared.STORAGE_KEY_POSITION)) {\n        return localStorage.getItem(_shared.STORAGE_KEY_POSITION);\n    }\n    return INDICATOR_POSITION;\n}\nconst OVERLAYS = {\n    Root: 'root',\n    Turbo: 'turbo',\n    Route: 'route',\n    Preferences: 'preferences'\n};\nfunction DevToolsPopover(param) {\n    let { routerType, disabled, issueCount, isStaticRoute, isTurbopack, isBuildError, hide, setIsErrorOverlayOpen } = param;\n    const menuRef = (0, _react.useRef)(null);\n    const triggerRef = (0, _react.useRef)(null);\n    const [open, setOpen] = (0, _react.useState)(null);\n    const [position, setPosition] = (0, _react.useState)(getInitialPosition());\n    const [selectedIndex, setSelectedIndex] = (0, _react.useState)(-1);\n    const isMenuOpen = open === OVERLAYS.Root;\n    const isTurbopackInfoOpen = open === OVERLAYS.Turbo;\n    const isRouteInfoOpen = open === OVERLAYS.Route;\n    const isPreferencesOpen = open === OVERLAYS.Preferences;\n    const { mounted: menuMounted, rendered: menuRendered } = (0, _usedelayedrender.useDelayedRender)(isMenuOpen, {\n        // Intentionally no fade in, makes the UI feel more immediate\n        enterDelay: 0,\n        // Graceful fade out to confirm that the UI did not break\n        exitDelay: _utils.MENU_DURATION_MS\n    });\n    // Features to make the menu accessible\n    (0, _utils.useFocusTrap)(menuRef, triggerRef, isMenuOpen);\n    (0, _utils.useClickOutside)(menuRef, triggerRef, isMenuOpen, closeMenu);\n    (0, _react.useEffect)(()=>{\n        if (open === null) {\n            // Avoid flashing selected state\n            const id = setTimeout(()=>{\n                setSelectedIndex(-1);\n            }, _utils.MENU_DURATION_MS);\n            return ()=>clearTimeout(id);\n        }\n    }, [\n        open\n    ]);\n    function select(index) {\n        var _menuRef_current;\n        if (index === 'first') {\n            setTimeout(()=>{\n                var _menuRef_current;\n                const all = (_menuRef_current = menuRef.current) == null ? void 0 : _menuRef_current.querySelectorAll('[role=\"menuitem\"]');\n                if (all) {\n                    const firstIndex = all[0].getAttribute('data-index');\n                    select(Number(firstIndex));\n                }\n            });\n            return;\n        }\n        if (index === 'last') {\n            setTimeout(()=>{\n                var _menuRef_current;\n                const all = (_menuRef_current = menuRef.current) == null ? void 0 : _menuRef_current.querySelectorAll('[role=\"menuitem\"]');\n                if (all) {\n                    const lastIndex = all.length - 1;\n                    select(lastIndex);\n                }\n            });\n            return;\n        }\n        const el = (_menuRef_current = menuRef.current) == null ? void 0 : _menuRef_current.querySelector('[data-index=\"' + index + '\"]');\n        if (el) {\n            setSelectedIndex(index);\n            el == null ? void 0 : el.focus();\n        }\n    }\n    function onMenuKeydown(e) {\n        e.preventDefault();\n        switch(e.key){\n            case 'ArrowDown':\n                const next = selectedIndex + 1;\n                select(next);\n                break;\n            case 'ArrowUp':\n                const prev = selectedIndex - 1;\n                select(prev);\n                break;\n            case 'Home':\n                select('first');\n                break;\n            case 'End':\n                select('last');\n                break;\n            default:\n                break;\n        }\n    }\n    function openErrorOverlay() {\n        setOpen(null);\n        if (issueCount > 0) {\n            setIsErrorOverlayOpen(true);\n        }\n    }\n    function toggleErrorOverlay() {\n        setIsErrorOverlayOpen((prev)=>!prev);\n    }\n    function openRootMenu() {\n        setOpen((prevOpen)=>{\n            if (prevOpen === null) select('first');\n            return OVERLAYS.Root;\n        });\n    }\n    function onTriggerClick() {\n        if (open === OVERLAYS.Root) {\n            setOpen(null);\n        } else {\n            openRootMenu();\n            setTimeout(()=>{\n                select('first');\n            });\n        }\n    }\n    function closeMenu() {\n        // Only close when we were on `Root`,\n        // otherwise it will close other overlays\n        setOpen((prevOpen)=>{\n            if (prevOpen === OVERLAYS.Root) {\n                return null;\n            }\n            return prevOpen;\n        });\n    }\n    function handleHideDevtools() {\n        setOpen(null);\n        hide();\n    }\n    const [vertical, horizontal] = position.split('-', 2);\n    const popover = {\n        [vertical]: 'calc(100% + 8px)',\n        [horizontal]: 0\n    };\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_toast.Toast, {\n        \"data-nextjs-toast\": true,\n        style: {\n            '--animate-out-duration-ms': \"\" + _utils.MENU_DURATION_MS + \"ms\",\n            '--animate-out-timing-function': _utils.MENU_CURVE,\n            boxShadow: 'none',\n            zIndex: 2147483647,\n            // Reset the toast component's default positions.\n            bottom: 'initial',\n            left: 'initial',\n            [vertical]: '20px',\n            [horizontal]: '20px'\n        },\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_nextlogo.NextLogo, {\n                ref: triggerRef,\n                \"aria-haspopup\": \"menu\",\n                \"aria-expanded\": isMenuOpen,\n                \"aria-controls\": \"nextjs-dev-tools-menu\",\n                \"aria-label\": \"\" + (isMenuOpen ? 'Close' : 'Open') + \" Next.js Dev Tools\",\n                \"data-nextjs-dev-tools-button\": true,\n                disabled: disabled,\n                issueCount: issueCount,\n                onTriggerClick: onTriggerClick,\n                toggleErrorOverlay: toggleErrorOverlay,\n                isDevBuilding: (0, _initialize.useIsDevBuilding)(),\n                isDevRendering: (0, _devrenderindicator.useIsDevRendering)(),\n                isBuildError: isBuildError\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_routeinfo.RouteInfo, {\n                isOpen: isRouteInfoOpen,\n                close: openRootMenu,\n                triggerRef: triggerRef,\n                style: popover,\n                routerType: routerType,\n                routeType: isStaticRoute ? 'Static' : 'Dynamic'\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_turbopackinfo.TurbopackInfo, {\n                isOpen: isTurbopackInfoOpen,\n                close: openRootMenu,\n                triggerRef: triggerRef,\n                style: popover\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_userpreferences.UserPreferences, {\n                isOpen: isPreferencesOpen,\n                close: openRootMenu,\n                triggerRef: triggerRef,\n                style: popover,\n                hide: handleHideDevtools,\n                setPosition: setPosition,\n                position: position\n            }),\n            menuMounted && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                ref: menuRef,\n                id: \"nextjs-dev-tools-menu\",\n                role: \"menu\",\n                dir: \"ltr\",\n                \"aria-orientation\": \"vertical\",\n                \"aria-label\": \"Next.js Dev Tools Items\",\n                tabIndex: -1,\n                className: \"dev-tools-indicator-menu\",\n                onKeyDown: onMenuKeydown,\n                \"data-rendered\": menuRendered,\n                style: popover,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(Context.Provider, {\n                    value: {\n                        closeMenu,\n                        selectedIndex,\n                        setSelectedIndex\n                    },\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"dev-tools-indicator-inner\",\n                            children: [\n                                issueCount > 0 && /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                    title: issueCount + \" \" + (issueCount === 1 ? 'issue' : 'issues') + \" found. Click to view details in the dev overlay.\",\n                                    index: 0,\n                                    label: \"Issues\",\n                                    value: /*#__PURE__*/ (0, _jsxruntime.jsx)(IssueCount, {\n                                        children: issueCount\n                                    }),\n                                    onClick: openErrorOverlay\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                    title: \"Current route is \" + (isStaticRoute ? 'static' : 'dynamic') + \".\",\n                                    label: \"Route\",\n                                    index: 1,\n                                    value: isStaticRoute ? 'Static' : 'Dynamic',\n                                    onClick: ()=>setOpen(OVERLAYS.Route),\n                                    \"data-nextjs-route-type\": isStaticRoute ? 'static' : 'dynamic'\n                                }),\n                                isTurbopack ? /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                    title: \"Turbopack is enabled.\",\n                                    label: \"Turbopack\",\n                                    value: \"Enabled\"\n                                }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                    index: 2,\n                                    title: \"Learn about Turbopack and how to enable it in your application.\",\n                                    label: \"Try Turbopack\",\n                                    value: /*#__PURE__*/ (0, _jsxruntime.jsx)(ChevronRight, {}),\n                                    onClick: ()=>setOpen(OVERLAYS.Turbo)\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                            className: \"dev-tools-indicator-footer\",\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                \"data-preferences\": true,\n                                label: \"Preferences\",\n                                value: /*#__PURE__*/ (0, _jsxruntime.jsx)(_gearicon.default, {}),\n                                onClick: ()=>setOpen(OVERLAYS.Preferences),\n                                index: isTurbopack ? 2 : 3\n                            })\n                        })\n                    ]\n                })\n            })\n        ]\n    });\n}\n_c1 = DevToolsPopover;\nfunction ChevronRight() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"none\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fill: \"#666\",\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M5.50011 1.93945L6.03044 2.46978L10.8537 7.293C11.2442 7.68353 11.2442 8.31669 10.8537 8.70722L6.03044 13.5304L5.50011 14.0608L4.43945 13.0001L4.96978 12.4698L9.43945 8.00011L4.96978 3.53044L4.43945 3.00011L5.50011 1.93945Z\"\n        })\n    });\n}\n_c2 = ChevronRight;\nfunction MenuItem(param) {\n    let { index, label, value, onClick, href, ...props } = param;\n    const isInteractive = typeof onClick === 'function' || typeof href === 'string';\n    const { closeMenu, selectedIndex, setSelectedIndex } = (0, _react.useContext)(Context);\n    const selected = selectedIndex === index;\n    function click() {\n        if (isInteractive) {\n            onClick == null ? void 0 : onClick();\n            closeMenu();\n            if (href) {\n                window.open(href, '_blank', 'noopener, noreferrer');\n            }\n        }\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        className: \"dev-tools-indicator-item\",\n        \"data-index\": index,\n        \"data-selected\": selected,\n        onClick: click,\n        // Needs `onMouseMove` instead of enter to work together\n        // with keyboard and mouse input\n        onMouseMove: ()=>{\n            if (isInteractive && index !== undefined && selectedIndex !== index) {\n                setSelectedIndex(index);\n            }\n        },\n        onMouseLeave: ()=>setSelectedIndex(-1),\n        onKeyDown: (e)=>{\n            if (e.key === 'Enter' || e.key === ' ') {\n                click();\n            }\n        },\n        role: isInteractive ? 'menuitem' : undefined,\n        tabIndex: selected ? 0 : -1,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                className: \"dev-tools-indicator-label\",\n                children: label\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                className: \"dev-tools-indicator-value\",\n                children: value\n            })\n        ]\n    });\n}\n_c3 = MenuItem;\nfunction IssueCount(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n        className: \"dev-tools-indicator-issue-count\",\n        \"data-has-issues\": children > 0,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                className: \"dev-tools-indicator-issue-count-indicator\"\n            }),\n            children\n        ]\n    });\n}\n_c4 = IssueCount;\nconst DEV_TOOLS_INDICATOR_STYLES = \"\\n  .dev-tools-indicator-menu {\\n    -webkit-font-smoothing: antialiased;\\n    display: flex;\\n    flex-direction: column;\\n    align-items: flex-start;\\n    background: var(--color-background-100);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background-clip: padding-box;\\n    box-shadow: var(--shadow-menu);\\n    border-radius: var(--rounded-xl);\\n    position: absolute;\\n    font-family: var(--font-stack-sans);\\n    z-index: 1000;\\n    overflow: hidden;\\n    opacity: 0;\\n    outline: 0;\\n    min-width: 248px;\\n    transition: opacity var(--animate-out-duration-ms)\\n      var(--animate-out-timing-function);\\n\\n    &[data-rendered='true'] {\\n      opacity: 1;\\n      scale: 1;\\n    }\\n  }\\n\\n  .dev-tools-indicator-inner {\\n    padding: 6px;\\n    width: 100%;\\n  }\\n\\n  .dev-tools-indicator-item {\\n    display: flex;\\n    align-items: center;\\n    padding: 8px 6px;\\n    height: var(--size-36);\\n    border-radius: 6px;\\n    text-decoration: none !important;\\n    user-select: none;\\n    white-space: nowrap;\\n\\n    svg {\\n      width: var(--size-16);\\n      height: var(--size-16);\\n    }\\n\\n    &:focus-visible {\\n      outline: 0;\\n    }\\n  }\\n\\n  .dev-tools-indicator-footer {\\n    background: var(--color-background-200);\\n    padding: 6px;\\n    border-top: 1px solid var(--color-gray-400);\\n    width: 100%;\\n  }\\n\\n  .dev-tools-indicator-item[data-selected='true'] {\\n    cursor: pointer;\\n    background-color: var(--color-gray-200);\\n  }\\n\\n  .dev-tools-indicator-label {\\n    font-size: var(--size-14);\\n    line-height: var(--size-20);\\n    color: var(--color-gray-1000);\\n  }\\n\\n  .dev-tools-indicator-value {\\n    font-size: var(--size-14);\\n    line-height: var(--size-20);\\n    color: var(--color-gray-900);\\n    margin-left: auto;\\n  }\\n\\n  .dev-tools-indicator-issue-count {\\n    --color-primary: var(--color-gray-800);\\n    --color-secondary: var(--color-gray-100);\\n    display: flex;\\n    flex-direction: row;\\n    align-items: center;\\n    justify-content: center;\\n    gap: 8px;\\n    min-width: var(--size-40);\\n    height: var(--size-24);\\n    background: var(--color-background-100);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background-clip: padding-box;\\n    box-shadow: var(--shadow-small);\\n    padding: 2px;\\n    color: var(--color-gray-1000);\\n    border-radius: 128px;\\n    font-weight: 500;\\n    font-size: var(--size-13);\\n    font-variant-numeric: tabular-nums;\\n\\n    &[data-has-issues='true'] {\\n      --color-primary: var(--color-red-800);\\n      --color-secondary: var(--color-red-100);\\n    }\\n\\n    .dev-tools-indicator-issue-count-indicator {\\n      width: var(--size-8);\\n      height: var(--size-8);\\n      background: var(--color-primary);\\n      box-shadow: 0 0 0 2px var(--color-secondary);\\n      border-radius: 50%;\\n    }\\n  }\\n\\n  .dev-tools-indicator-shortcut {\\n    display: flex;\\n    gap: 4px;\\n\\n    kbd {\\n      width: var(--size-20);\\n      height: var(--size-20);\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      border-radius: var(--rounded-md);\\n      border: 1px solid var(--color-gray-400);\\n      font-family: var(--font-stack-sans);\\n      background: var(--color-background-100);\\n      color: var(--color-gray-1000);\\n      text-align: center;\\n      font-size: var(--size-12);\\n      line-height: var(--size-16);\\n    }\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-tools-indicator.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"DevToolsIndicator\");\n$RefreshReg$(_c1, \"DevToolsPopover\");\n$RefreshReg$(_c2, \"ChevronRight\");\n$RefreshReg$(_c3, \"MenuItem\");\n$RefreshReg$(_c4, \"IssueCount\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js ***!
  \**********************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INFO_STYLES: function() {\n        return DEV_TOOLS_INFO_STYLES;\n    },\n    DevToolsInfo: function() {\n        return DevToolsInfo;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _utils = __webpack_require__(/*! ../utils */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js\");\nconst _usedelayedrender = __webpack_require__(/*! ../../../../hooks/use-delayed-render */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-delayed-render.js\");\nfunction DevToolsInfo(param) {\n    let { title, children, learnMoreLink, isOpen, triggerRef, close, ...props } = param;\n    const ref = (0, _react.useRef)(null);\n    const closeButtonRef = (0, _react.useRef)(null);\n    const { mounted, rendered } = (0, _usedelayedrender.useDelayedRender)(isOpen, {\n        // Intentionally no fade in, makes the UI feel more immediate\n        enterDelay: 0,\n        // Graceful fade out to confirm that the UI did not break\n        exitDelay: _utils.MENU_DURATION_MS\n    });\n    (0, _utils.useFocusTrap)(ref, triggerRef, isOpen, ()=>{\n        var _closeButtonRef_current;\n        (_closeButtonRef_current = closeButtonRef.current) == null ? void 0 : _closeButtonRef_current.focus();\n    });\n    (0, _utils.useClickOutside)(ref, triggerRef, isOpen, close);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        tabIndex: -1,\n        role: \"dialog\",\n        ref: ref,\n        \"data-info-popover\": true,\n        ...props,\n        \"data-rendered\": rendered,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n            className: \"dev-tools-info-container\",\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h1\", {\n                    className: \"dev-tools-info-title\",\n                    children: title\n                }),\n                children,\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"dev-tools-info-button-container\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                            ref: closeButtonRef,\n                            className: \"dev-tools-info-close-button\",\n                            onClick: close,\n                            children: \"Close\"\n                        }),\n                        learnMoreLink && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                            className: \"dev-tools-info-learn-more-button\",\n                            href: learnMoreLink,\n                            target: \"_blank\",\n                            rel: \"noreferrer noopener\",\n                            children: \"Learn More\"\n                        })\n                    ]\n                })\n            ]\n        })\n    });\n}\n_c = DevToolsInfo;\nconst DEV_TOOLS_INFO_STYLES = \"\\n  [data-info-popover] {\\n    -webkit-font-smoothing: antialiased;\\n    display: flex;\\n    flex-direction: column;\\n    align-items: flex-start;\\n    background: var(--color-background-100);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background-clip: padding-box;\\n    box-shadow: var(--shadow-menu);\\n    border-radius: var(--rounded-xl);\\n    position: absolute;\\n    font-family: var(--font-stack-sans);\\n    z-index: 1000;\\n    overflow: hidden;\\n    opacity: 0;\\n    outline: 0;\\n    min-width: 350px;\\n    transition: opacity var(--animate-out-duration-ms)\\n      var(--animate-out-timing-function);\\n\\n    &[data-rendered='true'] {\\n      opacity: 1;\\n      scale: 1;\\n    }\\n\\n    button:focus-visible {\\n      outline: var(--focus-ring);\\n    }\\n  }\\n\\n  .dev-tools-info-container {\\n    padding: 12px;\\n  }\\n\\n  .dev-tools-info-title {\\n    padding: 8px 6px;\\n    color: var(--color-gray-1000);\\n    font-size: var(--size-16);\\n    font-weight: 600;\\n    line-height: var(--size-20);\\n    margin: 0;\\n  }\\n\\n  .dev-tools-info-article {\\n    padding: 8px 6px;\\n    color: var(--color-gray-1000);\\n    font-size: var(--size-14);\\n    line-height: var(--size-20);\\n    margin: 0;\\n  }\\n  .dev-tools-info-paragraph {\\n    &:last-child {\\n      margin-bottom: 0;\\n    }\\n  }\\n\\n  .dev-tools-info-button-container {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    padding: 8px 6px;\\n  }\\n\\n  .dev-tools-info-close-button {\\n    padding: 0 8px;\\n    height: var(--size-28);\\n    font-size: var(--size-14);\\n    font-weight: 500;\\n    line-height: var(--size-20);\\n    transition: background var(--duration-short) ease;\\n    color: var(--color-gray-1000);\\n    border-radius: var(--rounded-md-2);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background: var(--color-background-200);\\n  }\\n\\n  .dev-tools-info-close-button:hover {\\n    background: var(--color-gray-400);\\n  }\\n\\n  .dev-tools-info-learn-more-button {\\n    align-content: center;\\n    padding: 0 8px;\\n    height: var(--size-28);\\n    font-size: var(--size-14);\\n    font-weight: 500;\\n    line-height: var(--size-20);\\n    transition: background var(--duration-short) ease;\\n    color: var(--color-background-100);\\n    border-radius: var(--rounded-md-2);\\n    background: var(--color-gray-1000);\\n  }\\n\\n  .dev-tools-info-learn-more-button:hover {\\n    text-decoration: none;\\n    color: var(--color-background-100);\\n    opacity: 0.9;\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-tools-info.js.map\nvar _c;\n$RefreshReg$(_c, \"DevToolsInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js ***!
  \******************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INFO_ROUTE_INFO_STYLES: function() {\n        return DEV_TOOLS_INFO_ROUTE_INFO_STYLES;\n    },\n    RouteInfo: function() {\n        return RouteInfo;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _devtoolsinfo = __webpack_require__(/*! ./dev-tools-info */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js\");\nfunction StaticRouteContent(param) {\n    let { routerType } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"article\", {\n        className: \"dev-tools-info-article\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: [\n                    \"The path\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                        className: \"dev-tools-info-code\",\n                        children: window.location.pathname\n                    }),\n                    ' ',\n                    'is marked as \"static\" since it will be prerendered during the build time.'\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: [\n                    \"With Static Rendering, routes are rendered at build time, or in the background after\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        className: \"dev-tools-info-link\",\n                        href: routerType === 'pages' ? 'https://nextjs.org/docs/pages/building-your-application/data-fetching/incremental-static-regeneration' : \"https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: \"data revalidation\"\n                    }),\n                    \".\"\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: \"Static rendering is useful when a route has data that is not personalized to the user and can be known at build time, such as a static blog post or a product page.\"\n            })\n        ]\n    });\n}\n_c = StaticRouteContent;\nfunction DynamicRouteContent(param) {\n    let { routerType } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"article\", {\n        className: \"dev-tools-info-article\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: [\n                    \"The path\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                        className: \"dev-tools-info-code\",\n                        children: window.location.pathname\n                    }),\n                    ' ',\n                    'is marked as \"dynamic\" since it will be rendered for each user at',\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"strong\", {\n                        children: \"request time\"\n                    }),\n                    \".\"\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: \"Dynamic rendering is useful when a route has data that is personalized to the user or has information that can only be known at request time, such as cookies or the URL's search params.\"\n            }),\n            routerType === 'pages' ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-pagraph\",\n                children: [\n                    \"Exporting the\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        className: \"dev-tools-info-link\",\n                        href: \"https://nextjs.org/docs/pages/building-your-application/data-fetching/get-server-side-props\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: \"getServerSideProps\"\n                    }),\n                    ' ',\n                    \"function will opt the route into dynamic rendering. This function will be called by the server on every request.\"\n                ]\n            }) : /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: [\n                    \"During rendering, if a\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        className: \"dev-tools-info-link\",\n                        href: \"https://nextjs.org/docs/app/building-your-application/rendering/server-components#dynamic-apis\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: \"Dynamic API\"\n                    }),\n                    ' ',\n                    \"or a\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        className: \"dev-tools-info-link\",\n                        href: \"https://nextjs.org/docs/app/api-reference/functions/fetch\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: \"fetch\"\n                    }),\n                    ' ',\n                    \"option of\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                        className: \"dev-tools-info-code\",\n                        children: \"{ cache: 'no-store' }\"\n                    }),\n                    ' ',\n                    \"is discovered, Next.js will switch to dynamically rendering the whole route.\"\n                ]\n            })\n        ]\n    });\n}\n_c1 = DynamicRouteContent;\nconst learnMoreLink = {\n    pages: {\n        static: 'https://nextjs.org/docs/pages/building-your-application/rendering/static-site-generation',\n        dynamic: 'https://nextjs.org/docs/pages/building-your-application/rendering/server-side-rendering'\n    },\n    app: {\n        static: 'https://nextjs.org/docs/app/building-your-application/rendering/server-components#static-rendering-default',\n        dynamic: 'https://nextjs.org/docs/app/building-your-application/rendering/server-components#dynamic-rendering'\n    }\n};\nfunction RouteInfo(param) {\n    let { routeType, routerType, ...props } = param;\n    const isStaticRoute = routeType === 'Static';\n    const learnMore = isStaticRoute ? learnMoreLink[routerType].static : learnMoreLink[routerType].dynamic;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_devtoolsinfo.DevToolsInfo, {\n        title: \"\" + routeType + \" Route\",\n        learnMoreLink: learnMore,\n        ...props,\n        children: isStaticRoute ? /*#__PURE__*/ (0, _jsxruntime.jsx)(StaticRouteContent, {\n            routerType: routerType\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(DynamicRouteContent, {\n            routerType: routerType\n        })\n    });\n}\n_c2 = RouteInfo;\nconst DEV_TOOLS_INFO_ROUTE_INFO_STYLES = \"\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=route-info.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"StaticRouteContent\");\n$RefreshReg$(_c1, \"DynamicRouteContent\");\n$RefreshReg$(_c2, \"RouteInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js ***!
  \**********************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES: function() {\n        return DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES;\n    },\n    TurbopackInfo: function() {\n        return TurbopackInfo;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _devtoolsinfo = __webpack_require__(/*! ./dev-tools-info */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js\");\nconst _copybutton = __webpack_require__(/*! ../../../copy-button */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/copy-button/index.js\");\nfunction TurbopackInfo(props) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_devtoolsinfo.DevToolsInfo, {\n        title: \"Turbopack\",\n        learnMoreLink: \"https://nextjs.org/docs/app/api-reference/turbopack\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"article\", {\n                className: \"dev-tools-info-article\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        className: \"dev-tools-info-paragraph\",\n                        children: [\n                            \"Turbopack is an incremental bundler optimized for JavaScript and TypeScript, written in Rust, and built into Next.js. Turbopack can be used in Next.js in both the\",\n                            ' ',\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                className: \"dev-tools-info-code\",\n                                children: \"pages\"\n                            }),\n                            \" and\",\n                            ' ',\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                className: \"dev-tools-info-code\",\n                                children: \"app\"\n                            }),\n                            \" directories for faster local development.\"\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        className: \"dev-tools-info-paragraph\",\n                        children: [\n                            \"To enable Turbopack, use the\",\n                            ' ',\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                className: \"dev-tools-info-code\",\n                                children: \"--turbopack\"\n                            }),\n                            \" flag when running the Next.js development server.\"\n                        ]\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                className: \"dev-tools-info-code-block-container\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"dev-tools-info-code-block\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_copybutton.CopyButton, {\n                            actionLabel: \"Copy Next.js Turbopack Command\",\n                            successLabel: \"Next.js Turbopack Command Copied\",\n                            content: '--turbopack',\n                            className: \"dev-tools-info-copy-button\"\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"pre\", {\n                            className: \"dev-tools-info-code-block-pre\",\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"code\", {\n                                children: [\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '  '\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '{'\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: [\n                                            '  ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"scripts\"'\n                                            }),\n                                            \": \",\n                                            '{'\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line dev-tools-info-highlight\",\n                                        children: [\n                                            '    ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"dev\"'\n                                            }),\n                                            \":\",\n                                            ' ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-value\",\n                                                children: '\"next dev --turbopack\"'\n                                            }),\n                                            \",\"\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: [\n                                            '    ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"build\"'\n                                            }),\n                                            \":\",\n                                            ' ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-value\",\n                                                children: '\"next build\"'\n                                            }),\n                                            \",\"\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: [\n                                            '    ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"start\"'\n                                            }),\n                                            \":\",\n                                            ' ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-value\",\n                                                children: '\"next start\"'\n                                            }),\n                                            \",\"\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: [\n                                            '    ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"lint\"'\n                                            }),\n                                            \":\",\n                                            ' ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-value\",\n                                                children: '\"next lint\"'\n                                            })\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '  }'\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '}'\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '  '\n                                    })\n                                ]\n                            })\n                        })\n                    ]\n                })\n            })\n        ]\n    });\n}\n_c = TurbopackInfo;\nconst DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES = \"\\n  .dev-tools-info-code {\\n    background: var(--color-gray-400);\\n    color: var(--color-gray-1000);\\n    font-family: var(--font-stack-monospace);\\n    padding: 2px 4px;\\n    margin: 0;\\n    font-size: var(--size-13);\\n    white-space: break-spaces;\\n    border-radius: var(--rounded-md-2);\\n  }\\n\\n  .dev-tools-info-code-block-container {\\n    padding: 6px;\\n  }\\n\\n  .dev-tools-info-code-block {\\n    position: relative;\\n    background: var(--color-background-200);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    border-radius: var(--rounded-md-2);\\n    min-width: 326px;\\n  }\\n\\n  .dev-tools-info-code-block-pre {\\n    margin: 0;\\n    font-family: var(--font-stack-monospace);\\n    font-size: var(--size-12);\\n  }\\n\\n  .dev-tools-info-copy-button {\\n    position: absolute;\\n\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    right: 8px;\\n    top: 8px;\\n    padding: 4px;\\n    height: var(--size-24);\\n    width: var(--size-24);\\n    border-radius: var(--rounded-md-2);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background: var(--color-background-100);\\n  }\\n\\n  .dev-tools-info-code-block-line {\\n    display: block;\\n    line-height: 1.5;\\n    padding: 0 16px;\\n  }\\n\\n  .dev-tools-info-code-block-line.dev-tools-info-highlight {\\n    border-left: 2px solid var(--color-blue-900);\\n    background: var(--color-blue-400);\\n  }\\n\\n  .dev-tools-info-code-block-json-key {\\n    color: var(--color-syntax-keyword);\\n  }\\n\\n  .dev-tools-info-code-block-json-value {\\n    color: var(--color-syntax-link);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=turbopack-info.js.map\nvar _c;\n$RefreshReg$(_c, \"TurbopackInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js ***!
  \************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INFO_USER_PREFERENCES_STYLES: function() {\n        return DEV_TOOLS_INFO_USER_PREFERENCES_STYLES;\n    },\n    UserPreferences: function() {\n        return UserPreferences;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _css = __webpack_require__(/*! ../../../../../utils/css */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nconst _eyeicon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../../icons/eye-icon */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/eye-icon.js\"));\nconst _shared = __webpack_require__(/*! ../../../../../shared */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _lighticon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../../icons/light-icon */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/light-icon.js\"));\nconst _darkicon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../../icons/dark-icon */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/dark-icon.js\"));\nconst _systemicon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../../icons/system-icon */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/system-icon.js\"));\nconst _devtoolsinfo = __webpack_require__(/*! ./dev-tools-info */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  .preferences-container {\\n    padding: 8px 6px;\\n    width: 100%;\\n  }\\n\\n  @media (min-width: 576px) {\\n    .preferences-container {\\n      width: 480px;\\n    }\\n  }\\n\\n  .preference-section:first-child {\\n    padding-top: 0;\\n  }\\n\\n  .preference-section {\\n    padding: 12px 0;\\n    border-bottom: 1px solid var(--color-gray-400);\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    gap: 24px;\\n  }\\n\\n  .preference-section:last-child {\\n    border-bottom: none;\\n  }\\n\\n  .preference-header {\\n    margin-bottom: 0;\\n    flex: 1;\\n  }\\n\\n  .preference-header label {\\n    font-size: var(--size-14);\\n    font-weight: 500;\\n    color: var(--color-gray-1000);\\n    margin: 0;\\n  }\\n\\n  .preference-description {\\n    color: var(--color-gray-900);\\n    font-size: var(--size-14);\\n    margin: 0;\\n  }\\n\\n  .preference-icon {\\n    display: flex;\\n    align-items: center;\\n    width: 16px;\\n    height: 16px;\\n  }\\n\\n  .select-button,\\n  .action-button {\\n    display: flex;\\n    align-items: center;\\n    gap: 8px;\\n    background: var(--color-background-100);\\n    border: 1px solid var(--color-gray-400);\\n    border-radius: var(--rounded-lg);\\n    font-weight: 400;\\n    font-size: var(--size-14);\\n    color: var(--color-gray-1000);\\n    padding: 6px 8px;\\n\\n    &:hover {\\n      background: var(--color-gray-100);\\n    }\\n  }\\n\\n  .preference-control-select {\\n    padding: 6px 8px;\\n    display: flex;\\n    align-items: center;\\n    gap: 8px;\\n    border-radius: var(--rounded-lg);\\n    border: 1px solid var(--color-gray-400);\\n\\n    &:hover {\\n      background: var(--color-gray-100);\\n    }\\n\\n    &:focus-within {\\n      outline: var(--focus-ring);\\n    }\\n  }\\n\\n  .preference-control-select select {\\n    font-size: var(--size-14);\\n    font-weight: 400;\\n    border: none;\\n    padding: 0 6px 0 0;\\n    border-radius: 0;\\n    outline: none;\\n    background: none;\\n  }\\n\\n  :global(.icon) {\\n    width: 18px;\\n    height: 18px;\\n    color: #666;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction getInitialPreference() {\n    if (typeof localStorage === 'undefined') {\n        return 'system';\n    }\n    const theme = localStorage.getItem(_shared.STORAGE_KEY_THEME);\n    return theme === 'dark' || theme === 'light' ? theme : 'system';\n}\nfunction UserPreferences(param) {\n    let { setPosition, position, hide, ...props } = param;\n    // derive initial theme from system preference\n    const [theme, setTheme] = (0, _react.useState)(getInitialPreference());\n    const handleThemeChange = (e)=>{\n        const portal = document.querySelector('nextjs-portal');\n        if (!portal) return;\n        setTheme(e.target.value);\n        if (e.target.value === 'system') {\n            portal.classList.remove('dark');\n            portal.classList.remove('light');\n            localStorage.removeItem(_shared.STORAGE_KEY_THEME);\n            return;\n        }\n        if (e.target.value === 'dark') {\n            portal.classList.add('dark');\n            portal.classList.remove('light');\n            localStorage.setItem(_shared.STORAGE_KEY_THEME, 'dark');\n        } else {\n            portal.classList.remove('dark');\n            portal.classList.add('light');\n            localStorage.setItem(_shared.STORAGE_KEY_THEME, 'light');\n        }\n    };\n    function handlePositionChange(e) {\n        setPosition(e.target.value);\n        localStorage.setItem(_shared.STORAGE_KEY_POSITION, e.target.value);\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_devtoolsinfo.DevToolsInfo, {\n        title: \"Preferences\",\n        ...props,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n            className: \"preferences-container\",\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"preference-section\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"preference-header\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"label\", {\n                                    htmlFor: \"theme\",\n                                    children: \"Theme\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    className: \"preference-description\",\n                                    children: \"Select your theme preference.\"\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"preference-control-select\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                    className: \"preference-icon\",\n                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(ThemeIcon, {\n                                        theme: theme\n                                    })\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"select\", {\n                                    id: \"theme\",\n                                    name: \"theme\",\n                                    className: \"select-button\",\n                                    value: theme,\n                                    onChange: handleThemeChange,\n                                    children: [\n                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                            value: \"system\",\n                                            children: \"System\"\n                                        }),\n                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                            value: \"light\",\n                                            children: \"Light\"\n                                        }),\n                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                            value: \"dark\",\n                                            children: \"Dark\"\n                                        })\n                                    ]\n                                })\n                            ]\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"preference-section\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"preference-header\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"label\", {\n                                    htmlFor: \"position\",\n                                    children: \"Position\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    className: \"preference-description\",\n                                    children: \"Adjust the placement of your dev tools.\"\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                            className: \"preference-control-select\",\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"select\", {\n                                id: \"position\",\n                                name: \"position\",\n                                className: \"select-button\",\n                                value: position,\n                                onChange: handlePositionChange,\n                                children: [\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                        value: \"bottom-left\",\n                                        children: \"Bottom Left\"\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                        value: \"bottom-right\",\n                                        children: \"Bottom Right\"\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                        value: \"top-left\",\n                                        children: \"Top Left\"\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                        value: \"top-right\",\n                                        children: \"Top Right\"\n                                    })\n                                ]\n                            })\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"preference-section\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"preference-header\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"label\", {\n                                    htmlFor: \"hide-dev-tools\",\n                                    children: \"Hide Dev Tools for this session\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    className: \"preference-description\",\n                                    children: \"Hide Dev Tools until you restart your dev server, or 1 day.\"\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                            className: \"preference-control\",\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"button\", {\n                                id: \"hide-dev-tools\",\n                                name: \"hide-dev-tools\",\n                                \"data-hide-dev-tools\": true,\n                                className: \"action-button\",\n                                onClick: hide,\n                                children: [\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"preference-icon\",\n                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_eyeicon.default, {})\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                        children: \"Hide\"\n                                    })\n                                ]\n                            })\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                    className: \"preference-section\",\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                        className: \"preference-header\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"label\", {\n                                children: \"Disable Dev Tools for this project\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                                className: \"preference-description\",\n                                children: [\n                                    \"To disable this UI completely, set\",\n                                    ' ',\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                        className: \"dev-tools-info-code\",\n                                        children: \"devIndicators: false\"\n                                    }),\n                                    ' ',\n                                    \"in your \",\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                        className: \"dev-tools-info-code\",\n                                        children: \"next.config\"\n                                    }),\n                                    ' ',\n                                    \"file.\"\n                                ]\n                            })\n                        ]\n                    })\n                })\n            ]\n        })\n    });\n}\n_c = UserPreferences;\nfunction ThemeIcon(param) {\n    let { theme } = param;\n    switch(theme){\n        case 'system':\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_systemicon.default, {});\n        case 'dark':\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_darkicon.default, {});\n        case 'light':\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lighticon.default, {});\n        default:\n            return null;\n    }\n}\n_c1 = ThemeIcon;\nconst DEV_TOOLS_INFO_USER_PREFERENCES_STYLES = (0, _css.css)(_templateObject());\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=user-preferences.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"UserPreferences\");\n$RefreshReg$(_c1, \"ThemeIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.js ***!
  \**************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Cross: function() {\n        return Cross;\n    },\n    NextLogo: function() {\n        return NextLogo;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _css = __webpack_require__(/*! ../../../../utils/css */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nconst _mergerefs = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../utils/merge-refs */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/merge-refs.js\"));\nconst _useminimumloadingtimemultiple = __webpack_require__(/*! ./use-minimum-loading-time-multiple */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n          [data-next-badge-root] {\\n            --timing: cubic-bezier(0.23, 0.88, 0.26, 0.92);\\n            --duration-long: 250ms;\\n            --color-outer-border: #171717;\\n            --color-inner-border: hsla(0, 0%, 100%, 0.14);\\n            --color-hover-alpha-subtle: hsla(0, 0%, 100%, 0.13);\\n            --color-hover-alpha-error: hsla(0, 0%, 100%, 0.2);\\n            --color-hover-alpha-error-2: hsla(0, 0%, 100%, 0.25);\\n            --mark-size: calc(var(--size) - var(--size-2) * 2);\\n\\n            --focus-color: var(--color-blue-800);\\n            --focus-ring: 2px solid var(--focus-color);\\n\\n            &:has([data-next-badge][data-error='true']) {\\n              --focus-color: #fff;\\n            }\\n          }\\n\\n          [data-disabled-icon] {\\n            display: flex;\\n            align-items: center;\\n            justify-content: center;\\n            padding-right: 4px;\\n          }\\n\\n          [data-next-badge] {\\n            -webkit-font-smoothing: antialiased;\\n            width: var(--size);\\n            height: var(--size);\\n            display: flex;\\n            align-items: center;\\n            position: relative;\\n            background: rgba(0, 0, 0, 0.8);\\n            box-shadow:\\n              0 0 0 1px var(--color-outer-border),\\n              inset 0 0 0 1px var(--color-inner-border),\\n              0px 16px 32px -8px rgba(0, 0, 0, 0.24);\\n            backdrop-filter: blur(48px);\\n            border-radius: var(--rounded-full);\\n            user-select: none;\\n            cursor: pointer;\\n            scale: 1;\\n            overflow: hidden;\\n            will-change: scale, box-shadow, width, background;\\n            transition:\\n              scale var(--duration-short) var(--timing),\\n              width var(--duration-long) var(--timing),\\n              box-shadow var(--duration-long) var(--timing),\\n              background var(--duration-short) ease;\\n\\n            &:active[data-error='false'] {\\n              scale: 0.95;\\n            }\\n\\n            &[data-animate='true']:not(:hover) {\\n              scale: 1.02;\\n            }\\n\\n            &[data-error='false']:has([data-next-mark]:focus-visible) {\\n              outline: var(--focus-ring);\\n              outline-offset: 3px;\\n            }\\n\\n            &[data-error='true'] {\\n              background: #ca2a30;\\n              --color-inner-border: #e5484d;\\n\\n              [data-next-mark] {\\n                background: var(--color-hover-alpha-error);\\n                outline-offset: 0px;\\n\\n                &:focus-visible {\\n                  outline: var(--focus-ring);\\n                  outline-offset: -1px;\\n                }\\n\\n                &:hover {\\n                  background: var(--color-hover-alpha-error-2);\\n                }\\n              }\\n            }\\n\\n            &[data-error-expanded='false'][data-error='true'] ~ [data-dot] {\\n              scale: 1;\\n            }\\n\\n            > div {\\n              display: flex;\\n            }\\n          }\\n\\n          [data-issues-collapse]:focus-visible {\\n            outline: var(--focus-ring);\\n          }\\n\\n          [data-issues]:has([data-issues-open]:focus-visible) {\\n            outline: var(--focus-ring);\\n            outline-offset: -1px;\\n          }\\n\\n          [data-dot] {\\n            content: '';\\n            width: var(--size-8);\\n            height: var(--size-8);\\n            background: #fff;\\n            box-shadow: 0 0 0 1px var(--color-outer-border);\\n            border-radius: 50%;\\n            position: absolute;\\n            top: 2px;\\n            right: 0px;\\n            scale: 0;\\n            pointer-events: none;\\n            transition: scale 200ms var(--timing);\\n            transition-delay: var(--duration-short);\\n          }\\n\\n          [data-issues] {\\n            --padding-left: 8px;\\n            display: flex;\\n            gap: 2px;\\n            align-items: center;\\n            padding-left: 8px;\\n            padding-right: 8px;\\n            height: var(--size-32);\\n            margin: 0 2px;\\n            border-radius: var(--rounded-full);\\n            transition: background var(--duration-short) ease;\\n\\n            &:has([data-issues-open]:hover) {\\n              background: var(--color-hover-alpha-error);\\n            }\\n\\n            &:has([data-issues-collapse]) {\\n              padding-right: calc(var(--padding-left) / 2);\\n            }\\n\\n            [data-cross] {\\n              translate: 0px -1px;\\n            }\\n          }\\n\\n          [data-issues-open] {\\n            font-size: var(--size-13);\\n            color: white;\\n            width: fit-content;\\n            height: 100%;\\n            display: flex;\\n            gap: 2px;\\n            align-items: center;\\n            margin: 0;\\n            line-height: var(--size-36);\\n            font-weight: 500;\\n            z-index: 2;\\n            white-space: nowrap;\\n\\n            &:focus-visible {\\n              outline: 0;\\n            }\\n          }\\n\\n          [data-issues-collapse] {\\n            width: var(--size-24);\\n            height: var(--size-24);\\n            border-radius: var(--rounded-full);\\n            transition: background var(--duration-short) ease;\\n\\n            &:hover {\\n              background: var(--color-hover-alpha-error);\\n            }\\n          }\\n\\n          [data-cross] {\\n            color: #fff;\\n            width: var(--size-12);\\n            height: var(--size-12);\\n          }\\n\\n          [data-next-mark] {\\n            width: var(--mark-size);\\n            height: var(--mark-size);\\n            margin-left: 2px;\\n            display: flex;\\n            align-items: center;\\n            border-radius: var(--rounded-full);\\n            transition: background var(--duration-long) var(--timing);\\n\\n            &:focus-visible {\\n              outline: 0;\\n            }\\n\\n            &:hover {\\n              background: var(--color-hover-alpha-subtle);\\n            }\\n\\n            svg {\\n              flex-shrink: 0;\\n              width: var(--size-40);\\n              height: var(--size-40);\\n            }\\n          }\\n\\n          [data-issues-count-animation] {\\n            display: grid;\\n            place-items: center center;\\n            font-variant-numeric: tabular-nums;\\n\\n            &[data-animate='false'] {\\n              [data-issues-count-exit],\\n              [data-issues-count-enter] {\\n                animation-duration: 0ms;\\n              }\\n            }\\n\\n            > * {\\n              grid-area: 1 / 1;\\n            }\\n\\n            [data-issues-count-exit] {\\n              animation: fadeOut 300ms var(--timing) forwards;\\n            }\\n\\n            [data-issues-count-enter] {\\n              animation: fadeIn 300ms var(--timing) forwards;\\n            }\\n          }\\n\\n          [data-issues-count-plural] {\\n            display: inline-block;\\n            &[data-animate='true'] {\\n              animation: fadeIn 300ms var(--timing) forwards;\\n            }\\n          }\\n\\n          .path0 {\\n            animation: draw0 1.5s ease-in-out infinite;\\n          }\\n\\n          .path1 {\\n            animation: draw1 1.5s ease-out infinite;\\n            animation-delay: 0.3s;\\n          }\\n\\n          .paused {\\n            stroke-dashoffset: 0;\\n          }\\n\\n          @keyframes fadeIn {\\n            0% {\\n              opacity: 0;\\n              filter: blur(2px);\\n              transform: translateY(8px);\\n            }\\n            100% {\\n              opacity: 1;\\n              filter: blur(0px);\\n              transform: translateY(0);\\n            }\\n          }\\n\\n          @keyframes fadeOut {\\n            0% {\\n              opacity: 1;\\n              filter: blur(0px);\\n              transform: translateY(0);\\n            }\\n            100% {\\n              opacity: 0;\\n              transform: translateY(-12px);\\n              filter: blur(2px);\\n            }\\n          }\\n\\n          @keyframes draw0 {\\n            0%,\\n            25% {\\n              stroke-dashoffset: -29.6;\\n            }\\n            25%,\\n            50% {\\n              stroke-dashoffset: 0;\\n            }\\n            50%,\\n            75% {\\n              stroke-dashoffset: 0;\\n            }\\n            75%,\\n            100% {\\n              stroke-dashoffset: 29.6;\\n            }\\n          }\\n\\n          @keyframes draw1 {\\n            0%,\\n            20% {\\n              stroke-dashoffset: -11.6;\\n            }\\n            20%,\\n            50% {\\n              stroke-dashoffset: 0;\\n            }\\n            50%,\\n            75% {\\n              stroke-dashoffset: 0;\\n            }\\n            75%,\\n            100% {\\n              stroke-dashoffset: 11.6;\\n            }\\n          }\\n\\n          @media (prefers-reduced-motion) {\\n            [data-issues-count-exit],\\n            [data-issues-count-enter],\\n            [data-issues-count-plural] {\\n              animation-duration: 0ms !important;\\n            }\\n          }\\n        \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst SIZE = '2.25rem' // 36px in 16px base\n;\nconst SIZE_PX = 36;\nconst SHORT_DURATION_MS = 150;\nconst NextLogo = /*#__PURE__*/ (0, _react.forwardRef)(_s(function NextLogo(param, propRef) {\n    _s();\n    let { disabled, issueCount, isDevBuilding, isDevRendering, isBuildError, onTriggerClick, toggleErrorOverlay, ...props } = param;\n    const hasError = issueCount > 0;\n    const [isErrorExpanded, setIsErrorExpanded] = (0, _react.useState)(hasError);\n    const [dismissed, setDismissed] = (0, _react.useState)(false);\n    const newErrorDetected = useUpdateAnimation(issueCount, SHORT_DURATION_MS);\n    const triggerRef = (0, _react.useRef)(null);\n    const ref = (0, _react.useRef)(null);\n    const [measuredWidth, pristine] = useMeasureWidth(ref);\n    const isLoading = (0, _useminimumloadingtimemultiple.useMinimumLoadingTimeMultiple)(isDevBuilding || isDevRendering);\n    const isExpanded = isErrorExpanded || disabled;\n    const style = (0, _react.useMemo)(()=>{\n        let width = SIZE;\n        // Animates the badge, if expanded\n        if (measuredWidth > SIZE_PX) width = measuredWidth;\n        // No animations on page load, assume the intrinsic width immediately\n        if (pristine && hasError) width = 'auto';\n        // Default state, collapsed\n        return {\n            width\n        };\n    }, [\n        measuredWidth,\n        pristine,\n        hasError\n    ]);\n    (0, _react.useEffect)(()=>{\n        setIsErrorExpanded(hasError);\n    }, [\n        hasError\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-next-badge-root\": true,\n        style: {\n            '--size': SIZE,\n            '--duration-short': \"\" + SHORT_DURATION_MS + \"ms\",\n            // if the indicator is disabled, hide the badge\n            // also allow the \"disabled\" state be dismissed, as long as there are no build errors\n            display: disabled && (!hasError || dismissed) ? 'none' : 'block'\n        },\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                children: (0, _css.css)(_templateObject())\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"data-next-badge\": true,\n                \"data-error\": hasError,\n                \"data-error-expanded\": isExpanded,\n                \"data-animate\": newErrorDetected,\n                style: style,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    ref: ref,\n                    children: [\n                        !disabled && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                            ref: (0, _mergerefs.default)(triggerRef, propRef),\n                            \"data-next-mark\": true,\n                            \"data-next-mark-loading\": isLoading,\n                            onClick: onTriggerClick,\n                            ...props,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(NextMark, {\n                                isLoading: isLoading,\n                                isDevBuilding: isDevBuilding\n                            })\n                        }),\n                        isExpanded && /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            \"data-issues\": true,\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"button\", {\n                                    \"data-issues-open\": true,\n                                    \"aria-label\": \"Open issues overlay\",\n                                    onClick: toggleErrorOverlay,\n                                    children: [\n                                        disabled && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                            \"data-disabled-icon\": true,\n                                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Warning, {})\n                                        }),\n                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(AnimateCount, {\n                                            animate: newErrorDetected,\n                                            \"data-issues-count-animation\": true,\n                                            children: issueCount\n                                        }, issueCount),\n                                        ' ',\n                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                            children: [\n                                                \"Issue\",\n                                                issueCount > 1 && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                    \"aria-hidden\": true,\n                                                    \"data-issues-count-plural\": true,\n                                                    // This only needs to animate once the count changes from 1 -> 2,\n                                                    // otherwise it should stay static between re-renders.\n                                                    \"data-animate\": newErrorDetected && issueCount === 2,\n                                                    children: \"s\"\n                                                })\n                                            ]\n                                        })\n                                    ]\n                                }),\n                                !isBuildError && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                                    \"data-issues-collapse\": true,\n                                    \"aria-label\": \"Collapse issues badge\",\n                                    onClick: ()=>{\n                                        var _triggerRef_current;\n                                        if (disabled) {\n                                            setDismissed(true);\n                                        } else {\n                                            setIsErrorExpanded(false);\n                                        }\n                                        (_triggerRef_current = triggerRef.current) == null ? void 0 : _triggerRef_current.focus();\n                                    },\n                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Cross, {\n                                        \"data-cross\": true\n                                    })\n                                })\n                            ]\n                        })\n                    ]\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"aria-hidden\": true,\n                \"data-dot\": true\n            })\n        ]\n    });\n}, \"jkUnATqmh1rTMIYx3BahGx7X0Qc=\", false, function() {\n    return [\n        useUpdateAnimation,\n        useMeasureWidth\n    ];\n}));\nfunction AnimateCount(param) {\n    let { children: count, animate = true, ...props } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        ...props,\n        \"data-animate\": animate,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"aria-hidden\": true,\n                \"data-issues-count-exit\": true,\n                children: count - 1\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"data-issues-count\": true,\n                \"data-issues-count-enter\": true,\n                children: count\n            })\n        ]\n    });\n}\n_c = AnimateCount;\nfunction useMeasureWidth(ref) {\n    const [width, setWidth] = (0, _react.useState)(0);\n    const [pristine, setPristine] = (0, _react.useState)(true);\n    (0, _react.useEffect)(()=>{\n        const el = ref.current;\n        if (!el) {\n            return;\n        }\n        const observer = new ResizeObserver(()=>{\n            const { width: w } = el.getBoundingClientRect();\n            setWidth((prevWidth)=>{\n                if (prevWidth !== 0) {\n                    setPristine(false);\n                }\n                return w;\n            });\n        });\n        observer.observe(el);\n        return ()=>observer.disconnect();\n    }, [\n        ref\n    ]);\n    return [\n        width,\n        pristine\n    ];\n}\nfunction useUpdateAnimation(issueCount, animationDurationMs) {\n    if (animationDurationMs === void 0) animationDurationMs = 0;\n    const lastUpdatedTimeStamp = (0, _react.useRef)(null);\n    const [animate, setAnimate] = (0, _react.useState)(false);\n    (0, _react.useEffect)(()=>{\n        if (issueCount > 0) {\n            const deltaMs = lastUpdatedTimeStamp.current ? Date.now() - lastUpdatedTimeStamp.current : -1;\n            lastUpdatedTimeStamp.current = Date.now();\n            // We don't animate if `issueCount` changes too quickly\n            if (deltaMs <= animationDurationMs) {\n                return;\n            }\n            setAnimate(true);\n            // It is important to use a CSS transitioned state, not a CSS keyframed animation\n            // because if the issue count increases faster than the animation duration, it\n            // will abruptly stop and not transition smoothly back to its original state.\n            const timeoutId = window.setTimeout(()=>{\n                setAnimate(false);\n            }, animationDurationMs);\n            return ()=>{\n                clearTimeout(timeoutId);\n            };\n        }\n    }, [\n        issueCount,\n        animationDurationMs\n    ]);\n    return animate;\n}\nfunction NextMark(param) {\n    let { isLoading, isDevBuilding } = param;\n    const strokeColor = isDevBuilding ? 'rgba(255,255,255,0.7)' : 'white';\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n        width: \"40\",\n        height: \"40\",\n        viewBox: \"0 0 40 40\",\n        fill: \"none\",\n        \"data-next-mark-loading\": isLoading,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"g\", {\n                transform: \"translate(8.5, 13)\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        className: isLoading ? 'path0' : 'paused',\n                        d: \"M13.3 15.2 L2.34 1 V12.6\",\n                        fill: \"none\",\n                        stroke: \"url(#next_logo_paint0_linear_1357_10853)\",\n                        strokeWidth: \"1.86\",\n                        mask: \"url(#next_logo_mask0)\",\n                        strokeDasharray: \"29.6\",\n                        strokeDashoffset: \"29.6\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        className: isLoading ? 'path1' : 'paused',\n                        d: \"M11.825 1.5 V13.1\",\n                        strokeWidth: \"1.86\",\n                        stroke: \"url(#next_logo_paint1_linear_1357_10853)\",\n                        strokeDasharray: \"11.6\",\n                        strokeDashoffset: \"11.6\"\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"defs\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"next_logo_paint0_linear_1357_10853\",\n                        x1: \"9.95555\",\n                        y1: \"11.1226\",\n                        x2: \"15.4778\",\n                        y2: \"17.9671\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                stopColor: strokeColor\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"0.604072\",\n                                stopColor: strokeColor,\n                                stopOpacity: \"0\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: strokeColor,\n                                stopOpacity: \"0\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"next_logo_paint1_linear_1357_10853\",\n                        x1: \"11.8222\",\n                        y1: \"1.40039\",\n                        x2: \"11.791\",\n                        y2: \"9.62542\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                stopColor: strokeColor\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: strokeColor,\n                                stopOpacity: \"0\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"mask\", {\n                        id: \"next_logo_mask0\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"rect\", {\n                                width: \"100%\",\n                                height: \"100%\",\n                                fill: \"white\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"rect\", {\n                                width: \"5\",\n                                height: \"1.5\",\n                                fill: \"black\"\n                            })\n                        ]\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c1 = NextMark;\nfunction Warning() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"12\",\n        height: \"12\",\n        viewBox: \"0 0 12 12\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M3.98071 1.125L1.125 3.98071L1.125 8.01929L3.98071 10.875H8.01929L10.875 8.01929V3.98071L8.01929 1.125H3.98071ZM3.82538 0C3.62647 0 3.4357 0.0790176 3.29505 0.21967L0.21967 3.29505C0.0790176 3.4357 0 3.62647 0 3.82538V8.17462C0 8.37353 0.0790178 8.5643 0.21967 8.70495L3.29505 11.7803C3.4357 11.921 3.62647 12 3.82538 12H8.17462C8.37353 12 8.5643 11.921 8.70495 11.7803L11.7803 8.70495C11.921 8.5643 12 8.37353 12 8.17462V3.82538C12 3.62647 11.921 3.4357 11.7803 3.29505L8.70495 0.21967C8.5643 0.0790177 8.37353 0 8.17462 0H3.82538ZM6.5625 2.8125V3.375V6V6.5625H5.4375V6V3.375V2.8125H6.5625ZM6 9C6.41421 9 6.75 8.66421 6.75 8.25C6.75 7.83579 6.41421 7.5 6 7.5C5.58579 7.5 5.25 7.83579 5.25 8.25C5.25 8.66421 5.58579 9 6 9Z\",\n            fill: \"#EAEAEA\"\n        })\n    });\n}\n_c2 = Warning;\nfunction Cross(props) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"12\",\n        height: \"12\",\n        viewBox: \"0 0 14 14\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M3.08889 11.8384L2.62486 12.3024L1.69678 11.3744L2.16082 10.9103L6.07178 6.99937L2.16082 3.08841L1.69678 2.62437L2.62486 1.69629L3.08889 2.16033L6.99986 6.07129L10.9108 2.16033L11.3749 1.69629L12.3029 2.62437L11.8389 3.08841L7.92793 6.99937L11.8389 10.9103L12.3029 11.3744L11.3749 12.3024L10.9108 11.8384L6.99986 7.92744L3.08889 11.8384Z\",\n            fill: \"currentColor\"\n        })\n    });\n}\n_c3 = Cross;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=next-logo.js.map\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"AnimateCount\");\n$RefreshReg$(_c1, \"NextMark\");\n$RefreshReg$(_c2, \"Warning\");\n$RefreshReg$(_c3, \"Cross\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.js\n"));

/***/ })

}]);