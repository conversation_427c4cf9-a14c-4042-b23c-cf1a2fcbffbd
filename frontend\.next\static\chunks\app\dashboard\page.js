/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/dashboard/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(app-pages-browser)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2JvdCU1QyU1Q3RyYWRpbmdib3RfZmluYWwlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0xBQXVHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxib3RcXFxcdHJhZGluZ2JvdF9maW5hbFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardOrdersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_OrdersTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/OrdersTable */ \"(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx\");\n/* harmony import */ var _components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/DashboardTabs */ \"(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/BalancesDisplay */ \"(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx\");\n/* harmony import */ var _components_dashboard_MarketPriceDisplay__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/MarketPriceDisplay */ \"(app-pages-browser)/./src/components/dashboard/MarketPriceDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction DashboardOrdersPage() {\n    _s();\n    const { config } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__.useTradingContext)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-2xl font-bold text-primary\",\n                                children: [\n                                    \"Active Orders (\",\n                                    config.crypto1,\n                                    \"/\",\n                                    config.crypto2,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Current state of your target price levels. Prices update in real-time.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MarketPriceDisplay__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_OrdersTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardOrdersPage, \"/gxybvPIHrqyOR+dYDWY4VFMpPk=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__.useTradingContext\n    ];\n});\n_c = DashboardOrdersPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardOrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/BalancesDisplay.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BalancesDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bitcoin.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction BalancesDisplay() {\n    _s();\n    const { crypto1Balance, crypto2Balance, stablecoinBalance, config, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    const [editingBalance, setEditingBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tempValues, setTempValues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        crypto1: crypto1Balance.toString(),\n        crypto2: crypto2Balance.toString(),\n        stablecoin: stablecoinBalance.toString()\n    });\n    const formatBalance = (balance)=>balance.toFixed(config.numDigits);\n    const handleEdit = (balanceType)=>{\n        setEditingBalance(balanceType);\n        setTempValues({\n            crypto1: crypto1Balance.toString(),\n            crypto2: crypto2Balance.toString(),\n            stablecoin: stablecoinBalance.toString()\n        });\n    };\n    const handleSave = (balanceType)=>{\n        const newValue = parseFloat(tempValues[balanceType]);\n        if (!isNaN(newValue) && newValue >= 0) {\n            if (balanceType === 'crypto1') {\n                dispatch({\n                    type: 'UPDATE_BALANCES',\n                    payload: {\n                        crypto1: newValue,\n                        crypto2: crypto2Balance\n                    }\n                });\n            } else if (balanceType === 'crypto2') {\n                dispatch({\n                    type: 'UPDATE_BALANCES',\n                    payload: {\n                        crypto1: crypto1Balance,\n                        crypto2: newValue\n                    }\n                });\n            } else if (balanceType === 'stablecoin') {\n                dispatch({\n                    type: 'UPDATE_STABLECOIN_BALANCE',\n                    payload: newValue\n                });\n            }\n        }\n        setEditingBalance(null);\n    };\n    const handleCancel = ()=>{\n        setEditingBalance(null);\n        setTempValues({\n            crypto1: crypto1Balance.toString(),\n            crypto2: crypto2Balance.toString(),\n            stablecoin: stablecoinBalance.toString()\n        });\n    };\n    const renderBalanceCard = (title, balance, balanceType, icon, currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"border-2 border-border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-sm font-medium text-muted-foreground\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 9\n                        }, this),\n                        icon\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    children: editingBalance === balanceType ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                type: \"number\",\n                                value: tempValues[balanceType],\n                                onChange: (e)=>setTempValues((prev)=>({\n                                            ...prev,\n                                            [balanceType]: e.target.value\n                                        })),\n                                className: \"text-lg font-bold\",\n                                step: \"any\",\n                                min: \"0\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        size: \"sm\",\n                                        onClick: ()=>handleSave(balanceType),\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Save\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: handleCancel,\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cancel\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-foreground\",\n                                        children: formatBalance(balance)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"Available \",\n                                            currency\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                size: \"sm\",\n                                variant: \"ghost\",\n                                onClick: ()=>handleEdit(balanceType),\n                                className: \"ml-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n            lineNumber: 60,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid gap-4 md:grid-cols-3 mb-6\",\n        children: [\n            renderBalanceCard(\"\".concat(config.crypto1, \" Balance\"), crypto1Balance, 'crypto1', /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5 text-primary\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this), config.crypto1),\n            renderBalanceCard(\"\".concat(config.crypto2, \" Balance\"), crypto2Balance, 'crypto2', /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5 text-primary\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this), config.crypto2),\n            renderBalanceCard(\"Stablecoin Balance (\".concat(config.preferredStablecoin || 'N/A', \")\"), stablecoinBalance, 'stablecoin', /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5 text-primary\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, this), 'Stablecoins')\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n_s(BalancesDisplay, \"NTICQWigKma+9pPtsoUOoa+lf24=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext\n    ];\n});\n_c = BalancesDisplay;\nvar _c;\n$RefreshReg$(_c, \"BalancesDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/DashboardTabs.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_History_LineChart_ListOrdered_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=History,LineChart,ListOrdered!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-ordered.js\");\n/* harmony import */ var _barrel_optimize_names_History_LineChart_ListOrdered_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=History,LineChart,ListOrdered!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_History_LineChart_ListOrdered_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=History,LineChart,ListOrdered!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-line.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst tabConfig = [\n    {\n        value: \"orders\",\n        label: \"Orders\",\n        href: \"/dashboard\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_History_LineChart_ListOrdered_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n            lineNumber: 9,\n            columnNumber: 65\n        }, undefined)\n    },\n    {\n        value: \"history\",\n        label: \"History\",\n        href: \"/dashboard/history\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_History_LineChart_ListOrdered_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n            lineNumber: 10,\n            columnNumber: 75\n        }, undefined)\n    },\n    {\n        value: \"analytics\",\n        label: \"Analytics\",\n        href: \"/dashboard/analytics\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_History_LineChart_ListOrdered_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n            lineNumber: 11,\n            columnNumber: 81\n        }, undefined)\n    }\n];\nfunction DashboardTabs() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // Determine active tab based on pathname\n    let activeTabValue = \"orders\"; // Default\n    if (pathname === \"/dashboard/history\") activeTabValue = \"history\";\n    else if (pathname === \"/dashboard/analytics\") activeTabValue = \"analytics\";\n    const onTabChange = (value)=>{\n        const tab = tabConfig.find((t)=>t.value === value);\n        if (tab) {\n            router.push(tab.href);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_2__.Tabs, {\n        value: activeTabValue,\n        onValueChange: onTabChange,\n        className: \"w-full mb-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_2__.TabsList, {\n            className: \"grid w-full grid-cols-3 bg-card border-2 border-border\",\n            children: tabConfig.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_2__.TabsTrigger, {\n                    value: tab.value,\n                    className: \"text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-bold\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            tab.icon,\n                            tab.label\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 13\n                    }, this)\n                }, tab.value, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardTabs.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardTabs, \"gA9e4WsoP6a20xDgQgrFkfMP8lc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = DashboardTabs;\nvar _c;\n$RefreshReg$(_c, \"DashboardTabs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/MarketPriceDisplay.tsx":
/*!*********************************************************!*\
  !*** ./src/components/dashboard/MarketPriceDisplay.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarketPriceDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction MarketPriceDisplay() {\n    _s();\n    const { config, currentMarketPrice } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    const formatPrice = (price)=>price.toFixed(config.numDigits);\n    // Check if crypto pair is selected\n    const isCryptoPairSelected = config.crypto1 && config.crypto2;\n    if (!isCryptoPairSelected) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4 p-3 bg-muted/50 border border-border rounded-md\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center gap-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-5 w-5 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-muted-foreground\",\n                            children: \"Select crypto pair to view market price\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-5 w-5 text-green-500\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-muted-foreground\",\n                            children: \"Current Market Price\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-foreground\",\n                            children: [\n                                config.crypto1,\n                                \"/\",\n                                config.crypto2,\n                                \":\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-2xl font-bold text-primary\",\n                            children: [\n                                \"$\",\n                                formatPrice(currentMarketPrice)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_s(MarketPriceDisplay, \"b4ROlSOR9OZ7niaJVf3M+kC2fh8=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext\n    ];\n});\n_c = MarketPriceDisplay;\nvar _c;\n$RefreshReg$(_c, \"MarketPriceDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarketPriceDisplay.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx":
/*!**************************************************!*\
  !*** ./src/components/dashboard/OrdersTable.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrdersTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction OrdersTable() {\n    _s();\n    const { getDisplayOrders, config, currentMarketPrice } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    const displayOrders = getDisplayOrders();\n    const formatNumber = function(num) {\n        let forceSign = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (num === undefined || num === null || isNaN(num)) return \"-\";\n        const fixedNum = num.toFixed(config.numDigits);\n        if (forceSign && num > 0) return \"+\".concat(fixedNum);\n        return fixedNum;\n    };\n    const formatPercent = (num)=>{\n        if (num === undefined || num === null || isNaN(num)) return \"-\";\n        return \"\".concat(num.toFixed(2), \"%\");\n    };\n    // Check if crypto pair is selected\n    const isCryptoPairSelected = config.crypto1 && config.crypto2;\n    const columns = [\n        {\n            key: \"#\",\n            label: \"#\"\n        },\n        {\n            key: \"status\",\n            label: \"Status\"\n        },\n        {\n            key: \"orderLevel\",\n            label: \"Level\"\n        },\n        {\n            key: \"valueLevel\",\n            label: \"Value\"\n        },\n        {\n            key: \"crypto2Var\",\n            label: \"\".concat(config.crypto2 || 'Crypto2', \" Var.\")\n        },\n        {\n            key: \"crypto1Var\",\n            label: \"\".concat(config.crypto1 || 'Crypto1', \" Var.\")\n        },\n        {\n            key: \"targetPrice\",\n            label: \"Target Price\"\n        },\n        {\n            key: \"percentFromActualPrice\",\n            label: \"% from Actual\"\n        },\n        {\n            key: \"incomeCrypto1\",\n            label: \"Income \".concat(config.crypto1 || 'Crypto1')\n        },\n        {\n            key: \"incomeCrypto2\",\n            label: \"Income \".concat(config.crypto2 || 'Crypto2')\n        },\n        {\n            key: \"originalCostCrypto2\",\n            label: \"Original Cost \".concat(config.crypto2 || 'Crypto2')\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-2 border-border rounded-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n            className: \"w-full whitespace-nowrap\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.Table, {\n                    className: \"min-w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                className: \"bg-card hover:bg-card\",\n                                children: columns.map((col)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                        className: \"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm\",\n                                        children: col.label\n                                    }, col.key, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableBody, {\n                            children: !isCryptoPairSelected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    colSpan: columns.length,\n                                    className: \"h-24 text-center text-muted-foreground\",\n                                    children: \"Please select both Crypto 1 and Crypto 2 to view trading data.\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 15\n                            }, this) : displayOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    colSpan: columns.length,\n                                    className: \"h-24 text-center text-muted-foreground\",\n                                    children: 'No target prices set. Use \"Set Target Prices\" in the sidebar.'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 15\n                            }, this) : displayOrders.map((row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                    className: \"hover:bg-card/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: row.counter\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: row.status === \"Full\" ? \"default\" : \"secondary\",\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(row.status === \"Full\" ? \"bg-green-600 text-white\" : \"bg-yellow-500 text-black\", \"font-bold\"),\n                                                children: row.status\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: row.orderLevel\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: formatNumber(row.valueLevel)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-3 py-2 text-xs\", row.crypto2Var && row.crypto2Var < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                            children: formatNumber(row.crypto2Var, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-3 py-2 text-xs\", row.crypto1Var && row.crypto1Var < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                            children: formatNumber(row.crypto1Var, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs font-semibold text-primary\",\n                                            children: formatNumber(row.targetPrice)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-3 py-2 text-xs\", row.percentFromActualPrice < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                            children: formatPercent(row.percentFromActualPrice)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-3 py-2 text-xs\", row.incomeCrypto1 && row.incomeCrypto1 < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                            children: formatNumber(row.incomeCrypto1)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 20\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-3 py-2 text-xs\", row.incomeCrypto2 && row.incomeCrypto2 < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                            children: formatNumber(row.incomeCrypto2)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: formatNumber(row.originalCostCrypto2)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, row.id, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollBar, {\n                    orientation: \"horizontal\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_s(OrdersTable, \"UL7ACZxyGIzSlHTRji6CgpbrwIo=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext\n    ];\n});\n_c = OrdersTable;\nvar _c;\n$RefreshReg$(_c, \"OrdersTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9PcmRlcnNUYWJsZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFFMEI7QUFDb0M7QUFRL0I7QUFDcUM7QUFDdEI7QUFFYjtBQUVsQixTQUFTWTs7SUFDdEIsTUFBTSxFQUFFQyxnQkFBZ0IsRUFBRUMsTUFBTSxFQUFFQyxrQkFBa0IsRUFBRSxHQUFHZCwyRUFBaUJBO0lBQzFFLE1BQU1lLGdCQUFnQkg7SUFFdEIsTUFBTUksZUFBZSxTQUFDQztZQUFjQyw2RUFBWTtRQUM5QyxJQUFJRCxRQUFRRSxhQUFhRixRQUFRLFFBQVFHLE1BQU1ILE1BQU0sT0FBTztRQUM1RCxNQUFNSSxXQUFXSixJQUFJSyxPQUFPLENBQUNULE9BQU9VLFNBQVM7UUFDN0MsSUFBSUwsYUFBYUQsTUFBTSxHQUFHLE9BQU8sSUFBYSxPQUFUSTtRQUNyQyxPQUFPQTtJQUNUO0lBRUEsTUFBTUcsZ0JBQWdCLENBQUNQO1FBQ3JCLElBQUlBLFFBQVFFLGFBQWFGLFFBQVEsUUFBUUcsTUFBTUgsTUFBTSxPQUFPO1FBQzVELE9BQU8sR0FBa0IsT0FBZkEsSUFBSUssT0FBTyxDQUFDLElBQUc7SUFDM0I7SUFFQSxtQ0FBbUM7SUFDbkMsTUFBTUcsdUJBQXVCWixPQUFPYSxPQUFPLElBQUliLE9BQU9jLE9BQU87SUFFN0QsTUFBTUMsVUFBVTtRQUNkO1lBQUVDLEtBQUs7WUFBS0MsT0FBTztRQUFJO1FBQ3ZCO1lBQUVELEtBQUs7WUFBVUMsT0FBTztRQUFTO1FBQ2pDO1lBQUVELEtBQUs7WUFBY0MsT0FBTztRQUFRO1FBQ3BDO1lBQUVELEtBQUs7WUFBY0MsT0FBTztRQUFRO1FBQ3BDO1lBQUVELEtBQUs7WUFBY0MsT0FBTyxHQUErQixPQUE1QmpCLE9BQU9jLE9BQU8sSUFBSSxXQUFVO1FBQU87UUFDbEU7WUFBRUUsS0FBSztZQUFjQyxPQUFPLEdBQStCLE9BQTVCakIsT0FBT2EsT0FBTyxJQUFJLFdBQVU7UUFBTztRQUNsRTtZQUFFRyxLQUFLO1lBQWVDLE9BQU87UUFBZTtRQUM1QztZQUFFRCxLQUFLO1lBQTBCQyxPQUFPO1FBQWdCO1FBQ3hEO1lBQUVELEtBQUs7WUFBaUJDLE9BQU8sVUFBc0MsT0FBNUJqQixPQUFPYSxPQUFPLElBQUk7UUFBWTtRQUN2RTtZQUFFRyxLQUFLO1lBQWlCQyxPQUFPLFVBQXNDLE9BQTVCakIsT0FBT2MsT0FBTyxJQUFJO1FBQVk7UUFDdkU7WUFBRUUsS0FBSztZQUF1QkMsT0FBTyxpQkFBNkMsT0FBNUJqQixPQUFPYyxPQUFPLElBQUk7UUFBWTtLQUNyRjtJQUVELHFCQUNFLDhEQUFDSTtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDekIsa0VBQVVBO1lBQUN5QixXQUFVOzs4QkFDcEIsOERBQUMvQix1REFBS0E7b0JBQUMrQixXQUFVOztzQ0FDZiw4REFBQzNCLDZEQUFXQTtzQ0FDViw0RUFBQ0MsMERBQVFBO2dDQUFDMEIsV0FBVTswQ0FDakJKLFFBQVFLLEdBQUcsQ0FBQyxDQUFDQyxvQkFDWiw4REFBQzlCLDJEQUFTQTt3Q0FBZTRCLFdBQVU7a0RBQ2hDRSxJQUFJSixLQUFLO3VDQURJSSxJQUFJTCxHQUFHOzs7Ozs7Ozs7Ozs7Ozs7c0NBTTdCLDhEQUFDM0IsMkRBQVNBO3NDQUNQLENBQUN1QixxQ0FDQSw4REFBQ25CLDBEQUFRQTswQ0FDUCw0RUFBQ0gsMkRBQVNBO29DQUFDZ0MsU0FBU1AsUUFBUVEsTUFBTTtvQ0FBRUosV0FBVTs4Q0FBeUM7Ozs7Ozs7Ozs7dUNBSXZGakIsY0FBY3FCLE1BQU0sS0FBSyxrQkFDM0IsOERBQUM5QiwwREFBUUE7MENBQ1AsNEVBQUNILDJEQUFTQTtvQ0FBQ2dDLFNBQVNQLFFBQVFRLE1BQU07b0NBQUVKLFdBQVU7OENBQXlDOzs7Ozs7Ozs7O3VDQUt6RmpCLGNBQWNrQixHQUFHLENBQUMsQ0FBQ0ksb0JBQ2pCLDhEQUFDL0IsMERBQVFBO29DQUFjMEIsV0FBVTs7c0RBQy9CLDhEQUFDN0IsMkRBQVNBOzRDQUFDNkIsV0FBVTtzREFBcUJLLElBQUlDLE9BQU87Ozs7OztzREFDckQsOERBQUNuQywyREFBU0E7NENBQUM2QixXQUFVO3NEQUNuQiw0RUFBQ3ZCLHVEQUFLQTtnREFBQzhCLFNBQVNGLElBQUlHLE1BQU0sS0FBSyxTQUFTLFlBQVk7Z0RBQzdDUixXQUFXdEIsOENBQUVBLENBQUMyQixJQUFJRyxNQUFNLEtBQUssU0FBUyw0QkFBNEIsNEJBQTRCOzBEQUNsR0gsSUFBSUcsTUFBTTs7Ozs7Ozs7Ozs7c0RBR2YsOERBQUNyQywyREFBU0E7NENBQUM2QixXQUFVO3NEQUFxQkssSUFBSUksVUFBVTs7Ozs7O3NEQUN4RCw4REFBQ3RDLDJEQUFTQTs0Q0FBQzZCLFdBQVU7c0RBQXFCaEIsYUFBYXFCLElBQUlLLFVBQVU7Ozs7OztzREFDckUsOERBQUN2QywyREFBU0E7NENBQUM2QixXQUFXdEIsOENBQUVBLENBQUMscUJBQXFCMkIsSUFBSU0sVUFBVSxJQUFJTixJQUFJTSxVQUFVLEdBQUcsSUFBSSxxQkFBcUI7c0RBQ3ZHM0IsYUFBYXFCLElBQUlNLFVBQVUsRUFBRTs7Ozs7O3NEQUVoQyw4REFBQ3hDLDJEQUFTQTs0Q0FBQzZCLFdBQVd0Qiw4Q0FBRUEsQ0FBQyxxQkFBcUIyQixJQUFJTyxVQUFVLElBQUlQLElBQUlPLFVBQVUsR0FBRyxJQUFJLHFCQUFxQjtzREFDdkc1QixhQUFhcUIsSUFBSU8sVUFBVSxFQUFFOzs7Ozs7c0RBRWhDLDhEQUFDekMsMkRBQVNBOzRDQUFDNkIsV0FBVTtzREFBZ0RoQixhQUFhcUIsSUFBSVEsV0FBVzs7Ozs7O3NEQUNqRyw4REFBQzFDLDJEQUFTQTs0Q0FBQzZCLFdBQVd0Qiw4Q0FBRUEsQ0FBQyxxQkFBcUIyQixJQUFJUyxzQkFBc0IsR0FBRyxJQUFJLHFCQUFxQjtzREFDakd0QixjQUFjYSxJQUFJUyxzQkFBc0I7Ozs7OztzREFFMUMsOERBQUMzQywyREFBU0E7NENBQUM2QixXQUFXdEIsOENBQUVBLENBQUMscUJBQXFCMkIsSUFBSVUsYUFBYSxJQUFJVixJQUFJVSxhQUFhLEdBQUcsSUFBSSxxQkFBcUI7c0RBQzlHL0IsYUFBYXFCLElBQUlVLGFBQWE7Ozs7OztzREFFakMsOERBQUM1QywyREFBU0E7NENBQUM2QixXQUFXdEIsOENBQUVBLENBQUMscUJBQXFCMkIsSUFBSVcsYUFBYSxJQUFJWCxJQUFJVyxhQUFhLEdBQUcsSUFBSSxxQkFBcUI7c0RBQzdHaEMsYUFBYXFCLElBQUlXLGFBQWE7Ozs7OztzREFFakMsOERBQUM3QywyREFBU0E7NENBQUM2QixXQUFVO3NEQUFxQmhCLGFBQWFxQixJQUFJWSxtQkFBbUI7Ozs7Ozs7bUNBMUJqRVosSUFBSWEsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFnQzdCLDhEQUFDMUMsaUVBQVNBO29CQUFDMkMsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJL0I7R0FqR3dCeEM7O1FBQ21DWCx1RUFBaUJBOzs7S0FEcERXIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxkYXNoYm9hcmRcXE9yZGVyc1RhYmxlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVRyYWRpbmdDb250ZXh0IH0gZnJvbSAnQC9jb250ZXh0cy9UcmFkaW5nQ29udGV4dCc7XG5pbXBvcnQge1xuICBUYWJsZSxcbiAgVGFibGVCb2R5LFxuICBUYWJsZUNlbGwsXG4gIFRhYmxlSGVhZCxcbiAgVGFibGVIZWFkZXIsXG4gIFRhYmxlUm93LFxufSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3RhYmxlXCI7XG5pbXBvcnQgeyBTY3JvbGxBcmVhLCBTY3JvbGxCYXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3Njcm9sbC1hcmVhXCI7XG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIjtcbmltcG9ydCB0eXBlIHsgRGlzcGxheU9yZGVyUm93IH0gZnJvbSAnQC9saWIvdHlwZXMnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE9yZGVyc1RhYmxlKCkge1xuICBjb25zdCB7IGdldERpc3BsYXlPcmRlcnMsIGNvbmZpZywgY3VycmVudE1hcmtldFByaWNlIH0gPSB1c2VUcmFkaW5nQ29udGV4dCgpO1xuICBjb25zdCBkaXNwbGF5T3JkZXJzID0gZ2V0RGlzcGxheU9yZGVycygpO1xuXG4gIGNvbnN0IGZvcm1hdE51bWJlciA9IChudW0/OiBudW1iZXIsIGZvcmNlU2lnbiA9IGZhbHNlKSA9PiB7XG4gICAgaWYgKG51bSA9PT0gdW5kZWZpbmVkIHx8IG51bSA9PT0gbnVsbCB8fCBpc05hTihudW0pKSByZXR1cm4gXCItXCI7XG4gICAgY29uc3QgZml4ZWROdW0gPSBudW0udG9GaXhlZChjb25maWcubnVtRGlnaXRzKTtcbiAgICBpZiAoZm9yY2VTaWduICYmIG51bSA+IDApIHJldHVybiBgKyR7Zml4ZWROdW19YDtcbiAgICByZXR1cm4gZml4ZWROdW07XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0UGVyY2VudCA9IChudW0/OiBudW1iZXIpID0+IHtcbiAgICBpZiAobnVtID09PSB1bmRlZmluZWQgfHwgbnVtID09PSBudWxsIHx8IGlzTmFOKG51bSkpIHJldHVybiBcIi1cIjtcbiAgICByZXR1cm4gYCR7bnVtLnRvRml4ZWQoMil9JWA7XG4gIH1cblxuICAvLyBDaGVjayBpZiBjcnlwdG8gcGFpciBpcyBzZWxlY3RlZFxuICBjb25zdCBpc0NyeXB0b1BhaXJTZWxlY3RlZCA9IGNvbmZpZy5jcnlwdG8xICYmIGNvbmZpZy5jcnlwdG8yO1xuXG4gIGNvbnN0IGNvbHVtbnMgPSBbXG4gICAgeyBrZXk6IFwiI1wiLCBsYWJlbDogXCIjXCIgfSxcbiAgICB7IGtleTogXCJzdGF0dXNcIiwgbGFiZWw6IFwiU3RhdHVzXCIgfSxcbiAgICB7IGtleTogXCJvcmRlckxldmVsXCIsIGxhYmVsOiBcIkxldmVsXCIgfSxcbiAgICB7IGtleTogXCJ2YWx1ZUxldmVsXCIsIGxhYmVsOiBcIlZhbHVlXCIgfSxcbiAgICB7IGtleTogXCJjcnlwdG8yVmFyXCIsIGxhYmVsOiBgJHtjb25maWcuY3J5cHRvMiB8fCAnQ3J5cHRvMid9IFZhci5gIH0sXG4gICAgeyBrZXk6IFwiY3J5cHRvMVZhclwiLCBsYWJlbDogYCR7Y29uZmlnLmNyeXB0bzEgfHwgJ0NyeXB0bzEnfSBWYXIuYCB9LFxuICAgIHsga2V5OiBcInRhcmdldFByaWNlXCIsIGxhYmVsOiBcIlRhcmdldCBQcmljZVwiIH0sXG4gICAgeyBrZXk6IFwicGVyY2VudEZyb21BY3R1YWxQcmljZVwiLCBsYWJlbDogXCIlIGZyb20gQWN0dWFsXCIgfSxcbiAgICB7IGtleTogXCJpbmNvbWVDcnlwdG8xXCIsIGxhYmVsOiBgSW5jb21lICR7Y29uZmlnLmNyeXB0bzEgfHwgJ0NyeXB0bzEnfWAgfSxcbiAgICB7IGtleTogXCJpbmNvbWVDcnlwdG8yXCIsIGxhYmVsOiBgSW5jb21lICR7Y29uZmlnLmNyeXB0bzIgfHwgJ0NyeXB0bzInfWAgfSxcbiAgICB7IGtleTogXCJvcmlnaW5hbENvc3RDcnlwdG8yXCIsIGxhYmVsOiBgT3JpZ2luYWwgQ29zdCAke2NvbmZpZy5jcnlwdG8yIHx8ICdDcnlwdG8yJ31gIH0sXG4gIF07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci0yIGJvcmRlci1ib3JkZXIgcm91bmRlZC1zbVwiPlxuICAgICAgPFNjcm9sbEFyZWEgY2xhc3NOYW1lPVwidy1mdWxsIHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgIDxUYWJsZSBjbGFzc05hbWU9XCJtaW4tdy1mdWxsXCI+XG4gICAgICAgICAgPFRhYmxlSGVhZGVyPlxuICAgICAgICAgICAgPFRhYmxlUm93IGNsYXNzTmFtZT1cImJnLWNhcmQgaG92ZXI6YmctY2FyZFwiPlxuICAgICAgICAgICAgICB7Y29sdW1ucy5tYXAoKGNvbCkgPT4gKFxuICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQga2V5PXtjb2wua2V5fSBjbGFzc05hbWU9XCJmb250LWJvbGQgdGV4dC1mb3JlZ3JvdW5kIHdoaXRlc3BhY2Utbm93cmFwIHB4LTMgcHktMiB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICB7Y29sLmxhYmVsfVxuICAgICAgICAgICAgICAgIDwvVGFibGVIZWFkPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvVGFibGVSb3c+XG4gICAgICAgICAgPC9UYWJsZUhlYWRlcj5cbiAgICAgICAgICA8VGFibGVCb2R5PlxuICAgICAgICAgICAgeyFpc0NyeXB0b1BhaXJTZWxlY3RlZCA/IChcbiAgICAgICAgICAgICAgPFRhYmxlUm93PlxuICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY29sU3Bhbj17Y29sdW1ucy5sZW5ndGh9IGNsYXNzTmFtZT1cImgtMjQgdGV4dC1jZW50ZXIgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICBQbGVhc2Ugc2VsZWN0IGJvdGggQ3J5cHRvIDEgYW5kIENyeXB0byAyIHRvIHZpZXcgdHJhZGluZyBkYXRhLlxuICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgKSA6IGRpc3BsYXlPcmRlcnMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICA8VGFibGVSb3c+XG4gICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBjb2xTcGFuPXtjb2x1bW5zLmxlbmd0aH0gY2xhc3NOYW1lPVwiaC0yNCB0ZXh0LWNlbnRlciB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgIE5vIHRhcmdldCBwcmljZXMgc2V0LiBVc2UgXCJTZXQgVGFyZ2V0IFByaWNlc1wiIGluIHRoZSBzaWRlYmFyLlxuICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgZGlzcGxheU9yZGVycy5tYXAoKHJvdzogRGlzcGxheU9yZGVyUm93KSA9PiAoXG4gICAgICAgICAgICAgICAgPFRhYmxlUm93IGtleT17cm93LmlkfSBjbGFzc05hbWU9XCJob3ZlcjpiZy1jYXJkLzgwXCI+XG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGNsYXNzTmFtZT1cInB4LTMgcHktMiB0ZXh0LXhzXCI+e3Jvdy5jb3VudGVyfTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBjbGFzc05hbWU9XCJweC0zIHB5LTIgdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD17cm93LnN0YXR1cyA9PT0gXCJGdWxsXCIgPyBcImRlZmF1bHRcIiA6IFwic2Vjb25kYXJ5XCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKHJvdy5zdGF0dXMgPT09IFwiRnVsbFwiID8gXCJiZy1ncmVlbi02MDAgdGV4dC13aGl0ZVwiIDogXCJiZy15ZWxsb3ctNTAwIHRleHQtYmxhY2tcIiwgXCJmb250LWJvbGRcIil9PlxuICAgICAgICAgICAgICAgICAgICAgIHtyb3cuc3RhdHVzfVxuICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGNsYXNzTmFtZT1cInB4LTMgcHktMiB0ZXh0LXhzXCI+e3Jvdy5vcmRlckxldmVsfTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBjbGFzc05hbWU9XCJweC0zIHB5LTIgdGV4dC14c1wiPntmb3JtYXROdW1iZXIocm93LnZhbHVlTGV2ZWwpfTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBjbGFzc05hbWU9e2NuKFwicHgtMyBweS0yIHRleHQteHNcIiwgcm93LmNyeXB0bzJWYXIgJiYgcm93LmNyeXB0bzJWYXIgPCAwID8gXCJ0ZXh0LWRlc3RydWN0aXZlXCIgOiBcInRleHQtZ3JlZW4tNDAwXCIpfT5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdE51bWJlcihyb3cuY3J5cHRvMlZhciwgdHJ1ZSl9XG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPXtjbihcInB4LTMgcHktMiB0ZXh0LXhzXCIsIHJvdy5jcnlwdG8xVmFyICYmIHJvdy5jcnlwdG8xVmFyIDwgMCA/IFwidGV4dC1kZXN0cnVjdGl2ZVwiIDogXCJ0ZXh0LWdyZWVuLTQwMFwiKX0+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXROdW1iZXIocm93LmNyeXB0bzFWYXIsIHRydWUpfVxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGNsYXNzTmFtZT1cInB4LTMgcHktMiB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1wcmltYXJ5XCI+e2Zvcm1hdE51bWJlcihyb3cudGFyZ2V0UHJpY2UpfTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBjbGFzc05hbWU9e2NuKFwicHgtMyBweS0yIHRleHQteHNcIiwgcm93LnBlcmNlbnRGcm9tQWN0dWFsUHJpY2UgPCAwID8gXCJ0ZXh0LWRlc3RydWN0aXZlXCIgOiBcInRleHQtZ3JlZW4tNDAwXCIpfT5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFBlcmNlbnQocm93LnBlcmNlbnRGcm9tQWN0dWFsUHJpY2UpfVxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBjbGFzc05hbWU9e2NuKFwicHgtMyBweS0yIHRleHQteHNcIiwgcm93LmluY29tZUNyeXB0bzEgJiYgcm93LmluY29tZUNyeXB0bzEgPCAwID8gXCJ0ZXh0LWRlc3RydWN0aXZlXCIgOiBcInRleHQtZ3JlZW4tNDAwXCIpfT5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdE51bWJlcihyb3cuaW5jb21lQ3J5cHRvMSl9XG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPXtjbihcInB4LTMgcHktMiB0ZXh0LXhzXCIsIHJvdy5pbmNvbWVDcnlwdG8yICYmIHJvdy5pbmNvbWVDcnlwdG8yIDwgMCA/IFwidGV4dC1kZXN0cnVjdGl2ZVwiIDogXCJ0ZXh0LWdyZWVuLTQwMFwiKX0+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXROdW1iZXIocm93LmluY29tZUNyeXB0bzIpfVxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGNsYXNzTmFtZT1cInB4LTMgcHktMiB0ZXh0LXhzXCI+e2Zvcm1hdE51bWJlcihyb3cub3JpZ2luYWxDb3N0Q3J5cHRvMil9PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgPC9UYWJsZVJvdz5cbiAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9UYWJsZUJvZHk+XG4gICAgICAgIDwvVGFibGU+XG4gICAgICAgIDxTY3JvbGxCYXIgb3JpZW50YXRpb249XCJob3Jpem9udGFsXCIgLz5cbiAgICAgIDwvU2Nyb2xsQXJlYT5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVRyYWRpbmdDb250ZXh0IiwiVGFibGUiLCJUYWJsZUJvZHkiLCJUYWJsZUNlbGwiLCJUYWJsZUhlYWQiLCJUYWJsZUhlYWRlciIsIlRhYmxlUm93IiwiU2Nyb2xsQXJlYSIsIlNjcm9sbEJhciIsIkJhZGdlIiwiY24iLCJPcmRlcnNUYWJsZSIsImdldERpc3BsYXlPcmRlcnMiLCJjb25maWciLCJjdXJyZW50TWFya2V0UHJpY2UiLCJkaXNwbGF5T3JkZXJzIiwiZm9ybWF0TnVtYmVyIiwibnVtIiwiZm9yY2VTaWduIiwidW5kZWZpbmVkIiwiaXNOYU4iLCJmaXhlZE51bSIsInRvRml4ZWQiLCJudW1EaWdpdHMiLCJmb3JtYXRQZXJjZW50IiwiaXNDcnlwdG9QYWlyU2VsZWN0ZWQiLCJjcnlwdG8xIiwiY3J5cHRvMiIsImNvbHVtbnMiLCJrZXkiLCJsYWJlbCIsImRpdiIsImNsYXNzTmFtZSIsIm1hcCIsImNvbCIsImNvbFNwYW4iLCJsZW5ndGgiLCJyb3ciLCJjb3VudGVyIiwidmFyaWFudCIsInN0YXR1cyIsIm9yZGVyTGV2ZWwiLCJ2YWx1ZUxldmVsIiwiY3J5cHRvMlZhciIsImNyeXB0bzFWYXIiLCJ0YXJnZXRQcmljZSIsInBlcmNlbnRGcm9tQWN0dWFsUHJpY2UiLCJpbmNvbWVDcnlwdG8xIiwiaW5jb21lQ3J5cHRvMiIsIm9yaWdpbmFsQ29zdENyeXB0bzIiLCJpZCIsIm9yaWVudGF0aW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge(param) {\n    let { className, variant, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_c = Badge;\n\nvar _c;\n$RefreshReg$(_c, \"Badge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/badge.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/table.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/table.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Table: () => (/* binding */ Table),\n/* harmony export */   TableBody: () => (/* binding */ TableBody),\n/* harmony export */   TableCaption: () => (/* binding */ TableCaption),\n/* harmony export */   TableCell: () => (/* binding */ TableCell),\n/* harmony export */   TableFooter: () => (/* binding */ TableFooter),\n/* harmony export */   TableHead: () => (/* binding */ TableHead),\n/* harmony export */   TableHeader: () => (/* binding */ TableHeader),\n/* harmony export */   TableRow: () => (/* binding */ TableRow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\nconst Table = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full overflow-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            ref: ref,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-full caption-bottom text-sm\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n            lineNumber: 10,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Table;\nTable.displayName = \"Table\";\nconst TableHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"[&_tr]:border-b\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined);\n});\n_c3 = TableHeader;\nTableHeader.displayName = \"TableHeader\";\nconst TableBody = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"[&_tr:last-child]:border-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined);\n});\n_c5 = TableBody;\nTableBody.displayName = \"TableBody\";\nconst TableFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tfoot\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 43,\n        columnNumber: 3\n    }, undefined);\n});\n_c7 = TableFooter;\nTableFooter.displayName = \"TableFooter\";\nconst TableRow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c8 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 58,\n        columnNumber: 3\n    }, undefined);\n});\n_c9 = TableRow;\nTableRow.displayName = \"TableRow\";\nconst TableHead = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c10 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 73,\n        columnNumber: 3\n    }, undefined);\n});\n_c11 = TableHead;\nTableHead.displayName = \"TableHead\";\nconst TableCell = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c12 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 88,\n        columnNumber: 3\n    }, undefined);\n});\n_c13 = TableCell;\nTableCell.displayName = \"TableCell\";\nconst TableCaption = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c14 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"caption\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-4 text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\table.tsx\",\n        lineNumber: 100,\n        columnNumber: 3\n    }, undefined);\n});\n_c15 = TableCaption;\nTableCaption.displayName = \"TableCaption\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15;\n$RefreshReg$(_c, \"Table$React.forwardRef\");\n$RefreshReg$(_c1, \"Table\");\n$RefreshReg$(_c2, \"TableHeader$React.forwardRef\");\n$RefreshReg$(_c3, \"TableHeader\");\n$RefreshReg$(_c4, \"TableBody$React.forwardRef\");\n$RefreshReg$(_c5, \"TableBody\");\n$RefreshReg$(_c6, \"TableFooter$React.forwardRef\");\n$RefreshReg$(_c7, \"TableFooter\");\n$RefreshReg$(_c8, \"TableRow$React.forwardRef\");\n$RefreshReg$(_c9, \"TableRow\");\n$RefreshReg$(_c10, \"TableHead$React.forwardRef\");\n$RefreshReg$(_c11, \"TableHead\");\n$RefreshReg$(_c12, \"TableCell$React.forwardRef\");\n$RefreshReg$(_c13, \"TableCell\");\n$RefreshReg$(_c14, \"TableCaption$React.forwardRef\");\n$RefreshReg$(_c15, \"TableCaption\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/table.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors-node_modules_a","vendors-node_modules_lucide-react_dist_esm_I","vendors-node_modules_next_dist_a","vendors-node_modules_next_dist_client_a","vendors-node_modules_next_dist_client_components_f","vendors-node_modules_next_dist_client_components_m","vendors-node_modules_next_dist_client_components_n","vendors-node_modules_next_dist_client_components_react-dev-overlay_shared_js-0","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_call-stack_c-dc969f58","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-c82b02ac","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-13e6d335","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dialog_b","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_en","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_e","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ho","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_o","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d","vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s","vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c","vendors-node_modules_next_dist_client_components_redirect-","vendors-node_modules_next_dist_client_components_re","vendors-node_modules_next_dist_client_components_r","vendors-node_modules_next_dist_client_components_un","vendors-node_modules_next_dist_client_det","vendors-node_modules_next_dist_client_d","vendors-node_modules_next_dist_client_i","vendors-node_modules_next_dist_client_n","vendors-node_modules_next_dist_compiled_a","vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c","vendors-node_modules_next_dist_compiled_react-","vendors-node_modules_next_dist_compiled_r","vendors-node_modules_next_dist_lib_c","vendors-node_modules_next_dist_p","vendors-node_modules_next_dist_shared_lib_h","vendors-node_modules_next_dist_shared_lib_m","vendors-node_modules_next_dist_shared_lib_router_router_js-58cbbd23","vendors-node_modules_next_dist_shared_lib_ro","vendors-node_modules_next_dist_shared_lib_se","vendors-node_modules_next_d","vendors-node_modules_p","vendors-node_modules_radix-ui_react-p","vendors-node_modules_react-dom_cjs_react-dom_development_js-7b5aa877","vendors-node_modules_r","vendors-node_modules_s","default-_app-pages-browser_src_contexts_TradingContext_tsx-_app-pages-browser_src_lib_utils_ts","default-_app-pages-browser_src_components_ui_button_tsx-_app-pages-browser_src_components_ui_-7649cd","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);