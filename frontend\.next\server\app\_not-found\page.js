/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAIContext.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CTradingContext.tsx%22%2C%22ids%22%3A%5B%22TradingProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAIContext.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CTradingContext.tsx%22%2C%22ids%22%3A%5B%22TradingProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toaster.tsx */ \"(rsc)/./src/components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AIContext.tsx */ \"(rsc)/./src/contexts/AIContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/TradingContext.tsx */ \"(rsc)/./src/contexts/TradingContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAIContext.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CTradingContext.tsx%22%2C%22ids%22%3A%5B%22TradingProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fab99f159d6e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZmFiOTlmMTU5ZDZlXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var geist_font_sans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! geist/font/sans */ \"(rsc)/./node_modules/geist/dist/sans.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./src/components/ui/toaster.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(rsc)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _contexts_AIContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AIContext */ \"(rsc)/./src/contexts/AIContext.tsx\");\n\n // Correct import for Geist Sans\n\n\n\n\n\n// GeistSans from 'geist/font/sans' directly provides .variable and .className\n// No need to call it as a function like with next/font/google.\n// The variable it sets is typically --font-geist-sans.\nconst metadata = {\n    title: 'Pluto Trading Bot',\n    description: 'Simulated cryptocurrency trading bot with Neo Brutalist UI.'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: geist_font_sans__WEBPACK_IMPORTED_MODULE_1__.GeistSans.variable,\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans antialiased\",\n            suppressHydrationWarning: true,\n            children: [\n                \" \",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__.TradingProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AIContext__WEBPACK_IMPORTED_MODULE_6__.AIProvider, {\n                            children: [\n                                children,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\components\\ui\\toaster.tsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./src/contexts/AIContext.tsx":
/*!************************************!*\
  !*** ./src/contexts/AIContext.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AIProvider: () => (/* binding */ AIProvider),
/* harmony export */   useAIContext: () => (/* binding */ useAIContext)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AIProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AIProvider() from the server but AIProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AIContext.tsx",
"AIProvider",
);const useAIContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAIContext() from the server but useAIContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AIContext.tsx",
"useAIContext",
);

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AuthContext.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),
/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const TradingProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call TradingProvider() from the server but TradingProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\TradingContext.tsx",
"TradingProvider",
);const useTradingContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTradingContext() from the server but useTradingContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\TradingContext.tsx",
"useTradingContext",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNib3QlNUMlNUN0cmFkaW5nYm90X2ZpbmFsJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDYm90JTVDJTVDdHJhZGluZ2JvdF9maW5hbCU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2JvdCU1QyU1Q3RyYWRpbmdib3RfZmluYWwlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNib3QlNUMlNUN0cmFkaW5nYm90X2ZpbmFsJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNib3QlNUMlNUN0cmFkaW5nYm90X2ZpbmFsJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNib3QlNUMlNUN0cmFkaW5nYm90X2ZpbmFsJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNib3QlNUMlNUN0cmFkaW5nYm90X2ZpbmFsJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNib3QlNUMlNUN0cmFkaW5nYm90X2ZpbmFsJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBc0k7QUFDdEk7QUFDQSwwT0FBeUk7QUFDekk7QUFDQSwwT0FBeUk7QUFDekk7QUFDQSxvUkFBK0o7QUFDL0o7QUFDQSx3T0FBd0k7QUFDeEk7QUFDQSw0UEFBbUo7QUFDbko7QUFDQSxrUUFBc0o7QUFDdEo7QUFDQSxzUUFBdUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXGJvdFxcXFx0cmFkaW5nYm90X2ZpbmFsXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcYm90XFxcXHRyYWRpbmdib3RfZmluYWxcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcYm90XFxcXHRyYWRpbmdib3RfZmluYWxcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcYm90XFxcXHRyYWRpbmdib3RfZmluYWxcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAIContext.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CTradingContext.tsx%22%2C%22ids%22%3A%5B%22TradingProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAIContext.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CTradingContext.tsx%22%2C%22ids%22%3A%5B%22TradingProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toaster.tsx */ \"(ssr)/./src/components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AIContext.tsx */ \"(ssr)/./src/contexts/AIContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/TradingContext.tsx */ \"(ssr)/./src/contexts/TradingContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAIContext.tsx%22%2C%22ids%22%3A%5B%22AIProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CTradingContext.tsx%22%2C%22ids%22%3A%5B%22TradingProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive group border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 77,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./src/components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastClose, {}, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastViewport, {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AIContext.tsx":
/*!************************************!*\
  !*** ./src/contexts/AIContext.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIProvider: () => (/* binding */ AIProvider),\n/* harmony export */   useAIContext: () => (/* binding */ useAIContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AIProvider,useAIContext auto */ \n\nconst AIContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AIProvider = ({ children })=>{\n    const [suggestion, setSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getTradingModeSuggestion = async (input)=>{\n        setIsLoading(true);\n        setError(null);\n        setSuggestion(null);\n        try {\n            // For now, provide a mock response to avoid server import issues\n            // TODO: Implement proper API call to server action\n            const result = {\n                suggestedMode: input.riskTolerance === 'low' ? 'Stablecoin Swap' : 'Simple Spot',\n                reason: input.riskTolerance === 'low' ? 'Based on your low risk tolerance, Stablecoin Swap mode is recommended for more stable returns.' : 'Based on your risk profile, Simple Spot mode offers better profit potential for active trading.'\n            };\n            setSuggestion(result);\n        } catch (e) {\n            setError(e instanceof Error ? e.message : \"An unknown error occurred during AI suggestion.\");\n            console.error(\"Error fetching trading mode suggestion:\", e);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIContext.Provider, {\n        value: {\n            suggestion,\n            isLoading,\n            error,\n            getTradingModeSuggestion\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AIContext.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAIContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AIContext);\n    if (context === undefined) {\n        throw new Error('useAIContext must be used within an AIProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AIContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check auth status from localStorage\n            const storedAuthStatus = localStorage.getItem('plutoAuth');\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (storedAuthStatus === 'true' && authToken) {\n                setIsAuthenticated(true);\n            }\n            setIsLoading(false);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated && pathname !== '/login') {\n                router.push('/login');\n            } else if (!isLoading && isAuthenticated && pathname === '/login') {\n                router.push('/dashboard');\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        pathname,\n        router\n    ]);\n    const login = async (username, password)=>{\n        setIsLoading(true);\n        try {\n            const success = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authApi.login(username, password);\n            if (success) {\n                setIsAuthenticated(true);\n                router.push('/dashboard');\n                return true;\n            }\n            setIsAuthenticated(false);\n            return false;\n        } catch (error) {\n            console.error('Login failed:', error);\n            setIsAuthenticated(false);\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authApi.logout();\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            setIsAuthenticated(false);\n            router.push('/login');\n        }\n    };\n    // Consistent loading display across contexts\n    if (isLoading && !pathname?.startsWith('/_next/static/')) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-screen bg-background text-foreground\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-12 w-12 animate-spin text-primary\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"ml-4 text-xl\",\n                    children: \"Loading Pluto...\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!isAuthenticated && pathname !== '/login' && !pathname?.startsWith('/_next/static/')) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-screen bg-background text-foreground\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-12 w-12 animate-spin text-primary\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"ml-4 text-xl\",\n                    children: \"Redirecting to login...\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            isAuthenticated,\n            login,\n            logout,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(ssr)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(ssr)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(ssr)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \n\n\n// Force the correct stablecoins to ensure all 6 are available\nconst FORCE_STABLECOINS = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Default fallback value\n    return 1.0;\n};\n// Enhanced API function to get market price for any trading pair\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Try multiple API endpoints for better coverage\n        const symbol = `${config.crypto1}${config.crypto2}`.toUpperCase();\n        // First try Binance API\n        try {\n            const response = await fetch(`https://api.binance.com/api/v3/ticker/price?symbol=${symbol}`);\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(`✅ Price fetched from Binance: ${config.crypto1}/${config.crypto2} = ${price}`);\n                    return price;\n                }\n            }\n        } catch (binanceError) {\n            console.warn('Binance API failed, trying alternative...', binanceError);\n        }\n        // Fallback to CoinGecko API for broader pair support\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${crypto1Id}&vs_currencies=${crypto2Id}`);\n                if (response.ok) {\n                    const data = await response.json();\n                    const price = data[crypto1Id]?.[crypto2Id];\n                    if (price > 0) {\n                        console.log(`✅ Price fetched from CoinGecko: ${config.crypto1}/${config.crypto2} = ${price}`);\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed, using mock price...', geckoError);\n        }\n        // Final fallback to mock price\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(`⚠️ Using mock price: ${config.crypto1}/${config.crypto2} = ${mockPrice}`);\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${cryptoId}&vs_currencies=${stablecoinId}`);\n            if (response.ok) {\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? data[cryptoId]?.[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(`📊 Stablecoin rate: ${crypto}/${stablecoin} = ${rate}`);\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(`📊 Fallback stablecoin rate: ${crypto}/${stablecoin} = ${rate} (via USD)`);\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Helper function to get USD price (extracted from calculateFallbackMarketPrice)\nconst getUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies\n        'BTC': 109000,\n        'ETH': 4000,\n        'SOL': 240,\n        'ADA': 1.2,\n        'DOGE': 0.4,\n        'LINK': 25,\n        'MATIC': 0.5,\n        'DOT': 8,\n        'AVAX': 45,\n        'SHIB': 0.000030,\n        'XRP': 2.5,\n        'LTC': 110,\n        'BCH': 500,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    // Add small random fluctuation\n    const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%\n    const finalPrice = basePrice * (1 + fluctuation);\n    console.log(`📊 Fallback price calculation: ${config.crypto1} ($${crypto1USDPrice}) / ${config.crypto2} ($${crypto2USDPrice}) = ${finalPrice.toFixed(6)}`);\n    return finalPrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_CRYPTOS[0],\n    crypto2: (_lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_QUOTES_SIMPLE[_lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_CRYPTOS[0]] || DEFAULT_QUOTE_CURRENCIES)[0],\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: FORCE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown',\n    connectionStatus: 'online'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (false) {}\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (false) {}\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        case 'FLUCTUATE_MARKET_PRICE':\n            {\n                if (state.currentMarketPrice <= 0) return state;\n                // More realistic fluctuation: smaller, more frequent changes\n                const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update\n                const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);\n                return {\n                    ...state,\n                    currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice\n                };\n            }\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset)\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SET_CONNECTION_STATUS':\n            // If connection goes offline and bot is running, stop it\n            if (action.payload === 'offline' && state.botSystemStatus === 'Running') {\n                console.warn('🔴 Connection lost - stopping bot automatically');\n                return {\n                    ...state,\n                    connectionStatus: action.payload,\n                    botSystemStatus: 'Stopped'\n                };\n            }\n            return {\n                ...state,\n                connectionStatus: action.payload\n            };\n        case 'SET_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1,\n                crypto2Balance: action.payload.crypto2\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            // Session creation will be handled in the effect\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Reset all target price rows and clear history\n            const resetTargetPriceRows = state.targetPriceRows.map((row)=>({\n                    ...row,\n                    status: 'Free',\n                    orderLevel: 0,\n                    valueLevel: state.config.baseBid,\n                    crypto1AmountHeld: undefined,\n                    originalCostCrypto2: undefined,\n                    crypto1Var: undefined,\n                    crypto2Var: undefined,\n                    lastActionTimestamp: undefined\n                }));\n            lastActionTimestampPerCounter.clear();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: resetTargetPriceRows,\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = ({ children })=>{\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                const price = await getMarketPriceFromAPI(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Market price fluctuation effect - simulates real-time price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up price fluctuation interval for simulation\n            const priceFluctuationInterval = setInterval({\n                \"TradingProvider.useEffect.priceFluctuationInterval\": ()=>{\n                    dispatch({\n                        type: 'FLUCTUATE_MARKET_PRICE'\n                    });\n                }\n            }[\"TradingProvider.useEffect.priceFluctuationInterval\"], 2000); // Update every 2 seconds for realistic simulation\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceFluctuationInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        dispatch\n    ]);\n    // Other effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            if (state.appSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && state.appSettings.alertOnOrderExecution) {\n                    soundPath = state.appSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && state.appSettings.alertOnError) {\n                    soundPath = state.appSettings.soundError;\n                }\n                if (soundPath) {\n                    audioRef.current.src = soundPath;\n                    audioRef.current.currentTime = 0; // Reset to beginning\n                    // Play the sound and limit duration to 2 seconds\n                    audioRef.current.play().then({\n                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                            // Set a timeout to pause the audio after 2 seconds\n                            setTimeout({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.pause();\n                                        audioRef.current.currentTime = 0; // Reset for next play\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                        }\n                    }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                        \"TradingProvider.useCallback[playSound]\": (err)=>console.error(\"Error playing sound:\", err)\n                    }[\"TradingProvider.useCallback[playSound]\"]);\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Telegram notification function\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async (message)=>{\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(`https://api.telegram.org/bot${telegramToken}/sendMessage`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Only check essential conditions including connection status\n            if (state.botSystemStatus !== 'Running' || state.connectionStatus !== 'online' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0) {\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(`🚀 CONTINUOUS TRADING: Price $${currentMarketPrice.toFixed(2)} | Targets: ${sortedRowsForLogic.length} | Balance: $${currentCrypto2Balance} ${config.crypto2}`);\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(`🎯 TARGETS IN RANGE (±${config.slippagePercent}%):`, targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>`Counter ${row.counter} (${row.status})`\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: `${config.crypto1}/${config.crypto2}`,\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(`✅ BUY: Counter ${activeRow.counter} bought ${amountCrypto1Bought.toFixed(6)} ${config.crypto1} at $${currentMarketPrice.toFixed(2)}`);\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: `Counter ${activeRow.counter}: ${amountCrypto1Bought.toFixed(6)} ${config.crypto1}`,\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(`🟢 <b>BUY EXECUTED</b>\\n` + `📊 Counter: ${activeRow.counter}\\n` + `💰 Amount: ${amountCrypto1Bought.toFixed(6)} ${config.crypto1}\\n` + `💵 Price: $${currentMarketPrice.toFixed(2)}\\n` + `💸 Cost: $${costCrypto2.toFixed(2)} ${config.crypto2}\\n` + `📈 Mode: Simple Spot`);\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: `${config.crypto1}/${config.crypto2}`,\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(`✅ SELL: Counter ${currentCounter - 1} sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1}. Profit: $${realizedProfit.toFixed(2)}`);\n                            toast({\n                                title: \"SELL Executed\",\n                                description: `Counter ${currentCounter - 1}: Profit $${realizedProfit.toFixed(2)}`,\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(`🔴 <b>SELL EXECUTED</b>\\n` + `📊 Counter: ${currentCounter - 1}\\n` + `💰 Amount: ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1}\\n` + `💵 Price: $${currentMarketPrice.toFixed(2)}\\n` + `💸 Received: $${crypto2Received.toFixed(2)} ${config.crypto2}\\n` + `${profitEmoji} Profit: $${realizedProfit.toFixed(2)} ${config.crypto2}\\n` + `📈 Mode: Simple Spot`);\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Get real market price for Crypto2/Stablecoin pair (synchronous for now)\n                                const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Get real market price for Crypto1/Stablecoin pair\n                                const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: `${config.crypto2}/${config.preferredStablecoin}`,\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: `${config.crypto1}/${config.preferredStablecoin}`,\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(`✅ STABLECOIN BUY: Counter ${activeRow.counter} | Step 1: Sold ${amountCrypto2ToUse} ${config.crypto2} → ${stablecoinObtained.toFixed(2)} ${config.preferredStablecoin} | Step 2: Bought ${crypto1Bought.toFixed(6)} ${config.crypto1} | Level: ${activeRow.orderLevel} → ${newLevel}`);\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: `Counter ${activeRow.counter}: ${crypto1Bought.toFixed(6)} ${config.crypto1} via ${config.preferredStablecoin}`,\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(`🟢 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n` + `📊 Counter: ${activeRow.counter}\\n` + `🔄 Step 1: Sold ${amountCrypto2ToUse.toFixed(2)} ${config.crypto2} → ${stablecoinObtained.toFixed(2)} ${config.preferredStablecoin}\\n` + `🔄 Step 2: Bought ${crypto1Bought.toFixed(6)} ${config.crypto1}\\n` + `📊 Level: ${activeRow.orderLevel} → ${newLevel}\\n` + `📈 Mode: Stablecoin Swap`);\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            const realizedProfitInCrypto2 = crypto2Reacquired - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 * config.incomeSplitCrypto1Percent / 100 / crypto1StablecoinPrice : 0;\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: `${config.crypto1}/${config.preferredStablecoin}`,\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: `${config.crypto2}/${config.preferredStablecoin}`,\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(`✅ STABLECOIN SELL: Counter ${currentCounter - 1} | Step A: Sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1} → ${stablecoinFromC1Sell.toFixed(2)} ${config.preferredStablecoin} | Step B: Bought ${crypto2Reacquired.toFixed(2)} ${config.crypto2} | Profit: ${realizedProfitInCrypto2.toFixed(2)} ${config.crypto2} | Level: ${inferiorRow.orderLevel} (unchanged)`);\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: `Counter ${currentCounter - 1}: Profit ${realizedProfitInCrypto2.toFixed(2)} ${config.crypto2} via ${config.preferredStablecoin}`,\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(`🔴 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n` + `📊 Counter: ${currentCounter - 1}\\n` + `🔄 Step A: Sold ${amountCrypto1ToSell.toFixed(6)} ${config.crypto1} → ${stablecoinFromC1Sell.toFixed(2)} ${config.preferredStablecoin}\\n` + `🔄 Step B: Bought ${crypto2Reacquired.toFixed(2)} ${config.crypto2}\\n` + `${profitEmoji} Profit: ${realizedProfitInCrypto2.toFixed(2)} ${config.crypto2}\\n` + `📊 Level: ${inferiorRow.orderLevel} (unchanged)\\n` + `📈 Mode: Stablecoin Swap`);\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(`🎯 CYCLE COMPLETE: ${actionsTaken} actions taken at price $${currentMarketPrice.toFixed(2)}`);\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                const configData = {\n                    name: `${config.crypto1}/${config.crypto2} ${config.tradingMode}`,\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return response.config?.id || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(`${apiUrl}/health/`);\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(`Backend health check failed with status: ${healthResponse.status} ${healthResponse.statusText}`);\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', `${apiUrl}/health/`);\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Handle session creation when bot starts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Create a session when bot starts if one doesn't exist\n            if (state.botSystemStatus === 'WarmingUp') {\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // Create a new session with current config\n                    const sessionName = `${state.config.crypto1}/${state.config.crypto2} ${state.config.tradingMode}`;\n                    sessionManager.createNewSession(sessionName, state.config).then({\n                        \"TradingProvider.useEffect\": (newSessionId)=>{\n                            sessionManager.setCurrentSession(newSessionId);\n                            console.log(`✅ Auto-created session: ${sessionName} (${newSessionId})`);\n                            // Save initial state to the new session\n                            sessionManager.saveSession(newSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, true // isActive\n                            );\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('Failed to create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline)=>{\n                    console.log(`🌐 Network status changed: ${isOnline ? 'Online' : 'Offline'}`);\n                    if (!isOnline) {\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"You are currently offline. Data will be saved locally.\",\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    } else {\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. Auto-saving enabled.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(`🧠 High memory usage: ${usedMB.toFixed(2)}MB`);\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: `Memory usage is high (${usedMB.toFixed(0)}MB). Consider refreshing the page.`,\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Connection status setter\n    const setConnectionStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setConnectionStatus]\": (status)=>{\n            dispatch({\n                type: 'SET_CONNECTION_STATUS',\n                payload: status\n            });\n        }\n    }[\"TradingProvider.useCallback[setConnectionStatus]\"], [\n        dispatch\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        setConnectionStatus,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        backendStatus: state.backendStatus,\n        connectionStatus: state.connectionStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1346,\n        columnNumber: 5\n    }, undefined);\n};\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/TradingContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-toast.ts":
/*!********************************!*\
  !*** ./src/hooks/use-toast.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ // Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiApi: () => (/* binding */ aiApi),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   sessionApi: () => (/* binding */ sessionApi),\n/* harmony export */   tradingApi: () => (/* binding */ tradingApi),\n/* harmony export */   userApi: () => (/* binding */ userApi)\n/* harmony export */ });\n// API utility functions for backend communication\n// Configure base URL - can be overridden if needed for different environments\nconst API_BASE_URL = \"http://localhost:5000\" || 0;\nconsole.log('API Base URL:', API_BASE_URL); // Log the URL to help with debugging\n// Generic fetch wrapper with error handling\nasync function fetchWithAuth(endpoint, options = {}) {\n    const url = `${API_BASE_URL}${endpoint}`;\n    // Add auth token if available\n    const token = localStorage.getItem('plutoAuthToken');\n    const headers = {\n        'Content-Type': 'application/json',\n        ...token ? {\n            'Authorization': `Bearer ${token}`\n        } : {},\n        ...options.headers\n    };\n    try {\n        // Add timeout to fetch request\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 10000); // 10 second timeout\n        const response = await fetch(url, {\n            ...options,\n            headers,\n            signal: controller.signal\n        }).finally(()=>clearTimeout(timeoutId));\n        // Handle unauthorized responses\n        if (response.status === 401) {\n            localStorage.removeItem('plutoAuth');\n            localStorage.removeItem('plutoAuthToken');\n            localStorage.removeItem('plutoUser');\n            if (false) {}\n            throw new Error('Authentication expired. Please login again.');\n        }\n        // Parse JSON response (safely)\n        let data;\n        const contentType = response.headers.get('content-type');\n        if (contentType && contentType.includes('application/json')) {\n            data = await response.json();\n        } else {\n            // Handle non-JSON responses\n            const text = await response.text();\n            try {\n                data = JSON.parse(text);\n            } catch (e) {\n                // If it's not parseable JSON, use the text as is\n                data = {\n                    message: text\n                };\n            }\n        }\n        // Handle API errors\n        if (!response.ok) {\n            console.error('API error response:', {\n                status: response.status,\n                statusText: response.statusText,\n                url: response.url,\n                data: data\n            });\n            // Provide more specific error messages\n            if (response.status === 422) {\n                throw new Error(data.error || data.message || 'Validation error: Please check your input data');\n            } else if (response.status === 401) {\n                throw new Error('Authentication failed. Please log in again.');\n            } else if (response.status === 403) {\n                throw new Error('Access denied. You do not have permission for this action.');\n            } else if (response.status === 404) {\n                throw new Error('Resource not found.');\n            } else if (response.status >= 500) {\n                throw new Error('Server error. Please try again later.');\n            }\n            throw new Error(data.error || data.message || `API error: ${response.status}`);\n        }\n        return data;\n    } catch (error) {\n        // Handle network errors specifically\n        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {\n            console.error('Network error - Is the backend server running?:', error);\n            throw new Error('Cannot connect to server. Please check if the backend is running.');\n        }\n        // Handle timeout\n        if (error.name === 'AbortError') {\n            console.error('Request timeout:', error);\n            throw new Error('Request timed out. Server may be unavailable.');\n        }\n        console.error('API request failed:', error);\n        throw error;\n    }\n}\n// Auth API functions\nconst authApi = {\n    login: async (username, password)=>{\n        try {\n            // Use retry mechanism for login attempts\n            const data = await retryWithBackoff(async ()=>{\n                return await fetchWithAuth('/auth/login', {\n                    method: 'POST',\n                    body: JSON.stringify({\n                        username,\n                        password\n                    })\n                });\n            });\n            // The backend returns access_token in the response\n            if (data && data.access_token) {\n                localStorage.setItem('plutoAuthToken', data.access_token);\n                localStorage.setItem('plutoAuth', 'true');\n                // Also store user data if available\n                if (data.user) {\n                    localStorage.setItem('plutoUser', JSON.stringify(data.user));\n                }\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error('Login API error:', error);\n            return false;\n        }\n    },\n    register: async (username, password, email)=>{\n        // Use retry mechanism for registration attempts\n        return retryWithBackoff(async ()=>{\n            return await fetchWithAuth('/auth/register', {\n                method: 'POST',\n                body: JSON.stringify({\n                    username,\n                    password,\n                    email\n                })\n            });\n        });\n    },\n    logout: async ()=>{\n        localStorage.removeItem('plutoAuth');\n        localStorage.removeItem('plutoAuthToken');\n        localStorage.removeItem('plutoUser');\n        return true;\n    }\n};\n// Trading API functions\nconst tradingApi = {\n    getConfig: async (configId)=>{\n        return fetchWithAuth(configId ? `/trading/config/${configId}` : '/trading/config');\n    },\n    saveConfig: async (config)=>{\n        return fetchWithAuth('/trading/config', {\n            method: 'POST',\n            body: JSON.stringify(config)\n        });\n    },\n    updateConfig: async (configId, config)=>{\n        return fetchWithAuth(`/trading/config/${configId}`, {\n            method: 'PUT',\n            body: JSON.stringify(config)\n        });\n    },\n    startBot: async (configId)=>{\n        return fetchWithAuth(`/trading/bot/start/${configId}`, {\n            method: 'POST'\n        });\n    },\n    stopBot: async (configId)=>{\n        return fetchWithAuth(`/trading/bot/stop/${configId}`, {\n            method: 'POST'\n        });\n    },\n    getBotStatus: async (configId)=>{\n        return fetchWithAuth(`/trading/bot/status/${configId}`);\n    },\n    getTradeHistory: async (configId)=>{\n        const params = configId ? `?configId=${configId}` : '';\n        return fetchWithAuth(`/trading/history${params}`);\n    },\n    getBalances: async ()=>{\n        return fetchWithAuth('/trading/balances');\n    },\n    getMarketPrice: async (symbol)=>{\n        return fetchWithAuth(`/trading/market-data/${symbol}`);\n    },\n    getTradingPairs: async (exchange = 'binance')=>{\n        return fetchWithAuth(`/trading/exchange/trading-pairs?exchange=${exchange}`);\n    },\n    getCryptocurrencies: async (exchange = 'binance')=>{\n        return fetchWithAuth(`/trading/exchange/cryptocurrencies?exchange=${exchange}`);\n    }\n};\n// User API functions\nconst userApi = {\n    getProfile: async ()=>{\n        return fetchWithAuth('/user/profile');\n    },\n    updateProfile: async (profileData)=>{\n        return fetchWithAuth('/user/profile', {\n            method: 'PUT',\n            body: JSON.stringify(profileData)\n        });\n    },\n    saveApiKeys: async (exchangeData)=>{\n        return fetchWithAuth('/user/apikeys', {\n            method: 'POST',\n            body: JSON.stringify(exchangeData)\n        });\n    },\n    getApiKeys: async ()=>{\n        return fetchWithAuth('/user/apikeys');\n    }\n};\n// AI API functions\nconst aiApi = {\n    getTradingSuggestion: async (data)=>{\n        return fetchWithAuth('/ai/trading-suggestion', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n};\n// Session API functions\nconst sessionApi = {\n    getAllSessions: async (includeInactive = true)=>{\n        return fetchWithAuth(`/sessions/?include_inactive=${includeInactive}`);\n    },\n    createSession: async (sessionData)=>{\n        return fetchWithAuth('/sessions/', {\n            method: 'POST',\n            body: JSON.stringify(sessionData)\n        });\n    },\n    getSession: async (sessionId)=>{\n        return fetchWithAuth(`/sessions/${sessionId}`);\n    },\n    updateSession: async (sessionId, sessionData)=>{\n        return fetchWithAuth(`/sessions/${sessionId}`, {\n            method: 'PUT',\n            body: JSON.stringify(sessionData)\n        });\n    },\n    deleteSession: async (sessionId)=>{\n        return fetchWithAuth(`/sessions/${sessionId}`, {\n            method: 'DELETE'\n        });\n    },\n    activateSession: async (sessionId)=>{\n        return fetchWithAuth(`/sessions/${sessionId}/activate`, {\n            method: 'POST'\n        });\n    },\n    getSessionHistory: async (sessionId)=>{\n        return fetchWithAuth(`/sessions/${sessionId}/history`);\n    },\n    getActiveSession: async ()=>{\n        return fetchWithAuth('/sessions/active');\n    }\n};\n// Add a retry mechanism for transient connection issues\nconst retryWithBackoff = async (fn, maxRetries = 3)=>{\n    let retries = 0;\n    const execute = async ()=>{\n        try {\n            return await fn();\n        } catch (error) {\n            // Only retry on network errors, not on 4xx/5xx responses\n            if (error instanceof TypeError && error.message.includes('Failed to fetch') || error.name === 'AbortError') {\n                if (retries < maxRetries) {\n                    const delay = Math.pow(2, retries) * 500; // Exponential backoff\n                    console.log(`Retrying after ${delay}ms (attempt ${retries + 1}/${maxRetries})...`);\n                    retries++;\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                    return execute();\n                }\n            }\n            throw error;\n        }\n    };\n    return execute();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/network-monitor.ts":
/*!************************************!*\
  !*** ./src/lib/network-monitor.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoSaveManager: () => (/* binding */ AutoSaveManager),\n/* harmony export */   MemoryMonitor: () => (/* binding */ MemoryMonitor),\n/* harmony export */   NetworkMonitor: () => (/* binding */ NetworkMonitor)\n/* harmony export */ });\nclass NetworkMonitor {\n    constructor(){\n        this.isOnline = navigator.onLine;\n        this.listeners = new Set();\n        this.lastOnlineTime = Date.now();\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.reconnectInterval = 5000 // 5 seconds\n        ;\n        this.setupEventListeners();\n        this.startPeriodicCheck();\n    }\n    static getInstance() {\n        if (!NetworkMonitor.instance) {\n            NetworkMonitor.instance = new NetworkMonitor();\n        }\n        return NetworkMonitor.instance;\n    }\n    setupEventListeners() {\n        window.addEventListener('online', this.handleOnline.bind(this));\n        window.addEventListener('offline', this.handleOffline.bind(this));\n        // Listen for visibility change to check connection when tab becomes active\n        document.addEventListener('visibilitychange', ()=>{\n            if (!document.hidden) {\n                this.checkConnection();\n            }\n        });\n    }\n    handleOnline() {\n        console.log('🌐 Network: Back online');\n        this.isOnline = true;\n        this.lastOnlineTime = Date.now();\n        this.reconnectAttempts = 0;\n        this.notifyListeners(true);\n    }\n    handleOffline() {\n        console.log('🌐 Network: Gone offline');\n        this.isOnline = false;\n        this.notifyListeners(false);\n    }\n    async checkConnection() {\n        try {\n            // Try to fetch a small resource to verify actual connectivity\n            const response = await fetch('/api/health', {\n                method: 'HEAD',\n                cache: 'no-cache',\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            const isConnected = response.ok;\n            if (isConnected !== this.isOnline) {\n                this.isOnline = isConnected;\n                this.notifyListeners(isConnected);\n                if (isConnected) {\n                    this.lastOnlineTime = Date.now();\n                    this.reconnectAttempts = 0;\n                }\n            }\n            return isConnected;\n        } catch (error) {\n            // If fetch fails, we're likely offline\n            if (this.isOnline) {\n                this.isOnline = false;\n                this.notifyListeners(false);\n            }\n            return false;\n        }\n    }\n    startPeriodicCheck() {\n        // Use a more efficient interval with cleanup\n        const interval = setInterval(()=>{\n            this.checkConnection();\n        }, 30000); // Check every 30 seconds\n        // Store interval for cleanup\n        this.periodicInterval = interval;\n    }\n    cleanup() {\n        if (this.periodicInterval) {\n            clearInterval(this.periodicInterval);\n        }\n        this.listeners.clear();\n    }\n    notifyListeners(isOnline) {\n        this.listeners.forEach((listener)=>{\n            try {\n                listener(isOnline);\n            } catch (error) {\n                console.error('Error in network status listener:', error);\n            }\n        });\n    }\n    addListener(listener) {\n        this.listeners.add(listener);\n        // Return unsubscribe function\n        return ()=>{\n            this.listeners.delete(listener);\n        };\n    }\n    getStatus() {\n        return {\n            isOnline: this.isOnline,\n            lastOnlineTime: this.lastOnlineTime,\n            reconnectAttempts: this.reconnectAttempts\n        };\n    }\n    async forceCheck() {\n        return await this.checkConnection();\n    }\n    // Attempt to reconnect with exponential backoff\n    async attemptReconnect() {\n        if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n            console.log('🌐 Network: Max reconnect attempts reached');\n            return false;\n        }\n        this.reconnectAttempts++;\n        const delay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1), 30000);\n        console.log(`🌐 Network: Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);\n        await new Promise((resolve)=>setTimeout(resolve, delay));\n        const isConnected = await this.checkConnection();\n        if (!isConnected && this.reconnectAttempts < this.maxReconnectAttempts) {\n            // Schedule next attempt\n            setTimeout(()=>this.attemptReconnect(), 1000);\n        }\n        return isConnected;\n    }\n}\n// Auto-save functionality\nclass AutoSaveManager {\n    constructor(){\n        this.saveInterval = null;\n        this.saveFunction = null;\n        this.intervalMs = 30000 // 30 seconds default\n        ;\n        this.isEnabled = true;\n        this.lastSaveTime = 0;\n        this.networkMonitor = NetworkMonitor.getInstance();\n        this.setupNetworkListener();\n        this.setupBeforeUnloadListener();\n    }\n    static getInstance() {\n        if (!AutoSaveManager.instance) {\n            AutoSaveManager.instance = new AutoSaveManager();\n        }\n        return AutoSaveManager.instance;\n    }\n    setupNetworkListener() {\n        this.networkMonitor.addListener((isOnline)=>{\n            if (isOnline && this.saveFunction) {\n                // Save immediately when coming back online\n                console.log('💾 Auto-save: Saving on network reconnection');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        });\n    }\n    setupBeforeUnloadListener() {\n        window.addEventListener('beforeunload', ()=>{\n            if (this.saveFunction) {\n                console.log('💾 Auto-save: Saving before page unload');\n                this.saveFunction();\n            }\n        });\n        // Also save on page visibility change (when user switches tabs)\n        document.addEventListener('visibilitychange', ()=>{\n            if (document.hidden && this.saveFunction) {\n                console.log('💾 Auto-save: Saving on tab switch');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        });\n    }\n    enable(saveFunction, intervalMs = 30000) {\n        this.saveFunction = saveFunction;\n        this.intervalMs = intervalMs;\n        this.isEnabled = true;\n        this.stop(); // Clear any existing interval\n        this.saveInterval = setInterval(()=>{\n            if (this.isEnabled && this.saveFunction && this.networkMonitor.getStatus().isOnline) {\n                console.log('💾 Auto-save: Periodic save');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        }, this.intervalMs);\n        console.log(`💾 Auto-save: Enabled with ${intervalMs}ms interval`);\n    }\n    disable() {\n        this.isEnabled = false;\n        this.stop();\n        console.log('💾 Auto-save: Disabled');\n    }\n    stop() {\n        if (this.saveInterval) {\n            clearInterval(this.saveInterval);\n            this.saveInterval = null;\n        }\n    }\n    saveNow() {\n        if (this.saveFunction && this.networkMonitor.getStatus().isOnline) {\n            console.log('💾 Auto-save: Manual save triggered');\n            this.saveFunction();\n            this.lastSaveTime = Date.now();\n        }\n    }\n    getStatus() {\n        return {\n            isEnabled: this.isEnabled,\n            lastSaveTime: this.lastSaveTime,\n            intervalMs: this.intervalMs,\n            isOnline: this.networkMonitor.getStatus().isOnline\n        };\n    }\n}\n// Memory usage monitor to prevent memory leaks\nclass MemoryMonitor {\n    constructor(){\n        this.checkInterval = null;\n        this.warningThreshold = 100 * 1024 * 1024 // 100MB\n        ;\n        this.criticalThreshold = 200 * 1024 * 1024 // 200MB\n        ;\n        this.listeners = new Set();\n        this.startMonitoring();\n    }\n    static getInstance() {\n        if (!MemoryMonitor.instance) {\n            MemoryMonitor.instance = new MemoryMonitor();\n        }\n        return MemoryMonitor.instance;\n    }\n    startMonitoring() {\n        // Only monitor if performance.memory is available (Chrome)\n        if ('memory' in performance) {\n            this.checkInterval = setInterval(()=>{\n                this.checkMemoryUsage();\n            }, 60000); // Check every minute\n        }\n    }\n    checkMemoryUsage() {\n        if ('memory' in performance) {\n            const memory = performance.memory;\n            const usedJSHeapSize = memory.usedJSHeapSize;\n            this.notifyListeners(memory);\n            if (usedJSHeapSize > this.criticalThreshold) {\n                console.warn('🧠 Memory: Critical memory usage detected:', {\n                    used: `${(usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,\n                    total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,\n                    limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`\n                });\n                // Trigger garbage collection if possible\n                if ('gc' in window) {\n                    window.gc();\n                }\n            } else if (usedJSHeapSize > this.warningThreshold) {\n                console.log('🧠 Memory: High memory usage:', {\n                    used: `${(usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,\n                    total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`\n                });\n            }\n        }\n    }\n    notifyListeners(memory) {\n        this.listeners.forEach((listener)=>{\n            try {\n                listener(memory);\n            } catch (error) {\n                console.error('Error in memory monitor listener:', error);\n            }\n        });\n    }\n    addListener(listener) {\n        this.listeners.add(listener);\n        return ()=>this.listeners.delete(listener);\n    }\n    getMemoryUsage() {\n        if ('memory' in performance) {\n            return performance.memory;\n        }\n        return null;\n    }\n    stop() {\n        if (this.checkInterval) {\n            clearInterval(this.checkInterval);\n            this.checkInterval = null;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/network-monitor.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\nclass SessionManager {\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.loadSessionsFromStorage();\n        // Start with localStorage mode, check backend in background\n        this.useBackend = false;\n        // Check backend connection in background without blocking\n        setTimeout(()=>{\n            this.checkBackendConnection().catch(()=>{\n            // Silently fail and continue with localStorage\n            });\n        }, 1000);\n    }\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    async checkBackendConnection() {\n        try {\n            // Try a simple ping to the backend\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 1500); // 1.5 second timeout\n            const response = await fetch('http://localhost:5000/', {\n                method: 'GET',\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.status < 500) {\n                this.useBackend = true;\n                console.log('✅ Session Manager: Backend connection established');\n            } else {\n                throw new Error('Backend returned server error');\n            }\n        } catch (error) {\n            this.useBackend = false;\n            console.warn('⚠️ Session Manager: Backend unavailable, using localStorage fallback');\n            console.warn('💡 To enable backend features, start the backend server: python run.py');\n        }\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (true) return;\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            const currentSessionId = localStorage.getItem(CURRENT_SESSION_KEY);\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (true) return;\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            if (this.currentSessionId) {\n                localStorage.setItem(CURRENT_SESSION_KEY, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSession(name, config) {\n        if (this.useBackend) {\n            try {\n                // Validate config before sending\n                if (!config.crypto1 || !config.crypto2) {\n                    throw new Error('Invalid config: crypto1 and crypto2 are required');\n                }\n                const sessionData = {\n                    name,\n                    config: {\n                        tradingMode: config.tradingMode,\n                        crypto1: config.crypto1,\n                        crypto2: config.crypto2,\n                        baseBid: config.baseBid,\n                        multiplier: config.multiplier,\n                        numDigits: config.numDigits,\n                        slippagePercent: config.slippagePercent,\n                        incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                        incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                        preferredStablecoin: config.preferredStablecoin\n                    },\n                    targetPriceRows: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0\n                };\n                console.log('📤 Creating session with data:', sessionData);\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                const sessionId = response.session.id;\n                console.log('✅ Session created on backend:', sessionId);\n                return sessionId;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend:', error);\n                console.error('❌ Error details:', error.message);\n                // Don't fall back to localStorage for validation errors\n                if (error.message.includes('Validation error') || error.message.includes('422')) {\n                    throw error;\n                }\n                console.log('📱 Falling back to localStorage');\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: 10,\n            crypto2Balance: 100000,\n            stablecoinBalance: 0,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, isActive = false) {\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: session.runtime + (Date.now() - session.lastModified)\n            };\n            this.sessions.set(sessionId, updatedSession);\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    deleteSession(sessionId) {\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                localStorage.removeItem(CURRENT_SESSION_KEY);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: `${session.config.crypto1}/${session.config.crypto2}`,\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: session.runtime,\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            localStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>[\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    entry.amountCrypto1?.toFixed(session.config.numDigits) || '',\n                    entry.avgPrice?.toFixed(session.config.numDigits) || '',\n                    entry.valueCrypto2?.toFixed(session.config.numDigits) || '',\n                    entry.price1?.toFixed(session.config.numDigits) || '',\n                    entry.crypto1Symbol,\n                    entry.price2?.toFixed(session.config.numDigits) || '',\n                    entry.crypto2Symbol,\n                    entry.realizedProfitLossCrypto1?.toFixed(session.config.numDigits) || '',\n                    entry.realizedProfitLossCrypto2?.toFixed(session.config.numDigits) || ''\n                ].join(','))\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        localStorage.removeItem(CURRENT_SESSION_KEY);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData, intervalMs = 30000 // 30 seconds\n    ) {\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/session-manager.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/types.tsx":
/*!***************************!*\
  !*** ./src/lib/types.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AVAILABLE_CRYPTOS: () => (/* binding */ AVAILABLE_CRYPTOS),\n/* harmony export */   AVAILABLE_QUOTES_SIMPLE: () => (/* binding */ AVAILABLE_QUOTES_SIMPLE),\n/* harmony export */   AVAILABLE_STABLECOINS: () => (/* binding */ AVAILABLE_STABLECOINS),\n/* harmony export */   DEFAULT_APP_SETTINGS: () => (/* binding */ DEFAULT_APP_SETTINGS)\n/* harmony export */ });\nconst DEFAULT_APP_SETTINGS = {\n    soundAlertsEnabled: true,\n    alertOnOrderExecution: true,\n    alertOnError: true,\n    soundOrderExecution: '/sounds/order-executed.mp3',\n    soundError: '/sounds/error.mp3',\n    clearOrderHistoryOnStart: false\n};\n// Dummy constants, replace with actual values or logic as needed\nconst AVAILABLE_CRYPTOS = [\n    'BTC',\n    'ETH',\n    'ADA',\n    'SOL',\n    'DOGE',\n    'LINK',\n    'MATIC',\n    'DOT',\n    'AVAX',\n    'XRP',\n    'LTC',\n    'BCH',\n    'BNB',\n    'SHIB'\n];\nconst AVAILABLE_QUOTES_SIMPLE = {\n    BTC: [\n        'USDT',\n        'USDC',\n        'FDUSD',\n        'EUR'\n    ],\n    ETH: [\n        'USDT',\n        'USDC',\n        'FDUSD',\n        'BTC',\n        'EUR'\n    ],\n    ADA: [\n        'USDT',\n        'USDC',\n        'BTC',\n        'ETH'\n    ],\n    SOL: [\n        'USDT',\n        'USDC',\n        'BTC',\n        'ETH'\n    ]\n};\nconst AVAILABLE_STABLECOINS = [\n    'USDT',\n    'USDC',\n    'FDUSD',\n    'DAI'\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/types.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXHNyY1xcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/@opentelemetry","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/uuid","vendor-chunks/clsx","vendor-chunks/geist"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();