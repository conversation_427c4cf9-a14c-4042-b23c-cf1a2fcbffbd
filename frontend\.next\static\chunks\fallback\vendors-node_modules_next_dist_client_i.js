"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_i"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/lib/console.js":
/*!******************************************************!*\
  !*** ./node_modules/next/dist/client/lib/console.js ***!
  \******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatConsoleArgs: function() {\n        return formatConsoleArgs;\n    },\n    parseConsoleArgs: function() {\n        return parseConsoleArgs;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nfunction formatObject(arg, depth) {\n    switch(typeof arg){\n        case 'object':\n            if (arg === null) {\n                return 'null';\n            } else if (Array.isArray(arg)) {\n                let result = '[';\n                if (depth < 1) {\n                    for(let i = 0; i < arg.length; i++){\n                        if (result !== '[') {\n                            result += ',';\n                        }\n                        if (Object.prototype.hasOwnProperty.call(arg, i)) {\n                            result += formatObject(arg[i], depth + 1);\n                        }\n                    }\n                } else {\n                    result += arg.length > 0 ? '...' : '';\n                }\n                result += ']';\n                return result;\n            } else if (arg instanceof Error) {\n                return arg + '';\n            } else {\n                const keys = Object.keys(arg);\n                let result = '{';\n                if (depth < 1) {\n                    for(let i = 0; i < keys.length; i++){\n                        const key = keys[i];\n                        const desc = Object.getOwnPropertyDescriptor(arg, 'key');\n                        if (desc && !desc.get && !desc.set) {\n                            const jsonKey = JSON.stringify(key);\n                            if (jsonKey !== '\"' + key + '\"') {\n                                result += jsonKey + ': ';\n                            } else {\n                                result += key + ': ';\n                            }\n                            result += formatObject(desc.value, depth + 1);\n                        }\n                    }\n                } else {\n                    result += keys.length > 0 ? '...' : '';\n                }\n                result += '}';\n                return result;\n            }\n        case 'string':\n            return JSON.stringify(arg);\n        default:\n            return String(arg);\n    }\n}\nfunction formatConsoleArgs(args) {\n    let message;\n    let idx;\n    if (typeof args[0] === 'string') {\n        message = args[0];\n        idx = 1;\n    } else {\n        message = '';\n        idx = 0;\n    }\n    let result = '';\n    let startQuote = false;\n    for(let i = 0; i < message.length; ++i){\n        const char = message[i];\n        if (char !== '%' || i === message.length - 1 || idx >= args.length) {\n            result += char;\n            continue;\n        }\n        const code = message[++i];\n        switch(code){\n            case 'c':\n                {\n                    // TODO: We should colorize with HTML instead of turning into a string.\n                    // Ignore for now.\n                    result = startQuote ? \"\" + result + \"]\" : \"[\" + result;\n                    startQuote = !startQuote;\n                    idx++;\n                    break;\n                }\n            case 'O':\n            case 'o':\n                {\n                    result += formatObject(args[idx++], 0);\n                    break;\n                }\n            case 'd':\n            case 'i':\n                {\n                    result += parseInt(args[idx++], 10);\n                    break;\n                }\n            case 'f':\n                {\n                    result += parseFloat(args[idx++]);\n                    break;\n                }\n            case 's':\n                {\n                    result += String(args[idx++]);\n                    break;\n                }\n            default:\n                result += '%' + code;\n        }\n    }\n    for(; idx < args.length; idx++){\n        result += (idx > 0 ? ' ' : '') + formatObject(args[idx], 0);\n    }\n    return result;\n}\nfunction parseConsoleArgs(args) {\n    // See\n    // https://github.com/facebook/react/blob/65a56d0e99261481c721334a3ec4561d173594cd/packages/react-devtools-shared/src/backend/flight/renderer.js#L88-L93\n    //\n    // Logs replayed from the server look like this:\n    // [\n    //   \"%c%s%c %o\\n\\n%s\\n\\n%s\\n\",\n    //   \"background: #e6e6e6; ...\",\n    //   \" Server \", // can also be e.g. \" Prerender \"\n    //   \"\",\n    //   Error,\n    //   \"The above error occurred in the <Page> component.\",\n    //   ...\n    // ]\n    if (args.length > 3 && typeof args[0] === 'string' && args[0].startsWith('%c%s%c ') && typeof args[1] === 'string' && typeof args[2] === 'string' && typeof args[3] === 'string') {\n        const environmentName = args[2];\n        const maybeError = args[4];\n        return {\n            environmentName: environmentName.trim(),\n            error: (0, _iserror.default)(maybeError) ? maybeError : null\n        };\n    }\n    return {\n        environmentName: null,\n        error: null\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=console.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/lib/console.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/lib/is-error-thrown-while-rendering-rsc.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/client/lib/is-error-thrown-while-rendering-rsc.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"shouldRenderRootLevelErrorOverlay\", ({\n    enumerable: true,\n    get: function() {\n        return shouldRenderRootLevelErrorOverlay;\n    }\n}));\nconst shouldRenderRootLevelErrorOverlay = ()=>{\n    var _window___next_root_layout_missing_tags;\n    return !!((_window___next_root_layout_missing_tags = window.__next_root_layout_missing_tags) == null ? void 0 : _window___next_root_layout_missing_tags.length);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=is-error-thrown-while-rendering-rsc.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpYi9pcy1lcnJvci10aHJvd24td2hpbGUtcmVuZGVyaW5nLXJzYy5qcyIsIm1hcHBpbmdzIjoiOzs7O3FFQUFhQTs7O2VBQUFBOzs7QUFBTixNQUFNQSxvQ0FBb0M7UUFDdENDO0lBQVQsT0FBTyxDQUFDLEdBQUNBLDBDQUFBQSxPQUFPQywrQkFBQUEsS0FBK0IsZ0JBQXRDRCx3Q0FBd0NFLE1BQUFBO0FBQ25EIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGxpYlxcaXMtZXJyb3ItdGhyb3duLXdoaWxlLXJlbmRlcmluZy1yc2MudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHNob3VsZFJlbmRlclJvb3RMZXZlbEVycm9yT3ZlcmxheSA9ICgpID0+IHtcbiAgcmV0dXJuICEhd2luZG93Ll9fbmV4dF9yb290X2xheW91dF9taXNzaW5nX3RhZ3M/Lmxlbmd0aFxufVxuIl0sIm5hbWVzIjpbInNob3VsZFJlbmRlclJvb3RMZXZlbEVycm9yT3ZlcmxheSIsIndpbmRvdyIsIl9fbmV4dF9yb290X2xheW91dF9taXNzaW5nX3RhZ3MiLCJsZW5ndGgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/lib/is-error-thrown-while-rendering-rsc.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/index.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/client/index.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* global location */ // imports polyfill from `@next/polyfill-module` after build.\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    emitter: function() {\n        return emitter;\n    },\n    hydrate: function() {\n        return hydrate;\n    },\n    initialize: function() {\n        return initialize;\n    },\n    router: function() {\n        return router;\n    },\n    version: function() {\n        return version;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\n__webpack_require__(/*! ../build/polyfills/polyfill-module */ \"(pages-dir-browser)/./node_modules/next/dist/build/polyfills/polyfill-module.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _client = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom/client */ \"(pages-dir-browser)/./node_modules/react-dom/client.js\"));\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _mitt = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../shared/lib/mitt */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/mitt.js\"));\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ../shared/lib/router/utils/handle-smooth-scroll */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _isdynamic = __webpack_require__(/*! ../shared/lib/router/utils/is-dynamic */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");\nconst _querystring = __webpack_require__(/*! ../shared/lib/router/utils/querystring */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\");\nconst _runtimeconfigexternal = __webpack_require__(/*! ../shared/lib/runtime-config.external */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/runtime-config.external.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _portal = __webpack_require__(/*! ./portal */ \"(pages-dir-browser)/./node_modules/next/dist/client/portal/index.js\");\nconst _headmanager = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./head-manager */ \"(pages-dir-browser)/./node_modules/next/dist/client/head-manager.js\"));\nconst _pageloader = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./page-loader */ \"(pages-dir-browser)/./node_modules/next/dist/client/page-loader.js\"));\nconst _routeannouncer = __webpack_require__(/*! ./route-announcer */ \"(pages-dir-browser)/./node_modules/next/dist/client/route-announcer.js\");\nconst _router = __webpack_require__(/*! ./router */ \"(pages-dir-browser)/./node_modules/next/dist/client/router.js\");\nconst _iserror = __webpack_require__(/*! ../lib/is-error */ \"(pages-dir-browser)/./node_modules/next/dist/lib/is-error.js\");\nconst _imageconfigcontextsharedruntime = __webpack_require__(/*! ../shared/lib/image-config-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js\");\nconst _removebasepath = __webpack_require__(/*! ./remove-base-path */ \"(pages-dir-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ./has-base-path */ \"(pages-dir-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _adapters = __webpack_require__(/*! ../shared/lib/router/adapters */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/adapters.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../shared/lib/hooks-client-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _onrecoverableerror = __webpack_require__(/*! ./react-client-callbacks/on-recoverable-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\");\nconst _tracer = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./tracing/tracer */ \"(pages-dir-browser)/./node_modules/next/dist/client/tracing/tracer.js\"));\nconst _isnextroutererror = __webpack_require__(/*! ./components/is-next-router-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst version = \"15.2.3\";\nlet router;\nconst emitter = (0, _mitt.default)();\nconst looseToArray = (input)=>[].slice.call(input);\nlet initialData;\nlet defaultLocale = undefined;\nlet asPath;\nlet pageLoader;\nlet appElement;\nlet headManager;\nlet initialMatchesMiddleware = false;\nlet lastAppProps;\nlet lastRenderReject;\nlet devClient;\nlet CachedApp, onPerfEntry;\nlet CachedComponent;\nclass Container extends _react.default.Component {\n    componentDidCatch(componentErr, info) {\n        this.props.fn(componentErr, info);\n    }\n    componentDidMount() {\n        this.scrollToHash();\n        // We need to replace the router state if:\n        // - the page was (auto) exported and has a query string or search (hash)\n        // - it was auto exported and is a dynamic route (to provide params)\n        // - if it is a client-side skeleton (fallback render)\n        // - if middleware matches the current page (may have rewrite params)\n        // - if rewrites in next.config.js match (may have rewrite params)\n        if (router.isSsr && (initialData.isFallback || initialData.nextExport && ((0, _isdynamic.isDynamicRoute)(router.pathname) || location.search || false || initialMatchesMiddleware) || initialData.props && initialData.props.__N_SSG && (location.search || false || initialMatchesMiddleware))) {\n            // update query on mount for exported pages\n            router.replace(router.pathname + '?' + String((0, _querystring.assign)((0, _querystring.urlQueryToSearchParams)(router.query), new URLSearchParams(location.search))), asPath, {\n                // @ts-ignore\n                // WARNING: `_h` is an internal option for handing Next.js\n                // client-side hydration. Your app should _never_ use this property.\n                // It may change at any time without notice.\n                _h: 1,\n                // Fallback pages must trigger the data fetch, so the transition is\n                // not shallow.\n                // Other pages (strictly updating query) happens shallowly, as data\n                // requirements would already be present.\n                shallow: !initialData.isFallback && !initialMatchesMiddleware\n            }).catch((err)=>{\n                if (!err.cancelled) throw err;\n            });\n        }\n    }\n    componentDidUpdate() {\n        this.scrollToHash();\n    }\n    scrollToHash() {\n        let { hash } = location;\n        hash = hash && hash.substring(1);\n        if (!hash) return;\n        const el = document.getElementById(hash);\n        if (!el) return;\n        // If we call scrollIntoView() in here without a setTimeout\n        // it won't scroll properly.\n        setTimeout(()=>el.scrollIntoView(), 0);\n    }\n    render() {\n        if (false) {} else {\n            const { PagesDevOverlay } = __webpack_require__(/*! ./components/react-dev-overlay/pages/pages-dev-overlay */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/pages-dev-overlay.js\");\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(PagesDevOverlay, {\n                children: this.props.children\n            });\n        }\n    }\n}\nasync function initialize(opts) {\n    if (opts === void 0) opts = {};\n    // This makes sure this specific lines are removed in production\n    if (true) {\n        _tracer.default.onSpanEnd((__webpack_require__(/*! ./tracing/report-to-socket */ \"(pages-dir-browser)/./node_modules/next/dist/client/tracing/report-to-socket.js\")[\"default\"]));\n        devClient = opts.devClient;\n    }\n    initialData = JSON.parse(document.getElementById('__NEXT_DATA__').textContent);\n    window.__NEXT_DATA__ = initialData;\n    defaultLocale = initialData.defaultLocale;\n    const prefix = initialData.assetPrefix || '';\n    self.__next_set_public_path__(\"\" + prefix + \"/_next/\") //eslint-disable-line\n    ;\n    // Initialize next/config with the environment configuration\n    (0, _runtimeconfigexternal.setConfig)({\n        serverRuntimeConfig: {},\n        publicRuntimeConfig: initialData.runtimeConfig || {}\n    });\n    asPath = (0, _utils.getURL)();\n    // make sure not to attempt stripping basePath for 404s\n    if ((0, _hasbasepath.hasBasePath)(asPath)) {\n        asPath = (0, _removebasepath.removeBasePath)(asPath);\n    }\n    if (false) {}\n    if (initialData.scriptLoader) {\n        const { initScriptLoader } = __webpack_require__(/*! ./script */ \"(pages-dir-browser)/./node_modules/next/dist/client/script.js\");\n        initScriptLoader(initialData.scriptLoader);\n    }\n    pageLoader = new _pageloader.default(initialData.buildId, prefix);\n    const register = (param)=>{\n        let [r, f] = param;\n        return pageLoader.routeLoader.onEntrypoint(r, f);\n    };\n    if (window.__NEXT_P) {\n        // Defer page registration for another tick. This will increase the overall\n        // latency in hydrating the page, but reduce the total blocking time.\n        window.__NEXT_P.map((p)=>setTimeout(()=>register(p), 0));\n    }\n    window.__NEXT_P = [];\n    window.__NEXT_P.push = register;\n    headManager = (0, _headmanager.default)();\n    headManager.getIsSsr = ()=>{\n        return router.isSsr;\n    };\n    appElement = document.getElementById('__next');\n    return {\n        assetPrefix: prefix\n    };\n}\nfunction renderApp(App, appProps) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(App, {\n        ...appProps\n    });\n}\nfunction AppContainer(param) {\n    _s();\n    let { children } = param;\n    // Create a memoized value for next/navigation router context.\n    const adaptedForAppRouter = _react.default.useMemo({\n        \"AppContainer.useMemo[adaptedForAppRouter]\": ()=>{\n            return (0, _adapters.adaptForAppRouterInstance)(router);\n        }\n    }[\"AppContainer.useMemo[adaptedForAppRouter]\"], []);\n    var _self___NEXT_DATA___autoExport;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(Container, {\n        fn: (error)=>// eslint-disable-next-line @typescript-eslint/no-use-before-define\n            renderError({\n                App: CachedApp,\n                err: error\n            }).catch((err)=>console.error('Error rendering page: ', err)),\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n            value: adaptedForAppRouter,\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                value: (0, _adapters.adaptForSearchParams)(router),\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_adapters.PathnameContextProviderAdapter, {\n                    router: router,\n                    isAutoExport: (_self___NEXT_DATA___autoExport = self.__NEXT_DATA__.autoExport) != null ? _self___NEXT_DATA___autoExport : false,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                        value: (0, _adapters.adaptForPathParams)(router),\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_routercontextsharedruntime.RouterContext.Provider, {\n                            value: (0, _router.makePublicRouterInstance)(router),\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_headmanagercontextsharedruntime.HeadManagerContext.Provider, {\n                                value: headManager,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_imageconfigcontextsharedruntime.ImageConfigContext.Provider, {\n                                    value: {\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"dangerouslyAllowSVG\":false,\"unoptimized\":false,\"domains\":[],\"remotePatterns\":[],\"output\":\"standalone\"},\n                                    children: children\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        })\n    });\n}\n_s(AppContainer, \"F6BSfrFQNeqenuPnUMVY/6gI8uE=\");\n_c = AppContainer;\nconst wrapApp = (App)=>(wrappedAppProps)=>{\n        const appProps = {\n            ...wrappedAppProps,\n            Component: CachedComponent,\n            err: initialData.err,\n            router\n        };\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(AppContainer, {\n            children: renderApp(App, appProps)\n        });\n    };\n// This method handles all runtime and debug errors.\n// 404 and 500 errors are special kind of errors\n// and they are still handle via the main render method.\nfunction renderError(renderErrorProps) {\n    let { App, err } = renderErrorProps;\n    // In development runtime errors are caught by our overlay\n    // In production we catch runtime errors using componentDidCatch which will trigger renderError\n    if (true) {\n        // A Next.js rendering runtime error is always unrecoverable\n        // FIXME: let's make this recoverable (error in GIP client-transition)\n        devClient.onUnrecoverableError();\n        // We need to render an empty <App> so that the `<ReactDevOverlay>` can\n        // render itself.\n        // TODO: Fix disabled eslint rule\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        return doRender({\n            App: ()=>null,\n            props: {},\n            Component: ()=>null,\n            styleSheets: []\n        });\n    }\n    // Make sure we log the error to the console, otherwise users can't track down issues.\n    console.error(err);\n    console.error(\"A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred\");\n    return pageLoader.loadPage('/_error').then((param)=>{\n        let { page: ErrorComponent, styleSheets } = param;\n        return (lastAppProps == null ? void 0 : lastAppProps.Component) === ErrorComponent ? Promise.all(/*! import() */[__webpack_require__.e(\"vendors-node_modules_next_dist_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_l\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_shared_js-0\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_call-stack_c-dc969f58\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-c82b02ac\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-13e6d335\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dialog_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_en\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_e\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ho\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_o\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_redirect-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_router-reducer_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_i\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_lib_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_p\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_h\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_m\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_router_router_js-58cbbd23\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_ro\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_seg\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_s\"), __webpack_require__.e(\"vendors-node_modules_p\"), __webpack_require__.e(\"vendors-node_modules_react-dom_cjs_react-dom_development_js-7b5aa877\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! ../pages/_error */ \"(pages-dir-browser)/./node_modules/next/dist/pages/_error.js\", 23)).then((errorModule)=>{\n            return Promise.all(/*! import() */[__webpack_require__.e(\"vendors-node_modules_next_dist_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_l\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_shared_js-0\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_call-stack_c-dc969f58\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-c82b02ac\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-13e6d335\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dialog_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_en\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_e\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ho\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_o\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_redirect-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_router-reducer_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_i\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_lib_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_p\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_h\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_m\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_router_router_js-58cbbd23\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_ro\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_seg\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_s\"), __webpack_require__.e(\"vendors-node_modules_p\"), __webpack_require__.e(\"vendors-node_modules_react-dom_cjs_react-dom_development_js-7b5aa877\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! ../pages/_app */ \"(pages-dir-browser)/./node_modules/next/dist/pages/_app.js\", 23)).then((appModule)=>{\n                App = appModule.default;\n                renderErrorProps.App = App;\n                return errorModule;\n            });\n        }).then((m)=>({\n                ErrorComponent: m.default,\n                styleSheets: []\n            })) : {\n            ErrorComponent,\n            styleSheets\n        };\n    }).then((param)=>{\n        let { ErrorComponent, styleSheets } = param;\n        var _renderErrorProps_props;\n        // In production we do a normal render with the `ErrorComponent` as component.\n        // If we've gotten here upon initial render, we can use the props from the server.\n        // Otherwise, we need to call `getInitialProps` on `App` before mounting.\n        const AppTree = wrapApp(App);\n        const appCtx = {\n            Component: ErrorComponent,\n            AppTree,\n            router,\n            ctx: {\n                err,\n                pathname: initialData.page,\n                query: initialData.query,\n                asPath,\n                AppTree\n            }\n        };\n        return Promise.resolve(((_renderErrorProps_props = renderErrorProps.props) == null ? void 0 : _renderErrorProps_props.err) ? renderErrorProps.props : (0, _utils.loadGetInitialProps)(App, appCtx)).then((initProps)=>// eslint-disable-next-line @typescript-eslint/no-use-before-define\n            doRender({\n                ...renderErrorProps,\n                err,\n                Component: ErrorComponent,\n                styleSheets,\n                props: initProps\n            }));\n    });\n}\n// Dummy component that we render as a child of Root so that we can\n// toggle the correct styles before the page is rendered.\nfunction Head(param) {\n    _s1();\n    let { callback } = param;\n    // We use `useLayoutEffect` to guarantee the callback is executed\n    // as soon as React flushes the update.\n    _react.default.useLayoutEffect({\n        \"Head.useLayoutEffect\": ()=>callback()\n    }[\"Head.useLayoutEffect\"], [\n        callback\n    ]);\n    return null;\n}\n_s1(Head, \"n7/vCynhJvM+pLkyL2DMQUF0odM=\");\n_c1 = Head;\nconst performanceMarks = {\n    navigationStart: 'navigationStart',\n    beforeRender: 'beforeRender',\n    afterRender: 'afterRender',\n    afterHydrate: 'afterHydrate',\n    routeChange: 'routeChange'\n};\nconst performanceMeasures = {\n    hydration: 'Next.js-hydration',\n    beforeHydration: 'Next.js-before-hydration',\n    routeChangeToRender: 'Next.js-route-change-to-render',\n    render: 'Next.js-render'\n};\nlet reactRoot = null;\n// On initial render a hydrate should always happen\nlet shouldHydrate = true;\nfunction clearMarks() {\n    ;\n    [\n        performanceMarks.beforeRender,\n        performanceMarks.afterHydrate,\n        performanceMarks.afterRender,\n        performanceMarks.routeChange\n    ].forEach((mark)=>performance.clearMarks(mark));\n}\nfunction markHydrateComplete() {\n    if (!_utils.ST) return;\n    performance.mark(performanceMarks.afterHydrate) // mark end of hydration\n    ;\n    const hasBeforeRenderMark = performance.getEntriesByName(performanceMarks.beforeRender, 'mark').length;\n    if (hasBeforeRenderMark) {\n        const beforeHydrationMeasure = performance.measure(performanceMeasures.beforeHydration, performanceMarks.navigationStart, performanceMarks.beforeRender);\n        const hydrationMeasure = performance.measure(performanceMeasures.hydration, performanceMarks.beforeRender, performanceMarks.afterHydrate);\n        if ( true && // Old versions of Safari don't return `PerformanceMeasure`s from `performance.measure()`\n        beforeHydrationMeasure && hydrationMeasure) {\n            _tracer.default.startSpan('navigation-to-hydration', {\n                startTime: performance.timeOrigin + beforeHydrationMeasure.startTime,\n                attributes: {\n                    pathname: location.pathname,\n                    query: location.search\n                }\n            }).end(performance.timeOrigin + hydrationMeasure.startTime + hydrationMeasure.duration);\n        }\n    }\n    if (onPerfEntry) {\n        performance.getEntriesByName(performanceMeasures.hydration).forEach(onPerfEntry);\n    }\n    clearMarks();\n}\nfunction markRenderComplete() {\n    if (!_utils.ST) return;\n    performance.mark(performanceMarks.afterRender) // mark end of render\n    ;\n    const navStartEntries = performance.getEntriesByName(performanceMarks.routeChange, 'mark');\n    if (!navStartEntries.length) return;\n    const hasBeforeRenderMark = performance.getEntriesByName(performanceMarks.beforeRender, 'mark').length;\n    if (hasBeforeRenderMark) {\n        performance.measure(performanceMeasures.routeChangeToRender, navStartEntries[0].name, performanceMarks.beforeRender);\n        performance.measure(performanceMeasures.render, performanceMarks.beforeRender, performanceMarks.afterRender);\n        if (onPerfEntry) {\n            performance.getEntriesByName(performanceMeasures.render).forEach(onPerfEntry);\n            performance.getEntriesByName(performanceMeasures.routeChangeToRender).forEach(onPerfEntry);\n        }\n    }\n    clearMarks();\n    [\n        performanceMeasures.routeChangeToRender,\n        performanceMeasures.render\n    ].forEach((measure)=>performance.clearMeasures(measure));\n}\nfunction renderReactElement(domEl, fn) {\n    // mark start of hydrate/render\n    if (_utils.ST) {\n        performance.mark(performanceMarks.beforeRender);\n    }\n    const reactEl = fn(shouldHydrate ? markHydrateComplete : markRenderComplete);\n    if (!reactRoot) {\n        // Unlike with createRoot, you don't need a separate root.render() call here\n        reactRoot = _client.default.hydrateRoot(domEl, reactEl, {\n            onRecoverableError: _onrecoverableerror.onRecoverableError\n        });\n        // TODO: Remove shouldHydrate variable when React 18 is stable as it can depend on `reactRoot` existing\n        shouldHydrate = false;\n    } else {\n        const startTransition = _react.default.startTransition;\n        startTransition(()=>{\n            reactRoot.render(reactEl);\n        });\n    }\n}\nfunction Root(param) {\n    _s2();\n    let { callbacks, children } = param;\n    // We use `useLayoutEffect` to guarantee the callbacks are executed\n    // as soon as React flushes the update\n    _react.default.useLayoutEffect({\n        \"Root.useLayoutEffect\": ()=>callbacks.forEach({\n                \"Root.useLayoutEffect\": (callback)=>callback()\n            }[\"Root.useLayoutEffect\"])\n    }[\"Root.useLayoutEffect\"], [\n        callbacks\n    ]);\n    if (false) {}\n    return children;\n}\n_s2(Root, \"n7/vCynhJvM+pLkyL2DMQUF0odM=\");\n_c2 = Root;\nfunction doRender(input) {\n    let { App, Component, props, err } = input;\n    let styleSheets = 'initial' in input ? undefined : input.styleSheets;\n    Component = Component || lastAppProps.Component;\n    props = props || lastAppProps.props;\n    const appProps = {\n        ...props,\n        Component,\n        err,\n        router\n    };\n    // lastAppProps has to be set before ReactDom.render to account for ReactDom throwing an error.\n    lastAppProps = appProps;\n    let canceled = false;\n    let resolvePromise;\n    const renderPromise = new Promise((resolve, reject)=>{\n        if (lastRenderReject) {\n            lastRenderReject();\n        }\n        resolvePromise = ()=>{\n            lastRenderReject = null;\n            resolve();\n        };\n        lastRenderReject = ()=>{\n            canceled = true;\n            lastRenderReject = null;\n            const error = Object.defineProperty(new Error('Cancel rendering route'), \"__NEXT_ERROR_CODE\", {\n                value: \"E503\",\n                enumerable: false,\n                configurable: true\n            });\n            error.cancelled = true;\n            reject(error);\n        };\n    });\n    // This function has a return type to ensure it doesn't start returning a\n    // Promise. It should remain synchronous.\n    function onStart() {\n        if (!styleSheets || // We use `style-loader` in development, so we don't need to do anything\n        // unless we're in production:\n        \"development\" !== 'production') {\n            return false;\n        }\n        const currentStyleTags = looseToArray(document.querySelectorAll('style[data-n-href]'));\n        const currentHrefs = new Set(currentStyleTags.map((tag)=>tag.getAttribute('data-n-href')));\n        const noscript = document.querySelector('noscript[data-n-css]');\n        const nonce = noscript == null ? void 0 : noscript.getAttribute('data-n-css');\n        styleSheets.forEach((param)=>{\n            let { href, text } = param;\n            if (!currentHrefs.has(href)) {\n                const styleTag = document.createElement('style');\n                styleTag.setAttribute('data-n-href', href);\n                styleTag.setAttribute('media', 'x');\n                if (nonce) {\n                    styleTag.setAttribute('nonce', nonce);\n                }\n                document.head.appendChild(styleTag);\n                styleTag.appendChild(document.createTextNode(text));\n            }\n        });\n        return true;\n    }\n    function onHeadCommit() {\n        if (false) {}\n        if (input.scroll) {\n            const { x, y } = input.scroll;\n            (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n                window.scrollTo(x, y);\n            });\n        }\n    }\n    function onRootCommit() {\n        resolvePromise();\n    }\n    onStart();\n    const elem = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n                callback: onHeadCommit\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(AppContainer, {\n                children: [\n                    renderApp(App, appProps),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_portal.Portal, {\n                        type: \"next-route-announcer\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_routeannouncer.RouteAnnouncer, {})\n                    })\n                ]\n            })\n        ]\n    });\n    // We catch runtime errors using componentDidCatch which will trigger renderError\n    renderReactElement(appElement, (callback)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(Root, {\n            callbacks: [\n                callback,\n                onRootCommit\n            ],\n            children:  false ? /*#__PURE__*/ 0 : elem\n        }));\n    return renderPromise;\n}\nasync function render(renderingProps) {\n    // if an error occurs in a server-side page (e.g. in getInitialProps),\n    // skip re-rendering the error page client-side as data-fetching operations\n    // will already have been done on the server and NEXT_DATA contains the correct\n    // data for straight-forward hydration of the error page\n    if (renderingProps.err && // renderingProps.Component might be undefined if there is a top/module-level error\n    (typeof renderingProps.Component === 'undefined' || !renderingProps.isHydratePass)) {\n        await renderError(renderingProps);\n        return;\n    }\n    try {\n        await doRender(renderingProps);\n    } catch (err) {\n        const renderErr = (0, _iserror.getProperError)(err);\n        // bubble up cancelation errors\n        if (renderErr.cancelled) {\n            throw renderErr;\n        }\n        if (true) {\n            // Ensure this error is displayed in the overlay in development\n            setTimeout(()=>{\n                throw renderErr;\n            });\n        }\n        await renderError({\n            ...renderingProps,\n            err: renderErr\n        });\n    }\n}\nasync function hydrate(opts) {\n    let initialErr = initialData.err;\n    try {\n        const appEntrypoint = await pageLoader.routeLoader.whenEntrypoint('/_app');\n        if ('error' in appEntrypoint) {\n            throw appEntrypoint.error;\n        }\n        const { component: app, exports: mod } = appEntrypoint;\n        CachedApp = app;\n        if (mod && mod.reportWebVitals) {\n            onPerfEntry = (param)=>{\n                let { id, name, startTime, value, duration, entryType, entries, attribution } = param;\n                // Combines timestamp with random number for unique ID\n                const uniqueID = Date.now() + \"-\" + (Math.floor(Math.random() * (9e12 - 1)) + 1e12);\n                let perfStartEntry;\n                if (entries && entries.length) {\n                    perfStartEntry = entries[0].startTime;\n                }\n                const webVitals = {\n                    id: id || uniqueID,\n                    name,\n                    startTime: startTime || perfStartEntry,\n                    value: value == null ? duration : value,\n                    label: entryType === 'mark' || entryType === 'measure' ? 'custom' : 'web-vital'\n                };\n                if (attribution) {\n                    webVitals.attribution = attribution;\n                }\n                mod.reportWebVitals(webVitals);\n            };\n        }\n        const pageEntrypoint = // error, so we need to skip waiting for the entrypoint.\n         true && initialData.err ? {\n            error: initialData.err\n        } : await pageLoader.routeLoader.whenEntrypoint(initialData.page);\n        if ('error' in pageEntrypoint) {\n            throw pageEntrypoint.error;\n        }\n        CachedComponent = pageEntrypoint.component;\n        if (true) {\n            const { isValidElementType } = __webpack_require__(/*! next/dist/compiled/react-is */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/react-is/index.js\");\n            if (!isValidElementType(CachedComponent)) {\n                throw Object.defineProperty(new Error('The default export is not a React Component in page: \"' + initialData.page + '\"'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E286\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    } catch (error) {\n        // This catches errors like throwing in the top level of a module\n        initialErr = (0, _iserror.getProperError)(error);\n    }\n    if (true) {\n        const getServerError = (__webpack_require__(/*! ./components/react-dev-overlay/pages/client */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/client.js\").getServerError);\n        // Server-side runtime errors need to be re-thrown on the client-side so\n        // that the overlay is rendered.\n        if (initialErr) {\n            if (initialErr === initialData.err) {\n                setTimeout(()=>{\n                    let error;\n                    try {\n                        // Generate a new error object. We `throw` it because some browsers\n                        // will set the `stack` when thrown, and we want to ensure ours is\n                        // not overridden when we re-throw it below.\n                        throw Object.defineProperty(new Error(initialErr.message), \"__NEXT_ERROR_CODE\", {\n                            value: \"E394\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    } catch (e) {\n                        error = e;\n                    }\n                    error.name = initialErr.name;\n                    error.stack = initialErr.stack;\n                    const errSource = initialErr.source;\n                    // In development, error the navigation API usage in runtime,\n                    // since it's not allowed to be used in pages router as it doesn't contain error boundary like app router.\n                    if ((0, _isnextroutererror.isNextRouterError)(initialErr)) {\n                        error.message = 'Next.js navigation API is not allowed to be used in Pages Router.';\n                    }\n                    throw getServerError(error, errSource);\n                });\n            } else {\n                setTimeout(()=>{\n                    throw initialErr;\n                });\n            }\n        }\n    }\n    if (window.__NEXT_PRELOADREADY) {\n        await window.__NEXT_PRELOADREADY(initialData.dynamicIds);\n    }\n    router = (0, _router.createRouter)(initialData.page, initialData.query, asPath, {\n        initialProps: initialData.props,\n        pageLoader,\n        App: CachedApp,\n        Component: CachedComponent,\n        wrapApp,\n        err: initialErr,\n        isFallback: Boolean(initialData.isFallback),\n        subscription: (info, App, scroll)=>render(Object.assign({}, info, {\n                App,\n                scroll\n            })),\n        locale: initialData.locale,\n        locales: initialData.locales,\n        defaultLocale,\n        domainLocales: initialData.domainLocales,\n        isPreview: initialData.isPreview\n    });\n    initialMatchesMiddleware = await router._initialMatchesMiddlewarePromise;\n    const renderCtx = {\n        App: CachedApp,\n        initial: true,\n        Component: CachedComponent,\n        props: initialData.props,\n        err: initialErr,\n        isHydratePass: true\n    };\n    if (opts == null ? void 0 : opts.beforeRender) {\n        await opts.beforeRender();\n    }\n    render(renderCtx);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Root\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/index.js\n"));

/***/ })

}]);