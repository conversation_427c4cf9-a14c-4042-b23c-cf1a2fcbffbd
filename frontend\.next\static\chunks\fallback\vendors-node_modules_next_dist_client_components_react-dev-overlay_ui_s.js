"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/base.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/base.js ***!
  \**************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Base\", ({\n    enumerable: true,\n    get: function() {\n        return Base;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _css = __webpack_require__(/*! ../../utils/css */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n        :host {\\n          /* \\n           * Although the style applied to the shadow host is isolated,\\n           * the element that attached the shadow host (i.e. \\\"nextjs-portal\\\")\\n           * is still affected by the parent's style (e.g. \\\"body\\\"). This may\\n           * occur style conflicts like \\\"display: flex\\\", with other children\\n           * elements therefore give the shadow host an absolute position.\\n           */\\n          position: absolute;\\n\\n          --color-font: #757575;\\n          --color-backdrop: rgba(250, 250, 250, 0.8);\\n          --color-border-shadow: rgba(0, 0, 0, 0.145);\\n\\n          --color-title-color: #1f1f1f;\\n          --color-stack-notes: #777;\\n\\n          --color-accents-1: #808080;\\n          --color-accents-2: #222222;\\n          --color-accents-3: #404040;\\n\\n          --font-stack-monospace: '__nextjs-Geist Mono', 'Geist Mono',\\n            'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier,\\n            monospace;\\n          --font-stack-sans: '__nextjs-Geist', 'Geist', -apple-system,\\n            'Source Sans Pro', sans-serif;\\n\\n          font-family: var(--font-stack-sans);\\n\\n          /* TODO: Remove replaced ones. */\\n          --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n          --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1),\\n            0 1px 2px -1px rgb(0 0 0 / 0.1);\\n          --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1),\\n            0 2px 4px -2px rgb(0 0 0 / 0.1);\\n          --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),\\n            0 4px 6px -4px rgb(0 0 0 / 0.1);\\n          --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),\\n            0 8px 10px -6px rgb(0 0 0 / 0.1);\\n          --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n          --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\\n          --shadow-none: 0 0 #0000;\\n\\n          --shadow-small: 0px 2px 2px rgba(0, 0, 0, 0.04);\\n          --shadow-menu: 0px 1px 1px rgba(0, 0, 0, 0.02),\\n            0px 4px 8px -4px rgba(0, 0, 0, 0.04),\\n            0px 16px 24px -8px rgba(0, 0, 0, 0.06);\\n\\n          --focus-color: var(--color-blue-800);\\n          --focus-ring: 2px solid var(--focus-color);\\n\\n          --timing-swift: cubic-bezier(0.23, 0.88, 0.26, 0.92);\\n          --timing-overlay: cubic-bezier(0.175, 0.885, 0.32, 1.1);\\n\\n          --rounded-none: 0px;\\n          --rounded-sm: 2px;\\n          --rounded-md: 4px;\\n          --rounded-md-2: 6px;\\n          --rounded-lg: 8px;\\n          --rounded-xl: 12px;\\n          --rounded-2xl: 16px;\\n          --rounded-3xl: 24px;\\n          --rounded-4xl: 32px;\\n          --rounded-full: 9999px;\\n\\n          /* \\n            Suffix N of --size-N as px value when the base font size is 16px.\\n            Example: --size-1 is 1px, --size-2 is 2px, --size-3 is 3px, etc.\\n          */\\n          --size-1: 0.0625rem; /* 1px */\\n          --size-2: 0.125rem; /* 2px */\\n          --size-3: 0.1875rem; /* 3px */\\n          --size-4: 0.25rem; /* ...and more */\\n          --size-5: 0.3125rem;\\n          --size-6: 0.375rem;\\n          --size-7: 0.4375rem;\\n          --size-8: 0.5rem;\\n          --size-9: 0.5625rem;\\n          --size-10: 0.625rem;\\n          --size-11: 0.6875rem;\\n          --size-12: 0.75rem;\\n          --size-13: 0.8125rem;\\n          --size-14: 0.875rem;\\n          --size-15: 0.9375rem;\\n          /* If the base font size of the dev overlay changes e.g. 18px, \\n          just slide the window and make --size-18 as 1rem. */\\n          --size-16: 1rem;\\n          --size-17: 1.0625rem;\\n          --size-18: 1.125rem;\\n          --size-20: 1.25rem;\\n          --size-22: 1.375rem;\\n          --size-24: 1.5rem;\\n          --size-26: 1.625rem;\\n          --size-28: 1.75rem;\\n          --size-30: 1.875rem;\\n          --size-32: 2rem;\\n          --size-34: 2.125rem;\\n          --size-36: 2.25rem;\\n          --size-38: 2.375rem;\\n          --size-40: 2.5rem;\\n          --size-42: 2.625rem;\\n          --size-44: 2.75rem;\\n          --size-46: 2.875rem;\\n          --size-48: 3rem;\\n\\n          @media print {\\n            display: none;\\n          }\\n        }\\n\\n        h1,\\n        h2,\\n        h3,\\n        h4,\\n        h5,\\n        h6 {\\n          margin-bottom: 8px;\\n          font-weight: 500;\\n          line-height: 1.5;\\n        }\\n\\n        a {\\n          color: var(--color-blue-900);\\n          &:hover {\\n            color: var(--color-blue-900);\\n          }\\n          &:focus {\\n            outline: var(--focus-ring);\\n          }\\n        }\\n      \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction Base() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _css.css)(_templateObject())\n    });\n}\n_c = Base;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=base.js.map\nvar _c;\n$RefreshReg$(_c, \"Base\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/base.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/colors.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/colors.js ***!
  \****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Colors\", ({\n    enumerable: true,\n    get: function() {\n        return Colors;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _css = __webpack_require__(/*! ../../utils/css */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        '\\n        :host {\\n          /* \\n           * CAUTION: THIS IS A WORKAROUND!\\n           * For now, we use @babel/code-frame to parse the code frame which does not support option to change the color.\\n           * x-ref: https://github.com/babel/babel/blob/efa52324ff835b794c48080f14877b6caf32cd15/packages/babel-code-frame/src/defs.ts#L40-L54\\n           * So, we do a workaround mapping to change the color matching the theme.\\n           *\\n           * For example, in @babel/code-frame, the \"keyword\" is mapped to ANSI \"cyan\".\\n           * We want the \"keyword\" to use the \"syntax-keyword\" color in the theme.\\n           * So, we map the \"cyan\" to the \"syntax-keyword\" in the theme.\\n           */\\n          /* cyan: keyword */\\n          --color-ansi-cyan: var(--color-syntax-keyword);\\n          /* yellow: capitalized, jsxIdentifier, punctuation */\\n          --color-ansi-yellow: var(--color-syntax-function);\\n          /* magenta: number, regex */\\n          --color-ansi-magenta: var(--color-syntax-keyword);\\n          /* green: string */\\n          --color-ansi-green: var(--color-syntax-string);\\n          /* gray (bright black): comment, gutter */\\n          --color-ansi-bright-black: var(--color-syntax-comment);\\n\\n          /* Ansi - Temporary */\\n          --color-ansi-selection: var(--color-gray-alpha-300);\\n          --color-ansi-bg: var(--color-background-200);\\n          --color-ansi-fg: var(--color-gray-1000);\\n\\n          --color-ansi-white: var(--color-gray-700);\\n          --color-ansi-black: var(--color-gray-200);\\n          --color-ansi-blue: var(--color-blue-700);\\n          --color-ansi-red: var(--color-red-700);\\n          --color-ansi-bright-white: var(--color-gray-1000);\\n          --color-ansi-bright-blue: var(--color-blue-800);\\n          --color-ansi-bright-cyan: var(--color-blue-800);\\n          --color-ansi-bright-green: var(--color-green-800);\\n          --color-ansi-bright-magenta: var(--color-blue-800);\\n          --color-ansi-bright-red: var(--color-red-800);\\n          --color-ansi-bright-yellow: var(--color-amber-900);\\n\\n          /* Background Light */\\n          --color-background-100: #ffffff;\\n          --color-background-200: #fafafa;\\n\\n          /* Syntax Light */\\n          --color-syntax-comment: #545454;\\n          --color-syntax-constant: #171717;\\n          --color-syntax-function: #0054ad;\\n          --color-syntax-keyword: #a51850;\\n          --color-syntax-link: #066056;\\n          --color-syntax-parameter: #8f3e00;\\n          --color-syntax-punctuation: #171717;\\n          --color-syntax-string: #036157;\\n          --color-syntax-string-expression: #066056;\\n\\n          /* Gray Scale Light */\\n          --color-gray-100: #f2f2f2;\\n          --color-gray-200: #ebebeb;\\n          --color-gray-300: #e6e6e6;\\n          --color-gray-400: #eaeaea;\\n          --color-gray-500: #c9c9c9;\\n          --color-gray-600: #a8a8a8;\\n          --color-gray-700: #8f8f8f;\\n          --color-gray-800: #7d7d7d;\\n          --color-gray-900: #666666;\\n          --color-gray-1000: #171717;\\n\\n          /* Gray Alpha Scale Light */\\n          --color-gray-alpha-100: rgba(0, 0, 0, 0.05);\\n          --color-gray-alpha-200: rgba(0, 0, 0, 0.081);\\n          --color-gray-alpha-300: rgba(0, 0, 0, 0.1);\\n          --color-gray-alpha-400: rgba(0, 0, 0, 0.08);\\n          --color-gray-alpha-500: rgba(0, 0, 0, 0.21);\\n          --color-gray-alpha-600: rgba(0, 0, 0, 0.34);\\n          --color-gray-alpha-700: rgba(0, 0, 0, 0.44);\\n          --color-gray-alpha-800: rgba(0, 0, 0, 0.51);\\n          --color-gray-alpha-900: rgba(0, 0, 0, 0.605);\\n          --color-gray-alpha-1000: rgba(0, 0, 0, 0.91);\\n\\n          /* Blue Scale Light */\\n          --color-blue-100: #f0f7ff;\\n          --color-blue-200: #edf6ff;\\n          --color-blue-300: #e1f0ff;\\n          --color-blue-400: #cde7ff;\\n          --color-blue-500: #99ceff;\\n          --color-blue-600: #52aeff;\\n          --color-blue-700: #0070f3;\\n          --color-blue-800: #0060d1;\\n          --color-blue-900: #0067d6;\\n          --color-blue-1000: #0025ad;\\n\\n          /* Red Scale Light */\\n          --color-red-100: #fff0f0;\\n          --color-red-200: #ffebeb;\\n          --color-red-300: #ffe5e5;\\n          --color-red-400: #fdd8d8;\\n          --color-red-500: #f8baba;\\n          --color-red-600: #f87274;\\n          --color-red-700: #e5484d;\\n          --color-red-800: #da3036;\\n          --color-red-900: #ca2a30;\\n          --color-red-1000: #381316;\\n\\n          /* Amber Scale Light */\\n          --color-amber-100: #fff6e5;\\n          --color-amber-200: #fff4d5;\\n          --color-amber-300: #fef0cd;\\n          --color-amber-400: #ffddbf;\\n          --color-amber-500: #ffc96b;\\n          --color-amber-600: #f5b047;\\n          --color-amber-700: #ffb224;\\n          --color-amber-800: #ff990a;\\n          --color-amber-900: #a35200;\\n          --color-amber-1000: #4e2009;\\n\\n          /* Green Scale Light */\\n          --color-green-100: #effbef;\\n          --color-green-200: #eafaea;\\n          --color-green-300: #dcf6dc;\\n          --color-green-400: #c8f1c9;\\n          --color-green-500: #99e59f;\\n          --color-green-600: #6cda76;\\n          --color-green-700: #46a758;\\n          --color-green-800: #388e4a;\\n          --color-green-900: #297c3b;\\n          --color-green-1000: #18311e;\\n\\n          /* Turbopack Light - Temporary */\\n          --color-turbopack-text-red: #ff1e56;\\n          --color-turbopack-text-blue: #0096ff;\\n          --color-turbopack-border-red: #f0adbe;\\n          --color-turbopack-border-blue: #adccea;\\n          --color-turbopack-background-red: #fff7f9;\\n          --color-turbopack-background-blue: #f6fbff;\\n        }\\n      '\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction Colors() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _css.css)(_templateObject())\n    });\n}\n_c = Colors;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=colors.js.map\nvar _c;\n$RefreshReg$(_c, \"Colors\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/colors.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/component-styles.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/component-styles.js ***!
  \**************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ComponentStyles\", ({\n    enumerable: true,\n    get: function() {\n        return ComponentStyles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _codeframe = __webpack_require__(/*! ../components/code-frame/code-frame */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/code-frame/code-frame.js\");\nconst _dialog = __webpack_require__(/*! ../components/dialog */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/index.js\");\nconst _erroroverlaylayout = __webpack_require__(/*! ../components/errors/error-overlay-layout/error-overlay-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js\");\nconst _erroroverlaybottomstack = __webpack_require__(/*! ../components/errors/error-overlay-bottom-stack */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-bottom-stack/index.js\");\nconst _erroroverlaypagination = __webpack_require__(/*! ../components/errors/error-overlay-pagination/error-overlay-pagination */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-pagination/error-overlay-pagination.js\");\nconst _styles = __webpack_require__(/*! ../components/overlay/styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js\");\nconst _erroroverlayfooter = __webpack_require__(/*! ../components/errors/error-overlay-footer/error-overlay-footer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-overlay-footer.js\");\nconst _terminal = __webpack_require__(/*! ../components/terminal/terminal */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/terminal.js\");\nconst _toast = __webpack_require__(/*! ../components/toast */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/index.js\");\nconst _versionstalenessinfo = __webpack_require__(/*! ../components/version-staleness-info/version-staleness-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info.js\");\nconst _builderror = __webpack_require__(/*! ../container/build-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/build-error.js\");\nconst _errors = __webpack_require__(/*! ../container/errors */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/errors.js\");\nconst _runtimeerror = __webpack_require__(/*! ../container/runtime-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/index.js\");\nconst _copybutton = __webpack_require__(/*! ../components/copy-button */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/copy-button/index.js\");\nconst _callstackframe = __webpack_require__(/*! ../components/call-stack-frame/call-stack-frame */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/call-stack-frame/call-stack-frame.js\");\nconst _devtoolsindicator = __webpack_require__(/*! ../components/errors/dev-tools-indicator/dev-tools-indicator */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.js\");\nconst _css = __webpack_require__(/*! ../../utils/css */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nconst _editorlink = __webpack_require__(/*! ../components/terminal/editor-link */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/editor-link.js\");\nconst _environmentnamelabel = __webpack_require__(/*! ../components/errors/environment-name-label/environment-name-label */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/environment-name-label/environment-name-label.js\");\nconst _devtoolsinfo = __webpack_require__(/*! ../components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js\");\nconst _turbopackinfo = __webpack_require__(/*! ../components/errors/dev-tools-indicator/dev-tools-info/turbopack-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js\");\nconst _routeinfo = __webpack_require__(/*! ../components/errors/dev-tools-indicator/dev-tools-info/route-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js\");\nconst _userpreferences = __webpack_require__(/*! ../components/errors/dev-tools-indicator/dev-tools-info/user-preferences */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n      \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction ComponentStyles() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _css.css)(_templateObject(), _copybutton.COPY_BUTTON_STYLES, _callstackframe.CALL_STACK_FRAME_STYLES, _environmentnamelabel.ENVIRONMENT_NAME_LABEL_STYLES, _styles.styles, _toast.styles, _dialog.styles, _erroroverlaylayout.styles, _erroroverlayfooter.styles, _erroroverlaybottomstack.styles, _erroroverlaypagination.styles, _codeframe.CODE_FRAME_STYLES, _terminal.TERMINAL_STYLES, _editorlink.EDITOR_LINK_STYLES, _builderror.styles, _errors.styles, _runtimeerror.styles, _versionstalenessinfo.styles, _devtoolsindicator.DEV_TOOLS_INDICATOR_STYLES, _devtoolsinfo.DEV_TOOLS_INFO_STYLES, _turbopackinfo.DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES, _routeinfo.DEV_TOOLS_INFO_ROUTE_INFO_STYLES, _userpreferences.DEV_TOOLS_INFO_USER_PREFERENCES_STYLES)\n    });\n}\n_c = ComponentStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=component-styles.js.map\nvar _c;\n$RefreshReg$(_c, \"ComponentStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/component-styles.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/css-reset.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/css-reset.js ***!
  \*******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CssReset\", ({\n    enumerable: true,\n    get: function() {\n        return CssReset;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _css = __webpack_require__(/*! ../../utils/css */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n        :host {\\n          all: initial;\\n\\n          /* the direction property is not reset by 'all' */\\n          direction: ltr;\\n        }\\n\\n        /*!\\n         * Bootstrap Reboot v4.4.1 (https://getbootstrap.com/)\\n         * Copyright 2011-2019 The Bootstrap Authors\\n         * Copyright 2011-2019 Twitter, Inc.\\n         * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\\n         * Forked from Normalize.css, licensed MIT (https://github.com/necolas/normalize.css/blob/master/LICENSE.md)\\n         */\\n        *,\\n        *::before,\\n        *::after {\\n          box-sizing: border-box;\\n        }\\n\\n        :host {\\n          font-family: sans-serif;\\n          line-height: 1.15;\\n          -webkit-text-size-adjust: 100%;\\n          -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\n        }\\n\\n        article,\\n        aside,\\n        figcaption,\\n        figure,\\n        footer,\\n        header,\\n        hgroup,\\n        main,\\n        nav,\\n        section {\\n          display: block;\\n        }\\n\\n        :host {\\n          margin: 0;\\n          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,\\n            'Helvetica Neue', Arial, 'Noto Sans', sans-serif,\\n            'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',\\n            'Noto Color Emoji';\\n          font-size: 16px;\\n          font-weight: 400;\\n          line-height: 1.5;\\n          color: var(--color-font);\\n          text-align: left;\\n        }\\n\\n        :host:not(button) {\\n          background-color: #fff;\\n        }\\n\\n        [tabindex='-1']:focus:not(:focus-visible) {\\n          outline: 0 !important;\\n        }\\n\\n        hr {\\n          box-sizing: content-box;\\n          height: 0;\\n          overflow: visible;\\n        }\\n\\n        h1,\\n        h2,\\n        h3,\\n        h4,\\n        h5,\\n        h6 {\\n          margin-top: 0;\\n          margin-bottom: 8px;\\n        }\\n\\n        p {\\n          margin-top: 0;\\n          margin-bottom: 16px;\\n        }\\n\\n        abbr[title],\\n        abbr[data-original-title] {\\n          text-decoration: underline;\\n          -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n          cursor: help;\\n          border-bottom: 0;\\n          -webkit-text-decoration-skip-ink: none;\\n          text-decoration-skip-ink: none;\\n        }\\n\\n        address {\\n          margin-bottom: 16px;\\n          font-style: normal;\\n          line-height: inherit;\\n        }\\n\\n        ol,\\n        ul,\\n        dl {\\n          margin-top: 0;\\n          margin-bottom: 16px;\\n        }\\n\\n        ol ol,\\n        ul ul,\\n        ol ul,\\n        ul ol {\\n          margin-bottom: 0;\\n        }\\n\\n        dt {\\n          font-weight: 700;\\n        }\\n\\n        dd {\\n          margin-bottom: 8px;\\n          margin-left: 0;\\n        }\\n\\n        blockquote {\\n          margin: 0 0 16px;\\n        }\\n\\n        b,\\n        strong {\\n          font-weight: bolder;\\n        }\\n\\n        small {\\n          font-size: 80%;\\n        }\\n\\n        sub,\\n        sup {\\n          position: relative;\\n          font-size: 75%;\\n          line-height: 0;\\n          vertical-align: baseline;\\n        }\\n\\n        sub {\\n          bottom: -0.25em;\\n        }\\n\\n        sup {\\n          top: -0.5em;\\n        }\\n\\n        a {\\n          color: #007bff;\\n          text-decoration: none;\\n          background-color: transparent;\\n        }\\n\\n        a:hover {\\n          color: #0056b3;\\n          text-decoration: underline;\\n        }\\n\\n        a:not([href]) {\\n          color: inherit;\\n          text-decoration: none;\\n        }\\n\\n        a:not([href]):hover {\\n          color: inherit;\\n          text-decoration: none;\\n        }\\n\\n        pre,\\n        code,\\n        kbd,\\n        samp {\\n          font-family: SFMono-Regular, Menlo, Monaco, Consolas,\\n            'Liberation Mono', 'Courier New', monospace;\\n          font-size: 1em;\\n        }\\n\\n        pre {\\n          margin-top: 0;\\n          margin-bottom: 16px;\\n          overflow: auto;\\n        }\\n\\n        figure {\\n          margin: 0 0 16px;\\n        }\\n\\n        img {\\n          vertical-align: middle;\\n          border-style: none;\\n        }\\n\\n        svg {\\n          overflow: hidden;\\n          vertical-align: middle;\\n        }\\n\\n        table {\\n          border-collapse: collapse;\\n        }\\n\\n        caption {\\n          padding-top: 12px;\\n          padding-bottom: 12px;\\n          color: #6c757d;\\n          text-align: left;\\n          caption-side: bottom;\\n        }\\n\\n        th {\\n          text-align: inherit;\\n        }\\n\\n        label {\\n          display: inline-block;\\n          margin-bottom: 8px;\\n        }\\n\\n        button {\\n          border-radius: 0;\\n          border: 0;\\n          padding: 0;\\n          margin: 0;\\n          background: none;\\n          appearance: none;\\n          -webkit-appearance: none;\\n        }\\n\\n        button:focus {\\n          outline: 1px dotted;\\n          outline: 5px auto -webkit-focus-ring-color;\\n        }\\n\\n        button:focus:not(:focus-visible) {\\n          outline: none;\\n        }\\n\\n        input,\\n        button,\\n        select,\\n        optgroup,\\n        textarea {\\n          margin: 0;\\n          font-family: inherit;\\n          font-size: inherit;\\n          line-height: inherit;\\n        }\\n\\n        button,\\n        input {\\n          overflow: visible;\\n        }\\n\\n        button,\\n        select {\\n          text-transform: none;\\n        }\\n\\n        select {\\n          word-wrap: normal;\\n        }\\n\\n        button,\\n        [type='button'],\\n        [type='reset'],\\n        [type='submit'] {\\n          -webkit-appearance: button;\\n        }\\n\\n        button:not(:disabled),\\n        [type='button']:not(:disabled),\\n        [type='reset']:not(:disabled),\\n        [type='submit']:not(:disabled) {\\n          cursor: pointer;\\n        }\\n\\n        button::-moz-focus-inner,\\n        [type='button']::-moz-focus-inner,\\n        [type='reset']::-moz-focus-inner,\\n        [type='submit']::-moz-focus-inner {\\n          padding: 0;\\n          border-style: none;\\n        }\\n\\n        input[type='radio'],\\n        input[type='checkbox'] {\\n          box-sizing: border-box;\\n          padding: 0;\\n        }\\n\\n        input[type='date'],\\n        input[type='time'],\\n        input[type='datetime-local'],\\n        input[type='month'] {\\n          -webkit-appearance: listbox;\\n        }\\n\\n        textarea {\\n          overflow: auto;\\n          resize: vertical;\\n        }\\n\\n        fieldset {\\n          min-width: 0;\\n          padding: 0;\\n          margin: 0;\\n          border: 0;\\n        }\\n\\n        legend {\\n          display: block;\\n          width: 100%;\\n          max-width: 100%;\\n          padding: 0;\\n          margin-bottom: 8px;\\n          font-size: 24px;\\n          line-height: inherit;\\n          color: inherit;\\n          white-space: normal;\\n        }\\n\\n        progress {\\n          vertical-align: baseline;\\n        }\\n\\n        [type='number']::-webkit-inner-spin-button,\\n        [type='number']::-webkit-outer-spin-button {\\n          height: auto;\\n        }\\n\\n        [type='search'] {\\n          outline-offset: -2px;\\n          -webkit-appearance: none;\\n        }\\n\\n        [type='search']::-webkit-search-decoration {\\n          -webkit-appearance: none;\\n        }\\n\\n        ::-webkit-file-upload-button {\\n          font: inherit;\\n          -webkit-appearance: button;\\n        }\\n\\n        output {\\n          display: inline-block;\\n        }\\n\\n        summary {\\n          display: list-item;\\n          cursor: pointer;\\n        }\\n\\n        template {\\n          display: none;\\n        }\\n\\n        [hidden] {\\n          display: none !important;\\n        }\\n      \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction CssReset() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _css.css)(_templateObject())\n    });\n}\n_c = CssReset;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=css-reset.js.map\nvar _c;\n$RefreshReg$(_c, \"CssReset\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/css-reset.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/dark-theme.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/dark-theme.js ***!
  \********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DarkTheme\", ({\n    enumerable: true,\n    get: function() {\n        return DarkTheme;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _css = __webpack_require__(/*! ../../utils/css */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n      :host(.dark) {\\n        \",\n        \"\\n        \",\n        \"\\n      }\\n\\n      @media (prefers-color-scheme: dark) {\\n        :host(:not(.light)) {\\n          \",\n        \"\\n          \",\n        \"\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst colors = \"\\n  /* Background Dark */\\n  --color-background-100: #0a0a0a;\\n  --color-background-200: #000000;\\n\\n  /* Syntax Dark */\\n  --color-syntax-comment: #a0a0a0;\\n  --color-syntax-constant: #ededed;\\n  --color-syntax-function: #52a9ff;\\n  --color-syntax-keyword: #f76e99;\\n  --color-syntax-link: #0ac5b2;\\n  --color-syntax-parameter: #f1a10d;\\n  --color-syntax-punctuation: #ededed;\\n  --color-syntax-string: #0ac5b2;\\n  --color-syntax-string-expression: #0ac5b2;\\n\\n  /* Gray Scale Dark */\\n  --color-gray-100: #1a1a1a;\\n  --color-gray-200: #1f1f1f;\\n  --color-gray-300: #292929;\\n  --color-gray-400: #2e2e2e;\\n  --color-gray-500: #454545;\\n  --color-gray-600: #878787;\\n  --color-gray-700: #8f8f8f;\\n  --color-gray-800: #7d7d7d;\\n  --color-gray-900: #a0a0a0;\\n  --color-gray-1000: #ededed;\\n\\n  /* Gray Alpha Scale Dark */\\n  --color-gray-alpha-100: rgba(255, 255, 255, 0.066);\\n  --color-gray-alpha-200: rgba(255, 255, 255, 0.087);\\n  --color-gray-alpha-300: rgba(255, 255, 255, 0.125);\\n  --color-gray-alpha-400: rgba(255, 255, 255, 0.145);\\n  --color-gray-alpha-500: rgba(255, 255, 255, 0.239);\\n  --color-gray-alpha-600: rgba(255, 255, 255, 0.506);\\n  --color-gray-alpha-700: rgba(255, 255, 255, 0.54);\\n  --color-gray-alpha-800: rgba(255, 255, 255, 0.47);\\n  --color-gray-alpha-900: rgba(255, 255, 255, 0.61);\\n  --color-gray-alpha-1000: rgba(255, 255, 255, 0.923);\\n\\n  /* Blue Scale Dark */\\n  --color-blue-100: #0f1b2d;\\n  --color-blue-200: #10243e;\\n  --color-blue-300: #0f3058;\\n  --color-blue-400: #0d3868;\\n  --color-blue-500: #0a4481;\\n  --color-blue-600: #0091ff;\\n  --color-blue-700: #0070f3;\\n  --color-blue-800: #0060d1;\\n  --color-blue-900: #52a9ff;\\n  --color-blue-1000: #eaf6ff;\\n\\n  /* Red Scale Dark */\\n  --color-red-100: #2a1314;\\n  --color-red-200: #3d1719;\\n  --color-red-300: #551a1e;\\n  --color-red-400: #671e22;\\n  --color-red-500: #822025;\\n  --color-red-600: #e5484d;\\n  --color-red-700: #e5484d;\\n  --color-red-800: #da3036;\\n  --color-red-900: #ff6369;\\n  --color-red-1000: #ffecee;\\n\\n  /* Amber Scale Dark */\\n  --color-amber-100: #271700;\\n  --color-amber-200: #341c00;\\n  --color-amber-300: #4a2900;\\n  --color-amber-400: #573300;\\n  --color-amber-500: #693f05;\\n  --color-amber-600: #e79c13;\\n  --color-amber-700: #ffb224;\\n  --color-amber-800: #ff990a;\\n  --color-amber-900: #f1a10d;\\n  --color-amber-1000: #fef3dd;\\n\\n  /* Green Scale Dark */\\n  --color-green-100: #0b2211;\\n  --color-green-200: #0f2c17;\\n  --color-green-300: #11351b;\\n  --color-green-400: #0c461b;\\n  --color-green-500: #126427;\\n  --color-green-600: #1a9338;\\n  --color-green-700: #46a758;\\n  --color-green-800: #388e4a;\\n  --color-green-900: #63c174;\\n  --color-green-1000: #e5fbeb;\\n\\n  /* Turbopack Dark - Temporary */\\n  --color-turbopack-text-red: #ff6d92;\\n  --color-turbopack-text-blue: #45b2ff;\\n  --color-turbopack-border-red: #6e293b;\\n  --color-turbopack-border-blue: #284f80;\\n  --color-turbopack-background-red: #250d12;\\n  --color-turbopack-background-blue: #0a1723;\\n\";\nconst base = \"\\n  --color-font: white;\\n  --color-backdrop: rgba(0, 0, 0, 0.8);\\n  --color-border-shadow: rgba(255, 255, 255, 0.145);\\n\\n  --color-title-color: #fafafa;\\n  --color-stack-notes: #a9a9a9;\\n\";\nfunction DarkTheme() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _css.css)(_templateObject(), base, colors, base, colors)\n    });\n}\n_c = DarkTheme;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dark-theme.js.map\nvar _c;\n$RefreshReg$(_c, \"DarkTheme\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/dark-theme.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js ***!
  \***********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/**\n * Merge multiple args to a single string with spaces. Useful for merging class names.\n * @example\n * cx('foo', 'bar') // 'foo bar'\n * cx('foo', null, 'bar', undefined, 'baz', false) // 'foo bar baz'\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"cx\", ({\n    enumerable: true,\n    get: function() {\n        return cx;\n    }\n}));\nfunction cx() {\n    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n        args[_key] = arguments[_key];\n    }\n    return args.filter(Boolean).join(' ');\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=cx.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvdXRpbHMvY3guanMiLCJtYXBwaW5ncyI6IkFBQUE7Ozs7O0NBS0M7Ozs7c0NBQ2VBOzs7ZUFBQUE7OztBQUFULFNBQVNBO0lBQUcsaUNBQUdDLE9BQUg7UUFBR0EsSUFBQUEsQ0FBSCx1QkFBOEM7O0lBQy9ELE9BQU9BLEtBQUtDLE1BQU0sQ0FBQ0MsU0FBU0MsSUFBSSxDQUFDO0FBQ25DIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcdXRpbHNcXGN4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTWVyZ2UgbXVsdGlwbGUgYXJncyB0byBhIHNpbmdsZSBzdHJpbmcgd2l0aCBzcGFjZXMuIFVzZWZ1bCBmb3IgbWVyZ2luZyBjbGFzcyBuYW1lcy5cbiAqIEBleGFtcGxlXG4gKiBjeCgnZm9vJywgJ2JhcicpIC8vICdmb28gYmFyJ1xuICogY3goJ2ZvbycsIG51bGwsICdiYXInLCB1bmRlZmluZWQsICdiYXonLCBmYWxzZSkgLy8gJ2ZvbyBiYXIgYmF6J1xuICovXG5leHBvcnQgZnVuY3Rpb24gY3goLi4uYXJnczogKHN0cmluZyB8IHVuZGVmaW5lZCB8IG51bGwgfCBmYWxzZSlbXSk6IHN0cmluZyB7XG4gIHJldHVybiBhcmdzLmZpbHRlcihCb29sZWFuKS5qb2luKCcgJylcbn1cbiJdLCJuYW1lcyI6WyJjeCIsImFyZ3MiLCJmaWx0ZXIiLCJCb29sZWFuIiwiam9pbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/merge-refs.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/merge-refs.js ***!
  \*******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return mergeRefs;\n    }\n}));\nfunction mergeRefs() {\n    for(var _len = arguments.length, inputRefs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputRefs[_key] = arguments[_key];\n    }\n    const filteredInputRefs = inputRefs.filter(Boolean);\n    if (filteredInputRefs.length <= 1) {\n        const firstRef = filteredInputRefs[0];\n        return firstRef || null;\n    }\n    return function mergedRefs(ref) {\n        for (const inputRef of filteredInputRefs){\n            if (typeof inputRef === 'function') {\n                inputRef(ref);\n            } else if (inputRef) {\n                ;\n                inputRef.current = ref;\n            }\n        }\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=merge-refs.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/merge-refs.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/parse-url-from-text.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/parse-url-from-text.js ***!
  \****************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"parseUrlFromText\", ({\n    enumerable: true,\n    get: function() {\n        return parseUrlFromText;\n    }\n}));\nfunction parseUrlFromText(text, matcherFunc) {\n    const linkRegex = /https?:\\/\\/[^\\s/$.?#].[^\\s)'\"]*/gi;\n    const links = Array.from(text.matchAll(linkRegex), (match)=>match[0]);\n    if (matcherFunc) {\n        return links.filter((link)=>matcherFunc(link));\n    }\n    return links;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=parse-url-from-text.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvdXRpbHMvcGFyc2UtdXJsLWZyb20tdGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7O29EQUFnQkE7OztlQUFBQTs7O0FBQVQsU0FBU0EsaUJBQ2RDLElBQVksRUFDWkMsV0FBdUM7SUFFdkMsTUFBTUMsWUFBWTtJQUNsQixNQUFNQyxRQUFRQyxNQUFNQyxJQUFJLENBQUNMLEtBQUtNLFFBQVEsQ0FBQ0osWUFBWSxDQUFDSyxRQUFVQSxLQUFLLENBQUMsRUFBRTtJQUV0RSxJQUFJTixhQUFhO1FBQ2YsT0FBT0UsTUFBTUssTUFBTSxDQUFDLENBQUNDLE9BQVNSLFlBQVlRO0lBQzVDO0lBRUEsT0FBT047QUFDVCIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXHV0aWxzXFxwYXJzZS11cmwtZnJvbS10ZXh0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBwYXJzZVVybEZyb21UZXh0KFxuICB0ZXh0OiBzdHJpbmcsXG4gIG1hdGNoZXJGdW5jPzogKHRleHQ6IHN0cmluZykgPT4gYm9vbGVhblxuKTogc3RyaW5nW10ge1xuICBjb25zdCBsaW5rUmVnZXggPSAvaHR0cHM/OlxcL1xcL1teXFxzLyQuPyNdLlteXFxzKSdcIl0qL2dpXG4gIGNvbnN0IGxpbmtzID0gQXJyYXkuZnJvbSh0ZXh0Lm1hdGNoQWxsKGxpbmtSZWdleCksIChtYXRjaCkgPT4gbWF0Y2hbMF0pXG5cbiAgaWYgKG1hdGNoZXJGdW5jKSB7XG4gICAgcmV0dXJuIGxpbmtzLmZpbHRlcigobGluaykgPT4gbWF0Y2hlckZ1bmMobGluaykpXG4gIH1cblxuICByZXR1cm4gbGlua3Ncbn1cbiJdLCJuYW1lcyI6WyJwYXJzZVVybEZyb21UZXh0IiwidGV4dCIsIm1hdGNoZXJGdW5jIiwibGlua1JlZ2V4IiwibGlua3MiLCJBcnJheSIsImZyb20iLCJtYXRjaEFsbCIsIm1hdGNoIiwiZmlsdGVyIiwibGluayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/parse-url-from-text.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/use-open-in-editor.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/use-open-in-editor.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useOpenInEditor\", ({\n    enumerable: true,\n    get: function() {\n        return useOpenInEditor;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction useOpenInEditor(param) {\n    let { file, lineNumber, column } = param === void 0 ? {} : param;\n    const openInEditor = (0, _react.useCallback)(()=>{\n        if (file == null || lineNumber == null || column == null) return;\n        const params = new URLSearchParams();\n        params.append('file', file);\n        params.append('lineNumber', String(lineNumber));\n        params.append('column', String(column));\n        self.fetch(( false || '') + \"/__nextjs_launch-editor?\" + params.toString()).then(()=>{}, (cause)=>{\n            console.error('Failed to open file \"' + file + \" (\" + lineNumber + \":\" + column + ')\" in your editor. Cause:', cause);\n        });\n    }, [\n        file,\n        lineNumber,\n        column\n    ]);\n    return openInEditor;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-open-in-editor.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/use-open-in-editor.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/base.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/base.js ***!
  \**************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Base\", ({\n    enumerable: true,\n    get: function() {\n        return Base;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _css = __webpack_require__(/*! ../../utils/css */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n        :host {\\n          /* \\n           * Although the style applied to the shadow host is isolated,\\n           * the element that attached the shadow host (i.e. \\\"nextjs-portal\\\")\\n           * is still affected by the parent's style (e.g. \\\"body\\\"). This may\\n           * occur style conflicts like \\\"display: flex\\\", with other children\\n           * elements therefore give the shadow host an absolute position.\\n           */\\n          position: absolute;\\n\\n          --color-font: #757575;\\n          --color-backdrop: rgba(250, 250, 250, 0.8);\\n          --color-border-shadow: rgba(0, 0, 0, 0.145);\\n\\n          --color-title-color: #1f1f1f;\\n          --color-stack-notes: #777;\\n\\n          --color-accents-1: #808080;\\n          --color-accents-2: #222222;\\n          --color-accents-3: #404040;\\n\\n          --font-stack-monospace: '__nextjs-Geist Mono', 'Geist Mono',\\n            'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier,\\n            monospace;\\n          --font-stack-sans: '__nextjs-Geist', 'Geist', -apple-system,\\n            'Source Sans Pro', sans-serif;\\n\\n          font-family: var(--font-stack-sans);\\n\\n          /* TODO: Remove replaced ones. */\\n          --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n          --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1),\\n            0 1px 2px -1px rgb(0 0 0 / 0.1);\\n          --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1),\\n            0 2px 4px -2px rgb(0 0 0 / 0.1);\\n          --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),\\n            0 4px 6px -4px rgb(0 0 0 / 0.1);\\n          --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),\\n            0 8px 10px -6px rgb(0 0 0 / 0.1);\\n          --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n          --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\\n          --shadow-none: 0 0 #0000;\\n\\n          --shadow-small: 0px 2px 2px rgba(0, 0, 0, 0.04);\\n          --shadow-menu: 0px 1px 1px rgba(0, 0, 0, 0.02),\\n            0px 4px 8px -4px rgba(0, 0, 0, 0.04),\\n            0px 16px 24px -8px rgba(0, 0, 0, 0.06);\\n\\n          --focus-color: var(--color-blue-800);\\n          --focus-ring: 2px solid var(--focus-color);\\n\\n          --timing-swift: cubic-bezier(0.23, 0.88, 0.26, 0.92);\\n          --timing-overlay: cubic-bezier(0.175, 0.885, 0.32, 1.1);\\n\\n          --rounded-none: 0px;\\n          --rounded-sm: 2px;\\n          --rounded-md: 4px;\\n          --rounded-md-2: 6px;\\n          --rounded-lg: 8px;\\n          --rounded-xl: 12px;\\n          --rounded-2xl: 16px;\\n          --rounded-3xl: 24px;\\n          --rounded-4xl: 32px;\\n          --rounded-full: 9999px;\\n\\n          /* \\n            Suffix N of --size-N as px value when the base font size is 16px.\\n            Example: --size-1 is 1px, --size-2 is 2px, --size-3 is 3px, etc.\\n          */\\n          --size-1: 0.0625rem; /* 1px */\\n          --size-2: 0.125rem; /* 2px */\\n          --size-3: 0.1875rem; /* 3px */\\n          --size-4: 0.25rem; /* ...and more */\\n          --size-5: 0.3125rem;\\n          --size-6: 0.375rem;\\n          --size-7: 0.4375rem;\\n          --size-8: 0.5rem;\\n          --size-9: 0.5625rem;\\n          --size-10: 0.625rem;\\n          --size-11: 0.6875rem;\\n          --size-12: 0.75rem;\\n          --size-13: 0.8125rem;\\n          --size-14: 0.875rem;\\n          --size-15: 0.9375rem;\\n          /* If the base font size of the dev overlay changes e.g. 18px, \\n          just slide the window and make --size-18 as 1rem. */\\n          --size-16: 1rem;\\n          --size-17: 1.0625rem;\\n          --size-18: 1.125rem;\\n          --size-20: 1.25rem;\\n          --size-22: 1.375rem;\\n          --size-24: 1.5rem;\\n          --size-26: 1.625rem;\\n          --size-28: 1.75rem;\\n          --size-30: 1.875rem;\\n          --size-32: 2rem;\\n          --size-34: 2.125rem;\\n          --size-36: 2.25rem;\\n          --size-38: 2.375rem;\\n          --size-40: 2.5rem;\\n          --size-42: 2.625rem;\\n          --size-44: 2.75rem;\\n          --size-46: 2.875rem;\\n          --size-48: 3rem;\\n\\n          @media print {\\n            display: none;\\n          }\\n        }\\n\\n        h1,\\n        h2,\\n        h3,\\n        h4,\\n        h5,\\n        h6 {\\n          margin-bottom: 8px;\\n          font-weight: 500;\\n          line-height: 1.5;\\n        }\\n\\n        a {\\n          color: var(--color-blue-900);\\n          &:hover {\\n            color: var(--color-blue-900);\\n          }\\n          &:focus {\\n            outline: var(--focus-ring);\\n          }\\n        }\\n      \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction Base() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _css.css)(_templateObject())\n    });\n}\n_c = Base;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=base.js.map\nvar _c;\n$RefreshReg$(_c, \"Base\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/base.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/colors.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/colors.js ***!
  \****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Colors\", ({\n    enumerable: true,\n    get: function() {\n        return Colors;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _css = __webpack_require__(/*! ../../utils/css */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        '\\n        :host {\\n          /* \\n           * CAUTION: THIS IS A WORKAROUND!\\n           * For now, we use @babel/code-frame to parse the code frame which does not support option to change the color.\\n           * x-ref: https://github.com/babel/babel/blob/efa52324ff835b794c48080f14877b6caf32cd15/packages/babel-code-frame/src/defs.ts#L40-L54\\n           * So, we do a workaround mapping to change the color matching the theme.\\n           *\\n           * For example, in @babel/code-frame, the \"keyword\" is mapped to ANSI \"cyan\".\\n           * We want the \"keyword\" to use the \"syntax-keyword\" color in the theme.\\n           * So, we map the \"cyan\" to the \"syntax-keyword\" in the theme.\\n           */\\n          /* cyan: keyword */\\n          --color-ansi-cyan: var(--color-syntax-keyword);\\n          /* yellow: capitalized, jsxIdentifier, punctuation */\\n          --color-ansi-yellow: var(--color-syntax-function);\\n          /* magenta: number, regex */\\n          --color-ansi-magenta: var(--color-syntax-keyword);\\n          /* green: string */\\n          --color-ansi-green: var(--color-syntax-string);\\n          /* gray (bright black): comment, gutter */\\n          --color-ansi-bright-black: var(--color-syntax-comment);\\n\\n          /* Ansi - Temporary */\\n          --color-ansi-selection: var(--color-gray-alpha-300);\\n          --color-ansi-bg: var(--color-background-200);\\n          --color-ansi-fg: var(--color-gray-1000);\\n\\n          --color-ansi-white: var(--color-gray-700);\\n          --color-ansi-black: var(--color-gray-200);\\n          --color-ansi-blue: var(--color-blue-700);\\n          --color-ansi-red: var(--color-red-700);\\n          --color-ansi-bright-white: var(--color-gray-1000);\\n          --color-ansi-bright-blue: var(--color-blue-800);\\n          --color-ansi-bright-cyan: var(--color-blue-800);\\n          --color-ansi-bright-green: var(--color-green-800);\\n          --color-ansi-bright-magenta: var(--color-blue-800);\\n          --color-ansi-bright-red: var(--color-red-800);\\n          --color-ansi-bright-yellow: var(--color-amber-900);\\n\\n          /* Background Light */\\n          --color-background-100: #ffffff;\\n          --color-background-200: #fafafa;\\n\\n          /* Syntax Light */\\n          --color-syntax-comment: #545454;\\n          --color-syntax-constant: #171717;\\n          --color-syntax-function: #0054ad;\\n          --color-syntax-keyword: #a51850;\\n          --color-syntax-link: #066056;\\n          --color-syntax-parameter: #8f3e00;\\n          --color-syntax-punctuation: #171717;\\n          --color-syntax-string: #036157;\\n          --color-syntax-string-expression: #066056;\\n\\n          /* Gray Scale Light */\\n          --color-gray-100: #f2f2f2;\\n          --color-gray-200: #ebebeb;\\n          --color-gray-300: #e6e6e6;\\n          --color-gray-400: #eaeaea;\\n          --color-gray-500: #c9c9c9;\\n          --color-gray-600: #a8a8a8;\\n          --color-gray-700: #8f8f8f;\\n          --color-gray-800: #7d7d7d;\\n          --color-gray-900: #666666;\\n          --color-gray-1000: #171717;\\n\\n          /* Gray Alpha Scale Light */\\n          --color-gray-alpha-100: rgba(0, 0, 0, 0.05);\\n          --color-gray-alpha-200: rgba(0, 0, 0, 0.081);\\n          --color-gray-alpha-300: rgba(0, 0, 0, 0.1);\\n          --color-gray-alpha-400: rgba(0, 0, 0, 0.08);\\n          --color-gray-alpha-500: rgba(0, 0, 0, 0.21);\\n          --color-gray-alpha-600: rgba(0, 0, 0, 0.34);\\n          --color-gray-alpha-700: rgba(0, 0, 0, 0.44);\\n          --color-gray-alpha-800: rgba(0, 0, 0, 0.51);\\n          --color-gray-alpha-900: rgba(0, 0, 0, 0.605);\\n          --color-gray-alpha-1000: rgba(0, 0, 0, 0.91);\\n\\n          /* Blue Scale Light */\\n          --color-blue-100: #f0f7ff;\\n          --color-blue-200: #edf6ff;\\n          --color-blue-300: #e1f0ff;\\n          --color-blue-400: #cde7ff;\\n          --color-blue-500: #99ceff;\\n          --color-blue-600: #52aeff;\\n          --color-blue-700: #0070f3;\\n          --color-blue-800: #0060d1;\\n          --color-blue-900: #0067d6;\\n          --color-blue-1000: #0025ad;\\n\\n          /* Red Scale Light */\\n          --color-red-100: #fff0f0;\\n          --color-red-200: #ffebeb;\\n          --color-red-300: #ffe5e5;\\n          --color-red-400: #fdd8d8;\\n          --color-red-500: #f8baba;\\n          --color-red-600: #f87274;\\n          --color-red-700: #e5484d;\\n          --color-red-800: #da3036;\\n          --color-red-900: #ca2a30;\\n          --color-red-1000: #381316;\\n\\n          /* Amber Scale Light */\\n          --color-amber-100: #fff6e5;\\n          --color-amber-200: #fff4d5;\\n          --color-amber-300: #fef0cd;\\n          --color-amber-400: #ffddbf;\\n          --color-amber-500: #ffc96b;\\n          --color-amber-600: #f5b047;\\n          --color-amber-700: #ffb224;\\n          --color-amber-800: #ff990a;\\n          --color-amber-900: #a35200;\\n          --color-amber-1000: #4e2009;\\n\\n          /* Green Scale Light */\\n          --color-green-100: #effbef;\\n          --color-green-200: #eafaea;\\n          --color-green-300: #dcf6dc;\\n          --color-green-400: #c8f1c9;\\n          --color-green-500: #99e59f;\\n          --color-green-600: #6cda76;\\n          --color-green-700: #46a758;\\n          --color-green-800: #388e4a;\\n          --color-green-900: #297c3b;\\n          --color-green-1000: #18311e;\\n\\n          /* Turbopack Light - Temporary */\\n          --color-turbopack-text-red: #ff1e56;\\n          --color-turbopack-text-blue: #0096ff;\\n          --color-turbopack-border-red: #f0adbe;\\n          --color-turbopack-border-blue: #adccea;\\n          --color-turbopack-background-red: #fff7f9;\\n          --color-turbopack-background-blue: #f6fbff;\\n        }\\n      '\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction Colors() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _css.css)(_templateObject())\n    });\n}\n_c = Colors;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=colors.js.map\nvar _c;\n$RefreshReg$(_c, \"Colors\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/colors.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/component-styles.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/component-styles.js ***!
  \**************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ComponentStyles\", ({\n    enumerable: true,\n    get: function() {\n        return ComponentStyles;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _codeframe = __webpack_require__(/*! ../components/code-frame/code-frame */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/code-frame/code-frame.js\");\nconst _dialog = __webpack_require__(/*! ../components/dialog */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/index.js\");\nconst _erroroverlaylayout = __webpack_require__(/*! ../components/errors/error-overlay-layout/error-overlay-layout */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js\");\nconst _erroroverlaybottomstack = __webpack_require__(/*! ../components/errors/error-overlay-bottom-stack */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-bottom-stack/index.js\");\nconst _erroroverlaypagination = __webpack_require__(/*! ../components/errors/error-overlay-pagination/error-overlay-pagination */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-pagination/error-overlay-pagination.js\");\nconst _styles = __webpack_require__(/*! ../components/overlay/styles */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js\");\nconst _erroroverlayfooter = __webpack_require__(/*! ../components/errors/error-overlay-footer/error-overlay-footer */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-overlay-footer.js\");\nconst _terminal = __webpack_require__(/*! ../components/terminal/terminal */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/terminal.js\");\nconst _toast = __webpack_require__(/*! ../components/toast */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/index.js\");\nconst _versionstalenessinfo = __webpack_require__(/*! ../components/version-staleness-info/version-staleness-info */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info.js\");\nconst _builderror = __webpack_require__(/*! ../container/build-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/build-error.js\");\nconst _errors = __webpack_require__(/*! ../container/errors */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/errors.js\");\nconst _runtimeerror = __webpack_require__(/*! ../container/runtime-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/index.js\");\nconst _copybutton = __webpack_require__(/*! ../components/copy-button */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/copy-button/index.js\");\nconst _callstackframe = __webpack_require__(/*! ../components/call-stack-frame/call-stack-frame */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/call-stack-frame/call-stack-frame.js\");\nconst _devtoolsindicator = __webpack_require__(/*! ../components/errors/dev-tools-indicator/dev-tools-indicator */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.js\");\nconst _css = __webpack_require__(/*! ../../utils/css */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nconst _editorlink = __webpack_require__(/*! ../components/terminal/editor-link */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/editor-link.js\");\nconst _environmentnamelabel = __webpack_require__(/*! ../components/errors/environment-name-label/environment-name-label */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/environment-name-label/environment-name-label.js\");\nconst _devtoolsinfo = __webpack_require__(/*! ../components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js\");\nconst _turbopackinfo = __webpack_require__(/*! ../components/errors/dev-tools-indicator/dev-tools-info/turbopack-info */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js\");\nconst _routeinfo = __webpack_require__(/*! ../components/errors/dev-tools-indicator/dev-tools-info/route-info */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js\");\nconst _userpreferences = __webpack_require__(/*! ../components/errors/dev-tools-indicator/dev-tools-info/user-preferences */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n        \",\n        \"\\n      \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction ComponentStyles() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _css.css)(_templateObject(), _copybutton.COPY_BUTTON_STYLES, _callstackframe.CALL_STACK_FRAME_STYLES, _environmentnamelabel.ENVIRONMENT_NAME_LABEL_STYLES, _styles.styles, _toast.styles, _dialog.styles, _erroroverlaylayout.styles, _erroroverlayfooter.styles, _erroroverlaybottomstack.styles, _erroroverlaypagination.styles, _codeframe.CODE_FRAME_STYLES, _terminal.TERMINAL_STYLES, _editorlink.EDITOR_LINK_STYLES, _builderror.styles, _errors.styles, _runtimeerror.styles, _versionstalenessinfo.styles, _devtoolsindicator.DEV_TOOLS_INDICATOR_STYLES, _devtoolsinfo.DEV_TOOLS_INFO_STYLES, _turbopackinfo.DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES, _routeinfo.DEV_TOOLS_INFO_ROUTE_INFO_STYLES, _userpreferences.DEV_TOOLS_INFO_USER_PREFERENCES_STYLES)\n    });\n}\n_c = ComponentStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=component-styles.js.map\nvar _c;\n$RefreshReg$(_c, \"ComponentStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/component-styles.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/css-reset.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/css-reset.js ***!
  \*******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CssReset\", ({\n    enumerable: true,\n    get: function() {\n        return CssReset;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _css = __webpack_require__(/*! ../../utils/css */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n        :host {\\n          all: initial;\\n\\n          /* the direction property is not reset by 'all' */\\n          direction: ltr;\\n        }\\n\\n        /*!\\n         * Bootstrap Reboot v4.4.1 (https://getbootstrap.com/)\\n         * Copyright 2011-2019 The Bootstrap Authors\\n         * Copyright 2011-2019 Twitter, Inc.\\n         * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\\n         * Forked from Normalize.css, licensed MIT (https://github.com/necolas/normalize.css/blob/master/LICENSE.md)\\n         */\\n        *,\\n        *::before,\\n        *::after {\\n          box-sizing: border-box;\\n        }\\n\\n        :host {\\n          font-family: sans-serif;\\n          line-height: 1.15;\\n          -webkit-text-size-adjust: 100%;\\n          -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\n        }\\n\\n        article,\\n        aside,\\n        figcaption,\\n        figure,\\n        footer,\\n        header,\\n        hgroup,\\n        main,\\n        nav,\\n        section {\\n          display: block;\\n        }\\n\\n        :host {\\n          margin: 0;\\n          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,\\n            'Helvetica Neue', Arial, 'Noto Sans', sans-serif,\\n            'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',\\n            'Noto Color Emoji';\\n          font-size: 16px;\\n          font-weight: 400;\\n          line-height: 1.5;\\n          color: var(--color-font);\\n          text-align: left;\\n        }\\n\\n        :host:not(button) {\\n          background-color: #fff;\\n        }\\n\\n        [tabindex='-1']:focus:not(:focus-visible) {\\n          outline: 0 !important;\\n        }\\n\\n        hr {\\n          box-sizing: content-box;\\n          height: 0;\\n          overflow: visible;\\n        }\\n\\n        h1,\\n        h2,\\n        h3,\\n        h4,\\n        h5,\\n        h6 {\\n          margin-top: 0;\\n          margin-bottom: 8px;\\n        }\\n\\n        p {\\n          margin-top: 0;\\n          margin-bottom: 16px;\\n        }\\n\\n        abbr[title],\\n        abbr[data-original-title] {\\n          text-decoration: underline;\\n          -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n          cursor: help;\\n          border-bottom: 0;\\n          -webkit-text-decoration-skip-ink: none;\\n          text-decoration-skip-ink: none;\\n        }\\n\\n        address {\\n          margin-bottom: 16px;\\n          font-style: normal;\\n          line-height: inherit;\\n        }\\n\\n        ol,\\n        ul,\\n        dl {\\n          margin-top: 0;\\n          margin-bottom: 16px;\\n        }\\n\\n        ol ol,\\n        ul ul,\\n        ol ul,\\n        ul ol {\\n          margin-bottom: 0;\\n        }\\n\\n        dt {\\n          font-weight: 700;\\n        }\\n\\n        dd {\\n          margin-bottom: 8px;\\n          margin-left: 0;\\n        }\\n\\n        blockquote {\\n          margin: 0 0 16px;\\n        }\\n\\n        b,\\n        strong {\\n          font-weight: bolder;\\n        }\\n\\n        small {\\n          font-size: 80%;\\n        }\\n\\n        sub,\\n        sup {\\n          position: relative;\\n          font-size: 75%;\\n          line-height: 0;\\n          vertical-align: baseline;\\n        }\\n\\n        sub {\\n          bottom: -0.25em;\\n        }\\n\\n        sup {\\n          top: -0.5em;\\n        }\\n\\n        a {\\n          color: #007bff;\\n          text-decoration: none;\\n          background-color: transparent;\\n        }\\n\\n        a:hover {\\n          color: #0056b3;\\n          text-decoration: underline;\\n        }\\n\\n        a:not([href]) {\\n          color: inherit;\\n          text-decoration: none;\\n        }\\n\\n        a:not([href]):hover {\\n          color: inherit;\\n          text-decoration: none;\\n        }\\n\\n        pre,\\n        code,\\n        kbd,\\n        samp {\\n          font-family: SFMono-Regular, Menlo, Monaco, Consolas,\\n            'Liberation Mono', 'Courier New', monospace;\\n          font-size: 1em;\\n        }\\n\\n        pre {\\n          margin-top: 0;\\n          margin-bottom: 16px;\\n          overflow: auto;\\n        }\\n\\n        figure {\\n          margin: 0 0 16px;\\n        }\\n\\n        img {\\n          vertical-align: middle;\\n          border-style: none;\\n        }\\n\\n        svg {\\n          overflow: hidden;\\n          vertical-align: middle;\\n        }\\n\\n        table {\\n          border-collapse: collapse;\\n        }\\n\\n        caption {\\n          padding-top: 12px;\\n          padding-bottom: 12px;\\n          color: #6c757d;\\n          text-align: left;\\n          caption-side: bottom;\\n        }\\n\\n        th {\\n          text-align: inherit;\\n        }\\n\\n        label {\\n          display: inline-block;\\n          margin-bottom: 8px;\\n        }\\n\\n        button {\\n          border-radius: 0;\\n          border: 0;\\n          padding: 0;\\n          margin: 0;\\n          background: none;\\n          appearance: none;\\n          -webkit-appearance: none;\\n        }\\n\\n        button:focus {\\n          outline: 1px dotted;\\n          outline: 5px auto -webkit-focus-ring-color;\\n        }\\n\\n        button:focus:not(:focus-visible) {\\n          outline: none;\\n        }\\n\\n        input,\\n        button,\\n        select,\\n        optgroup,\\n        textarea {\\n          margin: 0;\\n          font-family: inherit;\\n          font-size: inherit;\\n          line-height: inherit;\\n        }\\n\\n        button,\\n        input {\\n          overflow: visible;\\n        }\\n\\n        button,\\n        select {\\n          text-transform: none;\\n        }\\n\\n        select {\\n          word-wrap: normal;\\n        }\\n\\n        button,\\n        [type='button'],\\n        [type='reset'],\\n        [type='submit'] {\\n          -webkit-appearance: button;\\n        }\\n\\n        button:not(:disabled),\\n        [type='button']:not(:disabled),\\n        [type='reset']:not(:disabled),\\n        [type='submit']:not(:disabled) {\\n          cursor: pointer;\\n        }\\n\\n        button::-moz-focus-inner,\\n        [type='button']::-moz-focus-inner,\\n        [type='reset']::-moz-focus-inner,\\n        [type='submit']::-moz-focus-inner {\\n          padding: 0;\\n          border-style: none;\\n        }\\n\\n        input[type='radio'],\\n        input[type='checkbox'] {\\n          box-sizing: border-box;\\n          padding: 0;\\n        }\\n\\n        input[type='date'],\\n        input[type='time'],\\n        input[type='datetime-local'],\\n        input[type='month'] {\\n          -webkit-appearance: listbox;\\n        }\\n\\n        textarea {\\n          overflow: auto;\\n          resize: vertical;\\n        }\\n\\n        fieldset {\\n          min-width: 0;\\n          padding: 0;\\n          margin: 0;\\n          border: 0;\\n        }\\n\\n        legend {\\n          display: block;\\n          width: 100%;\\n          max-width: 100%;\\n          padding: 0;\\n          margin-bottom: 8px;\\n          font-size: 24px;\\n          line-height: inherit;\\n          color: inherit;\\n          white-space: normal;\\n        }\\n\\n        progress {\\n          vertical-align: baseline;\\n        }\\n\\n        [type='number']::-webkit-inner-spin-button,\\n        [type='number']::-webkit-outer-spin-button {\\n          height: auto;\\n        }\\n\\n        [type='search'] {\\n          outline-offset: -2px;\\n          -webkit-appearance: none;\\n        }\\n\\n        [type='search']::-webkit-search-decoration {\\n          -webkit-appearance: none;\\n        }\\n\\n        ::-webkit-file-upload-button {\\n          font: inherit;\\n          -webkit-appearance: button;\\n        }\\n\\n        output {\\n          display: inline-block;\\n        }\\n\\n        summary {\\n          display: list-item;\\n          cursor: pointer;\\n        }\\n\\n        template {\\n          display: none;\\n        }\\n\\n        [hidden] {\\n          display: none !important;\\n        }\\n      \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction CssReset() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _css.css)(_templateObject())\n    });\n}\n_c = CssReset;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=css-reset.js.map\nvar _c;\n$RefreshReg$(_c, \"CssReset\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/css-reset.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/dark-theme.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/dark-theme.js ***!
  \********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DarkTheme\", ({\n    enumerable: true,\n    get: function() {\n        return DarkTheme;\n    }\n}));\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _css = __webpack_require__(/*! ../../utils/css */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n      :host(.dark) {\\n        \",\n        \"\\n        \",\n        \"\\n      }\\n\\n      @media (prefers-color-scheme: dark) {\\n        :host(:not(.light)) {\\n          \",\n        \"\\n          \",\n        \"\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst colors = \"\\n  /* Background Dark */\\n  --color-background-100: #0a0a0a;\\n  --color-background-200: #000000;\\n\\n  /* Syntax Dark */\\n  --color-syntax-comment: #a0a0a0;\\n  --color-syntax-constant: #ededed;\\n  --color-syntax-function: #52a9ff;\\n  --color-syntax-keyword: #f76e99;\\n  --color-syntax-link: #0ac5b2;\\n  --color-syntax-parameter: #f1a10d;\\n  --color-syntax-punctuation: #ededed;\\n  --color-syntax-string: #0ac5b2;\\n  --color-syntax-string-expression: #0ac5b2;\\n\\n  /* Gray Scale Dark */\\n  --color-gray-100: #1a1a1a;\\n  --color-gray-200: #1f1f1f;\\n  --color-gray-300: #292929;\\n  --color-gray-400: #2e2e2e;\\n  --color-gray-500: #454545;\\n  --color-gray-600: #878787;\\n  --color-gray-700: #8f8f8f;\\n  --color-gray-800: #7d7d7d;\\n  --color-gray-900: #a0a0a0;\\n  --color-gray-1000: #ededed;\\n\\n  /* Gray Alpha Scale Dark */\\n  --color-gray-alpha-100: rgba(255, 255, 255, 0.066);\\n  --color-gray-alpha-200: rgba(255, 255, 255, 0.087);\\n  --color-gray-alpha-300: rgba(255, 255, 255, 0.125);\\n  --color-gray-alpha-400: rgba(255, 255, 255, 0.145);\\n  --color-gray-alpha-500: rgba(255, 255, 255, 0.239);\\n  --color-gray-alpha-600: rgba(255, 255, 255, 0.506);\\n  --color-gray-alpha-700: rgba(255, 255, 255, 0.54);\\n  --color-gray-alpha-800: rgba(255, 255, 255, 0.47);\\n  --color-gray-alpha-900: rgba(255, 255, 255, 0.61);\\n  --color-gray-alpha-1000: rgba(255, 255, 255, 0.923);\\n\\n  /* Blue Scale Dark */\\n  --color-blue-100: #0f1b2d;\\n  --color-blue-200: #10243e;\\n  --color-blue-300: #0f3058;\\n  --color-blue-400: #0d3868;\\n  --color-blue-500: #0a4481;\\n  --color-blue-600: #0091ff;\\n  --color-blue-700: #0070f3;\\n  --color-blue-800: #0060d1;\\n  --color-blue-900: #52a9ff;\\n  --color-blue-1000: #eaf6ff;\\n\\n  /* Red Scale Dark */\\n  --color-red-100: #2a1314;\\n  --color-red-200: #3d1719;\\n  --color-red-300: #551a1e;\\n  --color-red-400: #671e22;\\n  --color-red-500: #822025;\\n  --color-red-600: #e5484d;\\n  --color-red-700: #e5484d;\\n  --color-red-800: #da3036;\\n  --color-red-900: #ff6369;\\n  --color-red-1000: #ffecee;\\n\\n  /* Amber Scale Dark */\\n  --color-amber-100: #271700;\\n  --color-amber-200: #341c00;\\n  --color-amber-300: #4a2900;\\n  --color-amber-400: #573300;\\n  --color-amber-500: #693f05;\\n  --color-amber-600: #e79c13;\\n  --color-amber-700: #ffb224;\\n  --color-amber-800: #ff990a;\\n  --color-amber-900: #f1a10d;\\n  --color-amber-1000: #fef3dd;\\n\\n  /* Green Scale Dark */\\n  --color-green-100: #0b2211;\\n  --color-green-200: #0f2c17;\\n  --color-green-300: #11351b;\\n  --color-green-400: #0c461b;\\n  --color-green-500: #126427;\\n  --color-green-600: #1a9338;\\n  --color-green-700: #46a758;\\n  --color-green-800: #388e4a;\\n  --color-green-900: #63c174;\\n  --color-green-1000: #e5fbeb;\\n\\n  /* Turbopack Dark - Temporary */\\n  --color-turbopack-text-red: #ff6d92;\\n  --color-turbopack-text-blue: #45b2ff;\\n  --color-turbopack-border-red: #6e293b;\\n  --color-turbopack-border-blue: #284f80;\\n  --color-turbopack-background-red: #250d12;\\n  --color-turbopack-background-blue: #0a1723;\\n\";\nconst base = \"\\n  --color-font: white;\\n  --color-backdrop: rgba(0, 0, 0, 0.8);\\n  --color-border-shadow: rgba(255, 255, 255, 0.145);\\n\\n  --color-title-color: #fafafa;\\n  --color-stack-notes: #a9a9a9;\\n\";\nfunction DarkTheme() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        children: (0, _css.css)(_templateObject(), base, colors, base, colors)\n    });\n}\n_c = DarkTheme;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dark-theme.js.map\nvar _c;\n$RefreshReg$(_c, \"DarkTheme\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/styles/dark-theme.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js ***!
  \***********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/**\n * Merge multiple args to a single string with spaces. Useful for merging class names.\n * @example\n * cx('foo', 'bar') // 'foo bar'\n * cx('foo', null, 'bar', undefined, 'baz', false) // 'foo bar baz'\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"cx\", ({\n    enumerable: true,\n    get: function() {\n        return cx;\n    }\n}));\nfunction cx() {\n    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n        args[_key] = arguments[_key];\n    }\n    return args.filter(Boolean).join(' ');\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=cx.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvdXRpbHMvY3guanMiLCJtYXBwaW5ncyI6IkFBQUE7Ozs7O0NBS0M7Ozs7c0NBQ2VBOzs7ZUFBQUE7OztBQUFULFNBQVNBO0lBQUcsaUNBQUdDLE9BQUg7UUFBR0EsSUFBQUEsQ0FBSCx1QkFBOEM7O0lBQy9ELE9BQU9BLEtBQUtDLE1BQU0sQ0FBQ0MsU0FBU0MsSUFBSSxDQUFDO0FBQ25DIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcdXRpbHNcXGN4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTWVyZ2UgbXVsdGlwbGUgYXJncyB0byBhIHNpbmdsZSBzdHJpbmcgd2l0aCBzcGFjZXMuIFVzZWZ1bCBmb3IgbWVyZ2luZyBjbGFzcyBuYW1lcy5cbiAqIEBleGFtcGxlXG4gKiBjeCgnZm9vJywgJ2JhcicpIC8vICdmb28gYmFyJ1xuICogY3goJ2ZvbycsIG51bGwsICdiYXInLCB1bmRlZmluZWQsICdiYXonLCBmYWxzZSkgLy8gJ2ZvbyBiYXIgYmF6J1xuICovXG5leHBvcnQgZnVuY3Rpb24gY3goLi4uYXJnczogKHN0cmluZyB8IHVuZGVmaW5lZCB8IG51bGwgfCBmYWxzZSlbXSk6IHN0cmluZyB7XG4gIHJldHVybiBhcmdzLmZpbHRlcihCb29sZWFuKS5qb2luKCcgJylcbn1cbiJdLCJuYW1lcyI6WyJjeCIsImFyZ3MiLCJmaWx0ZXIiLCJCb29sZWFuIiwiam9pbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/merge-refs.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/merge-refs.js ***!
  \*******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return mergeRefs;\n    }\n}));\nfunction mergeRefs() {\n    for(var _len = arguments.length, inputRefs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputRefs[_key] = arguments[_key];\n    }\n    const filteredInputRefs = inputRefs.filter(Boolean);\n    if (filteredInputRefs.length <= 1) {\n        const firstRef = filteredInputRefs[0];\n        return firstRef || null;\n    }\n    return function mergedRefs(ref) {\n        for (const inputRef of filteredInputRefs){\n            if (typeof inputRef === 'function') {\n                inputRef(ref);\n            } else if (inputRef) {\n                ;\n                inputRef.current = ref;\n            }\n        }\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=merge-refs.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/merge-refs.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/parse-url-from-text.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/parse-url-from-text.js ***!
  \****************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"parseUrlFromText\", ({\n    enumerable: true,\n    get: function() {\n        return parseUrlFromText;\n    }\n}));\nfunction parseUrlFromText(text, matcherFunc) {\n    const linkRegex = /https?:\\/\\/[^\\s/$.?#].[^\\s)'\"]*/gi;\n    const links = Array.from(text.matchAll(linkRegex), (match)=>match[0]);\n    if (matcherFunc) {\n        return links.filter((link)=>matcherFunc(link));\n    }\n    return links;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=parse-url-from-text.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvdXRpbHMvcGFyc2UtdXJsLWZyb20tdGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7O29EQUFnQkE7OztlQUFBQTs7O0FBQVQsU0FBU0EsaUJBQ2RDLElBQVksRUFDWkMsV0FBdUM7SUFFdkMsTUFBTUMsWUFBWTtJQUNsQixNQUFNQyxRQUFRQyxNQUFNQyxJQUFJLENBQUNMLEtBQUtNLFFBQVEsQ0FBQ0osWUFBWSxDQUFDSyxRQUFVQSxLQUFLLENBQUMsRUFBRTtJQUV0RSxJQUFJTixhQUFhO1FBQ2YsT0FBT0UsTUFBTUssTUFBTSxDQUFDLENBQUNDLE9BQVNSLFlBQVlRO0lBQzVDO0lBRUEsT0FBT047QUFDVCIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXHV0aWxzXFxwYXJzZS11cmwtZnJvbS10ZXh0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBwYXJzZVVybEZyb21UZXh0KFxuICB0ZXh0OiBzdHJpbmcsXG4gIG1hdGNoZXJGdW5jPzogKHRleHQ6IHN0cmluZykgPT4gYm9vbGVhblxuKTogc3RyaW5nW10ge1xuICBjb25zdCBsaW5rUmVnZXggPSAvaHR0cHM/OlxcL1xcL1teXFxzLyQuPyNdLlteXFxzKSdcIl0qL2dpXG4gIGNvbnN0IGxpbmtzID0gQXJyYXkuZnJvbSh0ZXh0Lm1hdGNoQWxsKGxpbmtSZWdleCksIChtYXRjaCkgPT4gbWF0Y2hbMF0pXG5cbiAgaWYgKG1hdGNoZXJGdW5jKSB7XG4gICAgcmV0dXJuIGxpbmtzLmZpbHRlcigobGluaykgPT4gbWF0Y2hlckZ1bmMobGluaykpXG4gIH1cblxuICByZXR1cm4gbGlua3Ncbn1cbiJdLCJuYW1lcyI6WyJwYXJzZVVybEZyb21UZXh0IiwidGV4dCIsIm1hdGNoZXJGdW5jIiwibGlua1JlZ2V4IiwibGlua3MiLCJBcnJheSIsImZyb20iLCJtYXRjaEFsbCIsIm1hdGNoIiwiZmlsdGVyIiwibGluayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/parse-url-from-text.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/use-open-in-editor.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/use-open-in-editor.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useOpenInEditor\", ({\n    enumerable: true,\n    get: function() {\n        return useOpenInEditor;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nfunction useOpenInEditor(param) {\n    let { file, lineNumber, column } = param === void 0 ? {} : param;\n    const openInEditor = (0, _react.useCallback)(()=>{\n        if (file == null || lineNumber == null || column == null) return;\n        const params = new URLSearchParams();\n        params.append('file', file);\n        params.append('lineNumber', String(lineNumber));\n        params.append('column', String(column));\n        self.fetch(( false || '') + \"/__nextjs_launch-editor?\" + params.toString()).then(()=>{}, (cause)=>{\n            console.error('Failed to open file \"' + file + \" (\" + lineNumber + \":\" + column + ')\" in your editor. Cause:', cause);\n        });\n    }, [\n        file,\n        lineNumber,\n        column\n    ]);\n    return openInEditor;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-open-in-editor.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/use-open-in-editor.js\n"));

/***/ })

}]);