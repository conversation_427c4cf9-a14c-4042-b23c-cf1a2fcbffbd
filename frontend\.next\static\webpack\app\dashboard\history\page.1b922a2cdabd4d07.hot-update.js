"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/history/page",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    async checkBackendConnection() {\n        try {\n            // Try a simple ping to the backend\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 1500); // 1.5 second timeout\n            const response = await fetch('http://localhost:5000/', {\n                method: 'GET',\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.status < 500) {\n                this.useBackend = true;\n                console.log('✅ Session Manager: Backend connection established');\n            } else {\n                throw new Error('Backend returned server error');\n            }\n        } catch (error) {\n            this.useBackend = false;\n            console.warn('⚠️ Session Manager: Backend unavailable, using localStorage fallback');\n            console.warn('💡 To enable backend features, start the backend server: python run.py');\n        }\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            const currentSessionId = localStorage.getItem(CURRENT_SESSION_KEY);\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            if (this.currentSessionId) {\n                localStorage.setItem(CURRENT_SESSION_KEY, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSession(name, config) {\n        if (this.useBackend) {\n            try {\n                // Validate config before sending\n                if (!config.crypto1 || !config.crypto2) {\n                    throw new Error('Invalid config: crypto1 and crypto2 are required');\n                }\n                const sessionData = {\n                    name,\n                    config: {\n                        tradingMode: config.tradingMode,\n                        crypto1: config.crypto1,\n                        crypto2: config.crypto2,\n                        baseBid: config.baseBid,\n                        multiplier: config.multiplier,\n                        numDigits: config.numDigits,\n                        slippagePercent: config.slippagePercent,\n                        incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                        incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                        preferredStablecoin: config.preferredStablecoin\n                    },\n                    targetPriceRows: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0\n                };\n                console.log('📤 Creating session with data:', sessionData);\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                const sessionId = response.session.id;\n                console.log('✅ Session created on backend:', sessionId);\n                return sessionId;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend:', error);\n                console.error('❌ Error details:', error.message);\n                // Don't fall back to localStorage for validation errors\n                if (error.message.includes('Validation error') || error.message.includes('422')) {\n                    throw error;\n                }\n                console.log('📱 Falling back to localStorage');\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: 10,\n            crypto2Balance: 100000,\n            stablecoinBalance: 0,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: session.runtime + (isActive ? Date.now() - session.lastModified : 0)\n            };\n            this.sessions.set(sessionId, updatedSession);\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    deleteSession(sessionId) {\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                localStorage.removeItem(CURRENT_SESSION_KEY);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: session.runtime,\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            localStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        localStorage.removeItem(CURRENT_SESSION_KEY);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.loadSessionsFromStorage();\n        // Start with localStorage mode, check backend in background\n        this.useBackend = false;\n        // Check backend connection in background without blocking\n        setTimeout(()=>{\n            this.checkBackendConnection().catch(()=>{\n            // Silently fail and continue with localStorage\n            });\n        }, 1000);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});