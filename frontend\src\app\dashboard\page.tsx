"use client";
import React from 'react';
import OrdersTable from '@/components/dashboard/OrdersTable';
import DashboardTabs from '@/components/dashboard/DashboardTabs';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useTradingContext } from '@/contexts/TradingContext';
import BalancesDisplay from '@/components/dashboard/BalancesDisplay';
import MarketPriceDisplay from '@/components/dashboard/MarketPriceDisplay';

export default function DashboardOrdersPage() {
  const { config } = useTradingContext();
  return (
    <div className="space-y-6">
      <DashboardTabs />
      <BalancesDisplay />
      <Card className="border-2 border-border">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-primary">Active Orders ({config.crypto1}/{config.crypto2})</CardTitle>
          <CardDescription>Current state of your target price levels. Prices update in real-time.</CardDescription>
        </CardHeader>
        <CardContent>
          <MarketPriceDisplay />
          <OrdersTable />
        </CardContent>
      </Card>
    </div>
  );
}
