"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dialog_b"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/body.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/body.js ***!
  \********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DIALOG_BODY_STYLES: function() {\n        return DIALOG_BODY_STYLES;\n    },\n    ErrorOverlayDialogBody: function() {\n        return ErrorOverlayDialogBody;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _dialog = __webpack_require__(/*! ../../dialog */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/index.js\");\nfunction ErrorOverlayDialogBody(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_dialog.DialogBody, {\n        className: \"nextjs-container-errors-body\",\n        children: children\n    });\n}\n_c = ErrorOverlayDialogBody;\nconst DIALOG_BODY_STYLES = \"\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=body.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayDialogBody\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9lcnJvcnMvZGlhbG9nL2JvZHkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBZWFBLGtCQUFrQjtlQUFsQkE7O0lBUkdDLHNCQUFzQjtlQUF0QkE7Ozs7b0NBUFc7QUFPcEIsZ0NBQWdDLEtBRVQ7SUFGUyxNQUNyQ0MsUUFBUSxFQUNvQixHQUZTO0lBR3JDLHFCQUNFLHFCQUFDQyxRQUFBQSxVQUFVO1FBQUNDLFdBQVU7a0JBQWdDRjs7QUFFMUQ7S0FOZ0JEO0FBUVQsTUFBTUQscUJBQXNCIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcY29tcG9uZW50c1xcZXJyb3JzXFxkaWFsb2dcXGJvZHkudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IERpYWxvZ0JvZHkgfSBmcm9tICcuLi8uLi9kaWFsb2cnXG5cbnR5cGUgRXJyb3JPdmVybGF5RGlhbG9nQm9keVByb3BzID0ge1xuICBjaGlsZHJlbj86IFJlYWN0LlJlYWN0Tm9kZVxuICBvbkNsb3NlPzogKCkgPT4gdm9pZFxufVxuXG5leHBvcnQgZnVuY3Rpb24gRXJyb3JPdmVybGF5RGlhbG9nQm9keSh7XG4gIGNoaWxkcmVuLFxufTogRXJyb3JPdmVybGF5RGlhbG9nQm9keVByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPERpYWxvZ0JvZHkgY2xhc3NOYW1lPVwibmV4dGpzLWNvbnRhaW5lci1lcnJvcnMtYm9keVwiPntjaGlsZHJlbn08L0RpYWxvZ0JvZHk+XG4gIClcbn1cblxuZXhwb3J0IGNvbnN0IERJQUxPR19CT0RZX1NUWUxFUyA9IGBgXG4iXSwibmFtZXMiOlsiRElBTE9HX0JPRFlfU1RZTEVTIiwiRXJyb3JPdmVybGF5RGlhbG9nQm9keSIsImNoaWxkcmVuIiwiRGlhbG9nQm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/body.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/dialog.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/dialog.js ***!
  \**********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DIALOG_STYLES: function() {\n        return DIALOG_STYLES;\n    },\n    ErrorOverlayDialog: function() {\n        return ErrorOverlayDialog;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _dialog = __webpack_require__(/*! ../../dialog/dialog */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog.js\");\nfunction ErrorOverlayDialog(param) {\n    let { children, onClose, ...props } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_dialog.Dialog, {\n        type: \"error\",\n        \"aria-labelledby\": \"nextjs__container_errors_label\",\n        \"aria-describedby\": \"nextjs__container_errors_desc\",\n        onClose: onClose,\n        className: \"error-overlay-dialog\",\n        ...props,\n        children: children\n    });\n}\n_c = ErrorOverlayDialog;\nconst DIALOG_STYLES = \"\\n  .error-overlay-dialog {\\n    overflow-y: auto;\\n    -webkit-font-smoothing: antialiased;\\n    background: var(--color-background-100);\\n    background-clip: padding-box;\\n    border: 1px solid var(--color-gray-400);\\n    border-radius: var(--rounded-xl);\\n    box-shadow: var(--shadow-menu);\\n    position: relative;\\n\\n    &:has(\\n        ~ [data-nextjs-error-overlay-nav] .error-overlay-notch[data-side='left']\\n      ) {\\n      border-top-left-radius: 0;\\n    }\\n\\n    &:has(\\n        ~ [data-nextjs-error-overlay-nav]\\n          .error-overlay-notch[data-side='right']\\n      ) {\\n      border-top-right-radius: 0;\\n    }\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dialog.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/dialog.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/header.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/header.js ***!
  \**********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DIALOG_HEADER_STYLES: function() {\n        return DIALOG_HEADER_STYLES;\n    },\n    ErrorOverlayDialogHeader: function() {\n        return ErrorOverlayDialogHeader;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _dialogheader = __webpack_require__(/*! ../../dialog/dialog-header */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-header.js\");\nfunction ErrorOverlayDialogHeader(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_dialogheader.DialogHeader, {\n        className: \"nextjs-container-errors-header\",\n        children: children\n    });\n}\n_c = ErrorOverlayDialogHeader;\nconst DIALOG_HEADER_STYLES = \"\\n  .nextjs-container-errors-header {\\n    position: relative;\\n  }\\n  .nextjs-container-errors-header > h1 {\\n    font-size: var(--size-20);\\n    line-height: var(--size-24);\\n    font-weight: bold;\\n    margin: calc(16px * 1.5) 0;\\n    color: var(--color-title-h1);\\n  }\\n  .nextjs-container-errors-header small {\\n    font-size: var(--size-14);\\n    color: var(--color-accents-1);\\n    margin-left: 16px;\\n  }\\n  .nextjs-container-errors-header small > span {\\n    font-family: var(--font-stack-monospace);\\n  }\\n  .nextjs-container-errors-header > div > small {\\n    margin: 0;\\n    margin-top: 4px;\\n  }\\n  .nextjs-container-errors-header > p > a {\\n    color: inherit;\\n    font-weight: bold;\\n  }\\n  .nextjs-container-errors-header\\n    > .nextjs-container-build-error-version-status {\\n    position: absolute;\\n    top: 16px;\\n    right: 16px;\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=header.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayDialogHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/header.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/body.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/body.js ***!
  \********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DIALOG_BODY_STYLES: function() {\n        return DIALOG_BODY_STYLES;\n    },\n    ErrorOverlayDialogBody: function() {\n        return ErrorOverlayDialogBody;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _dialog = __webpack_require__(/*! ../../dialog */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/index.js\");\nfunction ErrorOverlayDialogBody(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_dialog.DialogBody, {\n        className: \"nextjs-container-errors-body\",\n        children: children\n    });\n}\n_c = ErrorOverlayDialogBody;\nconst DIALOG_BODY_STYLES = \"\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=body.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayDialogBody\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9lcnJvcnMvZGlhbG9nL2JvZHkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBZWFBLGtCQUFrQjtlQUFsQkE7O0lBUkdDLHNCQUFzQjtlQUF0QkE7Ozs7b0NBUFc7QUFPcEIsZ0NBQWdDLEtBRVQ7SUFGUyxNQUNyQ0MsUUFBUSxFQUNvQixHQUZTO0lBR3JDLHFCQUNFLHFCQUFDQyxRQUFBQSxVQUFVO1FBQUNDLFdBQVU7a0JBQWdDRjs7QUFFMUQ7S0FOZ0JEO0FBUVQsTUFBTUQscUJBQXNCIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcY29tcG9uZW50c1xcZXJyb3JzXFxkaWFsb2dcXGJvZHkudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IERpYWxvZ0JvZHkgfSBmcm9tICcuLi8uLi9kaWFsb2cnXG5cbnR5cGUgRXJyb3JPdmVybGF5RGlhbG9nQm9keVByb3BzID0ge1xuICBjaGlsZHJlbj86IFJlYWN0LlJlYWN0Tm9kZVxuICBvbkNsb3NlPzogKCkgPT4gdm9pZFxufVxuXG5leHBvcnQgZnVuY3Rpb24gRXJyb3JPdmVybGF5RGlhbG9nQm9keSh7XG4gIGNoaWxkcmVuLFxufTogRXJyb3JPdmVybGF5RGlhbG9nQm9keVByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPERpYWxvZ0JvZHkgY2xhc3NOYW1lPVwibmV4dGpzLWNvbnRhaW5lci1lcnJvcnMtYm9keVwiPntjaGlsZHJlbn08L0RpYWxvZ0JvZHk+XG4gIClcbn1cblxuZXhwb3J0IGNvbnN0IERJQUxPR19CT0RZX1NUWUxFUyA9IGBgXG4iXSwibmFtZXMiOlsiRElBTE9HX0JPRFlfU1RZTEVTIiwiRXJyb3JPdmVybGF5RGlhbG9nQm9keSIsImNoaWxkcmVuIiwiRGlhbG9nQm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/body.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/dialog.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/dialog.js ***!
  \**********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DIALOG_STYLES: function() {\n        return DIALOG_STYLES;\n    },\n    ErrorOverlayDialog: function() {\n        return ErrorOverlayDialog;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _dialog = __webpack_require__(/*! ../../dialog/dialog */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog.js\");\nfunction ErrorOverlayDialog(param) {\n    let { children, onClose, ...props } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_dialog.Dialog, {\n        type: \"error\",\n        \"aria-labelledby\": \"nextjs__container_errors_label\",\n        \"aria-describedby\": \"nextjs__container_errors_desc\",\n        onClose: onClose,\n        className: \"error-overlay-dialog\",\n        ...props,\n        children: children\n    });\n}\n_c = ErrorOverlayDialog;\nconst DIALOG_STYLES = \"\\n  .error-overlay-dialog {\\n    overflow-y: auto;\\n    -webkit-font-smoothing: antialiased;\\n    background: var(--color-background-100);\\n    background-clip: padding-box;\\n    border: 1px solid var(--color-gray-400);\\n    border-radius: var(--rounded-xl);\\n    box-shadow: var(--shadow-menu);\\n    position: relative;\\n\\n    &:has(\\n        ~ [data-nextjs-error-overlay-nav] .error-overlay-notch[data-side='left']\\n      ) {\\n      border-top-left-radius: 0;\\n    }\\n\\n    &:has(\\n        ~ [data-nextjs-error-overlay-nav]\\n          .error-overlay-notch[data-side='right']\\n      ) {\\n      border-top-right-radius: 0;\\n    }\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dialog.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/dialog.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/header.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/header.js ***!
  \**********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DIALOG_HEADER_STYLES: function() {\n        return DIALOG_HEADER_STYLES;\n    },\n    ErrorOverlayDialogHeader: function() {\n        return ErrorOverlayDialogHeader;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _dialogheader = __webpack_require__(/*! ../../dialog/dialog-header */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/dialog-header.js\");\nfunction ErrorOverlayDialogHeader(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_dialogheader.DialogHeader, {\n        className: \"nextjs-container-errors-header\",\n        children: children\n    });\n}\n_c = ErrorOverlayDialogHeader;\nconst DIALOG_HEADER_STYLES = \"\\n  .nextjs-container-errors-header {\\n    position: relative;\\n  }\\n  .nextjs-container-errors-header > h1 {\\n    font-size: var(--size-20);\\n    line-height: var(--size-24);\\n    font-weight: bold;\\n    margin: calc(16px * 1.5) 0;\\n    color: var(--color-title-h1);\\n  }\\n  .nextjs-container-errors-header small {\\n    font-size: var(--size-14);\\n    color: var(--color-accents-1);\\n    margin-left: 16px;\\n  }\\n  .nextjs-container-errors-header small > span {\\n    font-family: var(--font-stack-monospace);\\n  }\\n  .nextjs-container-errors-header > div > small {\\n    margin: 0;\\n    margin-top: 4px;\\n  }\\n  .nextjs-container-errors-header > p > a {\\n    color: inherit;\\n    font-weight: bold;\\n  }\\n  .nextjs-container-errors-header\\n    > .nextjs-container-build-error-version-status {\\n    position: absolute;\\n    top: 16px;\\n    right: 16px;\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=header.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayDialogHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/header.js\n"));

/***/ })

}]);