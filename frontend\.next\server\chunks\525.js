"use strict";exports.id=525,exports.ids=[525],exports.modules={1305:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},1833:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},3341:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("ChartLine",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},9812:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("Coins",[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]])},21277:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("CircleDollarSign",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 18V6",key:"zqpxq5"}]])},25371:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("PowerOff",[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},26134:(e,t,r)=>{r.d(t,{UC:()=>et,VY:()=>ea,ZL:()=>Q,bL:()=>J,bm:()=>en,hE:()=>er,hJ:()=>ee,l9:()=>Y});var a=r(43210),n=r(70569),o=r(98599),i=r(11273),l=r(96963),d=r(65551),s=r(31355),c=r(32547),u=r(25028),p=r(46059),h=r(14163),y=r(1359),f=r(42247),k=r(63376),m=r(8730),g=r(60687),v="Dialog",[x,b]=(0,i.A)(v),[A,M]=x(v),w=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:o,onOpenChange:i,modal:s=!0}=e,c=a.useRef(null),u=a.useRef(null),[p=!1,h]=(0,d.i)({prop:n,defaultProp:o,onChange:i});return(0,g.jsx)(A,{scope:t,triggerRef:c,contentRef:u,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:p,onOpenChange:h,onOpenToggle:a.useCallback(()=>h(e=>!e),[h]),modal:s,children:r})};w.displayName=v;var C="DialogTrigger",j=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,i=M(C,r),l=(0,o.s)(t,i.triggerRef);return(0,g.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":$(i.open),...a,ref:l,onClick:(0,n.m)(e.onClick,i.onOpenToggle)})});j.displayName=C;var D="DialogPortal",[R,E]=x(D,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:o}=e,i=M(D,t);return(0,g.jsx)(R,{scope:t,forceMount:r,children:a.Children.map(n,e=>(0,g.jsx)(p.C,{present:r||i.open,children:(0,g.jsx)(u.Z,{asChild:!0,container:o,children:e})}))})};I.displayName=D;var O="DialogOverlay",L=a.forwardRef((e,t)=>{let r=E(O,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,o=M(O,e.__scopeDialog);return o.modal?(0,g.jsx)(p.C,{present:a||o.open,children:(0,g.jsx)(N,{...n,ref:t})}):null});L.displayName=O;var N=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=M(O,r);return(0,g.jsx)(f.A,{as:m.DX,allowPinchZoom:!0,shards:[n.contentRef],children:(0,g.jsx)(h.sG.div,{"data-state":$(n.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),P="DialogContent",q=a.forwardRef((e,t)=>{let r=E(P,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,o=M(P,e.__scopeDialog);return(0,g.jsx)(p.C,{present:a||o.open,children:o.modal?(0,g.jsx)(F,{...n,ref:t}):(0,g.jsx)(_,{...n,ref:t})})});q.displayName=P;var F=a.forwardRef((e,t)=>{let r=M(P,e.__scopeDialog),i=a.useRef(null),l=(0,o.s)(t,r.contentRef,i);return a.useEffect(()=>{let e=i.current;if(e)return(0,k.Eq)(e)},[]),(0,g.jsx)(G,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),_=a.forwardRef((e,t)=>{let r=M(P,e.__scopeDialog),n=a.useRef(!1),o=a.useRef(!1);return(0,g.jsx)(G,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let a=t.target;r.triggerRef.current?.contains(a)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),G=a.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:i,onCloseAutoFocus:l,...d}=e,u=M(P,r),p=a.useRef(null),h=(0,o.s)(t,p);return(0,y.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(c.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,g.jsx)(s.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":$(u.open),...d,ref:h,onDismiss:()=>u.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(X,{titleId:u.titleId}),(0,g.jsx)(U,{contentRef:p,descriptionId:u.descriptionId})]})]})}),z="DialogTitle",B=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=M(z,r);return(0,g.jsx)(h.sG.h2,{id:n.titleId,...a,ref:t})});B.displayName=z;var H="DialogDescription",S=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=M(H,r);return(0,g.jsx)(h.sG.p,{id:n.descriptionId,...a,ref:t})});S.displayName=H;var T="DialogClose",V=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,o=M(T,r);return(0,g.jsx)(h.sG.button,{type:"button",...a,ref:t,onClick:(0,n.m)(e.onClick,()=>o.onOpenChange(!1))})});function $(e){return e?"open":"closed"}V.displayName=T;var W="DialogTitleWarning",[Z,K]=(0,i.q)(W,{contentName:P,titleName:z,docsSlug:"dialog"}),X=({titleId:e})=>{let t=K(W),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return a.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},U=({contentRef:e,descriptionId:t})=>{let r=K("DialogDescriptionWarning"),n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return a.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(n)},[n,e,t]),null},J=w,Y=j,Q=I,ee=L,et=q,er=B,ea=S,en=V},29272:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("ListOrdered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},33886:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},40196:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("SquarePlus",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},40211:(e,t,r)=>{r.d(t,{C1:()=>C,bL:()=>w});var a=r(43210),n=r(98599),o=r(11273),i=r(70569),l=r(65551),d=r(83721),s=r(18853),c=r(46059),u=r(14163),p=r(60687),h="Checkbox",[y,f]=(0,o.A)(h),[k,m]=y(h),g=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:o,checked:d,defaultChecked:s,required:c,disabled:h,value:y="on",onCheckedChange:f,form:m,...g}=e,[v,x]=a.useState(null),w=(0,n.s)(t,e=>x(e)),C=a.useRef(!1),j=!v||m||!!v.closest("form"),[D=!1,R]=(0,l.i)({prop:d,defaultProp:s,onChange:f}),E=a.useRef(D);return a.useEffect(()=>{let e=v?.form;if(e){let t=()=>R(E.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[v,R]),(0,p.jsxs)(k,{scope:r,state:D,disabled:h,children:[(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":A(D)?"mixed":D,"aria-required":c,"data-state":M(D),"data-disabled":h?"":void 0,disabled:h,value:y,...g,ref:w,onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(e.onClick,e=>{R(e=>!!A(e)||!e),j&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),j&&(0,p.jsx)(b,{control:v,bubbles:!C.current,name:o,value:y,checked:D,required:c,disabled:h,form:m,style:{transform:"translateX(-100%)"},defaultChecked:!A(s)&&s})]})});g.displayName=h;var v="CheckboxIndicator",x=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:a,...n}=e,o=m(v,r);return(0,p.jsx)(c.C,{present:a||A(o.state)||!0===o.state,children:(0,p.jsx)(u.sG.span,{"data-state":M(o.state),"data-disabled":o.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});x.displayName=v;var b=e=>{let{control:t,checked:r,bubbles:n=!0,defaultChecked:o,...i}=e,l=a.useRef(null),c=(0,d.Z)(r),u=(0,s.X)(t);a.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(c!==r&&t){let a=new Event("click",{bubbles:n});e.indeterminate=A(r),t.call(e,!A(r)&&r),e.dispatchEvent(a)}},[c,r,n]);let h=a.useRef(!A(r)&&r);return(0,p.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o??h.current,...i,tabIndex:-1,ref:l,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function A(e){return"indeterminate"===e}function M(e){return A(e)?"indeterminate":e?"checked":"unchecked"}var w=g,C=x},44610:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},49497:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},59892:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("Bitcoin",[["path",{d:"M11.767 19.089c4.924.868 6.14-6.025 1.216-6.894m-1.216 6.894L5.86 18.047m5.908 1.042-.347 1.97m1.563-8.864c4.924.869 6.14-6.025 1.215-6.893m-1.215 6.893-3.94-.694m5.155-6.2L8.29 4.26m5.908 1.042.348-1.97M7.48 20.364l3.126-17.727",key:"yr8idg"}]])},62369:(e,t,r)=>{r.d(t,{b:()=>s});var a=r(43210),n=r(14163),o=r(60687),i="horizontal",l=["horizontal","vertical"],d=a.forwardRef((e,t)=>{var r;let{decorative:a,orientation:d=i,...s}=e,c=(r=d,l.includes(r))?d:i;return(0,o.jsx)(n.sG.div,{"data-orientation":c,...a?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...s,ref:t})});d.displayName="Separator";var s=d},72963:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},76485:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},91840:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},92375:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(82614).A)("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]])}};