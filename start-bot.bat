@echo off
echo ========================================
echo    PLUTO TRADING BOT - STARTUP SCRIPT
echo ========================================
echo.

:: Check if we're in the correct directory
if not exist "frontend" (
    echo ERROR: frontend directory not found!
    echo Please run this script from the project root directory.
    pause
    exit /b 1
)

if not exist "backend" (
    echo ERROR: backend directory not found!
    echo Please run this script from the project root directory.
    pause
    exit /b 1
)

echo [1/4] Cleaning frontend build cache...
cd frontend
if exist ".next" (
    rmdir /s /q .next
    echo     - Removed .next directory
)
if exist "node_modules\.cache" (
    rmdir /s /q node_modules\.cache
    echo     - Removed node_modules cache
)

echo [2/4] Starting backend server...
cd ..\backend
start "Pluto Backend" cmd /k "python run.py"
echo     - Backend starting in new window...

echo [3/4] Waiting for backend to initialize...
timeout /t 3 /nobreak >nul

echo [4/4] Starting frontend development server...
cd ..\frontend
start "Pluto Frontend" cmd /k "npm run dev"
echo     - Frontend starting in new window...

echo.
echo ========================================
echo    STARTUP COMPLETE!
echo ========================================
echo.
echo Backend: http://localhost:5000
echo Frontend: http://localhost:9002
echo.
echo Both servers are starting in separate windows.
echo Wait a few seconds, then open http://localhost:9002
echo.
echo Press any key to exit this script...
pause >nul
