"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js ***!
  \*********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"css\", ({\n    enumerable: true,\n    get: function() {\n        return css;\n    }\n}));\nfunction css(strings) {\n    for(var _len = arguments.length, keys = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        keys[_key - 1] = arguments[_key];\n    }\n    const lastIndex = strings.length - 1;\n    const str = strings.slice(0, lastIndex).reduce((p, s, i)=>p + s + keys[i], '') + strings[lastIndex];\n    return str // Remove comments\n    .replace(/\\/\\*[\\s\\S]*?\\*\\//g, '') // Remove whitespace, tabs, and newlines\n    .replace(/\\s+/g, ' ') // Remove spaces before and after semicolons, and spaces after commas\n    .replace(/\\s*([:;,{}])\\s*/g, '$1') // Remove extra semicolons\n    .replace(/;+}/g, '}') // Trim leading and trailing whitespaces\n    .trim();\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=css.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/dev-render-indicator.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/dev-render-indicator.js ***!
  \****************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/*\n * Singleton store to track whether the app is currently being rendered\n * Used by the dev tools indicator to show render status\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    devRenderIndicator: function() {\n        return devRenderIndicator;\n    },\n    useIsDevRendering: function() {\n        return useIsDevRendering;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nlet isVisible = false;\nlet listeners = [];\nconst subscribe = (listener)=>{\n    listeners.push(listener);\n    return ()=>{\n        listeners = listeners.filter((l)=>l !== listener);\n    };\n};\nconst getSnapshot = ()=>isVisible;\nconst show = ()=>{\n    isVisible = true;\n    listeners.forEach((listener)=>listener());\n};\nconst hide = ()=>{\n    isVisible = false;\n    listeners.forEach((listener)=>listener());\n};\nfunction useIsDevRendering() {\n    return (0, _react.useSyncExternalStore)(subscribe, getSnapshot);\n}\nconst devRenderIndicator = {\n    show,\n    hide\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-render-indicator.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/dev-render-indicator.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator-internal.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator-internal.js ***!
  \**********************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useSyncDevRenderIndicatorInternal\", ({\n    enumerable: true,\n    get: function() {\n        return useSyncDevRenderIndicatorInternal;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _devrenderindicator = __webpack_require__(/*! ./dev-render-indicator */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/dev-render-indicator.js\");\nconst useSyncDevRenderIndicatorInternal = ()=>{\n    const [isPending, startTransition] = (0, _react.useTransition)();\n    (0, _react.useEffect)(()=>{\n        if (isPending) {\n            _devrenderindicator.devRenderIndicator.show();\n        } else {\n            _devrenderindicator.devRenderIndicator.hide();\n        }\n    }, [\n        isPending\n    ]);\n    return startTransition;\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-sync-dev-render-indicator-internal.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdXRpbHMvZGV2LWluZGljYXRvci91c2Utc3luYy1kZXYtcmVuZGVyLWluZGljYXRvci1pbnRlcm5hbC5qcyIsIm1hcHBpbmdzIjoiOzs7O3FFQUdhQTs7O2VBQUFBOzs7bUNBSDRCO2dEQUNOO0FBRTVCLE1BQU1BLG9DQUFvQztJQUMvQyxNQUFNLENBQUNDLFdBQVdDLGdCQUFnQixHQUFHQyxDQUFBQSxHQUFBQSxPQUFBQSxhQUFBQTtJQUVyQ0MsQ0FBQUEsR0FBQUEsT0FBQUEsU0FBQUEsRUFBVTtRQUNSLElBQUlILFdBQVc7WUFDYkksb0JBQUFBLGtCQUFrQixDQUFDQyxJQUFJO1FBQ3pCLE9BQU87WUFDTEQsb0JBQUFBLGtCQUFrQixDQUFDRSxJQUFJO1FBQ3pCO0lBQ0YsR0FBRztRQUFDTjtLQUFVO0lBRWQsT0FBT0M7QUFDVCIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdXRpbHNcXGRldi1pbmRpY2F0b3JcXHVzZS1zeW5jLWRldi1yZW5kZXItaW5kaWNhdG9yLWludGVybmFsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVRyYW5zaXRpb24gfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IGRldlJlbmRlckluZGljYXRvciB9IGZyb20gJy4vZGV2LXJlbmRlci1pbmRpY2F0b3InXG5cbmV4cG9ydCBjb25zdCB1c2VTeW5jRGV2UmVuZGVySW5kaWNhdG9ySW50ZXJuYWwgPSAoKSA9PiB7XG4gIGNvbnN0IFtpc1BlbmRpbmcsIHN0YXJ0VHJhbnNpdGlvbl0gPSB1c2VUcmFuc2l0aW9uKClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc1BlbmRpbmcpIHtcbiAgICAgIGRldlJlbmRlckluZGljYXRvci5zaG93KClcbiAgICB9IGVsc2Uge1xuICAgICAgZGV2UmVuZGVySW5kaWNhdG9yLmhpZGUoKVxuICAgIH1cbiAgfSwgW2lzUGVuZGluZ10pXG5cbiAgcmV0dXJuIHN0YXJ0VHJhbnNpdGlvblxufVxuIl0sIm5hbWVzIjpbInVzZVN5bmNEZXZSZW5kZXJJbmRpY2F0b3JJbnRlcm5hbCIsImlzUGVuZGluZyIsInN0YXJ0VHJhbnNpdGlvbiIsInVzZVRyYW5zaXRpb24iLCJ1c2VFZmZlY3QiLCJkZXZSZW5kZXJJbmRpY2F0b3IiLCJzaG93IiwiaGlkZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator-internal.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.js ***!
  \*************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useSyncDevRenderIndicator\", ({\n    enumerable: true,\n    get: function() {\n        return useSyncDevRenderIndicator;\n    }\n}));\nconst NOOP = (fn)=>fn();\n_c = NOOP;\nconst useSyncDevRenderIndicator = ()=>{\n    let syncDevRenderIndicator = NOOP;\n    if (true) {\n        const { useSyncDevRenderIndicatorInternal } = __webpack_require__(/*! ./use-sync-dev-render-indicator-internal */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator-internal.js\");\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        syncDevRenderIndicator = useSyncDevRenderIndicatorInternal();\n    }\n    return syncDevRenderIndicator;\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-sync-dev-render-indicator.js.map\nvar _c;\n$RefreshReg$(_c, \"NOOP\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdXRpbHMvZGV2LWluZGljYXRvci91c2Utc3luYy1kZXYtcmVuZGVyLWluZGljYXRvci5qcyIsIm1hcHBpbmdzIjoiOzs7OzZEQU9hQTs7O2VBQUFBOzs7QUFQYixhQUFhLENBQUNFLEtBQW1CQTtLQUEzQkQ7QUFPQyxNQUFNRCw0QkFBNEI7SUFDdkMsSUFBSUcseUJBQXlCRjtJQUU3QixJQUFJRyxJQUFvQixFQUFvQjtRQUMxQyxNQUFNLEVBQUVHLGlDQUFpQyxFQUFFLEdBQ3pDQyxtQkFBT0EsQ0FBQyxnTUFBMEM7UUFFcEQsc0RBQXNEO1FBQ3RETCx5QkFBeUJJO0lBQzNCO0lBRUEsT0FBT0o7QUFDVCIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdXRpbHNcXGRldi1pbmRpY2F0b3JcXHVzZS1zeW5jLWRldi1yZW5kZXItaW5kaWNhdG9yLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBOT09QID0gKGZuOiAoKSA9PiB2b2lkKSA9PiBmbigpXG5cbi8qKlxuICogUmV0dXJucyBhIHRyYW5zaXRpb24gZnVuY3Rpb24gdGhhdCBjYW4gYmUgdXNlZCB0byB3cmFwIHJvdXRlciBhY3Rpb25zLlxuICogVGhpcyBhbGxvd3MgdXMgdG8gdGFwIGludG8gdGhlIHRyYW5zaXRpb24gc3RhdGUgb2YgdGhlIHJvdXRlciBhcyBhblxuICogYXBwcm94aW1hdGlvbiBvZiBSZWFjdCByZW5kZXIgdGltZS5cbiAqL1xuZXhwb3J0IGNvbnN0IHVzZVN5bmNEZXZSZW5kZXJJbmRpY2F0b3IgPSAoKSA9PiB7XG4gIGxldCBzeW5jRGV2UmVuZGVySW5kaWNhdG9yID0gTk9PUFxuXG4gIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50Jykge1xuICAgIGNvbnN0IHsgdXNlU3luY0RldlJlbmRlckluZGljYXRvckludGVybmFsIH0gPVxuICAgICAgcmVxdWlyZSgnLi91c2Utc3luYy1kZXYtcmVuZGVyLWluZGljYXRvci1pbnRlcm5hbCcpIGFzIHR5cGVvZiBpbXBvcnQoJy4vdXNlLXN5bmMtZGV2LXJlbmRlci1pbmRpY2F0b3ItaW50ZXJuYWwnKVxuXG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL3J1bGVzLW9mLWhvb2tzXG4gICAgc3luY0RldlJlbmRlckluZGljYXRvciA9IHVzZVN5bmNEZXZSZW5kZXJJbmRpY2F0b3JJbnRlcm5hbCgpXG4gIH1cblxuICByZXR1cm4gc3luY0RldlJlbmRlckluZGljYXRvclxufVxuIl0sIm5hbWVzIjpbInVzZVN5bmNEZXZSZW5kZXJJbmRpY2F0b3IiLCJOT09QIiwiZm4iLCJzeW5jRGV2UmVuZGVySW5kaWNhdG9yIiwicHJvY2VzcyIsImVudiIsIk5PREVfRU5WIiwidXNlU3luY0RldlJlbmRlckluZGljYXRvckludGVybmFsIiwicmVxdWlyZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/format-webpack-messages.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/format-webpack-messages.js ***!
  \*****************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/**\nMIT License\n\nCopyright (c) 2015-present, Facebook, Inc.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return formatWebpackMessages;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _stripansi = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/strip-ansi */ \"(app-pages-browser)/./node_modules/next/dist/compiled/strip-ansi/index.js\"));\n// This file is based on https://github.com/facebook/create-react-app/blob/7b1a32be6ec9f99a6c9a3c66813f3ac09c4736b9/packages/react-dev-utils/formatWebpackMessages.js\n// It's been edited to remove chalk and CRA-specific logic\nconst friendlySyntaxErrorLabel = 'Syntax error:';\nconst WEBPACK_BREAKING_CHANGE_POLYFILLS = '\\n\\nBREAKING CHANGE: webpack < 5 used to include polyfills for node.js core modules by default.';\nfunction isLikelyASyntaxError(message) {\n    return (0, _stripansi.default)(message).includes(friendlySyntaxErrorLabel);\n}\nlet hadMissingSassError = false;\n// Cleans up webpack error messages.\nfunction formatMessage(message, verbose, importTraceNote) {\n    // TODO: Replace this once webpack 5 is stable\n    if (typeof message === 'object' && message.message) {\n        const filteredModuleTrace = message.moduleTrace && message.moduleTrace.filter((trace)=>!/next-(middleware|client-pages|route|edge-function)-loader\\.js/.test(trace.originName));\n        let body = message.message;\n        const breakingChangeIndex = body.indexOf(WEBPACK_BREAKING_CHANGE_POLYFILLS);\n        if (breakingChangeIndex >= 0) {\n            body = body.slice(0, breakingChangeIndex);\n        }\n        message = (message.moduleName ? (0, _stripansi.default)(message.moduleName) + '\\n' : '') + (message.file ? (0, _stripansi.default)(message.file) + '\\n' : '') + body + (message.details && verbose ? '\\n' + message.details : '') + (filteredModuleTrace && filteredModuleTrace.length ? (importTraceNote || '\\n\\nImport trace for requested module:') + filteredModuleTrace.map((trace)=>\"\\n\" + trace.moduleName).join('') : '') + (message.stack && verbose ? '\\n' + message.stack : '');\n    }\n    let lines = message.split('\\n');\n    // Strip Webpack-added headers off errors/warnings\n    // https://github.com/webpack/webpack/blob/master/lib/ModuleError.js\n    lines = lines.filter((line)=>!/Module [A-z ]+\\(from/.test(line));\n    // Transform parsing error into syntax error\n    // TODO: move this to our ESLint formatter?\n    lines = lines.map((line)=>{\n        const parsingError = /Line (\\d+):(?:(\\d+):)?\\s*Parsing error: (.+)$/.exec(line);\n        if (!parsingError) {\n            return line;\n        }\n        const [, errorLine, errorColumn, errorMessage] = parsingError;\n        return friendlySyntaxErrorLabel + \" \" + errorMessage + \" (\" + errorLine + \":\" + errorColumn + \")\";\n    });\n    message = lines.join('\\n');\n    // Smoosh syntax errors (commonly found in CSS)\n    message = message.replace(/SyntaxError\\s+\\((\\d+):(\\d+)\\)\\s*(.+?)\\n/g, \"\" + friendlySyntaxErrorLabel + \" $3 ($1:$2)\\n\");\n    // Clean up export errors\n    message = message.replace(/^.*export '(.+?)' was not found in '(.+?)'.*$/gm, \"Attempted import error: '$1' is not exported from '$2'.\");\n    message = message.replace(/^.*export 'default' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm, \"Attempted import error: '$2' does not contain a default export (imported as '$1').\");\n    message = message.replace(/^.*export '(.+?)' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm, \"Attempted import error: '$1' is not exported from '$3' (imported as '$2').\");\n    lines = message.split('\\n');\n    // Remove leading newline\n    if (lines.length > 2 && lines[1].trim() === '') {\n        lines.splice(1, 1);\n    }\n    // Cleans up verbose \"module not found\" messages for files and packages.\n    if (lines[1] && lines[1].startsWith('Module not found: ')) {\n        lines = [\n            lines[0],\n            lines[1].replace('Error: ', '').replace('Module not found: Cannot find file:', 'Cannot find file:'),\n            ...lines.slice(2)\n        ];\n    }\n    // Add helpful message for users trying to use Sass for the first time\n    if (lines[1] && lines[1].match(/Cannot find module.+sass/)) {\n        // ./file.module.scss (<<loader info>>) => ./file.module.scss\n        const firstLine = lines[0].split('!');\n        lines[0] = firstLine[firstLine.length - 1];\n        lines[1] = \"To use Next.js' built-in Sass support, you first need to install `sass`.\\n\";\n        lines[1] += 'Run `npm i sass` or `yarn add sass` inside your workspace.\\n';\n        lines[1] += '\\nLearn more: https://nextjs.org/docs/messages/install-sass';\n        // dispose of unhelpful stack trace\n        lines = lines.slice(0, 2);\n        hadMissingSassError = true;\n    } else if (hadMissingSassError && message.match(/(sass-loader|resolve-url-loader: CSS error)/)) {\n        // dispose of unhelpful stack trace following missing sass module\n        lines = [];\n    }\n    if (!verbose) {\n        message = lines.join('\\n');\n        // Internal stacks are generally useless so we strip them... with the\n        // exception of stacks containing `webpack:` because they're normally\n        // from user code generated by Webpack. For more information see\n        // https://github.com/facebook/create-react-app/pull/1050\n        message = message.replace(/^\\s*at\\s((?!webpack:).)*:\\d+:\\d+[\\s)]*(\\n|$)/gm, '') // at ... ...:x:y\n        ;\n        message = message.replace(/^\\s*at\\s<anonymous>(\\n|$)/gm, '') // at <anonymous>\n        ;\n        message = message.replace(/File was processed with these loaders:\\n(.+[\\\\/](next[\\\\/]dist[\\\\/].+|@next[\\\\/]react-refresh-utils[\\\\/]loader)\\.js\\n)*You may need an additional loader to handle the result of these loaders.\\n/g, '');\n        lines = message.split('\\n');\n    }\n    // Remove duplicated newlines\n    lines = lines.filter((line, index, arr)=>index === 0 || line.trim() !== '' || line.trim() !== arr[index - 1].trim());\n    // Reassemble the message\n    message = lines.join('\\n');\n    return message.trim();\n}\nfunction formatWebpackMessages(json, verbose) {\n    const formattedErrors = json.errors.map((message)=>{\n        const isUnknownNextFontError = message.message.includes('An error occurred in `next/font`.');\n        return formatMessage(message, isUnknownNextFontError || verbose);\n    });\n    const formattedWarnings = json.warnings.map((message)=>{\n        return formatMessage(message, verbose);\n    });\n    // Reorder errors to put the most relevant ones first.\n    let reactServerComponentsError = -1;\n    for(let i = 0; i < formattedErrors.length; i++){\n        const error = formattedErrors[i];\n        if (error.includes('ReactServerComponentsError')) {\n            reactServerComponentsError = i;\n            break;\n        }\n    }\n    // Move the reactServerComponentsError to the top if it exists\n    if (reactServerComponentsError !== -1) {\n        const error = formattedErrors.splice(reactServerComponentsError, 1);\n        formattedErrors.unshift(error[0]);\n    }\n    const result = {\n        ...json,\n        errors: formattedErrors,\n        warnings: formattedWarnings\n    };\n    if (!verbose && result.errors.some(isLikelyASyntaxError)) {\n        // If there are any syntax errors, show just them.\n        result.errors = result.errors.filter(isLikelyASyntaxError);\n        result.warnings = [];\n    }\n    return result;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=format-webpack-messages.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/format-webpack-messages.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-error-by-type.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/get-error-by-type.js ***!
  \***********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getErrorByType: function() {\n        return getErrorByType;\n    },\n    useFrames: function() {\n        return useFrames;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _shared = __webpack_require__(/*! ../shared */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _stackframe = __webpack_require__(/*! ./stack-frame */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/stack-frame.js\");\nconst _errorsource = __webpack_require__(/*! ../../../../shared/lib/error-source */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/error-source.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst useFrames = (error)=>{\n    if ('use' in _react.default) {\n        const frames = error.frames;\n        if (typeof frames !== 'function') {\n            throw Object.defineProperty(new Error('Invariant: frames must be a function when the React version has React.use. This is a bug in Next.js.'), \"__NEXT_ERROR_CODE\", {\n                value: \"E636\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        return _react.default.use(frames());\n    } else {\n        if (!Array.isArray(error.frames)) {\n            throw Object.defineProperty(new Error('Invariant: frames must be an array when the React version does not have React.use. This is a bug in Next.js.'), \"__NEXT_ERROR_CODE\", {\n                value: \"E637\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        return error.frames;\n    }\n};\nasync function getErrorByType(ev, isAppDir) {\n    const { id, event } = ev;\n    switch(event.type){\n        case _shared.ACTION_UNHANDLED_ERROR:\n        case _shared.ACTION_UNHANDLED_REJECTION:\n            {\n                const baseError = {\n                    id,\n                    runtime: true,\n                    error: event.reason\n                };\n                if ('use' in _react.default) {\n                    const readyRuntimeError = {\n                        ...baseError,\n                        // createMemoizedPromise dedups calls to getOriginalStackFrames\n                        frames: createMemoizedPromise(async ()=>{\n                            return await (0, _stackframe.getOriginalStackFrames)(event.frames, (0, _errorsource.getErrorSource)(event.reason), isAppDir);\n                        })\n                    };\n                    if (event.type === _shared.ACTION_UNHANDLED_ERROR) {\n                        readyRuntimeError.componentStackFrames = event.componentStackFrames;\n                    }\n                    return readyRuntimeError;\n                } else {\n                    const readyRuntimeError = {\n                        ...baseError,\n                        // createMemoizedPromise dedups calls to getOriginalStackFrames\n                        frames: await (0, _stackframe.getOriginalStackFrames)(event.frames, (0, _errorsource.getErrorSource)(event.reason), isAppDir)\n                    };\n                    if (event.type === _shared.ACTION_UNHANDLED_ERROR) {\n                        readyRuntimeError.componentStackFrames = event.componentStackFrames;\n                    }\n                    return readyRuntimeError;\n                }\n            }\n        default:\n            {\n                break;\n            }\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const _ = event;\n    throw Object.defineProperty(new Error('type system invariant violation'), \"__NEXT_ERROR_CODE\", {\n        value: \"E335\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction createMemoizedPromise(promiseFactory) {\n    const cachedPromise = promiseFactory();\n    return function() {\n        return cachedPromise;\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-error-by-type.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-error-by-type.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-socket-url.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/get-socket-url.js ***!
  \********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getSocketUrl\", ({\n    enumerable: true,\n    get: function() {\n        return getSocketUrl;\n    }\n}));\nconst _normalizedassetprefix = __webpack_require__(/*! ../../../../shared/lib/normalized-asset-prefix */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/normalized-asset-prefix.js\");\nfunction getSocketProtocol(assetPrefix) {\n    let protocol = window.location.protocol;\n    try {\n        // assetPrefix is a url\n        protocol = new URL(assetPrefix).protocol;\n    } catch (e) {}\n    return protocol === 'http:' ? 'ws:' : 'wss:';\n}\nfunction getSocketUrl(assetPrefix) {\n    const prefix = (0, _normalizedassetprefix.normalizedAssetPrefix)(assetPrefix);\n    const protocol = getSocketProtocol(assetPrefix || '');\n    if (URL.canParse(prefix)) {\n        // since normalized asset prefix is ensured to be a URL format,\n        // we can safely replace the protocol\n        return prefix.replace(/^http/, 'ws');\n    }\n    const { hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? \":\" + port : '') + prefix;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-socket-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-socket-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-component-stack.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-component-stack.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"parseComponentStack\", ({\n    enumerable: true,\n    get: function() {\n        return parseComponentStack;\n    }\n}));\nvar LocationType = /*#__PURE__*/ function(LocationType) {\n    LocationType[\"FILE\"] = \"file\";\n    LocationType[\"WEBPACK_INTERNAL\"] = \"webpack-internal\";\n    LocationType[\"HTTP\"] = \"http\";\n    LocationType[\"PROTOCOL_RELATIVE\"] = \"protocol-relative\";\n    LocationType[\"UNKNOWN\"] = \"unknown\";\n    return LocationType;\n}(LocationType || {});\n/**\n * Get the type of frame line based on the location\n */ function getLocationType(location) {\n    if (location.startsWith('file://')) {\n        return \"file\";\n    }\n    if (location.includes('webpack-internal://')) {\n        return \"webpack-internal\";\n    }\n    if (location.startsWith('http://') || location.startsWith('https://')) {\n        return \"http\";\n    }\n    if (location.startsWith('//')) {\n        return \"protocol-relative\";\n    }\n    return \"unknown\";\n}\nfunction parseStackFrameLocation(location) {\n    const locationType = getLocationType(location);\n    const modulePath = location == null ? void 0 : location.replace(/^(webpack-internal:\\/\\/\\/|file:\\/\\/)(\\(.*\\)\\/)?/, '');\n    var _modulePath_match;\n    const [, file, lineNumber, column] = (_modulePath_match = modulePath == null ? void 0 : modulePath.match(/^(.+):(\\d+):(\\d+)/)) != null ? _modulePath_match : [];\n    switch(locationType){\n        case \"file\":\n        case \"webpack-internal\":\n            return {\n                canOpenInEditor: true,\n                file,\n                lineNumber: lineNumber ? Number(lineNumber) : undefined,\n                column: column ? Number(column) : undefined\n            };\n        // When the location is a URL we only show the file\n        // TODO: Resolve http(s) URLs through sourcemaps\n        case \"http\":\n        case \"protocol-relative\":\n        case \"unknown\":\n        default:\n            {\n                return {\n                    canOpenInEditor: false\n                };\n            }\n    }\n}\nfunction parseComponentStack(componentStack) {\n    const componentStackFrames = [];\n    for (const line of componentStack.trim().split('\\n')){\n        // TODO: support safari stack trace\n        // Get component and file from the component stack line\n        const match = /at ([^ ]+)( \\((.*)\\))?/.exec(line);\n        if (match == null ? void 0 : match[1]) {\n            const component = match[1];\n            const location = match[3];\n            if (!location) {\n                componentStackFrames.push({\n                    canOpenInEditor: false,\n                    component\n                });\n                continue;\n            }\n            // Stop parsing the component stack if we reach a Next.js component\n            if (location == null ? void 0 : location.includes('next/dist')) {\n                break;\n            }\n            const frameLocation = parseStackFrameLocation(location);\n            componentStackFrames.push({\n                component,\n                ...frameLocation\n            });\n        }\n    }\n    return componentStackFrames;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=parse-component-stack.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-component-stack.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-stack.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-stack.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"parseStack\", ({\n    enumerable: true,\n    get: function() {\n        return parseStack;\n    }\n}));\nconst _stacktraceparser = __webpack_require__(/*! next/dist/compiled/stacktrace-parser */ \"(app-pages-browser)/./node_modules/next/dist/compiled/stacktrace-parser/stack-trace-parser.cjs.js\");\nconst _ishydrationerror = __webpack_require__(/*! ../../is-hydration-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\");\nconst regexNextStatic = /\\/_next(\\/static\\/.+)/;\nfunction parseStack(stack) {\n    if (!stack) return [];\n    const messageAndStack = stack.replace(/^Error: /, '');\n    if ((0, _ishydrationerror.isReactHydrationErrorMessage)(messageAndStack)) {\n        const { stack: parsedStack } = (0, _ishydrationerror.getHydrationErrorStackInfo)(messageAndStack);\n        if (parsedStack) {\n            stack = parsedStack;\n        }\n    }\n    // throw away eval information that stacktrace-parser doesn't support\n    // adapted from https://github.com/stacktracejs/error-stack-parser/blob/9f33c224b5d7b607755eb277f9d51fcdb7287e24/error-stack-parser.js#L59C33-L59C62\n    stack = stack.split('\\n').map((line)=>{\n        if (line.includes('(eval ')) {\n            line = line.replace(/eval code/g, 'eval').replace(/\\(eval at [^()]* \\(/, '(file://').replace(/\\),.*$/g, ')');\n        }\n        return line;\n    }).join('\\n');\n    const frames = (0, _stacktraceparser.parse)(stack);\n    return frames.map((frame)=>{\n        try {\n            const url = new URL(frame.file);\n            const res = regexNextStatic.exec(url.pathname);\n            if (res) {\n                var _process_env___NEXT_DIST_DIR_replace, _process_env___NEXT_DIST_DIR;\n                const distDir = (_process_env___NEXT_DIST_DIR = \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\.next\") == null ? void 0 : (_process_env___NEXT_DIST_DIR_replace = _process_env___NEXT_DIST_DIR.replace(/\\\\/g, '/')) == null ? void 0 : _process_env___NEXT_DIST_DIR_replace.replace(/\\/$/, '');\n                if (distDir) {\n                    frame.file = 'file://' + distDir.concat(res.pop()) + url.search;\n                }\n            }\n        } catch (e) {}\n        return frame;\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=parse-stack.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdXRpbHMvcGFyc2Utc3RhY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs4Q0FTZ0JBOzs7ZUFBQUE7Ozs4Q0FUTTs4Q0FLZjtBQUVQLE1BQU1DLGtCQUFrQjtBQUVqQixTQUFTRCxXQUFXRSxLQUF5QjtJQUNsRCxJQUFJLENBQUNBLE9BQU8sT0FBTyxFQUFFO0lBQ3JCLE1BQU1DLGtCQUFrQkQsTUFBTUUsT0FBTyxDQUFDLFlBQVk7SUFDbEQsSUFBSUMsQ0FBQUEsR0FBQUEsa0JBQUFBLDRCQUFBQSxFQUE2QkYsa0JBQWtCO1FBQ2pELE1BQU0sRUFBRUQsT0FBT0ksV0FBVyxFQUFFLEdBQUdDLENBQUFBLEdBQUFBLGtCQUFBQSwwQkFBQUEsRUFBMkJKO1FBQzFELElBQUlHLGFBQWE7WUFDZkosUUFBUUk7UUFDVjtJQUNGO0lBRUEscUVBQXFFO0lBQ3JFLG9KQUFvSjtJQUNwSkosUUFBUUEsTUFDTE0sS0FBSyxDQUFDLE1BQ05DLEdBQUcsQ0FBQyxDQUFDQztRQUNKLElBQUlBLEtBQUtDLFFBQVEsQ0FBQyxXQUFXO1lBQzNCRCxPQUFPQSxLQUNKTixPQUFPLENBQUMsY0FBYyxRQUN0QkEsT0FBTyxDQUFDLHVCQUF1QixZQUMvQkEsT0FBTyxDQUFDLFdBQVc7UUFDeEI7UUFFQSxPQUFPTTtJQUNULEdBQ0NFLElBQUksQ0FBQztJQUVSLE1BQU1DLFNBQVNDLENBQUFBLEdBQUFBLGtCQUFBQSxLQUFBQSxFQUFNWjtJQUNyQixPQUFPVyxPQUFPSixHQUFHLENBQUMsQ0FBQ007UUFDakIsSUFBSTtZQUNGLE1BQU1DLE1BQU0sSUFBSUMsSUFBSUYsTUFBTUcsSUFBSTtZQUM5QixNQUFNQyxNQUFNbEIsZ0JBQWdCbUIsSUFBSSxDQUFDSixJQUFJSyxRQUFRO1lBQzdDLElBQUlGLEtBQUs7b0JBQ1NHLHNDQUFBQTtnQkFBaEIsTUFBTUMsVUFBQUEsQ0FBVUQsK0JBQUFBLDRDQUFZRyxLQUFlLGlCQUEzQkgsdUNBQUFBLDZCQUNabEIsT0FBTyxDQUFDLE9BQU8seUJBREhrQixxQ0FFWmxCLE9BQU8sQ0FBQyxPQUFPO2dCQUNuQixJQUFJbUIsU0FBUztvQkFDWFIsTUFBTUcsSUFBSSxHQUFHLFlBQVlLLFFBQVFHLE1BQU0sQ0FBQ1AsSUFBSVEsR0FBRyxNQUFPWCxJQUFJWSxNQUFNO2dCQUNsRTtZQUNGO1FBQ0YsRUFBRSxVQUFNLENBQUM7UUFDVCxPQUFPYjtJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xccmVhY3QtZGV2LW92ZXJsYXlcXHV0aWxzXFxwYXJzZS1zdGFjay50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBwYXJzZSB9IGZyb20gJ25leHQvZGlzdC9jb21waWxlZC9zdGFja3RyYWNlLXBhcnNlcidcbmltcG9ydCB0eXBlIHsgU3RhY2tGcmFtZSB9IGZyb20gJ25leHQvZGlzdC9jb21waWxlZC9zdGFja3RyYWNlLXBhcnNlcidcbmltcG9ydCB7XG4gIGdldEh5ZHJhdGlvbkVycm9yU3RhY2tJbmZvLFxuICBpc1JlYWN0SHlkcmF0aW9uRXJyb3JNZXNzYWdlLFxufSBmcm9tICcuLi8uLi9pcy1oeWRyYXRpb24tZXJyb3InXG5cbmNvbnN0IHJlZ2V4TmV4dFN0YXRpYyA9IC9cXC9fbmV4dChcXC9zdGF0aWNcXC8uKykvXG5cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZVN0YWNrKHN0YWNrOiBzdHJpbmcgfCB1bmRlZmluZWQpOiBTdGFja0ZyYW1lW10ge1xuICBpZiAoIXN0YWNrKSByZXR1cm4gW11cbiAgY29uc3QgbWVzc2FnZUFuZFN0YWNrID0gc3RhY2sucmVwbGFjZSgvXkVycm9yOiAvLCAnJylcbiAgaWYgKGlzUmVhY3RIeWRyYXRpb25FcnJvck1lc3NhZ2UobWVzc2FnZUFuZFN0YWNrKSkge1xuICAgIGNvbnN0IHsgc3RhY2s6IHBhcnNlZFN0YWNrIH0gPSBnZXRIeWRyYXRpb25FcnJvclN0YWNrSW5mbyhtZXNzYWdlQW5kU3RhY2spXG4gICAgaWYgKHBhcnNlZFN0YWNrKSB7XG4gICAgICBzdGFjayA9IHBhcnNlZFN0YWNrXG4gICAgfVxuICB9XG5cbiAgLy8gdGhyb3cgYXdheSBldmFsIGluZm9ybWF0aW9uIHRoYXQgc3RhY2t0cmFjZS1wYXJzZXIgZG9lc24ndCBzdXBwb3J0XG4gIC8vIGFkYXB0ZWQgZnJvbSBodHRwczovL2dpdGh1Yi5jb20vc3RhY2t0cmFjZWpzL2Vycm9yLXN0YWNrLXBhcnNlci9ibG9iLzlmMzNjMjI0YjVkN2I2MDc3NTVlYjI3N2Y5ZDUxZmNkYjcyODdlMjQvZXJyb3Itc3RhY2stcGFyc2VyLmpzI0w1OUMzMy1MNTlDNjJcbiAgc3RhY2sgPSBzdGFja1xuICAgIC5zcGxpdCgnXFxuJylcbiAgICAubWFwKChsaW5lKSA9PiB7XG4gICAgICBpZiAobGluZS5pbmNsdWRlcygnKGV2YWwgJykpIHtcbiAgICAgICAgbGluZSA9IGxpbmVcbiAgICAgICAgICAucmVwbGFjZSgvZXZhbCBjb2RlL2csICdldmFsJylcbiAgICAgICAgICAucmVwbGFjZSgvXFwoZXZhbCBhdCBbXigpXSogXFwoLywgJyhmaWxlOi8vJylcbiAgICAgICAgICAucmVwbGFjZSgvXFwpLC4qJC9nLCAnKScpXG4gICAgICB9XG5cbiAgICAgIHJldHVybiBsaW5lXG4gICAgfSlcbiAgICAuam9pbignXFxuJylcblxuICBjb25zdCBmcmFtZXMgPSBwYXJzZShzdGFjaylcbiAgcmV0dXJuIGZyYW1lcy5tYXAoKGZyYW1lKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVybCA9IG5ldyBVUkwoZnJhbWUuZmlsZSEpXG4gICAgICBjb25zdCByZXMgPSByZWdleE5leHRTdGF0aWMuZXhlYyh1cmwucGF0aG5hbWUpXG4gICAgICBpZiAocmVzKSB7XG4gICAgICAgIGNvbnN0IGRpc3REaXIgPSBwcm9jZXNzLmVudi5fX05FWFRfRElTVF9ESVJcbiAgICAgICAgICA/LnJlcGxhY2UoL1xcXFwvZywgJy8nKVxuICAgICAgICAgID8ucmVwbGFjZSgvXFwvJC8sICcnKVxuICAgICAgICBpZiAoZGlzdERpcikge1xuICAgICAgICAgIGZyYW1lLmZpbGUgPSAnZmlsZTovLycgKyBkaXN0RGlyLmNvbmNhdChyZXMucG9wKCkhKSArIHVybC5zZWFyY2hcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2gge31cbiAgICByZXR1cm4gZnJhbWVcbiAgfSlcbn1cbiJdLCJuYW1lcyI6WyJwYXJzZVN0YWNrIiwicmVnZXhOZXh0U3RhdGljIiwic3RhY2siLCJtZXNzYWdlQW5kU3RhY2siLCJyZXBsYWNlIiwiaXNSZWFjdEh5ZHJhdGlvbkVycm9yTWVzc2FnZSIsInBhcnNlZFN0YWNrIiwiZ2V0SHlkcmF0aW9uRXJyb3JTdGFja0luZm8iLCJzcGxpdCIsIm1hcCIsImxpbmUiLCJpbmNsdWRlcyIsImpvaW4iLCJmcmFtZXMiLCJwYXJzZSIsImZyYW1lIiwidXJsIiwiVVJMIiwiZmlsZSIsInJlcyIsImV4ZWMiLCJwYXRobmFtZSIsInByb2Nlc3MiLCJkaXN0RGlyIiwiZW52IiwiX19ORVhUX0RJU1RfRElSIiwiY29uY2F0IiwicG9wIiwic2VhcmNoIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-stack.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/stack-frame.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/stack-frame.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getFrameSource: function() {\n        return getFrameSource;\n    },\n    getOriginalStackFrames: function() {\n        return getOriginalStackFrames;\n    }\n});\nconst _webpackmodulepath = __webpack_require__(/*! ./webpack-module-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/webpack-module-path.js\");\nfunction getOriginalStackFrame(source, response) {\n    var _source_file;\n    async function _getOriginalStackFrame() {\n        var _body_originalStackFrame;\n        if (response.status === 'rejected') {\n            throw Object.defineProperty(new Error(response.reason), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        const body = response.value;\n        return {\n            error: false,\n            reason: null,\n            external: false,\n            sourceStackFrame: source,\n            originalStackFrame: body.originalStackFrame,\n            originalCodeFrame: body.originalCodeFrame || null,\n            ignored: ((_body_originalStackFrame = body.originalStackFrame) == null ? void 0 : _body_originalStackFrame.ignored) || false\n        };\n    }\n    // TODO: merge this section into ignoredList handling\n    if (source.file === 'file://' || ((_source_file = source.file) == null ? void 0 : _source_file.match(/https?:\\/\\//))) {\n        return Promise.resolve({\n            error: false,\n            reason: null,\n            external: true,\n            sourceStackFrame: source,\n            originalStackFrame: null,\n            originalCodeFrame: null,\n            ignored: true\n        });\n    }\n    return _getOriginalStackFrame().catch((err)=>{\n        var _err_message, _ref;\n        return {\n            error: true,\n            reason: (_ref = (_err_message = err == null ? void 0 : err.message) != null ? _err_message : err == null ? void 0 : err.toString()) != null ? _ref : 'Unknown Error',\n            external: false,\n            sourceStackFrame: source,\n            originalStackFrame: null,\n            originalCodeFrame: null,\n            ignored: false\n        };\n    });\n}\nasync function getOriginalStackFrames(frames, type, isAppDir) {\n    const req = {\n        frames,\n        isServer: type === 'server',\n        isEdgeServer: type === 'edge-server',\n        isAppDirectory: isAppDir\n    };\n    let res = undefined;\n    let reason = undefined;\n    try {\n        res = await fetch('/__nextjs_original-stack-frames', {\n            method: 'POST',\n            body: JSON.stringify(req)\n        });\n    } catch (e) {\n        reason = e + '';\n    }\n    // When fails to fetch the original stack frames, we reject here to be\n    // caught at `_getOriginalStackFrame()` and return the stack frames so\n    // that the error overlay can render.\n    if (res && res.ok && res.status !== 204) {\n        const data = await res.json();\n        return Promise.all(frames.map((frame, index)=>getOriginalStackFrame(frame, data[index])));\n    } else {\n        if (res) {\n            reason = await res.text();\n        }\n    }\n    return Promise.all(frames.map((frame)=>getOriginalStackFrame(frame, {\n            status: 'rejected',\n            reason: \"Failed to fetch the original stack frames \" + (reason ? \": \" + reason : '')\n        })));\n}\nfunction getFrameSource(frame) {\n    if (!frame.file) return '';\n    const isWebpackFrame = (0, _webpackmodulepath.isWebpackInternalResource)(frame.file);\n    let str = '';\n    // Skip URL parsing for webpack internal file paths.\n    if (isWebpackFrame) {\n        str = (0, _webpackmodulepath.formatFrameSourceFile)(frame.file);\n    } else {\n        try {\n            var _globalThis_location;\n            const u = new URL(frame.file);\n            let parsedPath = '';\n            // Strip the origin for same-origin scripts.\n            if (((_globalThis_location = globalThis.location) == null ? void 0 : _globalThis_location.origin) !== u.origin) {\n                // URLs can be valid without an `origin`, so long as they have a\n                // `protocol`. However, `origin` is preferred.\n                if (u.origin === 'null') {\n                    parsedPath += u.protocol;\n                } else {\n                    parsedPath += u.origin;\n                }\n            }\n            // Strip query string information as it's typically too verbose to be\n            // meaningful.\n            parsedPath += u.pathname;\n            str = (0, _webpackmodulepath.formatFrameSourceFile)(parsedPath);\n        } catch (e) {\n            str = (0, _webpackmodulepath.formatFrameSourceFile)(frame.file);\n        }\n    }\n    if (!(0, _webpackmodulepath.isWebpackInternalResource)(frame.file) && frame.lineNumber != null) {\n        if (str) {\n            if (frame.column != null) {\n                str += \" (\" + frame.lineNumber + \":\" + frame.column + \")\";\n            } else {\n                str += \" (\" + frame.lineNumber + \")\";\n            }\n        }\n    }\n    return str;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=stack-frame.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/stack-frame.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/use-websocket.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/use-websocket.js ***!
  \*******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    useSendMessage: function() {\n        return useSendMessage;\n    },\n    useTurbopack: function() {\n        return useTurbopack;\n    },\n    useWebsocket: function() {\n        return useWebsocket;\n    },\n    useWebsocketPing: function() {\n        return useWebsocketPing;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _getsocketurl = __webpack_require__(/*! ./get-socket-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-socket-url.js\");\nfunction useWebsocket(assetPrefix) {\n    const webSocketRef = (0, _react.useRef)(undefined);\n    (0, _react.useEffect)(()=>{\n        if (webSocketRef.current) {\n            return;\n        }\n        const url = (0, _getsocketurl.getSocketUrl)(assetPrefix);\n        webSocketRef.current = new window.WebSocket(\"\" + url + \"/_next/webpack-hmr\");\n    }, [\n        assetPrefix\n    ]);\n    return webSocketRef;\n}\nfunction useSendMessage(webSocketRef) {\n    const sendMessage = (0, _react.useCallback)((data)=>{\n        const socket = webSocketRef.current;\n        if (!socket || socket.readyState !== socket.OPEN) {\n            return;\n        }\n        return socket.send(data);\n    }, [\n        webSocketRef\n    ]);\n    return sendMessage;\n}\nfunction useTurbopack(sendMessage, onUpdateError) {\n    const turbopackState = (0, _react.useRef)({\n        init: false,\n        // Until the dynamic import resolves, queue any turbopack messages which will be replayed.\n        queue: [],\n        callback: undefined\n    });\n    const processTurbopackMessage = (0, _react.useCallback)((msg)=>{\n        const { callback, queue } = turbopackState.current;\n        if (callback) {\n            callback(msg);\n        } else {\n            queue.push(msg);\n        }\n    }, []);\n    (0, _react.useEffect)(()=>{\n        const { current: initCurrent } = turbopackState;\n        // TODO(WEB-1589): only install if `process.turbopack` set.\n        if (initCurrent.init) {\n            return;\n        }\n        initCurrent.init = true;\n        Promise.all(/*! import() */[__webpack_require__.e(\"vendors-node_modules_a\"), __webpack_require__.e(\"vendors-node_modules_lucide-react_dist_esm_I\"), __webpack_require__.e(\"vendors-node_modules_next_dist_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_f\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_m\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_shared_js-0\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ca\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_dialog_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_call-stack_c-dc969f58\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-c82b02ac\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-13e6d335\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dialog_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_en\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_e\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_ho\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_o\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_t\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_container_b\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_s\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_react-dev-overlay_utils_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_redirect-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_re\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_components_un\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_det\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_d\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_i\"), __webpack_require__.e(\"vendors-node_modules_next_dist_client_n\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_a\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-dom_cjs_react-dom-client_development_js-3139057c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_react-\"), __webpack_require__.e(\"vendors-node_modules_next_dist_compiled_r\"), __webpack_require__.e(\"vendors-node_modules_next_dist_lib_c\"), __webpack_require__.e(\"vendors-node_modules_next_dist_p\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_h\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_m\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_router_router_js-58cbbd23\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_ro\"), __webpack_require__.e(\"vendors-node_modules_next_dist_shared_lib_se\"), __webpack_require__.e(\"vendors-node_modules_next_d\"), __webpack_require__.e(\"vendors-node_modules_p\"), __webpack_require__.e(\"vendors-node_modules_radix-ui_react-p\"), __webpack_require__.e(\"vendors-node_modules_react-dom_cjs_react-dom_development_js-7b5aa877\"), __webpack_require__.e(\"vendors-node_modules_r\"), __webpack_require__.e(\"vendors-node_modules_s\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/noop-turbopack-hmr.js\", 23)).then((param)=>{\n            let { connect } = param;\n            const { current } = turbopackState;\n            connect({\n                addMessageListener (cb) {\n                    current.callback = cb;\n                    // Replay all Turbopack messages before we were able to establish the HMR client.\n                    for (const msg of current.queue){\n                        cb(msg);\n                    }\n                    current.queue = undefined;\n                },\n                sendMessage,\n                onUpdateError\n            });\n        });\n    }, [\n        sendMessage,\n        onUpdateError\n    ]);\n    return processTurbopackMessage;\n}\nfunction useWebsocketPing(websocketRef) {\n    _s();\n    const sendMessage = useSendMessage(websocketRef);\n    const { tree } = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    (0, _react.useEffect)(()=>{\n        // Never send pings when using Turbopack as it's not used.\n        // Pings were originally used to keep track of active routes in on-demand-entries with webpack.\n        if (false) {}\n        // Taken from on-demand-entries-client.js\n        const interval = setInterval(()=>{\n            sendMessage(JSON.stringify({\n                event: 'ping',\n                tree,\n                appDirRoute: true\n            }));\n        }, 2500);\n        return ()=>clearInterval(interval);\n    }, [\n        tree,\n        sendMessage\n    ]);\n}\n_s(useWebsocketPing, \"wUse5NG7XMV1uhKK1kY0LLDje8k=\", false, function() {\n    return [\n        useSendMessage\n    ];\n});\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-websocket.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/use-websocket.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/webpack-module-path.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/webpack-module-path.js ***!
  \*************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatFrameSourceFile: function() {\n        return formatFrameSourceFile;\n    },\n    isWebpackInternalResource: function() {\n        return isWebpackInternalResource;\n    }\n});\nconst replacementRegExes = [\n    /^webpack-internal:\\/\\/\\/(\\([\\w-]+\\)\\/)?/,\n    /^(webpack:\\/\\/\\/|webpack:\\/\\/(_N_E\\/)?)(\\([\\w-]+\\)\\/)?/\n];\nfunction isWebpackInternalResource(file) {\n    for (const regex of replacementRegExes){\n        if (regex.test(file)) return true;\n        file = file.replace(regex, '');\n    }\n    return false;\n}\nfunction formatFrameSourceFile(file) {\n    for (const regex of replacementRegExes){\n        file = file.replace(regex, '');\n    }\n    return file;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=webpack-module-path.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdXRpbHMvd2VicGFjay1tb2R1bGUtcGF0aC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUF1QmdCQSxxQkFBcUI7ZUFBckJBOztJQWxCQUMseUJBQXlCO2VBQXpCQTs7O0FBTGhCLE1BQU1DLHFCQUFxQjtJQUN6QjtJQUNBO0NBQ0Q7QUFFTSxTQUFTRCwwQkFBMEJFLElBQVk7SUFDcEQsS0FBSyxNQUFNQyxTQUFTRixtQkFBb0I7UUFDdEMsSUFBSUUsTUFBTUMsSUFBSSxDQUFDRixPQUFPLE9BQU87UUFFN0JBLE9BQU9BLEtBQUtHLE9BQU8sQ0FBQ0YsT0FBTztJQUM3QjtJQUVBLE9BQU87QUFDVDtBQVVPLFNBQVNKLHNCQUFzQkcsSUFBWTtJQUNoRCxLQUFLLE1BQU1DLFNBQVNGLG1CQUFvQjtRQUN0Q0MsT0FBT0EsS0FBS0csT0FBTyxDQUFDRixPQUFPO0lBQzdCO0lBRUEsT0FBT0Q7QUFDVCIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdXRpbHNcXHdlYnBhY2stbW9kdWxlLXBhdGgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgcmVwbGFjZW1lbnRSZWdFeGVzID0gW1xuICAvXndlYnBhY2staW50ZXJuYWw6XFwvXFwvXFwvKFxcKFtcXHctXStcXClcXC8pPy8sXG4gIC9eKHdlYnBhY2s6XFwvXFwvXFwvfHdlYnBhY2s6XFwvXFwvKF9OX0VcXC8pPykoXFwoW1xcdy1dK1xcKVxcLyk/Lyxcbl1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzV2VicGFja0ludGVybmFsUmVzb3VyY2UoZmlsZTogc3RyaW5nKSB7XG4gIGZvciAoY29uc3QgcmVnZXggb2YgcmVwbGFjZW1lbnRSZWdFeGVzKSB7XG4gICAgaWYgKHJlZ2V4LnRlc3QoZmlsZSkpIHJldHVybiB0cnVlXG5cbiAgICBmaWxlID0gZmlsZS5yZXBsYWNlKHJlZ2V4LCAnJylcbiAgfVxuXG4gIHJldHVybiBmYWxzZVxufVxuXG4vKipcbiAqIEZvcm1hdCB0aGUgd2VicGFjayBpbnRlcm5hbCBpZCB0byBvcmlnaW5hbCBmaWxlIHBhdGhcbiAqXG4gKiB3ZWJwYWNrLWludGVybmFsOi8vLy4vc3JjL2hlbGxvLnRzeCA9PiAuL3NyYy9oZWxsby50c3hcbiAqIHdlYnBhY2s6Ly9fTl9FLy4vc3JjL2hlbGxvLnRzeCA9PiAuL3NyYy9oZWxsby50c3hcbiAqIHdlYnBhY2s6Ly8uL3NyYy9oZWxsby50c3ggPT4gLi9zcmMvaGVsbG8udHN4XG4gKiB3ZWJwYWNrOi8vLy4vc3JjL2hlbGxvLnRzeCA9PiAuL3NyYy9oZWxsby50c3hcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdEZyYW1lU291cmNlRmlsZShmaWxlOiBzdHJpbmcpIHtcbiAgZm9yIChjb25zdCByZWdleCBvZiByZXBsYWNlbWVudFJlZ0V4ZXMpIHtcbiAgICBmaWxlID0gZmlsZS5yZXBsYWNlKHJlZ2V4LCAnJylcbiAgfVxuXG4gIHJldHVybiBmaWxlXG59XG4iXSwibmFtZXMiOlsiZm9ybWF0RnJhbWVTb3VyY2VGaWxlIiwiaXNXZWJwYWNrSW50ZXJuYWxSZXNvdXJjZSIsInJlcGxhY2VtZW50UmVnRXhlcyIsImZpbGUiLCJyZWdleCIsInRlc3QiLCJyZXBsYWNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/webpack-module-path.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js ***!
  \*********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"css\", ({\n    enumerable: true,\n    get: function() {\n        return css;\n    }\n}));\nfunction css(strings) {\n    for(var _len = arguments.length, keys = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        keys[_key - 1] = arguments[_key];\n    }\n    const lastIndex = strings.length - 1;\n    const str = strings.slice(0, lastIndex).reduce((p, s, i)=>p + s + keys[i], '') + strings[lastIndex];\n    return str // Remove comments\n    .replace(/\\/\\*[\\s\\S]*?\\*\\//g, '') // Remove whitespace, tabs, and newlines\n    .replace(/\\s+/g, ' ') // Remove spaces before and after semicolons, and spaces after commas\n    .replace(/\\s*([:;,{}])\\s*/g, '$1') // Remove extra semicolons\n    .replace(/;+}/g, '}') // Trim leading and trailing whitespaces\n    .trim();\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=css.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/dev-render-indicator.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/dev-render-indicator.js ***!
  \****************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/*\n * Singleton store to track whether the app is currently being rendered\n * Used by the dev tools indicator to show render status\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    devRenderIndicator: function() {\n        return devRenderIndicator;\n    },\n    useIsDevRendering: function() {\n        return useIsDevRendering;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nlet isVisible = false;\nlet listeners = [];\nconst subscribe = (listener)=>{\n    listeners.push(listener);\n    return ()=>{\n        listeners = listeners.filter((l)=>l !== listener);\n    };\n};\nconst getSnapshot = ()=>isVisible;\nconst show = ()=>{\n    isVisible = true;\n    listeners.forEach((listener)=>listener());\n};\nconst hide = ()=>{\n    isVisible = false;\n    listeners.forEach((listener)=>listener());\n};\nfunction useIsDevRendering() {\n    return (0, _react.useSyncExternalStore)(subscribe, getSnapshot);\n}\nconst devRenderIndicator = {\n    show,\n    hide\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-render-indicator.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/dev-render-indicator.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/format-webpack-messages.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/format-webpack-messages.js ***!
  \*****************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/**\nMIT License\n\nCopyright (c) 2015-present, Facebook, Inc.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return formatWebpackMessages;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _stripansi = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/strip-ansi */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/strip-ansi/index.js\"));\n// This file is based on https://github.com/facebook/create-react-app/blob/7b1a32be6ec9f99a6c9a3c66813f3ac09c4736b9/packages/react-dev-utils/formatWebpackMessages.js\n// It's been edited to remove chalk and CRA-specific logic\nconst friendlySyntaxErrorLabel = 'Syntax error:';\nconst WEBPACK_BREAKING_CHANGE_POLYFILLS = '\\n\\nBREAKING CHANGE: webpack < 5 used to include polyfills for node.js core modules by default.';\nfunction isLikelyASyntaxError(message) {\n    return (0, _stripansi.default)(message).includes(friendlySyntaxErrorLabel);\n}\nlet hadMissingSassError = false;\n// Cleans up webpack error messages.\nfunction formatMessage(message, verbose, importTraceNote) {\n    // TODO: Replace this once webpack 5 is stable\n    if (typeof message === 'object' && message.message) {\n        const filteredModuleTrace = message.moduleTrace && message.moduleTrace.filter((trace)=>!/next-(middleware|client-pages|route|edge-function)-loader\\.js/.test(trace.originName));\n        let body = message.message;\n        const breakingChangeIndex = body.indexOf(WEBPACK_BREAKING_CHANGE_POLYFILLS);\n        if (breakingChangeIndex >= 0) {\n            body = body.slice(0, breakingChangeIndex);\n        }\n        message = (message.moduleName ? (0, _stripansi.default)(message.moduleName) + '\\n' : '') + (message.file ? (0, _stripansi.default)(message.file) + '\\n' : '') + body + (message.details && verbose ? '\\n' + message.details : '') + (filteredModuleTrace && filteredModuleTrace.length ? (importTraceNote || '\\n\\nImport trace for requested module:') + filteredModuleTrace.map((trace)=>\"\\n\" + trace.moduleName).join('') : '') + (message.stack && verbose ? '\\n' + message.stack : '');\n    }\n    let lines = message.split('\\n');\n    // Strip Webpack-added headers off errors/warnings\n    // https://github.com/webpack/webpack/blob/master/lib/ModuleError.js\n    lines = lines.filter((line)=>!/Module [A-z ]+\\(from/.test(line));\n    // Transform parsing error into syntax error\n    // TODO: move this to our ESLint formatter?\n    lines = lines.map((line)=>{\n        const parsingError = /Line (\\d+):(?:(\\d+):)?\\s*Parsing error: (.+)$/.exec(line);\n        if (!parsingError) {\n            return line;\n        }\n        const [, errorLine, errorColumn, errorMessage] = parsingError;\n        return friendlySyntaxErrorLabel + \" \" + errorMessage + \" (\" + errorLine + \":\" + errorColumn + \")\";\n    });\n    message = lines.join('\\n');\n    // Smoosh syntax errors (commonly found in CSS)\n    message = message.replace(/SyntaxError\\s+\\((\\d+):(\\d+)\\)\\s*(.+?)\\n/g, \"\" + friendlySyntaxErrorLabel + \" $3 ($1:$2)\\n\");\n    // Clean up export errors\n    message = message.replace(/^.*export '(.+?)' was not found in '(.+?)'.*$/gm, \"Attempted import error: '$1' is not exported from '$2'.\");\n    message = message.replace(/^.*export 'default' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm, \"Attempted import error: '$2' does not contain a default export (imported as '$1').\");\n    message = message.replace(/^.*export '(.+?)' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm, \"Attempted import error: '$1' is not exported from '$3' (imported as '$2').\");\n    lines = message.split('\\n');\n    // Remove leading newline\n    if (lines.length > 2 && lines[1].trim() === '') {\n        lines.splice(1, 1);\n    }\n    // Cleans up verbose \"module not found\" messages for files and packages.\n    if (lines[1] && lines[1].startsWith('Module not found: ')) {\n        lines = [\n            lines[0],\n            lines[1].replace('Error: ', '').replace('Module not found: Cannot find file:', 'Cannot find file:'),\n            ...lines.slice(2)\n        ];\n    }\n    // Add helpful message for users trying to use Sass for the first time\n    if (lines[1] && lines[1].match(/Cannot find module.+sass/)) {\n        // ./file.module.scss (<<loader info>>) => ./file.module.scss\n        const firstLine = lines[0].split('!');\n        lines[0] = firstLine[firstLine.length - 1];\n        lines[1] = \"To use Next.js' built-in Sass support, you first need to install `sass`.\\n\";\n        lines[1] += 'Run `npm i sass` or `yarn add sass` inside your workspace.\\n';\n        lines[1] += '\\nLearn more: https://nextjs.org/docs/messages/install-sass';\n        // dispose of unhelpful stack trace\n        lines = lines.slice(0, 2);\n        hadMissingSassError = true;\n    } else if (hadMissingSassError && message.match(/(sass-loader|resolve-url-loader: CSS error)/)) {\n        // dispose of unhelpful stack trace following missing sass module\n        lines = [];\n    }\n    if (!verbose) {\n        message = lines.join('\\n');\n        // Internal stacks are generally useless so we strip them... with the\n        // exception of stacks containing `webpack:` because they're normally\n        // from user code generated by Webpack. For more information see\n        // https://github.com/facebook/create-react-app/pull/1050\n        message = message.replace(/^\\s*at\\s((?!webpack:).)*:\\d+:\\d+[\\s)]*(\\n|$)/gm, '') // at ... ...:x:y\n        ;\n        message = message.replace(/^\\s*at\\s<anonymous>(\\n|$)/gm, '') // at <anonymous>\n        ;\n        message = message.replace(/File was processed with these loaders:\\n(.+[\\\\/](next[\\\\/]dist[\\\\/].+|@next[\\\\/]react-refresh-utils[\\\\/]loader)\\.js\\n)*You may need an additional loader to handle the result of these loaders.\\n/g, '');\n        lines = message.split('\\n');\n    }\n    // Remove duplicated newlines\n    lines = lines.filter((line, index, arr)=>index === 0 || line.trim() !== '' || line.trim() !== arr[index - 1].trim());\n    // Reassemble the message\n    message = lines.join('\\n');\n    return message.trim();\n}\nfunction formatWebpackMessages(json, verbose) {\n    const formattedErrors = json.errors.map((message)=>{\n        const isUnknownNextFontError = message.message.includes('An error occurred in `next/font`.');\n        return formatMessage(message, isUnknownNextFontError || verbose);\n    });\n    const formattedWarnings = json.warnings.map((message)=>{\n        return formatMessage(message, verbose);\n    });\n    // Reorder errors to put the most relevant ones first.\n    let reactServerComponentsError = -1;\n    for(let i = 0; i < formattedErrors.length; i++){\n        const error = formattedErrors[i];\n        if (error.includes('ReactServerComponentsError')) {\n            reactServerComponentsError = i;\n            break;\n        }\n    }\n    // Move the reactServerComponentsError to the top if it exists\n    if (reactServerComponentsError !== -1) {\n        const error = formattedErrors.splice(reactServerComponentsError, 1);\n        formattedErrors.unshift(error[0]);\n    }\n    const result = {\n        ...json,\n        errors: formattedErrors,\n        warnings: formattedWarnings\n    };\n    if (!verbose && result.errors.some(isLikelyASyntaxError)) {\n        // If there are any syntax errors, show just them.\n        result.errors = result.errors.filter(isLikelyASyntaxError);\n        result.warnings = [];\n    }\n    return result;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=format-webpack-messages.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/format-webpack-messages.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-error-by-type.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/get-error-by-type.js ***!
  \***********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getErrorByType: function() {\n        return getErrorByType;\n    },\n    useFrames: function() {\n        return useFrames;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _shared = __webpack_require__(/*! ../shared */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _stackframe = __webpack_require__(/*! ./stack-frame */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/stack-frame.js\");\nconst _errorsource = __webpack_require__(/*! ../../../../shared/lib/error-source */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/error-source.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst useFrames = (error)=>{\n    if ('use' in _react.default) {\n        const frames = error.frames;\n        if (typeof frames !== 'function') {\n            throw Object.defineProperty(new Error('Invariant: frames must be a function when the React version has React.use. This is a bug in Next.js.'), \"__NEXT_ERROR_CODE\", {\n                value: \"E636\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        return _react.default.use(frames());\n    } else {\n        if (!Array.isArray(error.frames)) {\n            throw Object.defineProperty(new Error('Invariant: frames must be an array when the React version does not have React.use. This is a bug in Next.js.'), \"__NEXT_ERROR_CODE\", {\n                value: \"E637\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        return error.frames;\n    }\n};\nasync function getErrorByType(ev, isAppDir) {\n    const { id, event } = ev;\n    switch(event.type){\n        case _shared.ACTION_UNHANDLED_ERROR:\n        case _shared.ACTION_UNHANDLED_REJECTION:\n            {\n                const baseError = {\n                    id,\n                    runtime: true,\n                    error: event.reason\n                };\n                if ('use' in _react.default) {\n                    const readyRuntimeError = {\n                        ...baseError,\n                        // createMemoizedPromise dedups calls to getOriginalStackFrames\n                        frames: createMemoizedPromise(async ()=>{\n                            return await (0, _stackframe.getOriginalStackFrames)(event.frames, (0, _errorsource.getErrorSource)(event.reason), isAppDir);\n                        })\n                    };\n                    if (event.type === _shared.ACTION_UNHANDLED_ERROR) {\n                        readyRuntimeError.componentStackFrames = event.componentStackFrames;\n                    }\n                    return readyRuntimeError;\n                } else {\n                    const readyRuntimeError = {\n                        ...baseError,\n                        // createMemoizedPromise dedups calls to getOriginalStackFrames\n                        frames: await (0, _stackframe.getOriginalStackFrames)(event.frames, (0, _errorsource.getErrorSource)(event.reason), isAppDir)\n                    };\n                    if (event.type === _shared.ACTION_UNHANDLED_ERROR) {\n                        readyRuntimeError.componentStackFrames = event.componentStackFrames;\n                    }\n                    return readyRuntimeError;\n                }\n            }\n        default:\n            {\n                break;\n            }\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const _ = event;\n    throw Object.defineProperty(new Error('type system invariant violation'), \"__NEXT_ERROR_CODE\", {\n        value: \"E335\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction createMemoizedPromise(promiseFactory) {\n    const cachedPromise = promiseFactory();\n    return function() {\n        return cachedPromise;\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-error-by-type.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdXRpbHMvZ2V0LWVycm9yLWJ5LXR5cGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBc0NzQkEsY0FBYztlQUFkQTs7SUF0QlRDLFNBQVM7ZUFBVEE7Ozs7b0NBaEJzRDt3Q0FFNUI7eUNBR1I7NEVBQ2I7QUFVWCxNQUFNQSxZQUFZLENBQUNDO0lBQ3hCLElBQUksU0FBU0MsT0FBQUEsT0FBSyxFQUFFO1FBQ2xCLE1BQU1DLFNBQVNGLE1BQU1FLE1BQU07UUFFM0IsSUFBSSxPQUFPQSxXQUFXLFlBQVk7WUFDaEMsTUFBTSxxQkFFTCxDQUZLLElBQUlDLE1BQ1IseUdBREk7dUJBQUE7NEJBQUE7OEJBQUE7WUFFTjtRQUNGO1FBRUEsT0FBT0YsT0FBQUEsT0FBSyxDQUFDRyxHQUFHLENBQUVGO0lBQ3BCLE9BQU87UUFDTCxJQUFJLENBQUNHLE1BQU1DLE9BQU8sQ0FBQ04sTUFBTUUsTUFBTSxHQUFHO1lBQ2hDLE1BQU0scUJBRUwsQ0FGSyxJQUFJQyxNQUNSLGlIQURJO3VCQUFBOzRCQUFBOzhCQUFBO1lBRU47UUFDRjtRQUVBLE9BQU9ILE1BQU1FLE1BQU07SUFDckI7QUFDRjtBQUVPLGVBQWVKLGVBQ3BCUyxFQUF1QixFQUN2QkMsUUFBaUI7SUFFakIsTUFBTSxFQUFFQyxFQUFFLEVBQUVDLEtBQUssRUFBRSxHQUFHSDtJQUN0QixPQUFRRyxNQUFNQyxJQUFJO1FBQ2hCLEtBQUtDLFFBQUFBLHNCQUFzQjtRQUMzQixLQUFLQyxRQUFBQSwwQkFBMEI7WUFBRTtnQkFDL0IsTUFBTUMsWUFBWTtvQkFDaEJMO29CQUNBTSxTQUFTO29CQUNUZixPQUFPVSxNQUFNTSxNQUFNO2dCQUNyQjtnQkFFQSxJQUFJLFNBQVNmLE9BQUFBLE9BQUssRUFBRTtvQkFDbEIsTUFBTWdCLG9CQUF1Qzt3QkFDM0MsR0FBR0gsU0FBUzt3QkFDWiwrREFBK0Q7d0JBQy9EWixRQUFRZ0Isc0JBQXNCOzRCQUM1QixPQUFPLE1BQU1DLENBQUFBLEdBQUFBLFlBQUFBLHNCQUFBQSxFQUNYVCxNQUFNUixNQUFNLEVBQ1prQixDQUFBQSxHQUFBQSxhQUFBQSxjQUFBQSxFQUFlVixNQUFNTSxNQUFNLEdBQzNCUjt3QkFFSjtvQkFDRjtvQkFDQSxJQUFJRSxNQUFNQyxJQUFJLEtBQUtDLFFBQUFBLHNCQUFzQixFQUFFO3dCQUN6Q0ssa0JBQWtCSSxvQkFBb0IsR0FBR1gsTUFBTVcsb0JBQW9CO29CQUNyRTtvQkFDQSxPQUFPSjtnQkFDVCxPQUFPO29CQUNMLE1BQU1BLG9CQUF1Qzt3QkFDM0MsR0FBR0gsU0FBUzt3QkFDWiwrREFBK0Q7d0JBQy9EWixRQUFRLE1BQU1pQixDQUFBQSxHQUFBQSxZQUFBQSxzQkFBQUEsRUFDWlQsTUFBTVIsTUFBTSxFQUNaa0IsQ0FBQUEsR0FBQUEsYUFBQUEsY0FBQUEsRUFBZVYsTUFBTU0sTUFBTSxHQUMzQlI7b0JBRUo7b0JBQ0EsSUFBSUUsTUFBTUMsSUFBSSxLQUFLQyxRQUFBQSxzQkFBc0IsRUFBRTt3QkFDekNLLGtCQUFrQkksb0JBQW9CLEdBQUdYLE1BQU1XLG9CQUFvQjtvQkFDckU7b0JBQ0EsT0FBT0o7Z0JBQ1Q7WUFDRjtRQUNBO1lBQVM7Z0JBQ1A7WUFDRjtJQUNGO0lBQ0EsNkRBQTZEO0lBQzdELE1BQU1LLElBQVdaO0lBQ2pCLE1BQU0scUJBQTRDLENBQTVDLElBQUlQLE1BQU0sb0NBQVY7ZUFBQTtvQkFBQTtzQkFBQTtJQUEyQztBQUNuRDtBQUVBLFNBQVNlLHNCQUNQSyxjQUFnQztJQUVoQyxNQUFNQyxnQkFBZ0JEO0lBQ3RCLE9BQU87UUFDTCxPQUFPQztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xccmVhY3QtZGV2LW92ZXJsYXlcXHV0aWxzXFxnZXQtZXJyb3ItYnktdHlwZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBQ1RJT05fVU5IQU5ETEVEX0VSUk9SLCBBQ1RJT05fVU5IQU5ETEVEX1JFSkVDVElPTiB9IGZyb20gJy4uL3NoYXJlZCdcbmltcG9ydCB0eXBlIHsgU3VwcG9ydGVkRXJyb3JFdmVudCB9IGZyb20gJy4uL3VpL2NvbnRhaW5lci9ydW50aW1lLWVycm9yL3JlbmRlci1lcnJvcidcbmltcG9ydCB7IGdldE9yaWdpbmFsU3RhY2tGcmFtZXMgfSBmcm9tICcuL3N0YWNrLWZyYW1lJ1xuaW1wb3J0IHR5cGUgeyBPcmlnaW5hbFN0YWNrRnJhbWUgfSBmcm9tICcuL3N0YWNrLWZyYW1lJ1xuaW1wb3J0IHR5cGUgeyBDb21wb25lbnRTdGFja0ZyYW1lIH0gZnJvbSAnLi9wYXJzZS1jb21wb25lbnQtc3RhY2snXG5pbXBvcnQgeyBnZXRFcnJvclNvdXJjZSB9IGZyb20gJy4uLy4uLy4uLy4uL3NoYXJlZC9saWIvZXJyb3Itc291cmNlJ1xuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuXG5leHBvcnQgdHlwZSBSZWFkeVJ1bnRpbWVFcnJvciA9IHtcbiAgaWQ6IG51bWJlclxuICBydW50aW1lOiB0cnVlXG4gIGVycm9yOiBFcnJvciAmIHsgZW52aXJvbm1lbnROYW1lPzogc3RyaW5nIH1cbiAgZnJhbWVzOiBPcmlnaW5hbFN0YWNrRnJhbWVbXSB8ICgoKSA9PiBQcm9taXNlPE9yaWdpbmFsU3RhY2tGcmFtZVtdPilcbiAgY29tcG9uZW50U3RhY2tGcmFtZXM/OiBDb21wb25lbnRTdGFja0ZyYW1lW11cbn1cblxuZXhwb3J0IGNvbnN0IHVzZUZyYW1lcyA9IChlcnJvcjogUmVhZHlSdW50aW1lRXJyb3IpOiBPcmlnaW5hbFN0YWNrRnJhbWVbXSA9PiB7XG4gIGlmICgndXNlJyBpbiBSZWFjdCkge1xuICAgIGNvbnN0IGZyYW1lcyA9IGVycm9yLmZyYW1lc1xuXG4gICAgaWYgKHR5cGVvZiBmcmFtZXMgIT09ICdmdW5jdGlvbicpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgJ0ludmFyaWFudDogZnJhbWVzIG11c3QgYmUgYSBmdW5jdGlvbiB3aGVuIHRoZSBSZWFjdCB2ZXJzaW9uIGhhcyBSZWFjdC51c2UuIFRoaXMgaXMgYSBidWcgaW4gTmV4dC5qcy4nXG4gICAgICApXG4gICAgfVxuXG4gICAgcmV0dXJuIFJlYWN0LnVzZSgoZnJhbWVzIGFzICgpID0+IFByb21pc2U8T3JpZ2luYWxTdGFja0ZyYW1lW10+KSgpKVxuICB9IGVsc2Uge1xuICAgIGlmICghQXJyYXkuaXNBcnJheShlcnJvci5mcmFtZXMpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICdJbnZhcmlhbnQ6IGZyYW1lcyBtdXN0IGJlIGFuIGFycmF5IHdoZW4gdGhlIFJlYWN0IHZlcnNpb24gZG9lcyBub3QgaGF2ZSBSZWFjdC51c2UuIFRoaXMgaXMgYSBidWcgaW4gTmV4dC5qcy4nXG4gICAgICApXG4gICAgfVxuXG4gICAgcmV0dXJuIGVycm9yLmZyYW1lc1xuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRFcnJvckJ5VHlwZShcbiAgZXY6IFN1cHBvcnRlZEVycm9yRXZlbnQsXG4gIGlzQXBwRGlyOiBib29sZWFuXG4pOiBQcm9taXNlPFJlYWR5UnVudGltZUVycm9yPiB7XG4gIGNvbnN0IHsgaWQsIGV2ZW50IH0gPSBldlxuICBzd2l0Y2ggKGV2ZW50LnR5cGUpIHtcbiAgICBjYXNlIEFDVElPTl9VTkhBTkRMRURfRVJST1I6XG4gICAgY2FzZSBBQ1RJT05fVU5IQU5ETEVEX1JFSkVDVElPTjoge1xuICAgICAgY29uc3QgYmFzZUVycm9yID0ge1xuICAgICAgICBpZCxcbiAgICAgICAgcnVudGltZTogdHJ1ZSxcbiAgICAgICAgZXJyb3I6IGV2ZW50LnJlYXNvbixcbiAgICAgIH0gYXMgY29uc3RcblxuICAgICAgaWYgKCd1c2UnIGluIFJlYWN0KSB7XG4gICAgICAgIGNvbnN0IHJlYWR5UnVudGltZUVycm9yOiBSZWFkeVJ1bnRpbWVFcnJvciA9IHtcbiAgICAgICAgICAuLi5iYXNlRXJyb3IsXG4gICAgICAgICAgLy8gY3JlYXRlTWVtb2l6ZWRQcm9taXNlIGRlZHVwcyBjYWxscyB0byBnZXRPcmlnaW5hbFN0YWNrRnJhbWVzXG4gICAgICAgICAgZnJhbWVzOiBjcmVhdGVNZW1vaXplZFByb21pc2UoYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIGF3YWl0IGdldE9yaWdpbmFsU3RhY2tGcmFtZXMoXG4gICAgICAgICAgICAgIGV2ZW50LmZyYW1lcyxcbiAgICAgICAgICAgICAgZ2V0RXJyb3JTb3VyY2UoZXZlbnQucmVhc29uKSxcbiAgICAgICAgICAgICAgaXNBcHBEaXJcbiAgICAgICAgICAgIClcbiAgICAgICAgICB9KSxcbiAgICAgICAgfVxuICAgICAgICBpZiAoZXZlbnQudHlwZSA9PT0gQUNUSU9OX1VOSEFORExFRF9FUlJPUikge1xuICAgICAgICAgIHJlYWR5UnVudGltZUVycm9yLmNvbXBvbmVudFN0YWNrRnJhbWVzID0gZXZlbnQuY29tcG9uZW50U3RhY2tGcmFtZXNcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVhZHlSdW50aW1lRXJyb3JcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IHJlYWR5UnVudGltZUVycm9yOiBSZWFkeVJ1bnRpbWVFcnJvciA9IHtcbiAgICAgICAgICAuLi5iYXNlRXJyb3IsXG4gICAgICAgICAgLy8gY3JlYXRlTWVtb2l6ZWRQcm9taXNlIGRlZHVwcyBjYWxscyB0byBnZXRPcmlnaW5hbFN0YWNrRnJhbWVzXG4gICAgICAgICAgZnJhbWVzOiBhd2FpdCBnZXRPcmlnaW5hbFN0YWNrRnJhbWVzKFxuICAgICAgICAgICAgZXZlbnQuZnJhbWVzLFxuICAgICAgICAgICAgZ2V0RXJyb3JTb3VyY2UoZXZlbnQucmVhc29uKSxcbiAgICAgICAgICAgIGlzQXBwRGlyXG4gICAgICAgICAgKSxcbiAgICAgICAgfVxuICAgICAgICBpZiAoZXZlbnQudHlwZSA9PT0gQUNUSU9OX1VOSEFORExFRF9FUlJPUikge1xuICAgICAgICAgIHJlYWR5UnVudGltZUVycm9yLmNvbXBvbmVudFN0YWNrRnJhbWVzID0gZXZlbnQuY29tcG9uZW50U3RhY2tGcmFtZXNcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVhZHlSdW50aW1lRXJyb3JcbiAgICAgIH1cbiAgICB9XG4gICAgZGVmYXVsdDoge1xuICAgICAgYnJlYWtcbiAgICB9XG4gIH1cbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby11bnVzZWQtdmFyc1xuICBjb25zdCBfOiBuZXZlciA9IGV2ZW50XG4gIHRocm93IG5ldyBFcnJvcigndHlwZSBzeXN0ZW0gaW52YXJpYW50IHZpb2xhdGlvbicpXG59XG5cbmZ1bmN0aW9uIGNyZWF0ZU1lbW9pemVkUHJvbWlzZTxUPihcbiAgcHJvbWlzZUZhY3Rvcnk6ICgpID0+IFByb21pc2U8VD5cbik6ICgpID0+IFByb21pc2U8VD4ge1xuICBjb25zdCBjYWNoZWRQcm9taXNlID0gcHJvbWlzZUZhY3RvcnkoKVxuICByZXR1cm4gZnVuY3Rpb24gKCk6IFByb21pc2U8VD4ge1xuICAgIHJldHVybiBjYWNoZWRQcm9taXNlXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJnZXRFcnJvckJ5VHlwZSIsInVzZUZyYW1lcyIsImVycm9yIiwiUmVhY3QiLCJmcmFtZXMiLCJFcnJvciIsInVzZSIsIkFycmF5IiwiaXNBcnJheSIsImV2IiwiaXNBcHBEaXIiLCJpZCIsImV2ZW50IiwidHlwZSIsIkFDVElPTl9VTkhBTkRMRURfRVJST1IiLCJBQ1RJT05fVU5IQU5ETEVEX1JFSkVDVElPTiIsImJhc2VFcnJvciIsInJ1bnRpbWUiLCJyZWFzb24iLCJyZWFkeVJ1bnRpbWVFcnJvciIsImNyZWF0ZU1lbW9pemVkUHJvbWlzZSIsImdldE9yaWdpbmFsU3RhY2tGcmFtZXMiLCJnZXRFcnJvclNvdXJjZSIsImNvbXBvbmVudFN0YWNrRnJhbWVzIiwiXyIsInByb21pc2VGYWN0b3J5IiwiY2FjaGVkUHJvbWlzZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-error-by-type.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-socket-url.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/get-socket-url.js ***!
  \********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getSocketUrl\", ({\n    enumerable: true,\n    get: function() {\n        return getSocketUrl;\n    }\n}));\nconst _normalizedassetprefix = __webpack_require__(/*! ../../../../shared/lib/normalized-asset-prefix */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/normalized-asset-prefix.js\");\nfunction getSocketProtocol(assetPrefix) {\n    let protocol = window.location.protocol;\n    try {\n        // assetPrefix is a url\n        protocol = new URL(assetPrefix).protocol;\n    } catch (e) {}\n    return protocol === 'http:' ? 'ws:' : 'wss:';\n}\nfunction getSocketUrl(assetPrefix) {\n    const prefix = (0, _normalizedassetprefix.normalizedAssetPrefix)(assetPrefix);\n    const protocol = getSocketProtocol(assetPrefix || '');\n    if (URL.canParse(prefix)) {\n        // since normalized asset prefix is ensured to be a URL format,\n        // we can safely replace the protocol\n        return prefix.replace(/^http/, 'ws');\n    }\n    const { hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? \":\" + port : '') + prefix;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-socket-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/get-socket-url.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/node-stack-frames.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/node-stack-frames.js ***!
  \***********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getFilesystemFrame: function() {\n        return getFilesystemFrame;\n    },\n    getServerError: function() {\n        return getServerError;\n    }\n});\nconst _stacktraceparser = __webpack_require__(/*! next/dist/compiled/stacktrace-parser */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/stacktrace-parser/stack-trace-parser.cjs.js\");\nconst _errorsource = __webpack_require__(/*! ../../../../shared/lib/error-source */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/error-source.js\");\nfunction getFilesystemFrame(frame) {\n    const f = {\n        ...frame\n    };\n    if (typeof f.file === 'string') {\n        if (f.file.startsWith('/') || // Win32:\n        /^[a-z]:\\\\/i.test(f.file) || // Win32 UNC:\n        f.file.startsWith('\\\\\\\\')) {\n            f.file = \"file://\" + f.file;\n        }\n    }\n    return f;\n}\nfunction getServerError(error, type) {\n    if (error.name === 'TurbopackInternalError') {\n        // If this is an internal Turbopack error we shouldn't show internal details\n        // to the user. These are written to a log file instead.\n        const turbopackInternalError = Object.defineProperty(new Error('An unexpected Turbopack error occurred. Please see the output of `next dev` for more details.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E167\",\n            enumerable: false,\n            configurable: true\n        });\n        (0, _errorsource.decorateServerError)(turbopackInternalError, type);\n        return turbopackInternalError;\n    }\n    let n;\n    try {\n        throw Object.defineProperty(new Error(error.message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    } catch (e) {\n        n = e;\n    }\n    n.name = error.name;\n    try {\n        n.stack = n.toString() + \"\\n\" + (0, _stacktraceparser.parse)(error.stack).map(getFilesystemFrame).map((f)=>{\n            let str = \"    at \" + f.methodName;\n            if (f.file) {\n                let loc = f.file;\n                if (f.lineNumber) {\n                    loc += \":\" + f.lineNumber;\n                    if (f.column) {\n                        loc += \":\" + f.column;\n                    }\n                }\n                str += \" (\" + loc + \")\";\n            }\n            return str;\n        }).join('\\n');\n    } catch (e) {\n        n.stack = error.stack;\n    }\n    (0, _errorsource.decorateServerError)(n, type);\n    return n;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=node-stack-frames.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/node-stack-frames.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-component-stack.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-component-stack.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"parseComponentStack\", ({\n    enumerable: true,\n    get: function() {\n        return parseComponentStack;\n    }\n}));\nvar LocationType = /*#__PURE__*/ function(LocationType) {\n    LocationType[\"FILE\"] = \"file\";\n    LocationType[\"WEBPACK_INTERNAL\"] = \"webpack-internal\";\n    LocationType[\"HTTP\"] = \"http\";\n    LocationType[\"PROTOCOL_RELATIVE\"] = \"protocol-relative\";\n    LocationType[\"UNKNOWN\"] = \"unknown\";\n    return LocationType;\n}(LocationType || {});\n/**\n * Get the type of frame line based on the location\n */ function getLocationType(location) {\n    if (location.startsWith('file://')) {\n        return \"file\";\n    }\n    if (location.includes('webpack-internal://')) {\n        return \"webpack-internal\";\n    }\n    if (location.startsWith('http://') || location.startsWith('https://')) {\n        return \"http\";\n    }\n    if (location.startsWith('//')) {\n        return \"protocol-relative\";\n    }\n    return \"unknown\";\n}\nfunction parseStackFrameLocation(location) {\n    const locationType = getLocationType(location);\n    const modulePath = location == null ? void 0 : location.replace(/^(webpack-internal:\\/\\/\\/|file:\\/\\/)(\\(.*\\)\\/)?/, '');\n    var _modulePath_match;\n    const [, file, lineNumber, column] = (_modulePath_match = modulePath == null ? void 0 : modulePath.match(/^(.+):(\\d+):(\\d+)/)) != null ? _modulePath_match : [];\n    switch(locationType){\n        case \"file\":\n        case \"webpack-internal\":\n            return {\n                canOpenInEditor: true,\n                file,\n                lineNumber: lineNumber ? Number(lineNumber) : undefined,\n                column: column ? Number(column) : undefined\n            };\n        // When the location is a URL we only show the file\n        // TODO: Resolve http(s) URLs through sourcemaps\n        case \"http\":\n        case \"protocol-relative\":\n        case \"unknown\":\n        default:\n            {\n                return {\n                    canOpenInEditor: false\n                };\n            }\n    }\n}\nfunction parseComponentStack(componentStack) {\n    const componentStackFrames = [];\n    for (const line of componentStack.trim().split('\\n')){\n        // TODO: support safari stack trace\n        // Get component and file from the component stack line\n        const match = /at ([^ ]+)( \\((.*)\\))?/.exec(line);\n        if (match == null ? void 0 : match[1]) {\n            const component = match[1];\n            const location = match[3];\n            if (!location) {\n                componentStackFrames.push({\n                    canOpenInEditor: false,\n                    component\n                });\n                continue;\n            }\n            // Stop parsing the component stack if we reach a Next.js component\n            if (location == null ? void 0 : location.includes('next/dist')) {\n                break;\n            }\n            const frameLocation = parseStackFrameLocation(location);\n            componentStackFrames.push({\n                component,\n                ...frameLocation\n            });\n        }\n    }\n    return componentStackFrames;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=parse-component-stack.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-component-stack.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-stack.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-stack.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"parseStack\", ({\n    enumerable: true,\n    get: function() {\n        return parseStack;\n    }\n}));\nconst _stacktraceparser = __webpack_require__(/*! next/dist/compiled/stacktrace-parser */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/stacktrace-parser/stack-trace-parser.cjs.js\");\nconst _ishydrationerror = __webpack_require__(/*! ../../is-hydration-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\");\nconst regexNextStatic = /\\/_next(\\/static\\/.+)/;\nfunction parseStack(stack) {\n    if (!stack) return [];\n    const messageAndStack = stack.replace(/^Error: /, '');\n    if ((0, _ishydrationerror.isReactHydrationErrorMessage)(messageAndStack)) {\n        const { stack: parsedStack } = (0, _ishydrationerror.getHydrationErrorStackInfo)(messageAndStack);\n        if (parsedStack) {\n            stack = parsedStack;\n        }\n    }\n    // throw away eval information that stacktrace-parser doesn't support\n    // adapted from https://github.com/stacktracejs/error-stack-parser/blob/9f33c224b5d7b607755eb277f9d51fcdb7287e24/error-stack-parser.js#L59C33-L59C62\n    stack = stack.split('\\n').map((line)=>{\n        if (line.includes('(eval ')) {\n            line = line.replace(/eval code/g, 'eval').replace(/\\(eval at [^()]* \\(/, '(file://').replace(/\\),.*$/g, ')');\n        }\n        return line;\n    }).join('\\n');\n    const frames = (0, _stacktraceparser.parse)(stack);\n    return frames.map((frame)=>{\n        try {\n            const url = new URL(frame.file);\n            const res = regexNextStatic.exec(url.pathname);\n            if (res) {\n                var _process_env___NEXT_DIST_DIR_replace, _process_env___NEXT_DIST_DIR;\n                const distDir = (_process_env___NEXT_DIST_DIR = \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\.next\") == null ? void 0 : (_process_env___NEXT_DIST_DIR_replace = _process_env___NEXT_DIST_DIR.replace(/\\\\/g, '/')) == null ? void 0 : _process_env___NEXT_DIST_DIR_replace.replace(/\\/$/, '');\n                if (distDir) {\n                    frame.file = 'file://' + distDir.concat(res.pop()) + url.search;\n                }\n            }\n        } catch (e) {}\n        return frame;\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=parse-stack.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/parse-stack.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/stack-frame.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/stack-frame.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    getFrameSource: function() {\n        return getFrameSource;\n    },\n    getOriginalStackFrames: function() {\n        return getOriginalStackFrames;\n    }\n});\nconst _webpackmodulepath = __webpack_require__(/*! ./webpack-module-path */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/webpack-module-path.js\");\nfunction getOriginalStackFrame(source, response) {\n    var _source_file;\n    async function _getOriginalStackFrame() {\n        var _body_originalStackFrame;\n        if (response.status === 'rejected') {\n            throw Object.defineProperty(new Error(response.reason), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        const body = response.value;\n        return {\n            error: false,\n            reason: null,\n            external: false,\n            sourceStackFrame: source,\n            originalStackFrame: body.originalStackFrame,\n            originalCodeFrame: body.originalCodeFrame || null,\n            ignored: ((_body_originalStackFrame = body.originalStackFrame) == null ? void 0 : _body_originalStackFrame.ignored) || false\n        };\n    }\n    // TODO: merge this section into ignoredList handling\n    if (source.file === 'file://' || ((_source_file = source.file) == null ? void 0 : _source_file.match(/https?:\\/\\//))) {\n        return Promise.resolve({\n            error: false,\n            reason: null,\n            external: true,\n            sourceStackFrame: source,\n            originalStackFrame: null,\n            originalCodeFrame: null,\n            ignored: true\n        });\n    }\n    return _getOriginalStackFrame().catch((err)=>{\n        var _err_message, _ref;\n        return {\n            error: true,\n            reason: (_ref = (_err_message = err == null ? void 0 : err.message) != null ? _err_message : err == null ? void 0 : err.toString()) != null ? _ref : 'Unknown Error',\n            external: false,\n            sourceStackFrame: source,\n            originalStackFrame: null,\n            originalCodeFrame: null,\n            ignored: false\n        };\n    });\n}\nasync function getOriginalStackFrames(frames, type, isAppDir) {\n    const req = {\n        frames,\n        isServer: type === 'server',\n        isEdgeServer: type === 'edge-server',\n        isAppDirectory: isAppDir\n    };\n    let res = undefined;\n    let reason = undefined;\n    try {\n        res = await fetch('/__nextjs_original-stack-frames', {\n            method: 'POST',\n            body: JSON.stringify(req)\n        });\n    } catch (e) {\n        reason = e + '';\n    }\n    // When fails to fetch the original stack frames, we reject here to be\n    // caught at `_getOriginalStackFrame()` and return the stack frames so\n    // that the error overlay can render.\n    if (res && res.ok && res.status !== 204) {\n        const data = await res.json();\n        return Promise.all(frames.map((frame, index)=>getOriginalStackFrame(frame, data[index])));\n    } else {\n        if (res) {\n            reason = await res.text();\n        }\n    }\n    return Promise.all(frames.map((frame)=>getOriginalStackFrame(frame, {\n            status: 'rejected',\n            reason: \"Failed to fetch the original stack frames \" + (reason ? \": \" + reason : '')\n        })));\n}\nfunction getFrameSource(frame) {\n    if (!frame.file) return '';\n    const isWebpackFrame = (0, _webpackmodulepath.isWebpackInternalResource)(frame.file);\n    let str = '';\n    // Skip URL parsing for webpack internal file paths.\n    if (isWebpackFrame) {\n        str = (0, _webpackmodulepath.formatFrameSourceFile)(frame.file);\n    } else {\n        try {\n            var _globalThis_location;\n            const u = new URL(frame.file);\n            let parsedPath = '';\n            // Strip the origin for same-origin scripts.\n            if (((_globalThis_location = globalThis.location) == null ? void 0 : _globalThis_location.origin) !== u.origin) {\n                // URLs can be valid without an `origin`, so long as they have a\n                // `protocol`. However, `origin` is preferred.\n                if (u.origin === 'null') {\n                    parsedPath += u.protocol;\n                } else {\n                    parsedPath += u.origin;\n                }\n            }\n            // Strip query string information as it's typically too verbose to be\n            // meaningful.\n            parsedPath += u.pathname;\n            str = (0, _webpackmodulepath.formatFrameSourceFile)(parsedPath);\n        } catch (e) {\n            str = (0, _webpackmodulepath.formatFrameSourceFile)(frame.file);\n        }\n    }\n    if (!(0, _webpackmodulepath.isWebpackInternalResource)(frame.file) && frame.lineNumber != null) {\n        if (str) {\n            if (frame.column != null) {\n                str += \" (\" + frame.lineNumber + \":\" + frame.column + \")\";\n            } else {\n                str += \" (\" + frame.lineNumber + \")\";\n            }\n        }\n    }\n    return str;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=stack-frame.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/stack-frame.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/webpack-module-path.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/utils/webpack-module-path.js ***!
  \*************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatFrameSourceFile: function() {\n        return formatFrameSourceFile;\n    },\n    isWebpackInternalResource: function() {\n        return isWebpackInternalResource;\n    }\n});\nconst replacementRegExes = [\n    /^webpack-internal:\\/\\/\\/(\\([\\w-]+\\)\\/)?/,\n    /^(webpack:\\/\\/\\/|webpack:\\/\\/(_N_E\\/)?)(\\([\\w-]+\\)\\/)?/\n];\nfunction isWebpackInternalResource(file) {\n    for (const regex of replacementRegExes){\n        if (regex.test(file)) return true;\n        file = file.replace(regex, '');\n    }\n    return false;\n}\nfunction formatFrameSourceFile(file) {\n    for (const regex of replacementRegExes){\n        file = file.replace(regex, '');\n    }\n    return file;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=webpack-module-path.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/webpack-module-path.js\n"));

/***/ })

}]);