"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_react-dev-overlay_shared_js-0"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/shared.js ***!
  \******************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_BEFORE_REFRESH: function() {\n        return ACTION_BEFORE_REFRESH;\n    },\n    ACTION_BUILD_ERROR: function() {\n        return ACTION_BUILD_ERROR;\n    },\n    ACTION_BUILD_OK: function() {\n        return ACTION_BUILD_OK;\n    },\n    ACTION_DEBUG_INFO: function() {\n        return ACTION_DEBUG_INFO;\n    },\n    ACTION_DEV_INDICATOR: function() {\n        return ACTION_DEV_INDICATOR;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_STATIC_INDICATOR: function() {\n        return ACTION_STATIC_INDICATOR;\n    },\n    ACTION_UNHANDLED_ERROR: function() {\n        return ACTION_UNHANDLED_ERROR;\n    },\n    ACTION_UNHANDLED_REJECTION: function() {\n        return ACTION_UNHANDLED_REJECTION;\n    },\n    ACTION_VERSION_INFO: function() {\n        return ACTION_VERSION_INFO;\n    },\n    INITIAL_OVERLAY_STATE: function() {\n        return INITIAL_OVERLAY_STATE;\n    },\n    REACT_REFRESH_FULL_RELOAD_FROM_ERROR: function() {\n        return REACT_REFRESH_FULL_RELOAD_FROM_ERROR;\n    },\n    STORAGE_KEY_POSITION: function() {\n        return STORAGE_KEY_POSITION;\n    },\n    STORAGE_KEY_THEME: function() {\n        return STORAGE_KEY_THEME;\n    },\n    useErrorOverlayReducer: function() {\n        return useErrorOverlayReducer;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nvar _process_env___NEXT_DEV_INDICATOR;\nconst ACTION_STATIC_INDICATOR = 'static-indicator';\nconst ACTION_BUILD_OK = 'build-ok';\nconst ACTION_BUILD_ERROR = 'build-error';\nconst ACTION_BEFORE_REFRESH = 'before-fast-refresh';\nconst ACTION_REFRESH = 'fast-refresh';\nconst ACTION_VERSION_INFO = 'version-info';\nconst ACTION_UNHANDLED_ERROR = 'unhandled-error';\nconst ACTION_UNHANDLED_REJECTION = 'unhandled-rejection';\nconst ACTION_DEBUG_INFO = 'debug-info';\nconst ACTION_DEV_INDICATOR = 'dev-indicator';\nconst STORAGE_KEY_THEME = '__nextjs-dev-tools-theme';\nconst STORAGE_KEY_POSITION = '__nextjs-dev-tools-position';\nfunction pushErrorFilterDuplicates(errors, err) {\n    return [\n        ...errors.filter((e)=>{\n            // Filter out duplicate errors\n            return e.event.reason.stack !== err.event.reason.stack;\n        }),\n        err\n    ];\n}\nconst shouldDisableDevIndicator = ((_process_env___NEXT_DEV_INDICATOR = true) == null ? void 0 : _process_env___NEXT_DEV_INDICATOR.toString()) === 'false';\nconst INITIAL_OVERLAY_STATE = {\n    nextId: 1,\n    buildError: null,\n    errors: [],\n    notFound: false,\n    staticIndicator: false,\n    // To prevent flickering, set the initial state to disabled.\n    disableDevIndicator: true,\n    refreshState: {\n        type: 'idle'\n    },\n    rootLayoutMissingTags: [],\n    versionInfo: {\n        installed: '0.0.0',\n        staleness: 'unknown'\n    },\n    debugInfo: {\n        devtoolsFrontendUrl: undefined\n    }\n};\nfunction getInitialState(routerType) {\n    return {\n        ...INITIAL_OVERLAY_STATE,\n        routerType\n    };\n}\nfunction useErrorOverlayReducer(routerType) {\n    return (0, _react.useReducer)((_state, action)=>{\n        switch(action.type){\n            case ACTION_DEBUG_INFO:\n                {\n                    return {\n                        ..._state,\n                        debugInfo: action.debugInfo\n                    };\n                }\n            case ACTION_STATIC_INDICATOR:\n                {\n                    return {\n                        ..._state,\n                        staticIndicator: action.staticIndicator\n                    };\n                }\n            case ACTION_BUILD_OK:\n                {\n                    return {\n                        ..._state,\n                        buildError: null\n                    };\n                }\n            case ACTION_BUILD_ERROR:\n                {\n                    return {\n                        ..._state,\n                        buildError: action.message\n                    };\n                }\n            case ACTION_BEFORE_REFRESH:\n                {\n                    return {\n                        ..._state,\n                        refreshState: {\n                            type: 'pending',\n                            errors: []\n                        }\n                    };\n                }\n            case ACTION_REFRESH:\n                {\n                    return {\n                        ..._state,\n                        buildError: null,\n                        errors: // and UNHANDLED_REJECTION events might be dispatched between the\n                        // BEFORE_REFRESH and the REFRESH event. We want to keep those errors\n                        // around until the next refresh. Otherwise we run into a race\n                        // condition where those errors would be cleared on refresh completion\n                        // before they can be displayed.\n                        _state.refreshState.type === 'pending' ? _state.refreshState.errors : [],\n                        refreshState: {\n                            type: 'idle'\n                        }\n                    };\n                }\n            case ACTION_UNHANDLED_ERROR:\n            case ACTION_UNHANDLED_REJECTION:\n                {\n                    switch(_state.refreshState.type){\n                        case 'idle':\n                            {\n                                return {\n                                    ..._state,\n                                    nextId: _state.nextId + 1,\n                                    errors: pushErrorFilterDuplicates(_state.errors, {\n                                        id: _state.nextId,\n                                        event: action\n                                    })\n                                };\n                            }\n                        case 'pending':\n                            {\n                                return {\n                                    ..._state,\n                                    nextId: _state.nextId + 1,\n                                    refreshState: {\n                                        ..._state.refreshState,\n                                        errors: pushErrorFilterDuplicates(_state.refreshState.errors, {\n                                            id: _state.nextId,\n                                            event: action\n                                        })\n                                    }\n                                };\n                            }\n                        default:\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const _ = _state.refreshState;\n                            return _state;\n                    }\n                }\n            case ACTION_VERSION_INFO:\n                {\n                    return {\n                        ..._state,\n                        versionInfo: action.versionInfo\n                    };\n                }\n            case ACTION_DEV_INDICATOR:\n                {\n                    return {\n                        ..._state,\n                        disableDevIndicator: shouldDisableDevIndicator || !!action.devIndicator.disabledUntil\n                    };\n                }\n            default:\n                {\n                    return _state;\n                }\n        }\n    }, getInitialState(routerType));\n}\nconst REACT_REFRESH_FULL_RELOAD_FROM_ERROR = '[Fast Refresh] performing full reload because your application had an unrecoverable error';\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=shared.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/shared.js ***!
  \******************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_BEFORE_REFRESH: function() {\n        return ACTION_BEFORE_REFRESH;\n    },\n    ACTION_BUILD_ERROR: function() {\n        return ACTION_BUILD_ERROR;\n    },\n    ACTION_BUILD_OK: function() {\n        return ACTION_BUILD_OK;\n    },\n    ACTION_DEBUG_INFO: function() {\n        return ACTION_DEBUG_INFO;\n    },\n    ACTION_DEV_INDICATOR: function() {\n        return ACTION_DEV_INDICATOR;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_STATIC_INDICATOR: function() {\n        return ACTION_STATIC_INDICATOR;\n    },\n    ACTION_UNHANDLED_ERROR: function() {\n        return ACTION_UNHANDLED_ERROR;\n    },\n    ACTION_UNHANDLED_REJECTION: function() {\n        return ACTION_UNHANDLED_REJECTION;\n    },\n    ACTION_VERSION_INFO: function() {\n        return ACTION_VERSION_INFO;\n    },\n    INITIAL_OVERLAY_STATE: function() {\n        return INITIAL_OVERLAY_STATE;\n    },\n    REACT_REFRESH_FULL_RELOAD_FROM_ERROR: function() {\n        return REACT_REFRESH_FULL_RELOAD_FROM_ERROR;\n    },\n    STORAGE_KEY_POSITION: function() {\n        return STORAGE_KEY_POSITION;\n    },\n    STORAGE_KEY_THEME: function() {\n        return STORAGE_KEY_THEME;\n    },\n    useErrorOverlayReducer: function() {\n        return useErrorOverlayReducer;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nvar _process_env___NEXT_DEV_INDICATOR;\nconst ACTION_STATIC_INDICATOR = 'static-indicator';\nconst ACTION_BUILD_OK = 'build-ok';\nconst ACTION_BUILD_ERROR = 'build-error';\nconst ACTION_BEFORE_REFRESH = 'before-fast-refresh';\nconst ACTION_REFRESH = 'fast-refresh';\nconst ACTION_VERSION_INFO = 'version-info';\nconst ACTION_UNHANDLED_ERROR = 'unhandled-error';\nconst ACTION_UNHANDLED_REJECTION = 'unhandled-rejection';\nconst ACTION_DEBUG_INFO = 'debug-info';\nconst ACTION_DEV_INDICATOR = 'dev-indicator';\nconst STORAGE_KEY_THEME = '__nextjs-dev-tools-theme';\nconst STORAGE_KEY_POSITION = '__nextjs-dev-tools-position';\nfunction pushErrorFilterDuplicates(errors, err) {\n    return [\n        ...errors.filter((e)=>{\n            // Filter out duplicate errors\n            return e.event.reason.stack !== err.event.reason.stack;\n        }),\n        err\n    ];\n}\nconst shouldDisableDevIndicator = ((_process_env___NEXT_DEV_INDICATOR = true) == null ? void 0 : _process_env___NEXT_DEV_INDICATOR.toString()) === 'false';\nconst INITIAL_OVERLAY_STATE = {\n    nextId: 1,\n    buildError: null,\n    errors: [],\n    notFound: false,\n    staticIndicator: false,\n    // To prevent flickering, set the initial state to disabled.\n    disableDevIndicator: true,\n    refreshState: {\n        type: 'idle'\n    },\n    rootLayoutMissingTags: [],\n    versionInfo: {\n        installed: '0.0.0',\n        staleness: 'unknown'\n    },\n    debugInfo: {\n        devtoolsFrontendUrl: undefined\n    }\n};\nfunction getInitialState(routerType) {\n    return {\n        ...INITIAL_OVERLAY_STATE,\n        routerType\n    };\n}\nfunction useErrorOverlayReducer(routerType) {\n    return (0, _react.useReducer)((_state, action)=>{\n        switch(action.type){\n            case ACTION_DEBUG_INFO:\n                {\n                    return {\n                        ..._state,\n                        debugInfo: action.debugInfo\n                    };\n                }\n            case ACTION_STATIC_INDICATOR:\n                {\n                    return {\n                        ..._state,\n                        staticIndicator: action.staticIndicator\n                    };\n                }\n            case ACTION_BUILD_OK:\n                {\n                    return {\n                        ..._state,\n                        buildError: null\n                    };\n                }\n            case ACTION_BUILD_ERROR:\n                {\n                    return {\n                        ..._state,\n                        buildError: action.message\n                    };\n                }\n            case ACTION_BEFORE_REFRESH:\n                {\n                    return {\n                        ..._state,\n                        refreshState: {\n                            type: 'pending',\n                            errors: []\n                        }\n                    };\n                }\n            case ACTION_REFRESH:\n                {\n                    return {\n                        ..._state,\n                        buildError: null,\n                        errors: // and UNHANDLED_REJECTION events might be dispatched between the\n                        // BEFORE_REFRESH and the REFRESH event. We want to keep those errors\n                        // around until the next refresh. Otherwise we run into a race\n                        // condition where those errors would be cleared on refresh completion\n                        // before they can be displayed.\n                        _state.refreshState.type === 'pending' ? _state.refreshState.errors : [],\n                        refreshState: {\n                            type: 'idle'\n                        }\n                    };\n                }\n            case ACTION_UNHANDLED_ERROR:\n            case ACTION_UNHANDLED_REJECTION:\n                {\n                    switch(_state.refreshState.type){\n                        case 'idle':\n                            {\n                                return {\n                                    ..._state,\n                                    nextId: _state.nextId + 1,\n                                    errors: pushErrorFilterDuplicates(_state.errors, {\n                                        id: _state.nextId,\n                                        event: action\n                                    })\n                                };\n                            }\n                        case 'pending':\n                            {\n                                return {\n                                    ..._state,\n                                    nextId: _state.nextId + 1,\n                                    refreshState: {\n                                        ..._state.refreshState,\n                                        errors: pushErrorFilterDuplicates(_state.refreshState.errors, {\n                                            id: _state.nextId,\n                                            event: action\n                                        })\n                                    }\n                                };\n                            }\n                        default:\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const _ = _state.refreshState;\n                            return _state;\n                    }\n                }\n            case ACTION_VERSION_INFO:\n                {\n                    return {\n                        ..._state,\n                        versionInfo: action.versionInfo\n                    };\n                }\n            case ACTION_DEV_INDICATOR:\n                {\n                    return {\n                        ..._state,\n                        disableDevIndicator: shouldDisableDevIndicator || !!action.devIndicator.disabledUntil\n                    };\n                }\n            default:\n                {\n                    return _state;\n                }\n        }\n    }, getInitialState(routerType));\n}\nconst REACT_REFRESH_FULL_RELOAD_FROM_ERROR = '[Fast Refresh] performing full reload because your application had an unrecoverable error';\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=shared.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\n"));

/***/ })

}]);