exports.id=204,exports.ids=[204],exports.modules={11055:(e,t,r)=>{"use strict";async function*o(e,t=!0){for(let r of e)if("stream"in r)yield*r.stream();else if(ArrayBuffer.isView(r)){if(t){let e=r.byteOffset,t=r.byteOffset+r.byteLength;for(;e!==t;){let o=Math.min(t-e,65536),n=r.buffer.slice(e,e+o);e+=n.byteLength,yield new Uint8Array(n)}}else yield r}else{let e=0;for(;e!==r.size;){let t=r.slice(e,Math.min(r.size,e+65536)),o=await t.arrayBuffer();e+=o.byteLength,yield new Uint8Array(o)}}}r.d(t,{A:()=>i}),r(21519);let n=class e{#e=[];#t="";#r=0;#o="transparent";constructor(t=[],r={}){if("object"!=typeof t||null===t)throw TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if("function"!=typeof t[Symbol.iterator])throw TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if("object"!=typeof r&&"function"!=typeof r)throw TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");null===r&&(r={});let o=new TextEncoder;for(let r of t){let t;t=ArrayBuffer.isView(r)?new Uint8Array(r.buffer.slice(r.byteOffset,r.byteOffset+r.byteLength)):r instanceof ArrayBuffer?new Uint8Array(r.slice(0)):r instanceof e?r:o.encode(`${r}`),this.#r+=ArrayBuffer.isView(t)?t.byteLength:t.size,this.#e.push(t)}this.#o=`${void 0===r.endings?"transparent":r.endings}`;let n=void 0===r.type?"":String(r.type);this.#t=/^[\x20-\x7E]*$/.test(n)?n:""}get size(){return this.#r}get type(){return this.#t}async text(){let e=new TextDecoder,t="";for await(let r of o(this.#e,!1))t+=e.decode(r,{stream:!0});return t+e.decode()}async arrayBuffer(){let e=new Uint8Array(this.size),t=0;for await(let r of o(this.#e,!1))e.set(r,t),t+=r.length;return e.buffer}stream(){let e=o(this.#e,!0);return new globalThis.ReadableStream({type:"bytes",async pull(t){let r=await e.next();r.done?t.close():t.enqueue(r.value)},async cancel(){await e.return()}})}slice(t=0,r=this.size,o=""){let{size:n}=this,i=t<0?Math.max(n+t,0):Math.min(t,n),a=r<0?Math.max(n+r,0):Math.min(r,n),l=Math.max(a-i,0),s=this.#e,u=[],c=0;for(let e of s){if(c>=l)break;let t=ArrayBuffer.isView(e)?e.byteLength:e.size;if(i&&t<=i)i-=t,a-=t;else{let r;ArrayBuffer.isView(e)?c+=(r=e.subarray(i,Math.min(t,a))).byteLength:c+=(r=e.slice(i,Math.min(t,a))).size,a-=t,u.push(r),i=0}}let d=new e([],{type:String(o).toLowerCase()});return d.#r=l,d.#e=u,d}get[Symbol.toStringTag](){return"Blob"}static[Symbol.hasInstance](e){return e&&"object"==typeof e&&"function"==typeof e.constructor&&("function"==typeof e.stream||"function"==typeof e.arrayBuffer)&&/^(Blob|File)$/.test(e[Symbol.toStringTag])}};Object.defineProperties(n.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}});let i=n},21519:(e,t,r)=>{if(!globalThis.ReadableStream)try{let e=r(1708),{emitWarning:t}=e;try{e.emitWarning=()=>{},Object.assign(globalThis,r(37830)),e.emitWarning=t}catch(r){throw e.emitWarning=t,r}}catch(e){Object.assign(globalThis,r(33998))}try{let{Blob:e}=r(79428);e&&!e.prototype.stream&&(e.prototype.stream=function(e){let t=0,r=this;return new ReadableStream({type:"bytes",async pull(e){let o=r.slice(t,Math.min(r.size,t+65536)),n=await o.arrayBuffer();t+=n.byteLength,e.enqueue(new Uint8Array(n)),t===r.size&&e.close()}})})}catch(e){}},33998:function(e,t){(function(e){"use strict";var t,r,o;function n(){}function i(e){return"object"==typeof e&&null!==e||"function"==typeof e}function a(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch(e){}}let l=Promise,s=Promise.prototype.then,u=Promise.reject.bind(l);function c(e){return new l(e)}function d(e){return c(t=>t(e))}function f(e,t,r){return s.call(e,t,r)}function h(e,t,r){f(f(e,t,r),void 0,n)}function p(e,t){h(e,void 0,t)}function b(e){f(e,void 0,n)}let y=e=>{if("function"==typeof queueMicrotask)y=queueMicrotask;else{let e=d(void 0);y=t=>f(e,t)}return y(e)};function m(e,t,r){if("function"!=typeof e)throw TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function _(e,t,r){try{return d(m(e,t,r))}catch(e){return u(e)}}class g{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){let t=this._back,r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){let e=this._front,t=e,r=this._cursor,o=r+1,n=e._elements,i=n[r];return 16384===o&&(t=e._next,o=0),--this._size,this._cursor=o,e!==t&&(this._front=t),n[r]=void 0,i}forEach(e){let t=this._cursor,r=this._front,o=r._elements;for(;(t!==o.length||void 0!==r._next)&&(t!==o.length||(o=(r=r._next)._elements,t=0,0!==o.length));)e(o[t]),++t}peek(){let e=this._front,t=this._cursor;return e._elements[t]}}let v=Symbol("[[AbortSteps]]"),S=Symbol("[[ErrorSteps]]"),w=Symbol("[[CancelSteps]]"),T=Symbol("[[PullSteps]]"),R=Symbol("[[ReleaseSteps]]");function P(e,t){var r,o,n;e._ownerReadableStream=t,t._reader=e,"readable"===t._state?A(e):"closed"===t._state?(A(r=e),j(r)):(o=e,n=t._storedError,A(o),k(o,n))}function E(e,t){return t3(e._ownerReadableStream,t)}function C(e){let t=e._ownerReadableStream;"readable"===t._state?k(e,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,t){A(e),k(e,t)}(e,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),t._readableStreamController[R](),t._reader=void 0,e._ownerReadableStream=void 0}function q(e){return TypeError("Cannot "+e+" a stream using a released reader")}function A(e){e._closedPromise=c((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r})}function k(e,t){void 0!==e._closedPromise_reject&&(b(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function j(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}let O=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},B=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function W(e,t){if(void 0!==e&&"object"!=typeof e&&"function"!=typeof e)throw TypeError(`${t} is not an object.`)}function z(e,t){if("function"!=typeof e)throw TypeError(`${t} is not a function.`)}function $(e,t){if(("object"!=typeof e||null===e)&&"function"!=typeof e)throw TypeError(`${t} is not an object.`)}function L(e,t,r){if(void 0===e)throw TypeError(`Parameter ${t} is required in '${r}'.`)}function F(e,t,r){if(void 0===e)throw TypeError(`${t} is required in '${r}'.`)}function I(e){return Number(e)}function M(e,t){var r,o;let n=Number.MAX_SAFE_INTEGER,i=Number(e);if(!O(i=0===(r=i)?0:r))throw TypeError(`${t} is not a finite number`);if((i=0===(o=B(i))?0:o)<0||i>n)throw TypeError(`${t} is outside the accepted range of 0 to ${n}, inclusive`);return O(i)&&0!==i?i:0}function x(e,t){if(!t0(e))throw TypeError(`${t} is not a ReadableStream.`)}function U(e){return new Y(e)}function D(e,t){e._reader._readRequests.push(t)}function N(e,t,r){let o=e._reader._readRequests.shift();r?o._closeSteps():o._chunkSteps(t)}function H(e){return e._reader._readRequests.length}function V(e){let t=e._reader;return!!(void 0!==t&&Q(t))}class Y{constructor(e){if(L(e,1,"ReadableStreamDefaultReader"),x(e,"First parameter"),t1(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");P(this,e),this._readRequests=new g}get closed(){return Q(this)?this._closedPromise:u(K("closed"))}cancel(e){return Q(this)?void 0===this._ownerReadableStream?u(q("cancel")):E(this,e):u(K("cancel"))}read(){let e,t;if(!Q(this))return u(K("read"));if(void 0===this._ownerReadableStream)return u(q("read from"));let r=c((r,o)=>{e=r,t=o});return G(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!Q(this))throw K("releaseLock");if(void 0!==this._ownerReadableStream){var e;e=this,C(e),J(e,TypeError("Reader was released"))}}}function Q(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_readRequests"))&&e instanceof Y}function G(e,t){let r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[T](t)}function J(e,t){let r=e._readRequests;e._readRequests=new g,r.forEach(e=>{e._errorSteps(t)})}function K(e){return TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}Object.defineProperties(Y.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),a(Y.prototype.cancel,"cancel"),a(Y.prototype.read,"read"),a(Y.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Y.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});let Z=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);class X{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){let e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?f(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){let t=()=>this._returnSteps(e);return this._ongoingPromise?f(this._ongoingPromise,t,t):t()}_nextSteps(){let e,t;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let r=this._reader,o=c((r,o)=>{e=r,t=o});return G(r,{_chunkSteps:t=>{this._ongoingPromise=void 0,y(()=>e({value:t,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,C(r),e({value:void 0,done:!0})},_errorSteps:e=>{this._ongoingPromise=void 0,this._isFinished=!0,C(r),t(e)}}),o}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;let t=this._reader;if(!this._preventCancel){let r=E(t,e);return C(t),f(r,()=>({value:e,done:!0}),void 0)}return C(t),d({value:e,done:!0})}}let ee={next(){return et(this)?this._asyncIteratorImpl.next():u(er("next"))},return(e){return et(this)?this._asyncIteratorImpl.return(e):u(er("return"))}};function et(e){if(!i(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof X}catch(e){return!1}}function er(e){return TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}Object.setPrototypeOf(ee,Z);let eo=Number.isNaN||function(e){return e!=e};function en(e){return e.slice()}function ei(e,t,r,o,n){new Uint8Array(e).set(new Uint8Array(r,o,n),t)}let ea=e=>(ea="function"==typeof e.transfer?e=>e.transfer():"function"==typeof structuredClone?e=>structuredClone(e,{transfer:[e]}):e=>e)(e),el=e=>(el="boolean"==typeof e.detached?e=>e.detached:e=>0===e.byteLength)(e);function es(e,t,r){if(e.slice)return e.slice(t,r);let o=r-t,n=new ArrayBuffer(o);return ei(n,0,e,t,o),n}function eu(e,t){let r=e[t];if(null!=r){if("function"!=typeof r)throw TypeError(`${String(t)} is not a function`);return r}}let ec=null!==(o=null!==(t=Symbol.asyncIterator)&&void 0!==t?t:null===(r=Symbol.for)||void 0===r?void 0:r.call(Symbol,"Symbol.asyncIterator"))&&void 0!==o?o:"@@asyncIterator";function ed(e){return new Uint8Array(es(e.buffer,e.byteOffset,e.byteOffset+e.byteLength))}function ef(e){let t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function eh(e,t,r){if(!(!("number"!=typeof r||eo(r))&&!(r<0))||r===1/0)throw RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function ep(e){e._queue=new g,e._queueTotalSize=0}function eb(e){return e===DataView}class ey{constructor(){throw TypeError("Illegal constructor")}get view(){if(!eg(this))throw eN("view");return this._view}respond(e){if(!eg(this))throw eN("respond");if(L(e,1,"respond"),e=M(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");if(el(this._view.buffer))throw TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");ex(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!eg(this))throw eN("respondWithNewView");if(L(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");if(el(e.buffer))throw TypeError("The given view's buffer has been detached and so cannot be used as a response");eU(this._associatedReadableByteStreamController,e)}}Object.defineProperties(ey.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),a(ey.prototype.respond,"respond"),a(ey.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ey.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class em{constructor(){throw TypeError("Illegal constructor")}get byobRequest(){if(!e_(this))throw eH("byobRequest");return eI(this)}get desiredSize(){if(!e_(this))throw eH("desiredSize");return eM(this)}close(){if(!e_(this))throw eH("close");if(this._closeRequested)throw TypeError("The stream has already been closed; do not close it again!");let e=this._controlledReadableByteStream._state;if("readable"!==e)throw TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);ez(this)}enqueue(e){if(!e_(this))throw eH("enqueue");if(L(e,1,"enqueue"),!ArrayBuffer.isView(e))throw TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw TypeError("stream is closed or draining");let t=this._controlledReadableByteStream._state;if("readable"!==t)throw TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);e$(this,e)}error(e){if(!e_(this))throw eH("error");eL(this,e)}[w](e){eS(this),ep(this);let t=this._cancelAlgorithm(e);return eW(this),t}[T](e){let t=this._controlledReadableByteStream;if(this._queueTotalSize>0){eF(this,e);return}let r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){e._errorSteps(t);return}let o={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}D(t,e),ev(this)}[R](){if(this._pendingPullIntos.length>0){let e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new g,this._pendingPullIntos.push(e)}}}function e_(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream"))&&e instanceof em}function eg(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController"))&&e instanceof ey}function ev(e){if(function(e){let t=e._controlledReadableByteStream;return"readable"===t._state&&!e._closeRequested&&!!e._started&&!!(V(t)&&H(t)>0||eQ(t)&&eY(t)>0||eM(e)>0)}(e)){if(e._pulling){e._pullAgain=!0;return}e._pulling=!0,h(e._pullAlgorithm(),()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,ev(e)),null),t=>(eL(e,t),null))}}function eS(e){ek(e),e._pendingPullIntos=new g}function ew(e,t){let r=!1;"closed"===e._state&&(r=!0);let o=eT(t);"default"===t.readerType?N(e,o,r):function(e,t,r){let o=e._reader._readIntoRequests.shift();r?o._closeSteps(t):o._chunkSteps(t)}(e,o,r)}function eT(e){let t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function eR(e,t,r,o){e._queue.push({buffer:t,byteOffset:r,byteLength:o}),e._queueTotalSize+=o}function eP(e,t,r,o){let n;try{n=es(t,r,r+o)}catch(t){throw eL(e,t),t}eR(e,n,0,o)}function eE(e,t){t.bytesFilled>0&&eP(e,t.buffer,t.byteOffset,t.bytesFilled),eB(e)}function eC(e,t){let r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),o=t.bytesFilled+r,n=r,i=!1,a=o%t.elementSize,l=o-a;l>=t.minimumFill&&(n=l-t.bytesFilled,i=!0);let s=e._queue;for(;n>0;){let r=s.peek(),o=Math.min(n,r.byteLength),i=t.byteOffset+t.bytesFilled;ei(t.buffer,i,r.buffer,r.byteOffset,o),r.byteLength===o?s.shift():(r.byteOffset+=o,r.byteLength-=o),e._queueTotalSize-=o,eq(e,o,t),n-=o}return i}function eq(e,t,r){r.bytesFilled+=t}function eA(e){0===e._queueTotalSize&&e._closeRequested?(eW(e),t5(e._controlledReadableByteStream)):ev(e)}function ek(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function ej(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;let t=e._pendingPullIntos.peek();eC(e,t)&&(eB(e),ew(e._controlledReadableByteStream,t))}}function eO(e,t){let r=e._pendingPullIntos.peek();ek(e),"closed"===e._controlledReadableByteStream._state?function(e,t){"none"===t.readerType&&eB(e);let r=e._controlledReadableByteStream;if(eQ(r))for(;eY(r)>0;)ew(r,eB(e))}(e,r):function(e,t,r){if(eq(e,t,r),"none"===r.readerType){eE(e,r),ej(e);return}if(r.bytesFilled<r.minimumFill)return;eB(e);let o=r.bytesFilled%r.elementSize;if(o>0){let t=r.byteOffset+r.bytesFilled;eP(e,r.buffer,t-o,o)}r.bytesFilled-=o,ew(e._controlledReadableByteStream,r),ej(e)}(e,t,r),ev(e)}function eB(e){return e._pendingPullIntos.shift()}function eW(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function ez(e){let t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state){if(e._queueTotalSize>0){e._closeRequested=!0;return}if(e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){let t=TypeError("Insufficient bytes to fill elements in the given buffer");throw eL(e,t),t}}eW(e),t5(t)}}function e$(e,t){let r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;let{buffer:o,byteOffset:n,byteLength:i}=t;if(el(o))throw TypeError("chunk's buffer is detached and so cannot be enqueued");let a=ea(o);if(e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek();if(el(t.buffer))throw TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");ek(e),t.buffer=ea(t.buffer),"none"===t.readerType&&eE(e,t)}V(r)?(function(e){let t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;eF(e,t._readRequests.shift())}}(e),0===H(r)?eR(e,a,n,i):(e._pendingPullIntos.length>0&&eB(e),N(r,new Uint8Array(a,n,i),!1))):eQ(r)?(eR(e,a,n,i),ej(e)):eR(e,a,n,i),ev(e)}function eL(e,t){let r=e._controlledReadableByteStream;"readable"===r._state&&(eS(e),ep(e),eW(e),t8(r,t))}function eF(e,t){let r=e._queue.shift();e._queueTotalSize-=r.byteLength,eA(e);let o=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(o)}function eI(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){var t,r,o;let n=e._pendingPullIntos.peek(),i=new Uint8Array(n.buffer,n.byteOffset+n.bytesFilled,n.byteLength-n.bytesFilled),a=Object.create(ey.prototype);t=a,r=e,o=i,t._associatedReadableByteStreamController=r,t._view=o,e._byobRequest=a}return e._byobRequest}function eM(e){let t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function ex(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw RangeError("bytesWritten out of range")}r.buffer=ea(r.buffer),eO(e,t)}function eU(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw RangeError("The region specified by view is larger than byobRequest");let o=t.byteLength;r.buffer=ea(t.buffer),eO(e,o)}function eD(e,t,r,o,n,i,a){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,ep(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=i,t._pullAlgorithm=o,t._cancelAlgorithm=n,t._autoAllocateChunkSize=a,t._pendingPullIntos=new g,e._readableStreamController=t,h(d(r()),()=>(t._started=!0,ev(t),null),e=>(eL(t,e),null))}function eN(e){return TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function eH(e){return TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}Object.defineProperties(em.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),a(em.prototype.close,"close"),a(em.prototype.enqueue,"enqueue"),a(em.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(em.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function eV(e,t){e._reader._readIntoRequests.push(t)}function eY(e){return e._reader._readIntoRequests.length}function eQ(e){let t=e._reader;return!!(void 0!==t&&eJ(t))}class eG{constructor(e){if(L(e,1,"ReadableStreamBYOBReader"),x(e,"First parameter"),t1(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");if(!e_(e._readableStreamController))throw TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");P(this,e),this._readIntoRequests=new g}get closed(){return eJ(this)?this._closedPromise:u(eX("closed"))}cancel(e){return eJ(this)?void 0===this._ownerReadableStream?u(q("cancel")):E(this,e):u(eX("cancel"))}read(e,t={}){let r,o,n;if(!eJ(this))return u(eX("read"));if(!ArrayBuffer.isView(e))return u(TypeError("view must be an array buffer view"));if(0===e.byteLength)return u(TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return u(TypeError("view's buffer must have non-zero byteLength"));if(el(e.buffer))return u(TypeError("view's buffer has been detached"));try{var i,a;i="options",W(t,i),r={min:M(null!==(a=null==t?void 0:t.min)&&void 0!==a?a:1,`${i} has member 'min' that`)}}catch(e){return u(e)}let l=r.min;if(0===l)return u(TypeError("options.min must be greater than 0"));if(eb(e.constructor)){if(l>e.byteLength)return u(RangeError("options.min must be less than or equal to view's byteLength"))}else if(l>e.length)return u(RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return u(q("read from"));let s=c((e,t)=>{o=e,n=t});return eK(this,e,l,{_chunkSteps:e=>o({value:e,done:!1}),_closeSteps:e=>o({value:e,done:!0}),_errorSteps:e=>n(e)}),s}releaseLock(){if(!eJ(this))throw eX("releaseLock");if(void 0!==this._ownerReadableStream){var e;e=this,C(e),eZ(e,TypeError("Reader was released"))}}}function eJ(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_readIntoRequests"))&&e instanceof eG}function eK(e,t,r,o){let n=e._ownerReadableStream;n._disturbed=!0,"errored"===n._state?o._errorSteps(n._storedError):function(e,t,r,o){let n;let i=e._controlledReadableByteStream,a=t.constructor,l=eb(a)?1:a.BYTES_PER_ELEMENT,{byteOffset:s,byteLength:u}=t;try{n=ea(t.buffer)}catch(e){o._errorSteps(e);return}let c={buffer:n,bufferByteLength:n.byteLength,byteOffset:s,byteLength:u,bytesFilled:0,minimumFill:r*l,elementSize:l,viewConstructor:a,readerType:"byob"};if(e._pendingPullIntos.length>0){e._pendingPullIntos.push(c),eV(i,o);return}if("closed"===i._state){let e=new a(c.buffer,c.byteOffset,0);o._closeSteps(e);return}if(e._queueTotalSize>0){if(eC(e,c)){let t=eT(c);eA(e),o._chunkSteps(t);return}if(e._closeRequested){let t=TypeError("Insufficient bytes to fill elements in the given buffer");eL(e,t),o._errorSteps(t);return}}e._pendingPullIntos.push(c),eV(i,o),ev(e)}(n._readableStreamController,t,r,o)}function eZ(e,t){let r=e._readIntoRequests;e._readIntoRequests=new g,r.forEach(e=>{e._errorSteps(t)})}function eX(e){return TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function e0(e,t){let{highWaterMark:r}=e;if(void 0===r)return t;if(eo(r)||r<0)throw RangeError("Invalid highWaterMark");return r}function e1(e){let{size:t}=e;return t||(()=>1)}function e3(e,t){var r;W(e,t);let o=null==e?void 0:e.highWaterMark,n=null==e?void 0:e.size;return{highWaterMark:void 0===o?void 0:I(o),size:void 0===n?void 0:(z(r=n,`${t} has member 'size' that`),e=>I(r(e)))}}function e5(e,t){if(!e6(e))throw TypeError(`${t} is not a WritableStream.`)}Object.defineProperties(eG.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),a(eG.prototype.cancel,"cancel"),a(eG.prototype.read,"read"),a(eG.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(eG.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});let e8="function"==typeof AbortController;class e2{constructor(e={},t={}){void 0===e?e=null:$(e,"First parameter");let r=e3(t,"Second parameter"),o=function(e,t){var r,o,n,i,a,l,s,u;W(e,t);let c=null==e?void 0:e.abort,d=null==e?void 0:e.close,f=null==e?void 0:e.start,h=null==e?void 0:e.type,p=null==e?void 0:e.write;return{abort:void 0===c?void 0:(r=c,o=e,z(r,`${t} has member 'abort' that`),e=>_(r,o,[e])),close:void 0===d?void 0:(n=d,i=e,z(n,`${t} has member 'close' that`),()=>_(n,i,[])),start:void 0===f?void 0:(a=f,l=e,z(a,`${t} has member 'start' that`),e=>m(a,l,[e])),write:void 0===p?void 0:(s=p,u=e,z(s,`${t} has member 'write' that`),(e,t)=>_(s,u,[e,t])),type:h}}(e,"First parameter");if(e7(this),void 0!==o.type)throw RangeError("Invalid type is specified");let n=e1(r);(function(e,t,r,o){let n,i,a;let l=Object.create(tp.prototype);n=void 0!==t.start?()=>t.start(l):()=>void 0,i=void 0!==t.write?e=>t.write(e,l):()=>d(void 0),ty(e,l,n,i,void 0!==t.close?()=>t.close():()=>d(void 0),void 0!==t.abort?e=>t.abort(e):()=>d(void 0),r,o)})(this,o,e0(r,1),n)}get locked(){if(!e6(this))throw tT("locked");return e4(this)}abort(e){return e6(this)?e4(this)?u(TypeError("Cannot abort a stream that already has a writer")):e9(this,e):u(tT("abort"))}close(){return e6(this)?e4(this)?u(TypeError("Cannot close a stream that already has a writer")):tn(this)?u(TypeError("Cannot close an already-closing stream")):te(this):u(tT("close"))}getWriter(){var e;if(!e6(this))throw tT("getWriter");return e=this,new tl(e)}}function e7(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new g,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function e6(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_writableStreamController"))&&e instanceof e2}function e4(e){return void 0!==e._writer}function e9(e,t){var r;if("closed"===e._state||"errored"===e._state)return d(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);let o=e._state;if("closed"===o||"errored"===o)return d(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let n=!1;"erroring"===o&&(n=!0,t=void 0);let i=c((r,o)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:o,_reason:t,_wasAlreadyErroring:n}});return e._pendingAbortRequest._promise=i,n||tr(e,t),i}function te(e){var t;let r=e._state;if("closed"===r||"errored"===r)return u(TypeError(`The stream (in ${r} state) is not in the writable state and cannot be closed`));let o=c((t,r)=>{e._closeRequest={_resolve:t,_reject:r}}),n=e._writer;return void 0!==n&&e._backpressure&&"writable"===r&&tB(n),eh(t=e._writableStreamController,th,0),tg(t),o}function tt(e,t){if("writable"===e._state){tr(e,t);return}to(e)}function tr(e,t){var r;let o=e._writableStreamController;e._state="erroring",e._storedError=t;let n=e._writer;void 0!==n&&tc(n,t),void 0===(r=e)._inFlightWriteRequest&&void 0===r._inFlightCloseRequest&&o._started&&to(e)}function to(e){e._state="errored",e._writableStreamController[S]();let t=e._storedError;if(e._writeRequests.forEach(e=>{e._reject(t)}),e._writeRequests=new g,void 0===e._pendingAbortRequest){ti(e);return}let r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring){r._reject(t),ti(e);return}h(e._writableStreamController[v](r._reason),()=>(r._resolve(),ti(e),null),t=>(r._reject(t),ti(e),null))}function tn(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function ti(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);let t=e._writer;void 0!==t&&tq(t,e._storedError)}function ta(e,t){let r=e._writer;void 0!==r&&t!==e._backpressure&&(t?tk(r):tB(r)),e._backpressure=t}Object.defineProperties(e2.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),a(e2.prototype.abort,"abort"),a(e2.prototype.close,"close"),a(e2.prototype.getWriter,"getWriter"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(e2.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});class tl{constructor(e){var t,r,o,n,i;if(L(e,1,"WritableStreamDefaultWriter"),e5(e,"First parameter"),e4(e))throw TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;let a=e._state;if("writable"===a){!tn(e)&&e._backpressure?tk(this):(t=this,tk(t),tB(t)),tC(this)}else if("erroring"===a)tj(this,e._storedError),tC(this);else if("closed"===a){r=this,tk(r),tB(r),o=this,tC(o),tA(o)}else{let t=e._storedError;tj(this,t),n=this,i=t,tC(n),tq(n,i)}}get closed(){return ts(this)?this._closedPromise:u(tP("closed"))}get desiredSize(){if(!ts(this))throw tP("desiredSize");if(void 0===this._ownerWritableStream)throw tE("desiredSize");return function(e){let t=e._ownerWritableStream,r=t._state;return"errored"===r||"erroring"===r?null:"closed"===r?0:t_(t._writableStreamController)}(this)}get ready(){return ts(this)?this._readyPromise:u(tP("ready"))}abort(e){var t,r;if(!ts(this))return u(tP("abort"));if(void 0===this._ownerWritableStream)return u(tE("abort"));return t=this,r=e,e9(t._ownerWritableStream,r)}close(){if(!ts(this))return u(tP("close"));let e=this._ownerWritableStream;return void 0===e?u(tE("close")):tn(e)?u(TypeError("Cannot close an already-closing stream")):tu(this)}releaseLock(){if(!ts(this))throw tP("releaseLock");void 0!==this._ownerWritableStream&&td(this)}write(e){return ts(this)?void 0===this._ownerWritableStream?u(tE("write to")):tf(this,e):u(tP("write"))}}function ts(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream"))&&e instanceof tl}function tu(e){return te(e._ownerWritableStream)}function tc(e,t){"pending"===e._readyPromiseState?tO(e,t):tj(e,t)}function td(e){let t=e._ownerWritableStream,r=TypeError("Writer was released and can no longer be used to monitor the stream's closedness");tc(e,r),"pending"===e._closedPromiseState?tq(e,r):function(e,t){tC(e),tq(e,t)}(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function tf(e,t){let r=e._ownerWritableStream,o=r._writableStreamController,n=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return tv(e,t),1}}(o,t);if(r!==e._ownerWritableStream)return u(tE("write to"));let i=r._state;if("errored"===i)return u(r._storedError);if(tn(r)||"closed"===i)return u(TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===i)return u(r._storedError);let a=c((e,t)=>{r._writeRequests.push({_resolve:e,_reject:t})});return function(e,t,r){try{eh(e,t,r)}catch(t){tv(e,t);return}let o=e._controlledWritableStream;tn(o)||"writable"!==o._state||ta(o,function(e){return 0>=t_(e)}(e)),tg(e)}(o,t,n),a}Object.defineProperties(tl.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),a(tl.prototype.abort,"abort"),a(tl.prototype.close,"close"),a(tl.prototype.releaseLock,"releaseLock"),a(tl.prototype.write,"write"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tl.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});let th={};class tp{constructor(){throw TypeError("Illegal constructor")}get abortReason(){if(!tb(this))throw tR("abortReason");return this._abortReason}get signal(){if(!tb(this))throw tR("signal");if(void 0===this._abortController)throw TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e){if(!tb(this))throw tR("error");"writable"===this._controlledWritableStream._state&&tw(this,e)}[v](e){let t=this._abortAlgorithm(e);return tm(this),t}[S](){ep(this)}}function tb(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream"))&&e instanceof tp}function ty(e,t,r,o,n,i,a,l){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,ep(t),t._abortReason=void 0,t._abortController=function(){if(e8)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=l,t._strategyHWM=a,t._writeAlgorithm=o,t._closeAlgorithm=n,t._abortAlgorithm=i,ta(e,0>=t_(t)),h(d(r()),()=>(t._started=!0,tg(t),null),r=>(t._started=!0,tt(e,r),null))}function tm(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function t_(e){return e._strategyHWM-e._queueTotalSize}function tg(e){let t=e._controlledWritableStream;if(!e._started||void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state){to(t);return}if(0===e._queue.length)return;let r=e._queue.peek().value;r===th?function(e){let t=e._controlledWritableStream;t._inFlightCloseRequest=t._closeRequest,t._closeRequest=void 0,ef(e);let r=e._closeAlgorithm();tm(e),h(r,()=>((function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";let t=e._writer;void 0!==t&&tA(t)})(t),null),e=>(t._inFlightCloseRequest._reject(e),t._inFlightCloseRequest=void 0,void 0!==t._pendingAbortRequest&&(t._pendingAbortRequest._reject(e),t._pendingAbortRequest=void 0),tt(t,e),null))}(e):function(e,t){let r=e._controlledWritableStream;r._inFlightWriteRequest=r._writeRequests.shift(),h(e._writeAlgorithm(t),()=>{r._inFlightWriteRequest._resolve(void 0),r._inFlightWriteRequest=void 0;let t=r._state;return ef(e),tn(r)||"writable"!==t||ta(r,function(e){return 0>=t_(e)}(e)),tg(e),null},t=>("writable"===r._state&&tm(e),r._inFlightWriteRequest._reject(t),r._inFlightWriteRequest=void 0,tt(r,t),null))}(e,r)}function tv(e,t){"writable"===e._controlledWritableStream._state&&tw(e,t)}function tS(e){return 0>=t_(e)}function tw(e,t){let r=e._controlledWritableStream;tm(e),tr(r,t)}function tT(e){return TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function tR(e){return TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function tP(e){return TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function tE(e){return TypeError("Cannot "+e+" a stream using a released writer")}function tC(e){e._closedPromise=c((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"})}Object.defineProperties(tp.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tp.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function tq(e,t){void 0!==e._closedPromise_reject&&(b(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function tA(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function tk(e){e._readyPromise=c((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r}),e._readyPromiseState="pending"}function tj(e,t){tk(e),tO(e,t)}function tO(e,t){void 0!==e._readyPromise_reject&&(b(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function tB(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}let tW="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0,tz=function(){let e=null==tW?void 0:tW.DOMException;return!function(e){if("function"!=typeof e&&"object"!=typeof e||"DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(e)?void 0:e}()||function(){let e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return a(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function t$(e,t,r,o,i,a){let l=U(e),s=new tl(t);e._disturbed=!0;let y=!1,m=d(void 0);return c((_,g)=>{var v,S,w;let T;if(void 0!==a){if(T=()=>{let r=void 0!==a.reason?a.reason:new tz("Aborted","AbortError"),n=[];o||n.push(()=>"writable"===t._state?e9(t,r):d(void 0)),i||n.push(()=>"readable"===e._state?t3(e,r):d(void 0)),E(()=>Promise.all(n.map(e=>e())),!0,r)},a.aborted){T();return}a.addEventListener("abort",T)}if(P(e,l._closedPromise,e=>(o?q(!0,e):E(()=>e9(t,e),!0,e),null)),P(t,s._closedPromise,t=>(i?q(!0,t):E(()=>t3(e,t),!0,t),null)),v=e,S=l._closedPromise,w=()=>(r?q():E(()=>(function(e){let t=e._ownerWritableStream,r=t._state;return tn(t)||"closed"===r?d(void 0):"errored"===r?u(t._storedError):tu(e)})(s)),null),"closed"===v._state?w():h(S,w),tn(t)||"closed"===t._state){let t=TypeError("the destination writable stream closed before all data could be piped to it");i?q(!0,t):E(()=>t3(e,t),!0,t)}function R(){let e=m;return f(m,()=>e!==m?R():void 0)}function P(e,t,r){"errored"===e._state?r(e._storedError):p(t,r)}function E(e,r,o){if(!y){if(y=!0,"writable"!==t._state||tn(t))n();else h(R(),n)}function n(){return h(e(),()=>A(r,o),e=>A(!0,e)),null}}function q(e,r){if(!y){if(y=!0,"writable"!==t._state||tn(t))A(e,r);else h(R(),()=>A(e,r))}}function A(e,t){return td(s),C(l),void 0!==a&&a.removeEventListener("abort",T),e?g(t):_(void 0),null}b(c((e,t)=>{!function r(o){o?e():f(y?d(!0):f(s._readyPromise,()=>c((e,t)=>{G(l,{_chunkSteps:t=>{m=f(tf(s,t),void 0,n),e(!1)},_closeSteps:()=>e(!0),_errorSteps:t})})),r,t)}(!1)}))})}class tL{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!tF(this))throw tQ("desiredSize");return tH(this)}close(){if(!tF(this))throw tQ("close");if(!tV(this))throw TypeError("The stream is not in a state that permits close");tU(this)}enqueue(e){if(!tF(this))throw tQ("enqueue");if(!tV(this))throw TypeError("The stream is not in a state that permits enqueue");return tD(this,e)}error(e){if(!tF(this))throw tQ("error");tN(this,e)}[w](e){ep(this);let t=this._cancelAlgorithm(e);return tx(this),t}[T](e){let t=this._controlledReadableStream;if(this._queue.length>0){let r=ef(this);this._closeRequested&&0===this._queue.length?(tx(this),t5(t)):tI(this),e._chunkSteps(r)}else D(t,e),tI(this)}[R](){}}function tF(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream"))&&e instanceof tL}function tI(e){if(tM(e)){if(e._pulling){e._pullAgain=!0;return}e._pulling=!0,h(e._pullAlgorithm(),()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,tI(e)),null),t=>(tN(e,t),null))}}function tM(e){let t=e._controlledReadableStream;return!!tV(e)&&!!e._started&&!!(t1(t)&&H(t)>0||tH(e)>0)}function tx(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function tU(e){if(!tV(e))return;let t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(tx(e),t5(t))}function tD(e,t){if(!tV(e))return;let r=e._controlledReadableStream;if(t1(r)&&H(r)>0)N(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw tN(e,t),t}try{eh(e,t,r)}catch(t){throw tN(e,t),t}}tI(e)}function tN(e,t){let r=e._controlledReadableStream;"readable"===r._state&&(ep(e),tx(e),t8(r,t))}function tH(e){let t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function tV(e){let t=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===t}function tY(e,t,r,o,n,i,a){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,ep(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=a,t._strategyHWM=i,t._pullAlgorithm=o,t._cancelAlgorithm=n,e._readableStreamController=t,h(d(r()),()=>(t._started=!0,tI(t),null),e=>(tN(t,e),null))}function tQ(e){return TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function tG(e,t){W(e,t);let r=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,i=null==e?void 0:e.signal;return void 0!==i&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw TypeError(`${t} is not an AbortSignal.`)}(i,`${t} has member 'signal' that`),{preventAbort:!!r,preventCancel:!!o,preventClose:!!n,signal:i}}Object.defineProperties(tL.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),a(tL.prototype.close,"close"),a(tL.prototype.enqueue,"enqueue"),a(tL.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tL.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class tJ{constructor(e={},t={}){void 0===e?e=null:$(e,"First parameter");let r=e3(t,"Second parameter"),o=function(e,t){var r,o,n,i,a,l;W(e,t);let s=null==e?void 0:e.autoAllocateChunkSize,u=null==e?void 0:e.cancel,c=null==e?void 0:e.pull,d=null==e?void 0:e.start,f=null==e?void 0:e.type;return{autoAllocateChunkSize:void 0===s?void 0:M(s,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===u?void 0:(r=u,o=e,z(r,`${t} has member 'cancel' that`),e=>_(r,o,[e])),pull:void 0===c?void 0:(n=c,i=e,z(n,`${t} has member 'pull' that`),e=>_(n,i,[e])),start:void 0===d?void 0:(a=d,l=e,z(a,`${t} has member 'start' that`),e=>m(a,l,[e])),type:void 0===f?void 0:function(e,t){if("bytes"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}(f,`${t} has member 'type' that`)}}(e,"First parameter");if(tX(this),"bytes"===o.type){if(void 0!==r.size)throw RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){let o,n,i;let a=Object.create(em.prototype);o=void 0!==t.start?()=>t.start(a):()=>void 0,n=void 0!==t.pull?()=>t.pull(a):()=>d(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>d(void 0);let l=t.autoAllocateChunkSize;if(0===l)throw TypeError("autoAllocateChunkSize must be greater than 0");eD(e,a,o,n,i,r,l)}(this,o,e0(r,0))}else{let e=e1(r);!function(e,t,r,o){let n,i,a;let l=Object.create(tL.prototype);n=void 0!==t.start?()=>t.start(l):()=>void 0,i=void 0!==t.pull?()=>t.pull(l):()=>d(void 0),tY(e,l,n,i,void 0!==t.cancel?e=>t.cancel(e):()=>d(void 0),r,o)}(this,o,e0(r,1),e)}}get locked(){if(!t0(this))throw t2("locked");return t1(this)}cancel(e){return t0(this)?t1(this)?u(TypeError("Cannot cancel a stream that already has a reader")):t3(this,e):u(t2("cancel"))}getReader(e){if(!t0(this))throw t2("getReader");return void 0===function(e,t){W(e,t);let r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:function(e,t){if("byob"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?U(this):new eG(this)}pipeThrough(e,t={}){if(!t0(this))throw t2("pipeThrough");L(e,1,"pipeThrough");let r=function(e,t){W(e,t);let r=null==e?void 0:e.readable;F(r,"readable","ReadableWritablePair"),x(r,`${t} has member 'readable' that`);let o=null==e?void 0:e.writable;return F(o,"writable","ReadableWritablePair"),e5(o,`${t} has member 'writable' that`),{readable:r,writable:o}}(e,"First parameter"),o=tG(t,"Second parameter");if(t1(this))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(e4(r.writable))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return b(t$(this,r.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),r.readable}pipeTo(e,t={}){let r;if(!t0(this))return u(t2("pipeTo"));if(void 0===e)return u("Parameter 1 is required in 'pipeTo'.");if(!e6(e))return u(TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));try{r=tG(t,"Second parameter")}catch(e){return u(e)}return t1(this)?u(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):e4(e)?u(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):t$(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!t0(this))throw t2("tee");let e=e_(this._readableStreamController)?function(e){let t,r,o,n,i,a=U(e),l=!1,s=!1,u=!1,f=!1,h=!1,b=c(e=>{i=e});function m(e){p(e._closedPromise,t=>(e!==a||(eL(o._readableStreamController,t),eL(n._readableStreamController,t),f&&h||i(void 0)),null))}function _(){eJ(a)&&(C(a),m(a=U(e))),G(a,{_chunkSteps:t=>{y(()=>{s=!1,u=!1;let r=t;if(!f&&!h)try{r=ed(t)}catch(t){eL(o._readableStreamController,t),eL(n._readableStreamController,t),i(t3(e,t));return}f||e$(o._readableStreamController,t),h||e$(n._readableStreamController,r),l=!1,s?v():u&&S()})},_closeSteps:()=>{l=!1,f||ez(o._readableStreamController),h||ez(n._readableStreamController),o._readableStreamController._pendingPullIntos.length>0&&ex(o._readableStreamController,0),n._readableStreamController._pendingPullIntos.length>0&&ex(n._readableStreamController,0),f&&h||i(void 0)},_errorSteps:()=>{l=!1}})}function g(t,r){Q(a)&&(C(a),m(a=new eG(e)));let c=r?n:o,d=r?o:n;eK(a,t,1,{_chunkSteps:t=>{y(()=>{s=!1,u=!1;let o=r?h:f;if(r?f:h)o||eU(c._readableStreamController,t);else{let r;try{r=ed(t)}catch(t){eL(c._readableStreamController,t),eL(d._readableStreamController,t),i(t3(e,t));return}o||eU(c._readableStreamController,t),e$(d._readableStreamController,r)}l=!1,s?v():u&&S()})},_closeSteps:e=>{l=!1;let t=r?h:f,o=r?f:h;t||ez(c._readableStreamController),o||ez(d._readableStreamController),void 0!==e&&(t||eU(c._readableStreamController,e),!o&&d._readableStreamController._pendingPullIntos.length>0&&ex(d._readableStreamController,0)),t&&o||i(void 0)},_errorSteps:()=>{l=!1}})}function v(){if(l)return s=!0,d(void 0);l=!0;let e=eI(o._readableStreamController);return null===e?_():g(e._view,!1),d(void 0)}function S(){if(l)return u=!0,d(void 0);l=!0;let e=eI(n._readableStreamController);return null===e?_():g(e._view,!0),d(void 0)}function w(){}return o=tZ(w,v,function(o){if(f=!0,t=o,h){let o=t3(e,en([t,r]));i(o)}return b}),n=tZ(w,S,function(o){if(h=!0,r=o,f){let o=t3(e,en([t,r]));i(o)}return b}),m(a),[o,n]}(this):function(e,t){let r,o,n,i,a;let l=U(e),s=!1,u=!1,f=!1,h=!1,b=c(e=>{a=e});function m(){return s?u=!0:(s=!0,G(l,{_chunkSteps:e=>{y(()=>{u=!1,f||tD(n._readableStreamController,e),h||tD(i._readableStreamController,e),s=!1,u&&m()})},_closeSteps:()=>{s=!1,f||tU(n._readableStreamController),h||tU(i._readableStreamController),f&&h||a(void 0)},_errorSteps:()=>{s=!1}})),d(void 0)}function _(){}return n=tK(_,m,function(t){if(f=!0,r=t,h){let t=t3(e,en([r,o]));a(t)}return b}),i=tK(_,m,function(t){if(h=!0,o=t,f){let t=t3(e,en([r,o]));a(t)}return b}),p(l._closedPromise,e=>(tN(n._readableStreamController,e),tN(i._readableStreamController,e),f&&h||a(void 0),null)),[n,i]}(this);return en(e)}values(e){if(!t0(this))throw t2("values");return function(e,t){let r=new X(U(e),t),o=Object.create(ee);return o._asyncIteratorImpl=r,o}(this,(W(e,"First parameter"),{preventCancel:!!(null==e?void 0:e.preventCancel)}).preventCancel)}[ec](e){return this.values(e)}static from(e){var t;let r;return i(e)&&void 0!==e.getReader?(t=e.getReader(),r=tK(n,function(){let e;try{e=t.read()}catch(e){return u(e)}return f(e,e=>{if(!i(e))throw TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)tU(r._readableStreamController);else{let t=e.value;tD(r._readableStreamController,t)}},void 0)},function(e){try{return d(t.cancel(e))}catch(e){return u(e)}},0)):function(e){let t;let r=function e(t,r="sync",o){if(void 0===o){if("async"===r){if(void 0===(o=eu(t,ec))){let r=eu(t,Symbol.iterator);return function(e){let t={[Symbol.iterator]:()=>e.iterator},r=async function*(){return yield*t}(),o=r.next;return{iterator:r,nextMethod:o,done:!1}}(e(t,"sync",r))}}else o=eu(t,Symbol.iterator)}if(void 0===o)throw TypeError("The object is not iterable");let n=m(o,t,[]);if(!i(n))throw TypeError("The iterator method must return an object");let a=n.next;return{iterator:n,nextMethod:a,done:!1}}(e,"async");return t=tK(n,function(){let e;try{e=function(e){let t=m(e.nextMethod,e.iterator,[]);if(!i(t))throw TypeError("The iterator.next() method must return an object");return t}(r)}catch(e){return u(e)}return f(d(e),e=>{if(!i(e))throw TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(e.done)tU(t._readableStreamController);else{let r=e.value;tD(t._readableStreamController,r)}},void 0)},function(e){let t,o;let n=r.iterator;try{t=eu(n,"return")}catch(e){return u(e)}if(void 0===t)return d(void 0);try{o=m(t,n,[e])}catch(e){return u(e)}return f(d(o),e=>{if(!i(e))throw TypeError("The promise returned by the iterator.return() method must fulfill with an object")},void 0)},0)}(e)}}function tK(e,t,r,o=1,n=()=>1){let i=Object.create(tJ.prototype);return tX(i),tY(i,Object.create(tL.prototype),e,t,r,o,n),i}function tZ(e,t,r){let o=Object.create(tJ.prototype);return tX(o),eD(o,Object.create(em.prototype),e,t,r,0,void 0),o}function tX(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function t0(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_readableStreamController"))&&e instanceof tJ}function t1(e){return void 0!==e._reader}function t3(e,t){if(e._disturbed=!0,"closed"===e._state)return d(void 0);if("errored"===e._state)return u(e._storedError);t5(e);let r=e._reader;if(void 0!==r&&eJ(r)){let e=r._readIntoRequests;r._readIntoRequests=new g,e.forEach(e=>{e._closeSteps(void 0)})}return f(e._readableStreamController[w](t),n,void 0)}function t5(e){e._state="closed";let t=e._reader;if(void 0!==t&&(j(t),Q(t))){let e=t._readRequests;t._readRequests=new g,e.forEach(e=>{e._closeSteps()})}}function t8(e,t){e._state="errored",e._storedError=t;let r=e._reader;void 0!==r&&(k(r,t),Q(r)?J(r,t):eZ(r,t))}function t2(e){return TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function t7(e,t){W(e,t);let r=null==e?void 0:e.highWaterMark;return F(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:I(r)}}Object.defineProperties(tJ,{from:{enumerable:!0}}),Object.defineProperties(tJ.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),a(tJ.from,"from"),a(tJ.prototype.cancel,"cancel"),a(tJ.prototype.getReader,"getReader"),a(tJ.prototype.pipeThrough,"pipeThrough"),a(tJ.prototype.pipeTo,"pipeTo"),a(tJ.prototype.tee,"tee"),a(tJ.prototype.values,"values"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tJ.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(tJ.prototype,ec,{value:tJ.prototype.values,writable:!0,configurable:!0});let t6=e=>e.byteLength;a(t6,"size");class t4{constructor(e){L(e,1,"ByteLengthQueuingStrategy"),e=t7(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!re(this))throw t9("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!re(this))throw t9("size");return t6}}function t9(e){return TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function re(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark"))&&e instanceof t4}Object.defineProperties(t4.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(t4.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});let rt=()=>1;a(rt,"size");class rr{constructor(e){L(e,1,"CountQueuingStrategy"),e=t7(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!rn(this))throw ro("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!rn(this))throw ro("size");return rt}}function ro(e){return TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function rn(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark"))&&e instanceof rr}Object.defineProperties(rr.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(rr.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class ri{constructor(e={},t={},r={}){let o;void 0===e&&(e=null);let n=e3(t,"Second parameter"),i=e3(r,"Third parameter"),a=function(e,t){var r,o,n,i,a,l,s,u;W(e,t);let c=null==e?void 0:e.cancel,d=null==e?void 0:e.flush,f=null==e?void 0:e.readableType,h=null==e?void 0:e.start,p=null==e?void 0:e.transform,b=null==e?void 0:e.writableType;return{cancel:void 0===c?void 0:(r=c,o=e,z(r,`${t} has member 'cancel' that`),e=>_(r,o,[e])),flush:void 0===d?void 0:(n=d,i=e,z(n,`${t} has member 'flush' that`),e=>_(n,i,[e])),readableType:f,start:void 0===h?void 0:(a=h,l=e,z(a,`${t} has member 'start' that`),e=>m(a,l,[e])),transform:void 0===p?void 0:(s=p,u=e,z(s,`${t} has member 'transform' that`),(e,t)=>_(s,u,[e,t])),writableType:b}}(e,"First parameter");if(void 0!==a.readableType)throw RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw RangeError("Invalid writableType specified");let l=e0(i,0),s=e1(i),p=e0(n,1),b=e1(n);(function(e,t,r,o,n,i){function a(){return t}e._writable=function(e,t,r,o,n=1,i=()=>1){let a=Object.create(e2.prototype);return e7(a),ty(a,Object.create(tp.prototype),e,t,r,o,n,i),a}(a,function(t){return function(e,t){let r=e._transformStreamController;return e._backpressure?f(e._backpressureChangePromise,()=>{let o=e._writable;if("erroring"===o._state)throw o._storedError;return rb(r,t)},void 0):rb(r,t)}(e,t)},function(){return function(e){let t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;let r=e._readable;t._finishPromise=c((e,r)=>{t._finishPromise_resolve=e,t._finishPromise_reject=r});let o=t._flushAlgorithm();return rh(t),h(o,()=>("errored"===r._state?r_(t,r._storedError):(tU(r._readableStreamController),rm(t)),null),e=>(tN(r._readableStreamController,e),r_(t,e),null)),t._finishPromise}(e)},function(t){return function(e,t){let r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;let o=e._readable;r._finishPromise=c((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t});let n=r._cancelAlgorithm(t);return rh(r),h(n,()=>("errored"===o._state?r_(r,o._storedError):(tN(o._readableStreamController,t),rm(r)),null),e=>(tN(o._readableStreamController,e),r_(r,e),null)),r._finishPromise}(e,t)},r,o),e._readable=tK(a,function(){var t;return rc(t=e,!1),t._backpressureChangePromise},function(t){return function(e,t){let r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;let o=e._writable;r._finishPromise=c((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t});let n=r._cancelAlgorithm(t);return rh(r),h(n,()=>("errored"===o._state?r_(r,o._storedError):(tv(o._writableStreamController,t),ru(e),rm(r)),null),t=>(tv(o._writableStreamController,t),ru(e),r_(r,t),null)),r._finishPromise}(e,t)},n,i),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,rc(e,!0),e._transformStreamController=void 0})(this,c(e=>{o=e}),p,b,l,s),function(e,t){let r,o,n;let i=Object.create(rd.prototype);r=void 0!==t.transform?e=>t.transform(e,i):e=>{try{return rp(i,e),d(void 0)}catch(e){return u(e)}},o=void 0!==t.flush?()=>t.flush(i):()=>d(void 0),n=void 0!==t.cancel?e=>t.cancel(e):()=>d(void 0),i._controlledTransformStream=e,e._transformStreamController=i,i._transformAlgorithm=r,i._flushAlgorithm=o,i._cancelAlgorithm=n,i._finishPromise=void 0,i._finishPromise_resolve=void 0,i._finishPromise_reject=void 0}(this,a),void 0!==a.start?o(a.start(this._transformStreamController)):o(void 0)}get readable(){if(!ra(this))throw rg("readable");return this._readable}get writable(){if(!ra(this))throw rg("writable");return this._writable}}function ra(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_transformStreamController"))&&e instanceof ri}function rl(e,t){tN(e._readable._readableStreamController,t),rs(e,t)}function rs(e,t){rh(e._transformStreamController),tv(e._writable._writableStreamController,t),ru(e)}function ru(e){e._backpressure&&rc(e,!1)}function rc(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=c(t=>{e._backpressureChangePromise_resolve=t}),e._backpressure=t}Object.defineProperties(ri.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ri.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});class rd{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!rf(this))throw ry("desiredSize");return tH(this._controlledTransformStream._readable._readableStreamController)}enqueue(e){if(!rf(this))throw ry("enqueue");rp(this,e)}error(e){var t,r;if(!rf(this))throw ry("error");t=this,r=e,rl(t._controlledTransformStream,r)}terminate(){if(!rf(this))throw ry("terminate");(function(e){let t=e._controlledTransformStream;tU(t._readable._readableStreamController),rs(t,TypeError("TransformStream terminated"))})(this)}}function rf(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream"))&&e instanceof rd}function rh(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function rp(e,t){let r=e._controlledTransformStream,o=r._readable._readableStreamController;if(!tV(o))throw TypeError("Readable side is not in a state that permits enqueue");try{tD(o,t)}catch(e){throw rs(r,e),r._readable._storedError}!tM(o)!==r._backpressure&&rc(r,!0)}function rb(e,t){return f(e._transformAlgorithm(t),void 0,t=>{throw rl(e._controlledTransformStream,t),t})}function ry(e){return TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function rm(e){void 0!==e._finishPromise_resolve&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function r_(e,t){void 0!==e._finishPromise_reject&&(b(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function rg(e){return TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}Object.defineProperties(rd.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),a(rd.prototype.enqueue,"enqueue"),a(rd.prototype.error,"error"),a(rd.prototype.terminate,"terminate"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(rd.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0}),e.ByteLengthQueuingStrategy=t4,e.CountQueuingStrategy=rr,e.ReadableByteStreamController=em,e.ReadableStream=tJ,e.ReadableStreamBYOBReader=eG,e.ReadableStreamBYOBRequest=ey,e.ReadableStreamDefaultController=tL,e.ReadableStreamDefaultReader=Y,e.TransformStream=ri,e.TransformStreamDefaultController=rd,e.WritableStream=e2,e.WritableStreamDefaultController=tp,e.WritableStreamDefaultWriter=tl})(t)},40851:(e,t,r)=>{if(!globalThis.DOMException)try{let{MessageChannel:e}=r(73566),t=new e().port1,o=new ArrayBuffer;t.postMessage(o,[o,o])}catch(e){"DOMException"===e.constructor.name&&(globalThis.DOMException=e.constructor)}e.exports=globalThis.DOMException},41130:(e,t,r)=>{"use strict";r.d(t,{$n:()=>p,fS:()=>h});var o=r(11055),n=r(68355),{toStringTag:i,iterator:a,hasInstance:l}=Symbol,s=Math.random,u="append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","),c=(e,t,r)=>(e+="",/^(Blob|File)$/.test(t&&t[i])?[(r=void 0!==r?r+"":"File"==t[i]?t.name:"blob",e),t.name!==r||"blob"==t[i]?new n.A([t],r,t):t]:[e,t+""]),d=(e,t)=>(t?e:e.replace(/\r?\n|\r/g,"\r\n")).replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22"),f=(e,t,r)=>{if(t.length<r)throw TypeError(`Failed to execute '${e}' on 'FormData': ${r} arguments required, but only ${t.length} present.`)};let h=class{#n=[];constructor(...e){if(e.length)throw TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.")}get[i](){return"FormData"}[a](){return this.entries()}static[l](e){return e&&"object"==typeof e&&"FormData"===e[i]&&!u.some(t=>"function"!=typeof e[t])}append(...e){f("append",arguments,2),this.#n.push(c(...e))}delete(e){f("delete",arguments,1),e+="",this.#n=this.#n.filter(([t])=>t!==e)}get(e){f("get",arguments,1),e+="";for(var t=this.#n,r=t.length,o=0;o<r;o++)if(t[o][0]===e)return t[o][1];return null}getAll(e,t){return f("getAll",arguments,1),t=[],e+="",this.#n.forEach(r=>r[0]===e&&t.push(r[1])),t}has(e){return f("has",arguments,1),e+="",this.#n.some(t=>t[0]===e)}forEach(e,t){for(var[r,o]of(f("forEach",arguments,1),this))e.call(t,o,r,this)}set(...e){f("set",arguments,2);var t=[],r=!0;e=c(...e),this.#n.forEach(o=>{o[0]===e[0]?r&&(r=!t.push(e)):t.push(o)}),r&&t.push(e),this.#n=t}*entries(){yield*this.#n}*keys(){for(var[e]of this)yield e}*values(){for(var[,e]of this)yield e}};function p(e,t=o.A){var r=`${s()}${s()}`.replace(/\./g,"").slice(-28).padStart(32,"-"),n=[],i=`--${r}\r
Content-Disposition: form-data; name="`;return e.forEach((e,t)=>"string"==typeof e?n.push(i+d(t)+`"\r
\r
${e.replace(/\r(?!\n)|(?<!\r)\n/g,"\r\n")}\r
`):n.push(i+d(t)+`"; filename="${d(e.name,1)}"\r
Content-Type: ${e.type||"application/octet-stream"}\r
\r
`,e,"\r\n")),n.push(`--${r}--`),new t(n,{type:"multipart/form-data; boundary="+r})}},56177:(e,t,r)=>{"use strict";r.d(t,{ZH:()=>i.A});var o=r(73024);r(54379);var n=r(40851),i=r(68355);r(11055);let{stat:a}=o.promises;class l{#i;#a;constructor(e){this.#i=e.path,this.#a=e.start,this.size=e.size,this.lastModified=e.lastModified}slice(e,t){return new l({path:this.#i,lastModified:this.lastModified,size:t-e,start:this.#a+e})}async *stream(){let{mtimeMs:e}=await a(this.#i);if(e>this.lastModified)throw new n("The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.","NotReadableError");yield*(0,o.createReadStream)(this.#i,{start:this.#a,end:this.#a+this.size-1})}get[Symbol.toStringTag](){return"Blob"}}},68355:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(11055);let n=class extends o.A{#l=0;#s="";constructor(e,t,r={}){if(arguments.length<2)throw TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(e,r),null===r&&(r={});let o=void 0===r.lastModified?Date.now():Number(r.lastModified);Number.isNaN(o)||(this.#l=o),this.#s=String(t)}get name(){return this.#s}get lastModified(){return this.#l}get[Symbol.toStringTag](){return"File"}static[Symbol.hasInstance](e){return!!e&&e instanceof o.A&&/^(File)$/.test(e[Symbol.toStringTag])}}},98823:(e,t,r)=>{"use strict";r.d(t,{default:()=>G});var o=r(37067),n=r(44708),i=r(16141),a=r(57075),l=r(4573);let s=function(e){if(!/^data:/i.test(e))throw TypeError('`uri` does not appear to be a Data URI (must begin with "data:")');let t=(e=e.replace(/\r?\n/g,"")).indexOf(",");if(-1===t||t<=4)throw TypeError("malformed data: URI");let r=e.substring(5,t).split(";"),o="",n=!1,i=r[0]||"text/plain",a=i;for(let e=1;e<r.length;e++)"base64"===r[e]?n=!0:r[e]&&(a+=`;${r[e]}`,0===r[e].indexOf("charset=")&&(o=r[e].substring(8)));r[0]||o.length||(a+=";charset=US-ASCII",o="US-ASCII");let l=n?"base64":"ascii",s=unescape(e.substring(t+1)),u=Buffer.from(s,l);return u.type=i,u.typeFull=a,u.charset=o,u};var u=r(57975),c=r(11055),d=r(41130);class f extends Error{constructor(e,t){super(e),Error.captureStackTrace(this,this.constructor),this.type=t}get name(){return this.constructor.name}get[Symbol.toStringTag](){return this.constructor.name}}class h extends f{constructor(e,t,r){super(e,t),r&&(this.code=this.errno=r.code,this.erroredSysCall=r.syscall)}}let p=Symbol.toStringTag,b=e=>"object"==typeof e&&"function"==typeof e.append&&"function"==typeof e.delete&&"function"==typeof e.get&&"function"==typeof e.getAll&&"function"==typeof e.has&&"function"==typeof e.set&&"function"==typeof e.sort&&"URLSearchParams"===e[p],y=e=>e&&"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&"function"==typeof e.constructor&&/^(Blob|File)$/.test(e[p]),m=e=>"object"==typeof e&&("AbortSignal"===e[p]||"EventTarget"===e[p]),_=(e,t)=>{let r=new URL(t).hostname,o=new URL(e).hostname;return r===o||r.endsWith(`.${o}`)},g=(e,t)=>new URL(t).protocol===new URL(e).protocol,v=(0,u.promisify)(a.pipeline),S=Symbol("Body internals");class w{constructor(e,{size:t=0}={}){let r=null;null===e?e=null:b(e)?e=l.Buffer.from(e.toString()):y(e)||l.Buffer.isBuffer(e)||(u.types.isAnyArrayBuffer(e)?e=l.Buffer.from(e):ArrayBuffer.isView(e)?e=l.Buffer.from(e.buffer,e.byteOffset,e.byteLength):e instanceof a||(e instanceof d.fS?r=(e=(0,d.$n)(e)).type.split("=")[1]:e=l.Buffer.from(String(e))));let o=e;l.Buffer.isBuffer(e)?o=a.Readable.from(e):y(e)&&(o=a.Readable.from(e.stream())),this[S]={body:e,stream:o,boundary:r,disturbed:!1,error:null},this.size=t,e instanceof a&&e.on("error",e=>{let t=e instanceof f?e:new h(`Invalid response body while trying to fetch ${this.url}: ${e.message}`,"system",e);this[S].error=t})}get body(){return this[S].stream}get bodyUsed(){return this[S].disturbed}async arrayBuffer(){let{buffer:e,byteOffset:t,byteLength:r}=await T(this);return e.slice(t,t+r)}async formData(){let e=this.headers.get("content-type");if(e.startsWith("application/x-www-form-urlencoded")){let e=new d.fS;for(let[t,r]of new URLSearchParams(await this.text()))e.append(t,r);return e}let{toFormData:t}=await r.e(303).then(r.bind(r,39303));return t(this.body,e)}async blob(){let e=this.headers&&this.headers.get("content-type")||this[S].body&&this[S].body.type||"",t=await this.arrayBuffer();return new c.A([t],{type:e})}async json(){return JSON.parse(await this.text())}async text(){let e=await T(this);return new TextDecoder().decode(e)}buffer(){return T(this)}}async function T(e){if(e[S].disturbed)throw TypeError(`body used already for: ${e.url}`);if(e[S].disturbed=!0,e[S].error)throw e[S].error;let{body:t}=e;if(null===t||!(t instanceof a))return l.Buffer.alloc(0);let r=[],o=0;try{for await(let n of t){if(e.size>0&&o+n.length>e.size){let r=new h(`content size at ${e.url} over limit: ${e.size}`,"max-size");throw t.destroy(r),r}o+=n.length,r.push(n)}}catch(t){throw t instanceof f?t:new h(`Invalid response body while trying to fetch ${e.url}: ${t.message}`,"system",t)}if(!0===t.readableEnded||!0===t._readableState.ended)try{if(r.every(e=>"string"==typeof e))return l.Buffer.from(r.join(""));return l.Buffer.concat(r,o)}catch(t){throw new h(`Could not create Buffer from response body for ${e.url}: ${t.message}`,"system",t)}else throw new h(`Premature close of server response while trying to fetch ${e.url}`)}w.prototype.buffer=(0,u.deprecate)(w.prototype.buffer,"Please use 'response.arrayBuffer()' instead of 'response.buffer()'","node-fetch#buffer"),Object.defineProperties(w.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0},data:{get:(0,u.deprecate)(()=>{},"data doesn't exist, use json(), text(), arrayBuffer(), or body instead","https://github.com/node-fetch/node-fetch/issues/1000 (response)")}});let R=(e,t)=>{let r,o;let{body:n}=e[S];if(e.bodyUsed)throw Error("cannot clone body after it is used");return n instanceof a&&"function"!=typeof n.getBoundary&&(r=new a.PassThrough({highWaterMark:t}),o=new a.PassThrough({highWaterMark:t}),n.pipe(r),n.pipe(o),e[S].stream=r,n=o),n},P=(0,u.deprecate)(e=>e.getBoundary(),"form-data doesn't follow the spec and requires special treatment. Use alternative package","https://github.com/node-fetch/node-fetch/issues/1167"),E=(e,t)=>null===e?null:"string"==typeof e?"text/plain;charset=UTF-8":b(e)?"application/x-www-form-urlencoded;charset=UTF-8":y(e)?e.type||null:l.Buffer.isBuffer(e)||u.types.isAnyArrayBuffer(e)||ArrayBuffer.isView(e)?null:e instanceof d.fS?`multipart/form-data; boundary=${t[S].boundary}`:e&&"function"==typeof e.getBoundary?`multipart/form-data;boundary=${P(e)}`:e instanceof a?null:"text/plain;charset=UTF-8",C=e=>{let{body:t}=e[S];return null===t?0:y(t)?t.size:l.Buffer.isBuffer(t)?t.length:t&&"function"==typeof t.getLengthSync&&t.hasKnownLength&&t.hasKnownLength()?t.getLengthSync():null},q=async(e,{body:t})=>{null===t?e.end():await v(t,e)},A="function"==typeof o.validateHeaderName?o.validateHeaderName:e=>{if(!/^[\^`\-\w!#$%&'*+.|~]+$/.test(e)){let t=TypeError(`Header name must be a valid HTTP token [${e}]`);throw Object.defineProperty(t,"code",{value:"ERR_INVALID_HTTP_TOKEN"}),t}},k="function"==typeof o.validateHeaderValue?o.validateHeaderValue:(e,t)=>{if(/[^\t\u0020-\u007E\u0080-\u00FF]/.test(t)){let t=TypeError(`Invalid character in header content ["${e}"]`);throw Object.defineProperty(t,"code",{value:"ERR_INVALID_CHAR"}),t}};class j extends URLSearchParams{constructor(e){let t=[];if(e instanceof j)for(let[r,o]of Object.entries(e.raw()))t.push(...o.map(e=>[r,e]));else if(null==e);else if("object"!=typeof e||u.types.isBoxedPrimitive(e))throw TypeError("Failed to construct 'Headers': The provided value is not of type '(sequence<sequence<ByteString>> or record<ByteString, ByteString>)");else{let r=e[Symbol.iterator];if(null==r)t.push(...Object.entries(e));else{if("function"!=typeof r)throw TypeError("Header pairs must be iterable");t=[...e].map(e=>{if("object"!=typeof e||u.types.isBoxedPrimitive(e))throw TypeError("Each header pair must be an iterable object");return[...e]}).map(e=>{if(2!==e.length)throw TypeError("Each header pair must be a name/value tuple");return[...e]})}}return super(t=t.length>0?t.map(([e,t])=>(A(e),k(e,String(t)),[String(e).toLowerCase(),String(t)])):void 0),new Proxy(this,{get(e,t,r){switch(t){case"append":case"set":return(r,o)=>(A(r),k(r,String(o)),URLSearchParams.prototype[t].call(e,String(r).toLowerCase(),String(o)));case"delete":case"has":case"getAll":return r=>(A(r),URLSearchParams.prototype[t].call(e,String(r).toLowerCase()));case"keys":return()=>(e.sort(),new Set(URLSearchParams.prototype.keys.call(e)).keys());default:return Reflect.get(e,t,r)}}})}get[Symbol.toStringTag](){return this.constructor.name}toString(){return Object.prototype.toString.call(this)}get(e){let t=this.getAll(e);if(0===t.length)return null;let r=t.join(", ");return/^content-encoding$/i.test(e)&&(r=r.toLowerCase()),r}forEach(e,t){for(let r of this.keys())Reflect.apply(e,t,[this.get(r),r,this])}*values(){for(let e of this.keys())yield this.get(e)}*entries(){for(let e of this.keys())yield[e,this.get(e)]}[Symbol.iterator](){return this.entries()}raw(){return[...this.keys()].reduce((e,t)=>(e[t]=this.getAll(t),e),{})}[Symbol.for("nodejs.util.inspect.custom")](){return[...this.keys()].reduce((e,t)=>{let r=this.getAll(t);return"host"===t?e[t]=r[0]:e[t]=r.length>1?r:r[0],e},{})}}Object.defineProperties(j.prototype,["get","entries","forEach","values"].reduce((e,t)=>(e[t]={enumerable:!0},e),{}));let O=new Set([301,302,303,307,308]),B=e=>O.has(e),W=Symbol("Response internals");class z extends w{constructor(e=null,t={}){super(e,t);let r=null!=t.status?t.status:200,o=new j(t.headers);if(null!==e&&!o.has("Content-Type")){let t=E(e,this);t&&o.append("Content-Type",t)}this[W]={type:"default",url:t.url,status:r,statusText:t.statusText||"",headers:o,counter:t.counter,highWaterMark:t.highWaterMark}}get type(){return this[W].type}get url(){return this[W].url||""}get status(){return this[W].status}get ok(){return this[W].status>=200&&this[W].status<300}get redirected(){return this[W].counter>0}get statusText(){return this[W].statusText}get headers(){return this[W].headers}get highWaterMark(){return this[W].highWaterMark}clone(){return new z(R(this,this.highWaterMark),{type:this.type,url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected,size:this.size,highWaterMark:this.highWaterMark})}static redirect(e,t=302){if(!B(t))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');return new z(null,{headers:{location:new URL(e).toString()},status:t})}static error(){let e=new z(null,{status:0,statusText:""});return e[W].type="error",e}static json(e,t={}){let r=JSON.stringify(e);if(void 0===r)throw TypeError("data is not JSON serializable");let o=new j(t&&t.headers);return o.has("content-type")||o.set("content-type","application/json"),new z(r,{...t,headers:o})}get[Symbol.toStringTag](){return"Response"}}Object.defineProperties(z.prototype,{type:{enumerable:!0},url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}});var $=r(73136);let L=e=>{if(e.search)return e.search;let t=e.href.length-1,r=e.hash||("#"===e.href[t]?"#":"");return"?"===e.href[t-r.length]?"?":""};var F=r(77030);function I(e,t=!1){return null==e?"no-referrer":(e=new URL(e),/^(about|blob|data):$/.test(e.protocol))?"no-referrer":(e.username="",e.password="",e.hash="",t&&(e.pathname="",e.search=""),e)}let M=new Set(["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"]);function x(e){return!!(/^about:(blank|srcdoc)$/.test(e)||"data:"===e.protocol||/^(blob|filesystem):$/.test(e.protocol))||function(e){if(/^(http|ws)s:$/.test(e.protocol))return!0;let t=e.host.replace(/(^\[)|(]$)/g,""),r=(0,F.isIP)(t);return!!(4===r&&/^127\./.test(t)||6===r&&/^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(t))||!("localhost"===e.host||e.host.endsWith(".localhost"))&&"file:"===e.protocol}(e)}let U=Symbol("Request internals"),D=e=>"object"==typeof e&&"object"==typeof e[U],N=(0,u.deprecate)(()=>{},".data is not a valid RequestInit property, use .body instead","https://github.com/node-fetch/node-fetch/issues/1000 (request)");class H extends w{constructor(e,t={}){let r;if(D(e)?r=new URL(e.url):(r=new URL(e),e={}),""!==r.username||""!==r.password)throw TypeError(`${r} is an url with embedded credentials.`);let o=t.method||e.method||"GET";if(/^(delete|get|head|options|post|put)$/i.test(o)&&(o=o.toUpperCase()),!D(t)&&"data"in t&&N(),(null!=t.body||D(e)&&null!==e.body)&&("GET"===o||"HEAD"===o))throw TypeError("Request with GET/HEAD method cannot have body");let n=t.body?t.body:D(e)&&null!==e.body?R(e):null;super(n,{size:t.size||e.size||0});let i=new j(t.headers||e.headers||{});if(null!==n&&!i.has("Content-Type")){let e=E(n,this);e&&i.set("Content-Type",e)}let a=D(e)?e.signal:null;if("signal"in t&&(a=t.signal),null!=a&&!m(a))throw TypeError("Expected signal to be an instanceof AbortSignal or EventTarget");let l=null==t.referrer?e.referrer:t.referrer;if(""===l)l="no-referrer";else if(l){let e=new URL(l);l=/^about:(\/\/)?client$/.test(e)?"client":e}else l=void 0;this[U]={method:o,redirect:t.redirect||e.redirect||"follow",headers:i,parsedURL:r,signal:a,referrer:l},this.follow=void 0===t.follow?void 0===e.follow?20:e.follow:t.follow,this.compress=void 0===t.compress?void 0===e.compress||e.compress:t.compress,this.counter=t.counter||e.counter||0,this.agent=t.agent||e.agent,this.highWaterMark=t.highWaterMark||e.highWaterMark||16384,this.insecureHTTPParser=t.insecureHTTPParser||e.insecureHTTPParser||!1,this.referrerPolicy=t.referrerPolicy||e.referrerPolicy||""}get method(){return this[U].method}get url(){return(0,$.format)(this[U].parsedURL)}get headers(){return this[U].headers}get redirect(){return this[U].redirect}get signal(){return this[U].signal}get referrer(){return"no-referrer"===this[U].referrer?"":"client"===this[U].referrer?"about:client":this[U].referrer?this[U].referrer.toString():void 0}get referrerPolicy(){return this[U].referrerPolicy}set referrerPolicy(e){this[U].referrerPolicy=function(e){if(!M.has(e))throw TypeError(`Invalid referrerPolicy: ${e}`);return e}(e)}clone(){return new H(this)}get[Symbol.toStringTag](){return"Request"}}Object.defineProperties(H.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0},referrer:{enumerable:!0},referrerPolicy:{enumerable:!0}});let V=e=>{let{parsedURL:t}=e[U],r=new j(e[U].headers);r.has("Accept")||r.set("Accept","*/*");let o=null;if(null===e.body&&/^(post|put)$/i.test(e.method)&&(o="0"),null!==e.body){let t=C(e);"number"!=typeof t||Number.isNaN(t)||(o=String(t))}o&&r.set("Content-Length",o),""===e.referrerPolicy&&(e.referrerPolicy="strict-origin-when-cross-origin"),e.referrer&&"no-referrer"!==e.referrer?e[U].referrer=function(e,{referrerURLCallback:t,referrerOriginCallback:r}={}){if("no-referrer"===e.referrer||""===e.referrerPolicy)return null;let o=e.referrerPolicy;if("about:client"===e.referrer)return"no-referrer";let n=e.referrer,i=I(n),a=I(n,!0);i.toString().length>4096&&(i=a),t&&(i=t(i)),r&&(a=r(a));let l=new URL(e.url);switch(o){case"no-referrer":return"no-referrer";case"origin":return a;case"unsafe-url":return i;case"strict-origin":if(x(i)&&!x(l))return"no-referrer";return a.toString();case"strict-origin-when-cross-origin":if(i.origin===l.origin)return i;if(x(i)&&!x(l))return"no-referrer";return a;case"same-origin":if(i.origin===l.origin)return i;return"no-referrer";case"origin-when-cross-origin":if(i.origin===l.origin)return i;return a;case"no-referrer-when-downgrade":if(x(i)&&!x(l))return"no-referrer";return i;default:throw TypeError(`Invalid referrerPolicy: ${o}`)}}(e):e[U].referrer="no-referrer",e[U].referrer instanceof URL&&r.set("Referer",e.referrer),r.has("User-Agent")||r.set("User-Agent","node-fetch"),e.compress&&!r.has("Accept-Encoding")&&r.set("Accept-Encoding","gzip, deflate, br");let{agent:n}=e;"function"==typeof n&&(n=n(t));let i=L(t),a={path:t.pathname+i,method:e.method,headers:r[Symbol.for("nodejs.util.inspect.custom")](),insecureHTTPParser:e.insecureHTTPParser,agent:n};return{parsedURL:t,options:a}};class Y extends f{constructor(e,t="aborted"){super(e,t)}}r(56177);let Q=new Set(["data:","http:","https:"]);async function G(e,t){return new Promise((r,u)=>{let c=new H(e,t),{parsedURL:d,options:f}=V(c);if(!Q.has(d.protocol))throw TypeError(`node-fetch cannot load ${e}. URL scheme "${d.protocol.replace(/:$/,"")}" is not supported.`);if("data:"===d.protocol){let e=s(c.url);r(new z(e,{headers:{"Content-Type":e.typeFull}}));return}let p=("https:"===d.protocol?n:o).request,{signal:b}=c,y=null,m=()=>{let e=new Y("The operation was aborted.");u(e),c.body&&c.body instanceof a.Readable&&c.body.destroy(e),y&&y.body&&y.body.emit("error",e)};if(b&&b.aborted){m();return}let v=()=>{m(),w()},S=p(d.toString(),f);b&&b.addEventListener("abort",v);let w=()=>{S.abort(),b&&b.removeEventListener("abort",v)};S.on("error",e=>{u(new h(`request to ${c.url} failed, reason: ${e.message}`,"system",e)),w()}),function(e,t){let r;let o=l.Buffer.from("0\r\n\r\n"),n=!1,i=!1;e.on("response",e=>{let{headers:t}=e;n="chunked"===t["transfer-encoding"]&&!t["content-length"]}),e.on("socket",a=>{let s=()=>{if(n&&!i){let e=Error("Premature close");e.code="ERR_STREAM_PREMATURE_CLOSE",t(e)}},u=e=>{(i=0===l.Buffer.compare(e.slice(-5),o))||!r||(i=0===l.Buffer.compare(r.slice(-3),o.slice(0,3))&&0===l.Buffer.compare(e.slice(-2),o.slice(3))),r=e};a.prependListener("close",s),a.on("data",u),e.on("close",()=>{a.removeListener("close",s),a.removeListener("data",u)})})}(S,e=>{y&&y.body&&y.body.destroy(e)}),process.version<"v14"&&S.on("socket",e=>{let t;e.prependListener("end",()=>{t=e._eventsCount}),e.prependListener("close",r=>{if(y&&t<e._eventsCount&&!r){let e=Error("Premature close");e.code="ERR_STREAM_PREMATURE_CLOSE",y.body.emit("error",e)}})}),S.on("response",e=>{S.setTimeout(0);let o=function(e=[]){return new j(e.reduce((e,t,r,o)=>(r%2==0&&e.push(o.slice(r,r+2)),e),[]).filter(([e,t])=>{try{return A(e),k(e,String(t)),!0}catch{return!1}}))}(e.rawHeaders);if(B(e.statusCode)){let n=o.get("Location"),i=null;try{i=null===n?null:new URL(n,c.url)}catch{if("manual"!==c.redirect){u(new h(`uri requested responds with an invalid redirect URL: ${n}`,"invalid-redirect")),w();return}}switch(c.redirect){case"error":u(new h(`uri requested responds with a redirect, redirect mode is set to error: ${c.url}`,"no-redirect")),w();return;case"manual":break;case"follow":{if(null===i)break;if(c.counter>=c.follow){u(new h(`maximum redirect reached at: ${c.url}`,"max-redirect")),w();return}let n={headers:new j(c.headers),follow:c.follow,counter:c.counter+1,agent:c.agent,compress:c.compress,method:c.method,body:R(c),signal:c.signal,size:c.size,referrer:c.referrer,referrerPolicy:c.referrerPolicy};if(!_(c.url,i)||!g(c.url,i))for(let e of["authorization","www-authenticate","cookie","cookie2"])n.headers.delete(e);if(303!==e.statusCode&&c.body&&t.body instanceof a.Readable){u(new h("Cannot follow redirect with body being a readable stream","unsupported-redirect")),w();return}(303===e.statusCode||(301===e.statusCode||302===e.statusCode)&&"POST"===c.method)&&(n.method="GET",n.body=void 0,n.headers.delete("content-length"));let l=function(e){let t=(e.get("referrer-policy")||"").split(/[,\s]+/),r="";for(let e of t)e&&M.has(e)&&(r=e);return r}(o);l&&(n.referrerPolicy=l),r(G(new H(i,n))),w();return}default:return u(TypeError(`Redirect option '${c.redirect}' is not a valid value of RequestRedirect`))}}b&&e.once("end",()=>{b.removeEventListener("abort",v)});let n=(0,a.pipeline)(e,new a.PassThrough,e=>{e&&u(e)});process.version<"v12.10"&&e.on("aborted",v);let l={url:c.url,status:e.statusCode,statusText:e.statusMessage,headers:o,size:c.size,counter:c.counter,highWaterMark:c.highWaterMark},s=o.get("Content-Encoding");if(!c.compress||"HEAD"===c.method||null===s||204===e.statusCode||304===e.statusCode){r(y=new z(n,l));return}let d={flush:i.Z_SYNC_FLUSH,finishFlush:i.Z_SYNC_FLUSH};if("gzip"===s||"x-gzip"===s){r(y=new z(n=(0,a.pipeline)(n,i.createGunzip(d),e=>{e&&u(e)}),l));return}if("deflate"===s||"x-deflate"===s){let t=(0,a.pipeline)(e,new a.PassThrough,e=>{e&&u(e)});t.once("data",e=>{r(y=new z(n=(15&e[0])==8?(0,a.pipeline)(n,i.createInflate(),e=>{e&&u(e)}):(0,a.pipeline)(n,i.createInflateRaw(),e=>{e&&u(e)}),l))}),t.once("end",()=>{y||r(y=new z(n,l))});return}if("br"===s){r(y=new z(n=(0,a.pipeline)(n,i.createBrotliDecompress(),e=>{e&&u(e)}),l));return}r(y=new z(n,l))}),q(S,c).catch(u)})}}};