"use client";

import React, { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Check, X, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CryptoInputProps {
  label: string;
  value: string;
  allowedCryptos: string[];
  onValidCrypto: (crypto: string) => void;
  placeholder?: string;
  description?: string;
  className?: string;
}

export function CryptoInput({
  label,
  value,
  allowedCryptos,
  onValidCrypto,
  placeholder = "Enter crypto symbol",
  description,
  className
}: CryptoInputProps) {
  const [inputValue, setInputValue] = useState('');
  const [validationState, setValidationState] = useState<'idle' | 'valid' | 'invalid'>('idle');
  const [errorMessage, setErrorMessage] = useState('');
  const [selectedCrypto, setSelectedCrypto] = useState<string>('');

  const handleCheck = () => {
    const upperCaseInput = inputValue.toUpperCase().trim();

    if (!upperCaseInput) {
      setValidationState('invalid');
      setErrorMessage('Please enter a crypto symbol');
      return;
    }

    if (!allowedCryptos || !Array.isArray(allowedCryptos)) {
      setValidationState('invalid');
      setErrorMessage('No allowed cryptocurrencies configured');
      return;
    }

    if (allowedCryptos.includes(upperCaseInput)) {
      setValidationState('valid');
      setErrorMessage('');
      setSelectedCrypto(upperCaseInput);
      onValidCrypto(upperCaseInput);
    } else {
      setValidationState('invalid');
      setErrorMessage('Not available');
      setSelectedCrypto('');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    setValidationState('idle');
    setErrorMessage('');
    // Clear selected crypto when user starts typing again
    if (selectedCrypto) {
      setSelectedCrypto('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleCheck();
    }
  };

  const getValidationIcon = () => {
    switch (validationState) {
      case 'valid':
        return <Check className="h-4 w-4 text-green-500" />;
      case 'invalid':
        return <X className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getValidationColor = () => {
    switch (validationState) {
      case 'valid':
        return 'border-green-500';
      case 'invalid':
        return 'border-red-500';
      default:
        return '';
    }
  };

  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor={`crypto-input-${label}`}>{label}</Label>
      <div className="flex space-x-2">
        <div className="flex-1 relative">
          <Input
            id={`crypto-input-${label}`}
            value={inputValue}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            className={cn("pr-8", getValidationColor())}
          />
          {validationState !== 'idle' && (
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
              {getValidationIcon()}
            </div>
          )}
        </div>
        <Button 
          onClick={handleCheck}
          variant="outline"
          className="btn-neo"
          disabled={!inputValue.trim()}
        >
          Check
        </Button>
      </div>
      
      {/* Current selection display - only show after successful validation */}
      {selectedCrypto && (
        <div className="flex items-center space-x-2 text-sm">
          <Check className="h-4 w-4 text-green-500" />
          <span className="text-green-600 font-medium">Selected: {selectedCrypto}</span>
        </div>
      )}
      
      {/* Error message */}
      {validationState === 'invalid' && errorMessage && (
        <div className="flex items-start space-x-2 text-sm text-red-600">
          <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
          <span>{errorMessage}</span>
        </div>
      )}
      
      {/* Description */}
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
      
      {/* Available cryptos count */}
      <p className="text-xs text-muted-foreground">
        {allowedCryptos && Array.isArray(allowedCryptos) ? allowedCryptos.length : 0} cryptocurrencies available
      </p>
    </div>
  );
}
