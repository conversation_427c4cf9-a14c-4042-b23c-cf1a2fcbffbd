"use client";

import type { ReactNode } from 'react';
import React, { createContext, useContext, useState } from 'react';

// Define types locally to avoid server import issues
export interface TradingModeSuggestionInput {
  riskTolerance: string;
  preferredCryptocurrencies: string;
  investmentGoals: string;
}

export interface TradingModeSuggestionOutput {
  suggestedMode: 'Simple Spot' | 'Stablecoin Swap';
  reason: string;
}

interface AIContextType {
  suggestion: TradingModeSuggestionOutput | null;
  isLoading: boolean;
  error: string | null;
  getTradingModeSuggestion: (input: TradingModeSuggestionInput) => Promise<void>;
}

const AIContext = createContext<AIContextType | undefined>(undefined);

export const AIProvider = ({ children }: { children: ReactNode }) => {
  const [suggestion, setSuggestion] = useState<TradingModeSuggestionOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getTradingModeSuggestion = async (input: TradingModeSuggestionInput) => {
    setIsLoading(true);
    setError(null);
    setSuggestion(null);
    try {
      // For now, provide a mock response to avoid server import issues
      // TODO: Implement proper API call to server action
      const result: TradingModeSuggestionOutput = {
        suggestedMode: input.riskTolerance === 'low' ? 'Stablecoin Swap' : 'Simple Spot',
        reason: input.riskTolerance === 'low'
          ? 'Based on your low risk tolerance, Stablecoin Swap mode is recommended for more stable returns.'
          : 'Based on your risk profile, Simple Spot mode offers better profit potential for active trading.'
      };
      setSuggestion(result);
    } catch (e) {
      setError(e instanceof Error ? e.message : "An unknown error occurred during AI suggestion.");
      console.error("Error fetching trading mode suggestion:", e);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AIContext.Provider value={{ suggestion, isLoading, error, getTradingModeSuggestion }}>
      {children}
    </AIContext.Provider>
  );
};

export const useAIContext = (): AIContextType => {
  const context = useContext(AIContext);
  if (context === undefined) {
    throw new Error('useAIContext must be used within an AIProvider');
  }
  return context;
};
