"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_en"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/environment-name-label/environment-name-label.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/environment-name-label/environment-name-label.js ***!
  \******************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ENVIRONMENT_NAME_LABEL_STYLES: function() {\n        return ENVIRONMENT_NAME_LABEL_STYLES;\n    },\n    EnvironmentNameLabel: function() {\n        return EnvironmentNameLabel;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nfunction EnvironmentNameLabel(param) {\n    let { environmentName } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n        \"data-nextjs-environment-name-label\": true,\n        children: environmentName\n    });\n}\n_c = EnvironmentNameLabel;\nconst ENVIRONMENT_NAME_LABEL_STYLES = \"\\n  [data-nextjs-environment-name-label] {\\n    padding: 2px 6px;\\n    margin: 0;\\n    border-radius: var(--rounded-md-2);\\n    background: var(--color-gray-100);\\n    font-weight: 600;\\n    font-size: var(--size-12);\\n    color: var(--color-gray-900);\\n    font-family: var(--font-stack-monospace);\\n    line-height: var(--size-20);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=environment-name-label.js.map\nvar _c;\n$RefreshReg$(_c, \"EnvironmentNameLabel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9lcnJvcnMvZW52aXJvbm1lbnQtbmFtZS1sYWJlbC9lbnZpcm9ubWVudC1uYW1lLWxhYmVsLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQVFhQSw2QkFBNkI7ZUFBN0JBOztJQVJHQyxvQkFBb0I7ZUFBcEJBOzs7O0FBQVQsOEJBQThCLEtBSXBDO0lBSm9DLE1BQ25DQyxlQUFlLEVBR2hCLEdBSm9DO0lBS25DLHFCQUFPLHFCQUFDQyxRQUFBQTtRQUFLQyxvQ0FBa0M7a0JBQUVGOztBQUNuRDtLQU5nQkQ7QUFRVCxNQUFNRCxnQ0FBaUMiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xccmVhY3QtZGV2LW92ZXJsYXlcXHVpXFxjb21wb25lbnRzXFxlcnJvcnNcXGVudmlyb25tZW50LW5hbWUtbGFiZWxcXGVudmlyb25tZW50LW5hbWUtbGFiZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBFbnZpcm9ubWVudE5hbWVMYWJlbCh7XG4gIGVudmlyb25tZW50TmFtZSxcbn06IHtcbiAgZW52aXJvbm1lbnROYW1lOiBzdHJpbmdcbn0pIHtcbiAgcmV0dXJuIDxzcGFuIGRhdGEtbmV4dGpzLWVudmlyb25tZW50LW5hbWUtbGFiZWw+e2Vudmlyb25tZW50TmFtZX08L3NwYW4+XG59XG5cbmV4cG9ydCBjb25zdCBFTlZJUk9OTUVOVF9OQU1FX0xBQkVMX1NUWUxFUyA9IGBcbiAgW2RhdGEtbmV4dGpzLWVudmlyb25tZW50LW5hbWUtbGFiZWxdIHtcbiAgICBwYWRkaW5nOiAycHggNnB4O1xuICAgIG1hcmdpbjogMDtcbiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1yb3VuZGVkLW1kLTIpO1xuICAgIGJhY2tncm91bmQ6IHZhcigtLWNvbG9yLWdyYXktMTAwKTtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICAgIGZvbnQtc2l6ZTogdmFyKC0tc2l6ZS0xMik7XG4gICAgY29sb3I6IHZhcigtLWNvbG9yLWdyYXktOTAwKTtcbiAgICBmb250LWZhbWlseTogdmFyKC0tZm9udC1zdGFjay1tb25vc3BhY2UpO1xuICAgIGxpbmUtaGVpZ2h0OiB2YXIoLS1zaXplLTIwKTtcbiAgfVxuYFxuIl0sIm5hbWVzIjpbIkVOVklST05NRU5UX05BTUVfTEFCRUxfU1RZTEVTIiwiRW52aXJvbm1lbnROYW1lTGFiZWwiLCJlbnZpcm9ubWVudE5hbWUiLCJzcGFuIiwiZGF0YS1uZXh0anMtZW52aXJvbm1lbnQtbmFtZS1sYWJlbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/environment-name-label/environment-name-label.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-message/error-message.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-message/error-message.js ***!
  \************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorMessage: function() {\n        return ErrorMessage;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction ErrorMessage(param) {\n    let { errorMessage } = param;\n    const [isExpanded, setIsExpanded] = (0, _react.useState)(false);\n    const [shouldTruncate, setShouldTruncate] = (0, _react.useState)(false);\n    const messageRef = (0, _react.useRef)(null);\n    (0, _react.useLayoutEffect)(()=>{\n        if (messageRef.current) {\n            setShouldTruncate(messageRef.current.scrollHeight > 200);\n        }\n    }, [\n        errorMessage\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        className: \"nextjs__container_errors_wrapper\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                ref: messageRef,\n                id: \"nextjs__container_errors_desc\",\n                className: \"nextjs__container_errors_desc \" + (shouldTruncate && !isExpanded ? 'truncated' : ''),\n                children: errorMessage\n            }),\n            shouldTruncate && !isExpanded && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                        className: \"nextjs__container_errors_gradient_overlay\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                        onClick: ()=>setIsExpanded(true),\n                        className: \"nextjs__container_errors_expand_button\",\n                        \"aria-expanded\": isExpanded,\n                        \"aria-controls\": \"nextjs__container_errors_desc\",\n                        children: \"Show More\"\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c = ErrorMessage;\nconst styles = \"\\n  .nextjs__container_errors_wrapper {\\n    position: relative;\\n  }\\n\\n  .nextjs__container_errors_desc {\\n    margin: 0;\\n    margin-left: 4px;\\n    color: var(--color-red-900);\\n    font-weight: 500;\\n    font-size: var(--size-16);\\n    letter-spacing: -0.32px;\\n    line-height: var(--size-24);\\n    overflow-wrap: break-word;\\n    white-space: pre-wrap;\\n  }\\n\\n  .nextjs__container_errors_desc.truncated {\\n    max-height: 200px;\\n    overflow: hidden;\\n  }\\n\\n  .nextjs__container_errors_gradient_overlay {\\n    position: absolute;\\n    bottom: 0;\\n    left: 0;\\n    right: 0;\\n    height: 85px;\\n    background: linear-gradient(\\n      180deg,\\n      rgba(250, 250, 250, 0) 0%,\\n      var(--color-background-100) 100%\\n    );\\n  }\\n\\n  .nextjs__container_errors_expand_button {\\n    position: absolute;\\n    bottom: 10px;\\n    left: 50%;\\n    transform: translateX(-50%);\\n    display: flex;\\n    align-items: center;\\n    padding: 6px 8px;\\n    background: var(--color-background-100);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    border-radius: 999px;\\n    box-shadow:\\n      0px 2px 2px var(--color-gray-alpha-100),\\n      0px 8px 8px -8px var(--color-gray-alpha-100);\\n    font-size: var(--size-13);\\n    cursor: pointer;\\n    color: var(--color-gray-900);\\n    font-weight: 500;\\n    transition: background-color 0.2s ease;\\n  }\\n\\n  .nextjs__container_errors_expand_button:hover {\\n    background: var(--color-gray-100);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-message.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-message/error-message.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-bottom-stack/index.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-bottom-stack/index.js ***!
  \*****************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorOverlayBottomStack: function() {\n        return ErrorOverlayBottomStack;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nfunction ErrorOverlayBottomStack(param) {\n    let { errorCount, activeIdx } = param;\n    // If there are more than 2 errors to navigate, the stack count should remain at 2.\n    const stackCount = Math.min(errorCount - activeIdx - 1, 2);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        \"aria-hidden\": true,\n        className: \"error-overlay-bottom-stack\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n            className: \"error-overlay-bottom-stack-stack\",\n            \"data-stack-count\": stackCount,\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                    className: \"error-overlay-bottom-stack-layer error-overlay-bottom-stack-layer-1\",\n                    children: \"1\"\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                    className: \"error-overlay-bottom-stack-layer error-overlay-bottom-stack-layer-2\",\n                    children: \"2\"\n                })\n            ]\n        })\n    });\n}\n_c = ErrorOverlayBottomStack;\nconst styles = \"\\n  .error-overlay-bottom-stack-layer {\\n    width: 100%;\\n    height: var(--stack-layer-height);\\n    position: relative;\\n    border: 1px solid var(--color-gray-400);\\n    border-radius: var(--rounded-xl);\\n    background: var(--color-background-200);\\n    transition:\\n      translate 350ms var(--timing-swift),\\n      box-shadow 350ms var(--timing-swift);\\n  }\\n\\n  .error-overlay-bottom-stack-layer-1 {\\n    width: calc(100% - var(--size-24));\\n  }\\n\\n  .error-overlay-bottom-stack-layer-2 {\\n    width: calc(100% - var(--size-48));\\n    z-index: -1;\\n  }\\n\\n  .error-overlay-bottom-stack {\\n    width: 100%;\\n    position: absolute;\\n    bottom: -1px;\\n    height: 0;\\n    overflow: visible;\\n  }\\n\\n  .error-overlay-bottom-stack-stack {\\n    --stack-layer-height: 44px;\\n    --stack-layer-height-half: calc(var(--stack-layer-height) / 2);\\n    --stack-layer-trim: 13px;\\n    --shadow: 0px 0.925px 0.925px 0px rgba(0, 0, 0, 0.02),\\n      0px 3.7px 7.4px -3.7px rgba(0, 0, 0, 0.04),\\n      0px 14.8px 22.2px -7.4px rgba(0, 0, 0, 0.06);\\n\\n    display: grid;\\n    place-items: center center;\\n    width: 100%;\\n    position: fixed;\\n    overflow: hidden;\\n    z-index: -1;\\n    max-width: var(--next-dialog-max-width);\\n\\n    .error-overlay-bottom-stack-layer {\\n      grid-area: 1 / 1;\\n      /* Hide */\\n      translate: 0 calc(var(--stack-layer-height) * -1);\\n    }\\n\\n    &[data-stack-count='1'],\\n    &[data-stack-count='2'] {\\n      .error-overlay-bottom-stack-layer-1 {\\n        translate: 0\\n          calc(var(--stack-layer-height-half) * -1 - var(--stack-layer-trim));\\n      }\\n    }\\n\\n    &[data-stack-count='2'] {\\n      .error-overlay-bottom-stack-layer-2 {\\n        translate: 0 calc(var(--stack-layer-trim) * -1 * 2);\\n      }\\n    }\\n\\n    /* Only the bottom stack should have the shadow */\\n    &[data-stack-count='1'] .error-overlay-bottom-stack-layer-1 {\\n      box-shadow: var(--shadow);\\n    }\\n\\n    &[data-stack-count='2'] {\\n      .error-overlay-bottom-stack-layer-2 {\\n        box-shadow: var(--shadow);\\n      }\\n    }\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayBottomStack\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-bottom-stack/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.js ***!
  \***********************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorFeedback: function() {\n        return ErrorFeedback;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _thumbsup = __webpack_require__(/*! ../../../../icons/thumbs/thumbs-up */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/thumbs/thumbs-up.js\");\nconst _thumbsdown = __webpack_require__(/*! ../../../../icons/thumbs/thumbs-down */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/thumbs/thumbs-down.js\");\nconst _cx = __webpack_require__(/*! ../../../../utils/cx */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js\");\nfunction ErrorFeedback(param) {\n    let { errorCode, className } = param;\n    const [votedMap, setVotedMap] = (0, _react.useState)({});\n    const voted = votedMap[errorCode];\n    const hasVoted = voted !== undefined;\n    const disabled = false;\n    const handleFeedback = (0, _react.useCallback)(async (wasHelpful)=>{\n        // Optimistically set feedback state without loading/error states to keep implementation simple\n        setVotedMap((prev)=>({\n                ...prev,\n                [errorCode]: wasHelpful\n            }));\n        try {\n            const response = await fetch(( false || '') + \"/__nextjs_error_feedback?\" + new URLSearchParams({\n                errorCode,\n                wasHelpful: wasHelpful.toString()\n            }));\n            if (!response.ok) {\n                // Handle non-2xx HTTP responses here if needed\n                console.error('Failed to record feedback on the server.');\n            }\n        } catch (error) {\n            console.error('Failed to record feedback:', error);\n        }\n    }, [\n        errorCode\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        className: (0, _cx.cx)('error-feedback', className),\n        role: \"region\",\n        \"aria-label\": \"Error feedback\",\n        children: hasVoted ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n            className: \"error-feedback-thanks\",\n            role: \"status\",\n            \"aria-live\": \"polite\",\n            children: \"Thanks for your feedback!\"\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        href: \"https://nextjs.org/telemetry#error-feedback\",\n                        rel: \"noopener noreferrer\",\n                        target: \"_blank\",\n                        children: \"Was this helpful?\"\n                    })\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                    \"aria-disabled\": disabled ? 'true' : undefined,\n                    \"aria-label\": \"Mark as helpful\",\n                    onClick: disabled ? undefined : ()=>handleFeedback(true),\n                    className: (0, _cx.cx)('feedback-button', voted === true && 'voted'),\n                    title: disabled ? 'Feedback disabled due to setting NEXT_TELEMETRY_DISABLED' : undefined,\n                    type: \"button\",\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_thumbsup.ThumbsUp, {\n                        \"aria-hidden\": \"true\"\n                    })\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                    \"aria-disabled\": disabled ? 'true' : undefined,\n                    \"aria-label\": \"Mark as not helpful\",\n                    onClick: disabled ? undefined : ()=>handleFeedback(false),\n                    className: (0, _cx.cx)('feedback-button', voted === false && 'voted'),\n                    title: disabled ? 'Feedback disabled due to setting NEXT_TELEMETRY_DISABLED' : undefined,\n                    type: \"button\",\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_thumbsdown.ThumbsDown, {\n                        \"aria-hidden\": \"true\",\n                        // Optical alignment\n                        style: {\n                            translate: '1px 1px'\n                        }\n                    })\n                })\n            ]\n        })\n    });\n}\n_c = ErrorFeedback;\nconst styles = \"\\n  .error-feedback {\\n    display: flex;\\n    align-items: center;\\n    gap: 8px;\\n    white-space: nowrap;\\n    color: var(--color-gray-900);\\n  }\\n\\n  .error-feedback-thanks {\\n    height: var(--size-24);\\n    display: flex;\\n    align-items: center;\\n    padding-right: 4px; /* To match the 4px inner padding of the thumbs up and down icons */\\n  }\\n\\n  .feedback-button {\\n    background: none;\\n    border: none;\\n    border-radius: var(--rounded-md);\\n    width: var(--size-24);\\n    height: var(--size-24);\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    cursor: pointer;\\n\\n    &:focus {\\n      outline: var(--focus-ring);\\n    }\\n\\n    &:hover {\\n      background: var(--color-gray-alpha-100);\\n    }\\n\\n    &:active {\\n      background: var(--color-gray-alpha-200);\\n    }\\n  }\\n\\n  .feedback-button[aria-disabled='true'] {\\n    opacity: 0.7;\\n    cursor: not-allowed;\\n  }\\n\\n  .feedback-button.voted {\\n    background: var(--color-gray-alpha-200);\\n  }\\n\\n  .thumbs-up-icon,\\n  .thumbs-down-icon {\\n    color: var(--color-gray-900);\\n    width: var(--size-16);\\n    height: var(--size-16);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-feedback.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorFeedback\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-overlay-footer.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-overlay-footer.js ***!
  \**************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorOverlayFooter: function() {\n        return ErrorOverlayFooter;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _errorfeedback = __webpack_require__(/*! ./error-feedback/error-feedback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.js\");\nfunction ErrorOverlayFooter(param) {\n    let { errorCode, footerMessage } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"footer\", {\n        className: \"error-overlay-footer\",\n        children: [\n            footerMessage ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                className: \"error-overlay-footer-message\",\n                children: footerMessage\n            }) : null,\n            errorCode ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorfeedback.ErrorFeedback, {\n                className: \"error-feedback\",\n                errorCode: errorCode\n            }) : null\n        ]\n    });\n}\n_c = ErrorOverlayFooter;\nconst styles = \"\\n  .error-overlay-footer {\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: space-between;\\n\\n    gap: 8px;\\n    padding: 12px;\\n    background: var(--color-background-200);\\n    border-top: 1px solid var(--color-gray-400);\\n  }\\n\\n  .error-feedback {\\n    margin-left: auto;\\n\\n    p {\\n      font-size: var(--size-14);\\n      font-weight: 500;\\n      margin: 0;\\n    }\\n  }\\n\\n  .error-overlay-footer-message {\\n    color: var(--color-gray-900);\\n    margin: 0;\\n    font-size: var(--size-14);\\n    font-weight: 400;\\n    line-height: var(--size-20);\\n  }\\n\\n  \" + _errorfeedback.styles + \"\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-overlay-footer.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-overlay-footer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js ***!
  \**************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorOverlayLayout: function() {\n        return ErrorOverlayLayout;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _dialog = __webpack_require__(/*! ../../dialog */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/index.js\");\nconst _erroroverlaytoolbar = __webpack_require__(/*! ../error-overlay-toolbar/error-overlay-toolbar */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/error-overlay-toolbar.js\");\nconst _erroroverlayfooter = __webpack_require__(/*! ../error-overlay-footer/error-overlay-footer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-overlay-footer.js\");\nconst _errormessage = __webpack_require__(/*! ../error-message/error-message */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-message/error-message.js\");\nconst _errortypelabel = __webpack_require__(/*! ../error-type-label/error-type-label */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-type-label/error-type-label.js\");\nconst _erroroverlaynav = __webpack_require__(/*! ../error-overlay-nav/error-overlay-nav */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-nav/error-overlay-nav.js\");\nconst _dialog1 = __webpack_require__(/*! ../dialog/dialog */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/dialog.js\");\nconst _header = __webpack_require__(/*! ../dialog/header */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/header.js\");\nconst _body = __webpack_require__(/*! ../dialog/body */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/body.js\");\nconst _callstack = __webpack_require__(/*! ../call-stack/call-stack */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js\");\nconst _overlay = __webpack_require__(/*! ../overlay/overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/overlay/overlay.js\");\nconst _erroroverlaybottomstack = __webpack_require__(/*! ../error-overlay-bottom-stack */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-bottom-stack/index.js\");\nconst _environmentnamelabel = __webpack_require__(/*! ../environment-name-label/environment-name-label */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/environment-name-label/environment-name-label.js\");\nconst _utils = __webpack_require__(/*! ../dev-tools-indicator/utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js\");\nfunction ErrorOverlayLayout(param) {\n    _s();\n    let { errorMessage, errorType, children, errorCode, error, debugInfo, isBuildError, onClose, versionInfo, runtimeErrors, activeIdx, setActiveIndex, footerMessage, isTurbopack, dialogResizerRef, // If it's not being passed, we should just render the component as it is being\n    // used without the context of a parent component that controls its state (e.g. Storybook).\n    rendered = true, transitionDurationMs } = param;\n    const animationProps = {\n        'data-rendered': rendered,\n        style: {\n            '--transition-duration': \"\" + transitionDurationMs + \"ms\"\n        }\n    };\n    const hasFooter = Boolean(footerMessage || errorCode);\n    const dialogRef = _react.useRef(null);\n    (0, _utils.useFocusTrap)(dialogRef, null, rendered);\n    var _runtimeErrors_length;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_overlay.ErrorOverlayOverlay, {\n        fixed: isBuildError,\n        ...animationProps,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n            \"data-nextjs-dialog-root\": true,\n            ref: dialogRef,\n            ...animationProps,\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(_dialog1.ErrorOverlayDialog, {\n                    onClose: onClose,\n                    dialogResizerRef: dialogResizerRef,\n                    \"data-has-footer\": hasFooter,\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(_dialog.DialogContent, {\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(_header.ErrorOverlayDialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                            className: \"nextjs__container_errors__error_title\",\n                                            // allow assertion in tests before error rating is implemented\n                                            \"data-nextjs-error-code\": errorCode,\n                                            children: [\n                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                                                    \"data-nextjs-error-label-group\": true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_errortypelabel.ErrorTypeLabel, {\n                                                            errorType: errorType\n                                                        }),\n                                                        error.environmentName && /*#__PURE__*/ (0, _jsxruntime.jsx)(_environmentnamelabel.EnvironmentNameLabel, {\n                                                            environmentName: error.environmentName\n                                                        })\n                                                    ]\n                                                }),\n                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_erroroverlaytoolbar.ErrorOverlayToolbar, {\n                                                    error: error,\n                                                    debugInfo: debugInfo\n                                                })\n                                            ]\n                                        }),\n                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_errormessage.ErrorMessage, {\n                                            errorMessage: errorMessage\n                                        })\n                                    ]\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_body.ErrorOverlayDialogBody, {\n                                    children: children\n                                })\n                            ]\n                        }),\n                        hasFooter && /*#__PURE__*/ (0, _jsxruntime.jsx)(_dialog.DialogFooter, {\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_erroroverlayfooter.ErrorOverlayFooter, {\n                                footerMessage: footerMessage,\n                                errorCode: errorCode\n                            })\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_erroroverlaybottomstack.ErrorOverlayBottomStack, {\n                            errorCount: (_runtimeErrors_length = runtimeErrors == null ? void 0 : runtimeErrors.length) != null ? _runtimeErrors_length : 0,\n                            activeIdx: activeIdx != null ? activeIdx : 0\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(_erroroverlaynav.ErrorOverlayNav, {\n                    runtimeErrors: runtimeErrors,\n                    activeIdx: activeIdx,\n                    setActiveIndex: setActiveIndex,\n                    versionInfo: versionInfo,\n                    isTurbopack: isTurbopack\n                })\n            ]\n        })\n    });\n}\n_s(ErrorOverlayLayout, \"PIKAI2iqVXWUNBzGqO4PRv0eqek=\");\n_c = ErrorOverlayLayout;\nconst styles = \"\\n  \" + _overlay.OVERLAY_STYLES + \"\\n  \" + _dialog1.DIALOG_STYLES + \"\\n  \" + _header.DIALOG_HEADER_STYLES + \"\\n  \" + _body.DIALOG_BODY_STYLES + \"\\n\\n  \" + _erroroverlaynav.styles + \"\\n  \" + _errortypelabel.styles + \"\\n  \" + _errormessage.styles + \"\\n  \" + _erroroverlaytoolbar.styles + \"\\n  \" + _callstack.CALL_STACK_STYLES + \"\\n\\n  [data-nextjs-error-label-group] {\\n    display: flex;\\n    align-items: center;\\n    gap: 8px;\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-overlay-layout.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-nav/error-overlay-nav.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-nav/error-overlay-nav.js ***!
  \********************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorOverlayNav: function() {\n        return ErrorOverlayNav;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _erroroverlaypagination = __webpack_require__(/*! ../error-overlay-pagination/error-overlay-pagination */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-pagination/error-overlay-pagination.js\");\nconst _versionstalenessinfo = __webpack_require__(/*! ../../version-staleness-info/version-staleness-info */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info.js\");\nfunction ErrorOverlayNav(param) {\n    let { runtimeErrors, activeIdx, setActiveIndex, versionInfo, isTurbopack } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-error-overlay-nav\": true,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(Notch, {\n                side: \"left\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_erroroverlaypagination.ErrorOverlayPagination, {\n                    runtimeErrors: runtimeErrors != null ? runtimeErrors : [],\n                    activeIdx: activeIdx != null ? activeIdx : 0,\n                    onActiveIndexChange: setActiveIndex != null ? setActiveIndex : ()=>{}\n                })\n            }),\n            versionInfo && /*#__PURE__*/ (0, _jsxruntime.jsx)(Notch, {\n                side: \"right\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_versionstalenessinfo.VersionStalenessInfo, {\n                    versionInfo: versionInfo,\n                    isTurbopack: isTurbopack\n                })\n            })\n        ]\n    });\n}\n_c = ErrorOverlayNav;\nconst styles = \"\\n  [data-nextjs-error-overlay-nav] {\\n    --notch-height: 2.625rem; /* 42px */\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n\\n    width: 100%;\\n\\n    outline: none;\\n    translate: 1px 1px;\\n    max-width: var(--next-dialog-max-width);\\n\\n    .error-overlay-notch {\\n      --stroke-color: var(--color-gray-400);\\n      --background-color: var(--color-background-100);\\n\\n      translate: -1px 0;\\n      width: auto;\\n      height: var(--notch-height);\\n      padding: 12px;\\n      background: var(--background-color);\\n      border: 1px solid var(--stroke-color);\\n      border-bottom: none;\\n      position: relative;\\n\\n      &[data-side='left'] {\\n        padding-right: 0;\\n        border-radius: var(--rounded-xl) 0 0 0;\\n\\n        .error-overlay-notch-tail {\\n          right: -54px;\\n        }\\n\\n        > *:not(.error-overlay-notch-tail) {\\n          margin-right: -10px;\\n        }\\n      }\\n\\n      &[data-side='right'] {\\n        padding-left: 0;\\n        border-radius: 0 var(--rounded-xl) 0 0;\\n\\n        .error-overlay-notch-tail {\\n          left: -54px;\\n          transform: rotateY(180deg);\\n        }\\n\\n        > *:not(.error-overlay-notch-tail) {\\n          margin-left: -12px;\\n        }\\n      }\\n\\n      .error-overlay-notch-tail {\\n        position: absolute;\\n        top: -1px;\\n        pointer-events: none;\\n        z-index: -1;\\n        height: calc(100% + 1px);\\n      }\\n    }\\n  }\\n\";\nfunction Notch(param) {\n    let { children, side = 'left' } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        className: \"error-overlay-notch\",\n        \"data-side\": side,\n        children: [\n            children,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(Tail, {})\n        ]\n    });\n}\n_c1 = Notch;\nfunction Tail() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n        width: \"60\",\n        height: \"42\",\n        viewBox: \"0 0 60 42\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"error-overlay-notch-tail\",\n        preserveAspectRatio: \"none\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"mask\", {\n                id: \"error_overlay_nav_mask0_2667_14687\",\n                style: {\n                    maskType: 'alpha'\n                },\n                maskUnits: \"userSpaceOnUse\",\n                x: \"0\",\n                y: \"-1\",\n                width: \"60\",\n                height: \"43\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"mask\", {\n                        id: \"error_overlay_nav_path_1_outside_1_2667_14687\",\n                        maskUnits: \"userSpaceOnUse\",\n                        x: \"0\",\n                        y: \"-1\",\n                        width: \"60\",\n                        height: \"43\",\n                        fill: \"black\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"rect\", {\n                                fill: \"white\",\n                                y: \"-1\",\n                                width: \"60\",\n                                height: \"43\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                                d: \"M1 0L8.0783 0C15.772 0 22.7836 4.41324 26.111 11.3501L34.8889 29.6498C38.2164 36.5868 45.228 41 52.9217 41H60H1L1 0Z\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        d: \"M1 0L8.0783 0C15.772 0 22.7836 4.41324 26.111 11.3501L34.8889 29.6498C38.2164 36.5868 45.228 41 52.9217 41H60H1L1 0Z\",\n                        fill: \"white\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        d: \"M1 0V-1H0V0L1 0ZM1 41H0V42H1V41ZM34.8889 29.6498L33.9873 30.0823L34.8889 29.6498ZM26.111 11.3501L27.0127 10.9177L26.111 11.3501ZM1 1H8.0783V-1H1V1ZM60 40H1V42H60V40ZM2 41V0L0 0L0 41H2ZM25.2094 11.7826L33.9873 30.0823L35.7906 29.2174L27.0127 10.9177L25.2094 11.7826ZM52.9217 42H60V40H52.9217V42ZM33.9873 30.0823C37.4811 37.3661 44.8433 42 52.9217 42V40C45.6127 40 38.9517 35.8074 35.7906 29.2174L33.9873 30.0823ZM8.0783 1C15.3873 1 22.0483 5.19257 25.2094 11.7826L27.0127 10.9177C23.5188 3.6339 16.1567 -1 8.0783 -1V1Z\",\n                        fill: \"black\",\n                        mask: \"url(#error_overlay_nav_path_1_outside_1_2667_14687)\"\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"g\", {\n                mask: \"url(#error_overlay_nav_mask0_2667_14687)\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"mask\", {\n                        id: \"error_overlay_nav_path_3_outside_2_2667_14687\",\n                        maskUnits: \"userSpaceOnUse\",\n                        x: \"-1\",\n                        y: \"0.0244141\",\n                        width: \"60\",\n                        height: \"43\",\n                        fill: \"black\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"rect\", {\n                                fill: \"white\",\n                                x: \"-1\",\n                                y: \"0.0244141\",\n                                width: \"60\",\n                                height: \"43\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                                d: \"M0 1.02441H7.0783C14.772 1.02441 21.7836 5.43765 25.111 12.3746L33.8889 30.6743C37.2164 37.6112 44.228 42.0244 51.9217 42.0244H59H0L0 1.02441Z\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        d: \"M0 1.02441H7.0783C14.772 1.02441 21.7836 5.43765 25.111 12.3746L33.8889 30.6743C37.2164 37.6112 44.228 42.0244 51.9217 42.0244H59H0L0 1.02441Z\",\n                        fill: \"var(--background-color)\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        d: \"M0 1.02441L0 0.0244141H-1V1.02441H0ZM0 42.0244H-1V43.0244H0L0 42.0244ZM33.8889 30.6743L32.9873 31.1068L33.8889 30.6743ZM25.111 12.3746L26.0127 11.9421L25.111 12.3746ZM0 2.02441H7.0783V0.0244141H0L0 2.02441ZM59 41.0244H0L0 43.0244H59V41.0244ZM1 42.0244L1 1.02441H-1L-1 42.0244H1ZM24.2094 12.8071L32.9873 31.1068L34.7906 30.2418L26.0127 11.9421L24.2094 12.8071ZM51.9217 43.0244H59V41.0244H51.9217V43.0244ZM32.9873 31.1068C36.4811 38.3905 43.8433 43.0244 51.9217 43.0244V41.0244C44.6127 41.0244 37.9517 36.8318 34.7906 30.2418L32.9873 31.1068ZM7.0783 2.02441C14.3873 2.02441 21.0483 6.21699 24.2094 12.8071L26.0127 11.9421C22.5188 4.65831 15.1567 0.0244141 7.0783 0.0244141V2.02441Z\",\n                        fill: \"var(--stroke-color)\",\n                        mask: \"url(#error_overlay_nav_path_3_outside_2_2667_14687)\"\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c2 = Tail;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-overlay-nav.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ErrorOverlayNav\");\n$RefreshReg$(_c1, \"Notch\");\n$RefreshReg$(_c2, \"Tail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-nav/error-overlay-nav.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-pagination/error-overlay-pagination.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-pagination/error-overlay-pagination.js ***!
  \**********************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorOverlayPagination: function() {\n        return ErrorOverlayPagination;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _leftarrow = __webpack_require__(/*! ../../../icons/left-arrow */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/left-arrow.js\");\nconst _rightarrow = __webpack_require__(/*! ../../../icons/right-arrow */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/right-arrow.js\");\nfunction ErrorOverlayPagination(param) {\n    let { runtimeErrors, activeIdx, onActiveIndexChange } = param;\n    const handlePrevious = (0, _react.useCallback)(()=>(0, _react.startTransition)(()=>{\n            if (activeIdx > 0) {\n                onActiveIndexChange(Math.max(0, activeIdx - 1));\n            }\n        }), [\n        activeIdx,\n        onActiveIndexChange\n    ]);\n    const handleNext = (0, _react.useCallback)(()=>(0, _react.startTransition)(()=>{\n            if (activeIdx < runtimeErrors.length - 1) {\n                onActiveIndexChange(Math.max(0, Math.min(runtimeErrors.length - 1, activeIdx + 1)));\n            }\n        }), [\n        activeIdx,\n        runtimeErrors.length,\n        onActiveIndexChange\n    ]);\n    const buttonLeft = (0, _react.useRef)(null);\n    const buttonRight = (0, _react.useRef)(null);\n    const [nav, setNav] = (0, _react.useState)(null);\n    const onNav = (0, _react.useCallback)((el)=>{\n        setNav(el);\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (nav == null) {\n            return;\n        }\n        const root = nav.getRootNode();\n        const d = self.document;\n        function handler(e) {\n            if (e.key === 'ArrowLeft') {\n                e.preventDefault();\n                e.stopPropagation();\n                handlePrevious && handlePrevious();\n            } else if (e.key === 'ArrowRight') {\n                e.preventDefault();\n                e.stopPropagation();\n                handleNext && handleNext();\n            }\n        }\n        root.addEventListener('keydown', handler);\n        if (root !== d) {\n            d.addEventListener('keydown', handler);\n        }\n        return function() {\n            root.removeEventListener('keydown', handler);\n            if (root !== d) {\n                d.removeEventListener('keydown', handler);\n            }\n        };\n    }, [\n        nav,\n        handleNext,\n        handlePrevious\n    ]);\n    // Unlock focus for browsers like Firefox, that break all user focus if the\n    // currently focused item becomes disabled.\n    (0, _react.useEffect)(()=>{\n        if (nav == null) {\n            return;\n        }\n        const root = nav.getRootNode();\n        // Always true, but we do this for TypeScript:\n        if (root instanceof ShadowRoot) {\n            const a = root.activeElement;\n            if (activeIdx === 0) {\n                if (buttonLeft.current && a === buttonLeft.current) {\n                    buttonLeft.current.blur();\n                }\n            } else if (activeIdx === runtimeErrors.length - 1) {\n                if (buttonRight.current && a === buttonRight.current) {\n                    buttonRight.current.blur();\n                }\n            }\n        }\n    }, [\n        nav,\n        activeIdx,\n        runtimeErrors.length\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"nav\", {\n        className: \"error-overlay-pagination dialog-exclude-closing-from-outside-click\",\n        ref: onNav,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                ref: buttonLeft,\n                type: \"button\",\n                disabled: activeIdx === 0,\n                \"aria-disabled\": activeIdx === 0,\n                onClick: handlePrevious,\n                \"data-nextjs-dialog-error-previous\": true,\n                className: \"error-overlay-pagination-button\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_leftarrow.LeftArrow, {\n                    title: \"previous\",\n                    className: \"error-overlay-pagination-button-icon\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                className: \"error-overlay-pagination-count\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                        \"data-nextjs-dialog-error-index\": activeIdx,\n                        children: [\n                            activeIdx + 1,\n                            \"/\"\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                        \"data-nextjs-dialog-header-total-count\": true,\n                        children: runtimeErrors.length || 1\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                ref: buttonRight,\n                type: \"button\",\n                // If no errors or the last error is active, disable the button.\n                disabled: activeIdx >= runtimeErrors.length - 1,\n                \"aria-disabled\": activeIdx >= runtimeErrors.length - 1,\n                onClick: handleNext,\n                \"data-nextjs-dialog-error-next\": true,\n                className: \"error-overlay-pagination-button\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_rightarrow.RightArrow, {\n                    title: \"next\",\n                    className: \"error-overlay-pagination-button-icon\"\n                })\n            })\n        ]\n    });\n}\n_c = ErrorOverlayPagination;\nconst styles = \"\\n  .error-overlay-pagination {\\n    -webkit-font-smoothing: antialiased;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    gap: 8px;\\n    width: fit-content;\\n  }\\n\\n  .error-overlay-pagination-count {\\n    color: var(--color-gray-900);\\n    text-align: center;\\n    font-size: var(--size-14);\\n    font-weight: 500;\\n    line-height: var(--size-16);\\n    font-variant-numeric: tabular-nums;\\n  }\\n\\n  .error-overlay-pagination-button {\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n\\n    width: var(--size-24);\\n    height: var(--size-24);\\n    background: var(--color-gray-300);\\n    flex-shrink: 0;\\n\\n    border: none;\\n    border-radius: var(--rounded-full);\\n\\n    svg {\\n      width: var(--size-16);\\n      height: var(--size-16);\\n    }\\n\\n    &:focus-visible {\\n      outline: var(--focus-ring);\\n    }\\n\\n    &:not(:disabled):active {\\n      background: var(--color-gray-500);\\n    }\\n\\n    &:disabled {\\n      opacity: 0.5;\\n      cursor: not-allowed;\\n    }\\n  }\\n\\n  .error-overlay-pagination-button-icon {\\n    color: var(--color-gray-1000);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-overlay-pagination.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayPagination\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-pagination/error-overlay-pagination.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/copy-stack-trace-button.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/copy-stack-trace-button.js ***!
  \******************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CopyStackTraceButton\", ({\n    enumerable: true,\n    get: function() {\n        return CopyStackTraceButton;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _copybutton = __webpack_require__(/*! ../../copy-button */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/copy-button/index.js\");\nfunction CopyStackTraceButton(param) {\n    let { error } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_copybutton.CopyButton, {\n        \"data-nextjs-data-runtime-error-copy-stack\": true,\n        className: \"copy-stack-trace-button\",\n        actionLabel: \"Copy Stack Trace\",\n        successLabel: \"Stack Trace Copied\",\n        content: error.stack || '',\n        disabled: !error.stack\n    });\n}\n_c = CopyStackTraceButton;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=copy-stack-trace-button.js.map\nvar _c;\n$RefreshReg$(_c, \"CopyStackTraceButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9lcnJvcnMvZXJyb3Itb3ZlcmxheS10b29sYmFyL2NvcHktc3RhY2stdHJhY2UtYnV0dG9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7d0RBRWdCQTs7O2VBQUFBOzs7O3dDQUZXO0FBRXBCLDhCQUE4QixLQUEyQjtJQUEzQixNQUFFQyxLQUFLLEVBQW9CLEdBQTNCO0lBQ25DLHFCQUNFLHFCQUFDQyxZQUFBQSxVQUFVO1FBQ1RDLDJDQUF5QztRQUN6Q0MsV0FBVTtRQUNWQyxhQUFZO1FBQ1pDLGNBQWE7UUFDYkMsU0FBU04sTUFBTU8sS0FBSyxJQUFJO1FBQ3hCQyxVQUFVLENBQUNSLE1BQU1PLEtBQUs7O0FBRzVCO0tBWGdCUiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXGNvbXBvbmVudHNcXGVycm9yc1xcZXJyb3Itb3ZlcmxheS10b29sYmFyXFxjb3B5LXN0YWNrLXRyYWNlLWJ1dHRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ29weUJ1dHRvbiB9IGZyb20gJy4uLy4uL2NvcHktYnV0dG9uJ1xuXG5leHBvcnQgZnVuY3Rpb24gQ29weVN0YWNrVHJhY2VCdXR0b24oeyBlcnJvciB9OiB7IGVycm9yOiBFcnJvciB9KSB7XG4gIHJldHVybiAoXG4gICAgPENvcHlCdXR0b25cbiAgICAgIGRhdGEtbmV4dGpzLWRhdGEtcnVudGltZS1lcnJvci1jb3B5LXN0YWNrXG4gICAgICBjbGFzc05hbWU9XCJjb3B5LXN0YWNrLXRyYWNlLWJ1dHRvblwiXG4gICAgICBhY3Rpb25MYWJlbD1cIkNvcHkgU3RhY2sgVHJhY2VcIlxuICAgICAgc3VjY2Vzc0xhYmVsPVwiU3RhY2sgVHJhY2UgQ29waWVkXCJcbiAgICAgIGNvbnRlbnQ9e2Vycm9yLnN0YWNrIHx8ICcnfVxuICAgICAgZGlzYWJsZWQ9eyFlcnJvci5zdGFja31cbiAgICAvPlxuICApXG59XG4iXSwibmFtZXMiOlsiQ29weVN0YWNrVHJhY2VCdXR0b24iLCJlcnJvciIsIkNvcHlCdXR0b24iLCJkYXRhLW5leHRqcy1kYXRhLXJ1bnRpbWUtZXJyb3ItY29weS1zdGFjayIsImNsYXNzTmFtZSIsImFjdGlvbkxhYmVsIiwic3VjY2Vzc0xhYmVsIiwiY29udGVudCIsInN0YWNrIiwiZGlzYWJsZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/copy-stack-trace-button.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/docs-link-button.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/docs-link-button.js ***!
  \***********************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DocsLinkButton\", ({\n    enumerable: true,\n    get: function() {\n        return DocsLinkButton;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _ishydrationerror = __webpack_require__(/*! ../../../../../is-hydration-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\");\nconst _parseurlfromtext = __webpack_require__(/*! ../../../utils/parse-url-from-text */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/parse-url-from-text.js\");\nconst docsURLAllowlist = [\n    'https://nextjs.org',\n    'https://react.dev'\n];\nfunction docsLinkMatcher(text) {\n    return docsURLAllowlist.some((url)=>text.startsWith(url));\n}\nfunction getDocsURLFromErrorMessage(text) {\n    const urls = (0, _parseurlfromtext.parseUrlFromText)(text, docsLinkMatcher);\n    if (urls.length === 0) {\n        return null;\n    }\n    const href = urls[0];\n    // Replace react hydration error link with nextjs hydration error link\n    if (href === _ishydrationerror.REACT_HYDRATION_ERROR_LINK) {\n        return _ishydrationerror.NEXTJS_HYDRATION_ERROR_LINK;\n    }\n    return href;\n}\nfunction DocsLinkButton(param) {\n    let { errorMessage } = param;\n    const docsURL = getDocsURLFromErrorMessage(errorMessage);\n    if (!docsURL) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n            title: \"No related documentation found\",\n            \"aria-label\": \"No related documentation found\",\n            className: \"docs-link-button\",\n            disabled: true,\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(DocsIcon, {\n                className: \"error-overlay-toolbar-button-icon\",\n                width: 14,\n                height: 14\n            })\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n        title: \"Go to related documentation\",\n        \"aria-label\": \"Go to related documentation\",\n        className: \"docs-link-button\",\n        href: docsURL,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(DocsIcon, {\n            className: \"error-overlay-toolbar-button-icon\",\n            width: 14,\n            height: 14\n        })\n    });\n}\n_c = DocsLinkButton;\nfunction DocsIcon(props) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"14\",\n        height: \"14\",\n        viewBox: \"0 0 14 14\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M0 .875h4.375C5.448.875 6.401 1.39 7 2.187A3.276 3.276 0 0 1 9.625.875H14v11.156H9.4c-.522 0-1.023.208-1.392.577l-.544.543h-.928l-.544-.543c-.369-.37-.87-.577-1.392-.577H0V.875zm6.344 3.281a1.969 1.969 0 0 0-1.969-1.968H1.312v8.53H4.6c.622 0 1.225.177 1.744.502V4.156zm1.312 7.064V4.156c0-1.087.882-1.968 1.969-1.968h3.063v8.53H9.4c-.622 0-1.225.177-1.744.502z\",\n            fill: \"currentColor\"\n        })\n    });\n}\n_c1 = DocsIcon;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=docs-link-button.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"DocsLinkButton\");\n$RefreshReg$(_c1, \"DocsIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/docs-link-button.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/error-overlay-toolbar.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/error-overlay-toolbar.js ***!
  \****************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorOverlayToolbar: function() {\n        return ErrorOverlayToolbar;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _nodejsinspectorbutton = __webpack_require__(/*! ./nodejs-inspector-button */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/nodejs-inspector-button.js\");\nconst _copystacktracebutton = __webpack_require__(/*! ./copy-stack-trace-button */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/copy-stack-trace-button.js\");\nconst _docslinkbutton = __webpack_require__(/*! ./docs-link-button */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/docs-link-button.js\");\nfunction ErrorOverlayToolbar(param) {\n    let { error, debugInfo } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n        className: \"error-overlay-toolbar\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_copystacktracebutton.CopyStackTraceButton, {\n                error: error\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_docslinkbutton.DocsLinkButton, {\n                errorMessage: error.message\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_nodejsinspectorbutton.NodejsInspectorButton, {\n                devtoolsFrontendUrl: debugInfo == null ? void 0 : debugInfo.devtoolsFrontendUrl\n            })\n        ]\n    });\n}\n_c = ErrorOverlayToolbar;\nconst styles = \"\\n  .error-overlay-toolbar {\\n    display: flex;\\n    gap: 6px;\\n  }\\n\\n  .nodejs-inspector-button,\\n  .copy-stack-trace-button,\\n  .docs-link-button {\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n\\n    width: var(--size-28);\\n    height: var(--size-28);\\n    background: var(--color-background-100);\\n    background-clip: padding-box;\\n    border: 1px solid var(--color-gray-alpha-400);\\n    box-shadow: var(--shadow-small);\\n    border-radius: var(--rounded-full);\\n\\n    svg {\\n      width: var(--size-14);\\n      height: var(--size-14);\\n    }\\n\\n    &:focus {\\n      outline: var(--focus-ring);\\n    }\\n\\n    &:not(:disabled):hover {\\n      background: var(--color-gray-alpha-100);\\n    }\\n\\n    &:not(:disabled):active {\\n      background: var(--color-gray-alpha-200);\\n    }\\n\\n    &:disabled {\\n      background-color: var(--color-gray-100);\\n      cursor: not-allowed;\\n    }\\n  }\\n\\n  .error-overlay-toolbar-button-icon {\\n    color: var(--color-gray-900);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-overlay-toolbar.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayToolbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/error-overlay-toolbar.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/nodejs-inspector-button.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/nodejs-inspector-button.js ***!
  \******************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"NodejsInspectorButton\", ({\n    enumerable: true,\n    get: function() {\n        return NodejsInspectorButton;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _copybutton = __webpack_require__(/*! ../../copy-button */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/copy-button/index.js\");\n// Inline this helper to avoid widely used across the codebase,\n// as for this feature the Chrome detector doesn't need to be super accurate.\nfunction isChrome() {\n    if (false) {}\n    const isChromium = 'chrome' in window && window.chrome;\n    const vendorName = window.navigator.vendor;\n    return isChromium !== null && isChromium !== undefined && vendorName === 'Google Inc.';\n}\nconst isChromeBrowser = isChrome();\nfunction NodeJsIcon(props) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n        width: \"14\",\n        height: \"14\",\n        viewBox: \"0 0 14 14\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"mask\", {\n                id: \"nodejs_icon_mask_a\",\n                style: {\n                    maskType: 'luminance'\n                },\n                maskUnits: \"userSpaceOnUse\",\n                x: \"0\",\n                y: \"0\",\n                width: \"14\",\n                height: \"14\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M6.67.089 1.205 3.256a.663.663 0 0 0-.33.573v6.339c0 .237.126.455.33.574l5.466 3.17a.66.66 0 0 0 .66 0l5.465-3.17a.664.664 0 0 0 .329-.574V3.829a.663.663 0 0 0-.33-.573L7.33.089a.663.663 0 0 0-.661 0\",\n                    fill: \"#fff\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"g\", {\n                mask: \"url(#nodejs_icon_mask_a)\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M18.648 2.717 3.248-4.86-4.648 11.31l15.4 7.58 7.896-16.174z\",\n                    fill: \"url(#nodejs_icon_linear_gradient_b)\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"mask\", {\n                id: \"nodejs_icon_mask_c\",\n                style: {\n                    maskType: 'luminance'\n                },\n                maskUnits: \"userSpaceOnUse\",\n                x: \"1\",\n                y: \"0\",\n                width: \"12\",\n                height: \"14\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M1.01 10.57a.663.663 0 0 0 .195.17l4.688 2.72.781.45a.66.66 0 0 0 .51.063l5.764-10.597a.653.653 0 0 0-.153-.122L9.216 1.18 7.325.087a.688.688 0 0 0-.171-.07L1.01 10.57z\",\n                    fill: \"#fff\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"g\", {\n                mask: \"url(#nodejs_icon_mask_c)\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M-5.647 4.958 5.226 19.734l14.38-10.667L8.734-5.71-5.647 4.958z\",\n                    fill: \"url(#nodejs_icon_linear_gradient_d)\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"g\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"mask\", {\n                        id: \"nodejs_icon_mask_e\",\n                        style: {\n                            maskType: 'luminance'\n                        },\n                        maskUnits: \"userSpaceOnUse\",\n                        x: \"1\",\n                        y: \"0\",\n                        width: \"13\",\n                        height: \"14\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                            d: \"M6.934.004A.665.665 0 0 0 6.67.09L1.22 3.247l5.877 10.746a.655.655 0 0 0 .235-.08l5.465-3.17a.665.665 0 0 0 .319-.453L7.126.015a.684.684 0 0 0-.189-.01\",\n                            fill: \"#fff\"\n                        })\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"g\", {\n                        mask: \"url(#nodejs_icon_mask_e)\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                            d: \"M1.22.002v13.992h11.894V.002H1.22z\",\n                            fill: \"url(#nodejs_icon_linear_gradient_f)\"\n                        })\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"defs\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"nodejs_icon_linear_gradient_b\",\n                        x1: \"10.943\",\n                        y1: \"-1.084\",\n                        x2: \"2.997\",\n                        y2: \"15.062\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".3\",\n                                stopColor: \"#3E863D\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".5\",\n                                stopColor: \"#55934F\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".8\",\n                                stopColor: \"#5AAD45\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"nodejs_icon_linear_gradient_d\",\n                        x1: \"-.145\",\n                        y1: \"12.431\",\n                        x2: \"14.277\",\n                        y2: \"1.818\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".57\",\n                                stopColor: \"#3E863D\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".72\",\n                                stopColor: \"#619857\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: \"#76AC64\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"nodejs_icon_linear_gradient_f\",\n                        x1: \"1.225\",\n                        y1: \"6.998\",\n                        x2: \"13.116\",\n                        y2: \"6.998\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".16\",\n                                stopColor: \"#6BBF47\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".38\",\n                                stopColor: \"#79B461\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".47\",\n                                stopColor: \"#75AC64\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".7\",\n                                stopColor: \"#659E5A\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".9\",\n                                stopColor: \"#3E863D\"\n                            })\n                        ]\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c = NodeJsIcon;\nfunction NodeJsDisabledIcon(props) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n        width: \"14\",\n        height: \"14\",\n        viewBox: \"0 0 14 14\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"mask\", {\n                id: \"nodejs_icon_mask_a\",\n                style: {\n                    maskType: 'luminance'\n                },\n                maskUnits: \"userSpaceOnUse\",\n                x: \"0\",\n                y: \"0\",\n                width: \"14\",\n                height: \"14\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M6.67.089 1.205 3.256a.663.663 0 0 0-.33.573v6.339c0 .237.126.455.33.574l5.466 3.17a.66.66 0 0 0 .66 0l5.465-3.17a.664.664 0 0 0 .329-.574V3.829a.663.663 0 0 0-.33-.573L7.33.089a.663.663 0 0 0-.661 0\",\n                    fill: \"#fff\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"g\", {\n                mask: \"url(#nodejs_icon_mask_a)\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M18.648 2.717 3.248-4.86-4.646 11.31l15.399 7.58 7.896-16.174z\",\n                    fill: \"url(#nodejs_icon_linear_gradient_b)\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"mask\", {\n                id: \"nodejs_icon_mask_c\",\n                style: {\n                    maskType: 'luminance'\n                },\n                maskUnits: \"userSpaceOnUse\",\n                x: \"1\",\n                y: \"0\",\n                width: \"12\",\n                height: \"15\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M1.01 10.571a.66.66 0 0 0 .195.172l4.688 2.718.781.451a.66.66 0 0 0 .51.063l5.764-10.597a.653.653 0 0 0-.153-.122L9.216 1.181 7.325.09a.688.688 0 0 0-.171-.07L1.01 10.572z\",\n                    fill: \"#fff\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"g\", {\n                mask: \"url(#nodejs_icon_mask_c)\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M-5.647 4.96 5.226 19.736 19.606 9.07 8.734-5.707-5.647 4.96z\",\n                    fill: \"url(#nodejs_icon_linear_gradient_d)\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"g\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"mask\", {\n                        id: \"nodejs_icon_mask_e\",\n                        style: {\n                            maskType: 'luminance'\n                        },\n                        maskUnits: \"userSpaceOnUse\",\n                        x: \"1\",\n                        y: \"0\",\n                        width: \"13\",\n                        height: \"14\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                            d: \"M6.935.003a.665.665 0 0 0-.264.085l-5.45 3.158 5.877 10.747a.653.653 0 0 0 .235-.082l5.465-3.17a.665.665 0 0 0 .319-.452L7.127.014a.684.684 0 0 0-.189-.01\",\n                            fill: \"#fff\"\n                        })\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"g\", {\n                        mask: \"url(#nodejs_icon_mask_e)\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                            d: \"M1.222.001v13.992h11.893V0H1.222z\",\n                            fill: \"url(#nodejs_icon_linear_gradient_f)\"\n                        })\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"defs\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"nodejs_icon_linear_gradient_b\",\n                        x1: \"10.944\",\n                        y1: \"-1.084\",\n                        x2: \"2.997\",\n                        y2: \"15.062\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".3\",\n                                stopColor: \"#676767\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".5\",\n                                stopColor: \"#858585\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".8\",\n                                stopColor: \"#989A98\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"nodejs_icon_linear_gradient_d\",\n                        x1: \"-.145\",\n                        y1: \"12.433\",\n                        x2: \"14.277\",\n                        y2: \"1.819\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".57\",\n                                stopColor: \"#747474\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".72\",\n                                stopColor: \"#707070\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: \"#929292\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"nodejs_icon_linear_gradient_f\",\n                        x1: \"1.226\",\n                        y1: \"6.997\",\n                        x2: \"13.117\",\n                        y2: \"6.997\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".16\",\n                                stopColor: \"#878787\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".38\",\n                                stopColor: \"#A9A9A9\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".47\",\n                                stopColor: \"#A5A5A5\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".7\",\n                                stopColor: \"#8F8F8F\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".9\",\n                                stopColor: \"#626262\"\n                            })\n                        ]\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c1 = NodeJsDisabledIcon;\nconst label = 'Learn more about enabling Node.js inspector for server code with Chrome DevTools';\nfunction NodejsInspectorButton(param) {\n    let { devtoolsFrontendUrl } = param;\n    const content = devtoolsFrontendUrl || '';\n    const disabled = !content || !isChromeBrowser;\n    if (disabled) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            title: label,\n            \"aria-label\": label,\n            className: \"nodejs-inspector-button\",\n            href: \"https://nextjs.org/docs/app/building-your-application/configuring/debugging#server-side-code\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(NodeJsDisabledIcon, {\n                className: \"error-overlay-toolbar-button-icon\",\n                width: 14,\n                height: 14\n            })\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_copybutton.CopyButton, {\n        \"data-nextjs-data-runtime-error-copy-devtools-url\": true,\n        className: \"nodejs-inspector-button\",\n        actionLabel: 'Copy Chrome DevTools URL',\n        successLabel: \"Copied\",\n        content: content,\n        icon: /*#__PURE__*/ (0, _jsxruntime.jsx)(NodeJsIcon, {\n            className: \"error-overlay-toolbar-button-icon\",\n            width: 14,\n            height: 14\n        })\n    });\n}\n_c2 = NodejsInspectorButton;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=nodejs-inspector-button.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"NodeJsIcon\");\n$RefreshReg$(_c1, \"NodeJsDisabledIcon\");\n$RefreshReg$(_c2, \"NodejsInspectorButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/nodejs-inspector-button.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay/error-overlay.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay/error-overlay.js ***!
  \************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ErrorOverlay\", ({\n    enumerable: true,\n    get: function() {\n        return ErrorOverlay;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _builderror = __webpack_require__(/*! ../../../container/build-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/build-error.js\");\nconst _errors = __webpack_require__(/*! ../../../container/errors */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/errors.js\");\nconst _rootlayoutmissingtagserror = __webpack_require__(/*! ../../../container/root-layout-missing-tags-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/root-layout-missing-tags-error.js\");\nconst _usedelayedrender = __webpack_require__(/*! ../../../hooks/use-delayed-render */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-delayed-render.js\");\nconst transitionDurationMs = 200;\nfunction ErrorOverlay(param) {\n    let { state, runtimeErrors, isErrorOverlayOpen, setIsErrorOverlayOpen } = param;\n    var _state_rootLayoutMissingTags;\n    const isTurbopack = !!false;\n    // This hook lets us do an exit animation before unmounting the component\n    const { mounted, rendered } = (0, _usedelayedrender.useDelayedRender)(isErrorOverlayOpen, {\n        exitDelay: transitionDurationMs\n    });\n    const commonProps = {\n        rendered,\n        transitionDurationMs,\n        isTurbopack,\n        versionInfo: state.versionInfo\n    };\n    if (!!((_state_rootLayoutMissingTags = state.rootLayoutMissingTags) == null ? void 0 : _state_rootLayoutMissingTags.length)) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_rootlayoutmissingtagserror.RootLayoutMissingTagsError, {\n            ...commonProps,\n            // This is not a runtime error, forcedly display error overlay\n            rendered: true,\n            missingTags: state.rootLayoutMissingTags\n        });\n    }\n    if (state.buildError !== null) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_builderror.BuildError, {\n            ...commonProps,\n            message: state.buildError,\n            // This is not a runtime error, forcedly display error overlay\n            rendered: true\n        });\n    }\n    // No Runtime Errors.\n    if (!runtimeErrors.length) {\n        // Workaround React quirk that triggers \"Switch to client-side rendering\" if\n        // we return no Suspense boundary here.\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {});\n    }\n    if (!mounted) {\n        // Workaround React quirk that triggers \"Switch to client-side rendering\" if\n        // we return no Suspense boundary here.\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {});\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errors.Errors, {\n        ...commonProps,\n        debugInfo: state.debugInfo,\n        runtimeErrors: runtimeErrors,\n        onClose: ()=>{\n            setIsErrorOverlayOpen(false);\n        }\n    });\n}\n_c = ErrorOverlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-overlay.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9lcnJvcnMvZXJyb3Itb3ZlcmxheS9lcnJvci1vdmVybGF5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Z0RBa0JnQkE7OztlQUFBQTs7OzttQ0FoQlM7d0NBQ0U7b0NBQ0o7d0RBQ29COzhDQUNWO0FBR2pDLE1BQU1DLHVCQUF1QjtBQVN0QixzQkFBc0IsS0FVNUI7SUFWNEIsTUFDM0JDLEtBQUssRUFDTEMsYUFBYSxFQUNiQyxrQkFBa0IsRUFDbEJDLHFCQUFxQixFQU10QixHQVY0QjtRQXlCckJIO0lBZE4sTUFBTUksY0FBYyxDQUFDLENBQUNDLEtBQXFCO0lBRTNDLHlFQUF5RTtJQUN6RSxNQUFNLEVBQUVHLE9BQU8sRUFBRUMsUUFBUSxFQUFFLEdBQUdDLENBQUFBLEdBQUFBLGtCQUFBQSxnQkFBQUEsRUFBaUJSLG9CQUFvQjtRQUNqRVMsV0FBV1o7SUFDYjtJQUVBLE1BQU1hLGNBQWM7UUFDbEJIO1FBQ0FWO1FBQ0FLO1FBQ0FTLGFBQWFiLE1BQU1hLFdBQVc7SUFDaEM7SUFFQSxJQUFJLENBQUMsR0FBQ2IsK0JBQUFBLE1BQU1jLHFCQUFBQSxLQUFxQixnQkFBM0JkLDZCQUE2QmUsTUFBQUEsR0FBUTtRQUN6QyxxQkFDRSxxQkFBQ0MsNEJBQUFBLDBCQUEwQjtZQUN4QixHQUFHSixXQUFXO1lBQ2YsOERBQThEO1lBQzlESCxRQUFRO1lBQ1JRLGFBQWFqQixNQUFNYyxxQkFBcUI7O0lBRzlDO0lBRUEsSUFBSWQsTUFBTWtCLFVBQVUsS0FBSyxNQUFNO1FBQzdCLHFCQUNFLHFCQUFDQyxZQUFBQSxVQUFVO1lBQ1IsR0FBR1AsV0FBVztZQUNmUSxTQUFTcEIsTUFBTWtCLFVBQVU7WUFDekIsOERBQThEO1lBQzlEVCxRQUFROztJQUdkO0lBRUEscUJBQXFCO0lBQ3JCLElBQUksQ0FBQ1IsY0FBY2MsTUFBTSxFQUFFO1FBQ3pCLDRFQUE0RTtRQUM1RSx1Q0FBdUM7UUFDdkMscUJBQU8scUJBQUNNLE9BQUFBLFFBQVE7SUFDbEI7SUFFQSxJQUFJLENBQUNiLFNBQVM7UUFDWiw0RUFBNEU7UUFDNUUsdUNBQXVDO1FBQ3ZDLHFCQUFPLHFCQUFDYSxPQUFBQSxRQUFRO0lBQ2xCO0lBRUEscUJBQ0UscUJBQUNDLFFBQUFBLE1BQU07UUFDSixHQUFHVixXQUFXO1FBQ2ZXLFdBQVd2QixNQUFNdUIsU0FBUztRQUMxQnRCLGVBQWVBO1FBQ2Z1QixTQUFTO1lBQ1ByQixzQkFBc0I7UUFDeEI7O0FBR047S0F0RWdCTCIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXGNvbXBvbmVudHNcXGVycm9yc1xcZXJyb3Itb3ZlcmxheVxcZXJyb3Itb3ZlcmxheS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBPdmVybGF5U3RhdGUgfSBmcm9tICcuLi8uLi8uLi8uLi9zaGFyZWQnXG5cbmltcG9ydCB7IFN1c3BlbnNlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBCdWlsZEVycm9yIH0gZnJvbSAnLi4vLi4vLi4vY29udGFpbmVyL2J1aWxkLWVycm9yJ1xuaW1wb3J0IHsgRXJyb3JzIH0gZnJvbSAnLi4vLi4vLi4vY29udGFpbmVyL2Vycm9ycydcbmltcG9ydCB7IFJvb3RMYXlvdXRNaXNzaW5nVGFnc0Vycm9yIH0gZnJvbSAnLi4vLi4vLi4vY29udGFpbmVyL3Jvb3QtbGF5b3V0LW1pc3NpbmctdGFncy1lcnJvcidcbmltcG9ydCB7IHVzZURlbGF5ZWRSZW5kZXIgfSBmcm9tICcuLi8uLi8uLi9ob29rcy91c2UtZGVsYXllZC1yZW5kZXInXG5pbXBvcnQgdHlwZSB7IFJlYWR5UnVudGltZUVycm9yIH0gZnJvbSAnLi4vLi4vLi4vLi4vdXRpbHMvZ2V0LWVycm9yLWJ5LXR5cGUnXG5cbmNvbnN0IHRyYW5zaXRpb25EdXJhdGlvbk1zID0gMjAwXG5cbmV4cG9ydCBpbnRlcmZhY2UgRXJyb3JCYXNlUHJvcHMge1xuICByZW5kZXJlZDogYm9vbGVhblxuICB0cmFuc2l0aW9uRHVyYXRpb25NczogbnVtYmVyXG4gIGlzVHVyYm9wYWNrOiBib29sZWFuXG4gIHZlcnNpb25JbmZvOiBPdmVybGF5U3RhdGVbJ3ZlcnNpb25JbmZvJ11cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEVycm9yT3ZlcmxheSh7XG4gIHN0YXRlLFxuICBydW50aW1lRXJyb3JzLFxuICBpc0Vycm9yT3ZlcmxheU9wZW4sXG4gIHNldElzRXJyb3JPdmVybGF5T3Blbixcbn06IHtcbiAgc3RhdGU6IE92ZXJsYXlTdGF0ZVxuICBydW50aW1lRXJyb3JzOiBSZWFkeVJ1bnRpbWVFcnJvcltdXG4gIGlzRXJyb3JPdmVybGF5T3BlbjogYm9vbGVhblxuICBzZXRJc0Vycm9yT3ZlcmxheU9wZW46ICh2YWx1ZTogYm9vbGVhbikgPT4gdm9pZFxufSkge1xuICBjb25zdCBpc1R1cmJvcGFjayA9ICEhcHJvY2Vzcy5lbnYuVFVSQk9QQUNLXG5cbiAgLy8gVGhpcyBob29rIGxldHMgdXMgZG8gYW4gZXhpdCBhbmltYXRpb24gYmVmb3JlIHVubW91bnRpbmcgdGhlIGNvbXBvbmVudFxuICBjb25zdCB7IG1vdW50ZWQsIHJlbmRlcmVkIH0gPSB1c2VEZWxheWVkUmVuZGVyKGlzRXJyb3JPdmVybGF5T3Blbiwge1xuICAgIGV4aXREZWxheTogdHJhbnNpdGlvbkR1cmF0aW9uTXMsXG4gIH0pXG5cbiAgY29uc3QgY29tbW9uUHJvcHMgPSB7XG4gICAgcmVuZGVyZWQsXG4gICAgdHJhbnNpdGlvbkR1cmF0aW9uTXMsXG4gICAgaXNUdXJib3BhY2ssXG4gICAgdmVyc2lvbkluZm86IHN0YXRlLnZlcnNpb25JbmZvLFxuICB9XG5cbiAgaWYgKCEhc3RhdGUucm9vdExheW91dE1pc3NpbmdUYWdzPy5sZW5ndGgpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPFJvb3RMYXlvdXRNaXNzaW5nVGFnc0Vycm9yXG4gICAgICAgIHsuLi5jb21tb25Qcm9wc31cbiAgICAgICAgLy8gVGhpcyBpcyBub3QgYSBydW50aW1lIGVycm9yLCBmb3JjZWRseSBkaXNwbGF5IGVycm9yIG92ZXJsYXlcbiAgICAgICAgcmVuZGVyZWRcbiAgICAgICAgbWlzc2luZ1RhZ3M9e3N0YXRlLnJvb3RMYXlvdXRNaXNzaW5nVGFnc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG5cbiAgaWYgKHN0YXRlLmJ1aWxkRXJyb3IgIT09IG51bGwpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPEJ1aWxkRXJyb3JcbiAgICAgICAgey4uLmNvbW1vblByb3BzfVxuICAgICAgICBtZXNzYWdlPXtzdGF0ZS5idWlsZEVycm9yfVxuICAgICAgICAvLyBUaGlzIGlzIG5vdCBhIHJ1bnRpbWUgZXJyb3IsIGZvcmNlZGx5IGRpc3BsYXkgZXJyb3Igb3ZlcmxheVxuICAgICAgICByZW5kZXJlZFxuICAgICAgLz5cbiAgICApXG4gIH1cblxuICAvLyBObyBSdW50aW1lIEVycm9ycy5cbiAgaWYgKCFydW50aW1lRXJyb3JzLmxlbmd0aCkge1xuICAgIC8vIFdvcmthcm91bmQgUmVhY3QgcXVpcmsgdGhhdCB0cmlnZ2VycyBcIlN3aXRjaCB0byBjbGllbnQtc2lkZSByZW5kZXJpbmdcIiBpZlxuICAgIC8vIHdlIHJldHVybiBubyBTdXNwZW5zZSBib3VuZGFyeSBoZXJlLlxuICAgIHJldHVybiA8U3VzcGVuc2UgLz5cbiAgfVxuXG4gIGlmICghbW91bnRlZCkge1xuICAgIC8vIFdvcmthcm91bmQgUmVhY3QgcXVpcmsgdGhhdCB0cmlnZ2VycyBcIlN3aXRjaCB0byBjbGllbnQtc2lkZSByZW5kZXJpbmdcIiBpZlxuICAgIC8vIHdlIHJldHVybiBubyBTdXNwZW5zZSBib3VuZGFyeSBoZXJlLlxuICAgIHJldHVybiA8U3VzcGVuc2UgLz5cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPEVycm9yc1xuICAgICAgey4uLmNvbW1vblByb3BzfVxuICAgICAgZGVidWdJbmZvPXtzdGF0ZS5kZWJ1Z0luZm99XG4gICAgICBydW50aW1lRXJyb3JzPXtydW50aW1lRXJyb3JzfVxuICAgICAgb25DbG9zZT17KCkgPT4ge1xuICAgICAgICBzZXRJc0Vycm9yT3ZlcmxheU9wZW4oZmFsc2UpXG4gICAgICB9fVxuICAgIC8+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJFcnJvck92ZXJsYXkiLCJ0cmFuc2l0aW9uRHVyYXRpb25NcyIsInN0YXRlIiwicnVudGltZUVycm9ycyIsImlzRXJyb3JPdmVybGF5T3BlbiIsInNldElzRXJyb3JPdmVybGF5T3BlbiIsImlzVHVyYm9wYWNrIiwicHJvY2VzcyIsImVudiIsIlRVUkJPUEFDSyIsIm1vdW50ZWQiLCJyZW5kZXJlZCIsInVzZURlbGF5ZWRSZW5kZXIiLCJleGl0RGVsYXkiLCJjb21tb25Qcm9wcyIsInZlcnNpb25JbmZvIiwicm9vdExheW91dE1pc3NpbmdUYWdzIiwibGVuZ3RoIiwiUm9vdExheW91dE1pc3NpbmdUYWdzRXJyb3IiLCJtaXNzaW5nVGFncyIsImJ1aWxkRXJyb3IiLCJCdWlsZEVycm9yIiwibWVzc2FnZSIsIlN1c3BlbnNlIiwiRXJyb3JzIiwiZGVidWdJbmZvIiwib25DbG9zZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay/error-overlay.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/environment-name-label/environment-name-label.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/environment-name-label/environment-name-label.js ***!
  \******************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ENVIRONMENT_NAME_LABEL_STYLES: function() {\n        return ENVIRONMENT_NAME_LABEL_STYLES;\n    },\n    EnvironmentNameLabel: function() {\n        return EnvironmentNameLabel;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nfunction EnvironmentNameLabel(param) {\n    let { environmentName } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n        \"data-nextjs-environment-name-label\": true,\n        children: environmentName\n    });\n}\n_c = EnvironmentNameLabel;\nconst ENVIRONMENT_NAME_LABEL_STYLES = \"\\n  [data-nextjs-environment-name-label] {\\n    padding: 2px 6px;\\n    margin: 0;\\n    border-radius: var(--rounded-md-2);\\n    background: var(--color-gray-100);\\n    font-weight: 600;\\n    font-size: var(--size-12);\\n    color: var(--color-gray-900);\\n    font-family: var(--font-stack-monospace);\\n    line-height: var(--size-20);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=environment-name-label.js.map\nvar _c;\n$RefreshReg$(_c, \"EnvironmentNameLabel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9lcnJvcnMvZW52aXJvbm1lbnQtbmFtZS1sYWJlbC9lbnZpcm9ubWVudC1uYW1lLWxhYmVsLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQVFhQSw2QkFBNkI7ZUFBN0JBOztJQVJHQyxvQkFBb0I7ZUFBcEJBOzs7O0FBQVQsOEJBQThCLEtBSXBDO0lBSm9DLE1BQ25DQyxlQUFlLEVBR2hCLEdBSm9DO0lBS25DLHFCQUFPLHFCQUFDQyxRQUFBQTtRQUFLQyxvQ0FBa0M7a0JBQUVGOztBQUNuRDtLQU5nQkQ7QUFRVCxNQUFNRCxnQ0FBaUMiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xccmVhY3QtZGV2LW92ZXJsYXlcXHVpXFxjb21wb25lbnRzXFxlcnJvcnNcXGVudmlyb25tZW50LW5hbWUtbGFiZWxcXGVudmlyb25tZW50LW5hbWUtbGFiZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBFbnZpcm9ubWVudE5hbWVMYWJlbCh7XG4gIGVudmlyb25tZW50TmFtZSxcbn06IHtcbiAgZW52aXJvbm1lbnROYW1lOiBzdHJpbmdcbn0pIHtcbiAgcmV0dXJuIDxzcGFuIGRhdGEtbmV4dGpzLWVudmlyb25tZW50LW5hbWUtbGFiZWw+e2Vudmlyb25tZW50TmFtZX08L3NwYW4+XG59XG5cbmV4cG9ydCBjb25zdCBFTlZJUk9OTUVOVF9OQU1FX0xBQkVMX1NUWUxFUyA9IGBcbiAgW2RhdGEtbmV4dGpzLWVudmlyb25tZW50LW5hbWUtbGFiZWxdIHtcbiAgICBwYWRkaW5nOiAycHggNnB4O1xuICAgIG1hcmdpbjogMDtcbiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1yb3VuZGVkLW1kLTIpO1xuICAgIGJhY2tncm91bmQ6IHZhcigtLWNvbG9yLWdyYXktMTAwKTtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICAgIGZvbnQtc2l6ZTogdmFyKC0tc2l6ZS0xMik7XG4gICAgY29sb3I6IHZhcigtLWNvbG9yLWdyYXktOTAwKTtcbiAgICBmb250LWZhbWlseTogdmFyKC0tZm9udC1zdGFjay1tb25vc3BhY2UpO1xuICAgIGxpbmUtaGVpZ2h0OiB2YXIoLS1zaXplLTIwKTtcbiAgfVxuYFxuIl0sIm5hbWVzIjpbIkVOVklST05NRU5UX05BTUVfTEFCRUxfU1RZTEVTIiwiRW52aXJvbm1lbnROYW1lTGFiZWwiLCJlbnZpcm9ubWVudE5hbWUiLCJzcGFuIiwiZGF0YS1uZXh0anMtZW52aXJvbm1lbnQtbmFtZS1sYWJlbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/environment-name-label/environment-name-label.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-message/error-message.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-message/error-message.js ***!
  \************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorMessage: function() {\n        return ErrorMessage;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nfunction ErrorMessage(param) {\n    let { errorMessage } = param;\n    const [isExpanded, setIsExpanded] = (0, _react.useState)(false);\n    const [shouldTruncate, setShouldTruncate] = (0, _react.useState)(false);\n    const messageRef = (0, _react.useRef)(null);\n    (0, _react.useLayoutEffect)(()=>{\n        if (messageRef.current) {\n            setShouldTruncate(messageRef.current.scrollHeight > 200);\n        }\n    }, [\n        errorMessage\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        className: \"nextjs__container_errors_wrapper\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                ref: messageRef,\n                id: \"nextjs__container_errors_desc\",\n                className: \"nextjs__container_errors_desc \" + (shouldTruncate && !isExpanded ? 'truncated' : ''),\n                children: errorMessage\n            }),\n            shouldTruncate && !isExpanded && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                        className: \"nextjs__container_errors_gradient_overlay\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                        onClick: ()=>setIsExpanded(true),\n                        className: \"nextjs__container_errors_expand_button\",\n                        \"aria-expanded\": isExpanded,\n                        \"aria-controls\": \"nextjs__container_errors_desc\",\n                        children: \"Show More\"\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c = ErrorMessage;\nconst styles = \"\\n  .nextjs__container_errors_wrapper {\\n    position: relative;\\n  }\\n\\n  .nextjs__container_errors_desc {\\n    margin: 0;\\n    margin-left: 4px;\\n    color: var(--color-red-900);\\n    font-weight: 500;\\n    font-size: var(--size-16);\\n    letter-spacing: -0.32px;\\n    line-height: var(--size-24);\\n    overflow-wrap: break-word;\\n    white-space: pre-wrap;\\n  }\\n\\n  .nextjs__container_errors_desc.truncated {\\n    max-height: 200px;\\n    overflow: hidden;\\n  }\\n\\n  .nextjs__container_errors_gradient_overlay {\\n    position: absolute;\\n    bottom: 0;\\n    left: 0;\\n    right: 0;\\n    height: 85px;\\n    background: linear-gradient(\\n      180deg,\\n      rgba(250, 250, 250, 0) 0%,\\n      var(--color-background-100) 100%\\n    );\\n  }\\n\\n  .nextjs__container_errors_expand_button {\\n    position: absolute;\\n    bottom: 10px;\\n    left: 50%;\\n    transform: translateX(-50%);\\n    display: flex;\\n    align-items: center;\\n    padding: 6px 8px;\\n    background: var(--color-background-100);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    border-radius: 999px;\\n    box-shadow:\\n      0px 2px 2px var(--color-gray-alpha-100),\\n      0px 8px 8px -8px var(--color-gray-alpha-100);\\n    font-size: var(--size-13);\\n    cursor: pointer;\\n    color: var(--color-gray-900);\\n    font-weight: 500;\\n    transition: background-color 0.2s ease;\\n  }\\n\\n  .nextjs__container_errors_expand_button:hover {\\n    background: var(--color-gray-100);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-message.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-message/error-message.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-bottom-stack/index.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-bottom-stack/index.js ***!
  \*****************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorOverlayBottomStack: function() {\n        return ErrorOverlayBottomStack;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nfunction ErrorOverlayBottomStack(param) {\n    let { errorCount, activeIdx } = param;\n    // If there are more than 2 errors to navigate, the stack count should remain at 2.\n    const stackCount = Math.min(errorCount - activeIdx - 1, 2);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        \"aria-hidden\": true,\n        className: \"error-overlay-bottom-stack\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n            className: \"error-overlay-bottom-stack-stack\",\n            \"data-stack-count\": stackCount,\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                    className: \"error-overlay-bottom-stack-layer error-overlay-bottom-stack-layer-1\",\n                    children: \"1\"\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                    className: \"error-overlay-bottom-stack-layer error-overlay-bottom-stack-layer-2\",\n                    children: \"2\"\n                })\n            ]\n        })\n    });\n}\n_c = ErrorOverlayBottomStack;\nconst styles = \"\\n  .error-overlay-bottom-stack-layer {\\n    width: 100%;\\n    height: var(--stack-layer-height);\\n    position: relative;\\n    border: 1px solid var(--color-gray-400);\\n    border-radius: var(--rounded-xl);\\n    background: var(--color-background-200);\\n    transition:\\n      translate 350ms var(--timing-swift),\\n      box-shadow 350ms var(--timing-swift);\\n  }\\n\\n  .error-overlay-bottom-stack-layer-1 {\\n    width: calc(100% - var(--size-24));\\n  }\\n\\n  .error-overlay-bottom-stack-layer-2 {\\n    width: calc(100% - var(--size-48));\\n    z-index: -1;\\n  }\\n\\n  .error-overlay-bottom-stack {\\n    width: 100%;\\n    position: absolute;\\n    bottom: -1px;\\n    height: 0;\\n    overflow: visible;\\n  }\\n\\n  .error-overlay-bottom-stack-stack {\\n    --stack-layer-height: 44px;\\n    --stack-layer-height-half: calc(var(--stack-layer-height) / 2);\\n    --stack-layer-trim: 13px;\\n    --shadow: 0px 0.925px 0.925px 0px rgba(0, 0, 0, 0.02),\\n      0px 3.7px 7.4px -3.7px rgba(0, 0, 0, 0.04),\\n      0px 14.8px 22.2px -7.4px rgba(0, 0, 0, 0.06);\\n\\n    display: grid;\\n    place-items: center center;\\n    width: 100%;\\n    position: fixed;\\n    overflow: hidden;\\n    z-index: -1;\\n    max-width: var(--next-dialog-max-width);\\n\\n    .error-overlay-bottom-stack-layer {\\n      grid-area: 1 / 1;\\n      /* Hide */\\n      translate: 0 calc(var(--stack-layer-height) * -1);\\n    }\\n\\n    &[data-stack-count='1'],\\n    &[data-stack-count='2'] {\\n      .error-overlay-bottom-stack-layer-1 {\\n        translate: 0\\n          calc(var(--stack-layer-height-half) * -1 - var(--stack-layer-trim));\\n      }\\n    }\\n\\n    &[data-stack-count='2'] {\\n      .error-overlay-bottom-stack-layer-2 {\\n        translate: 0 calc(var(--stack-layer-trim) * -1 * 2);\\n      }\\n    }\\n\\n    /* Only the bottom stack should have the shadow */\\n    &[data-stack-count='1'] .error-overlay-bottom-stack-layer-1 {\\n      box-shadow: var(--shadow);\\n    }\\n\\n    &[data-stack-count='2'] {\\n      .error-overlay-bottom-stack-layer-2 {\\n        box-shadow: var(--shadow);\\n      }\\n    }\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayBottomStack\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-bottom-stack/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.js ***!
  \***********************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorFeedback: function() {\n        return ErrorFeedback;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _thumbsup = __webpack_require__(/*! ../../../../icons/thumbs/thumbs-up */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/thumbs/thumbs-up.js\");\nconst _thumbsdown = __webpack_require__(/*! ../../../../icons/thumbs/thumbs-down */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/thumbs/thumbs-down.js\");\nconst _cx = __webpack_require__(/*! ../../../../utils/cx */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js\");\nfunction ErrorFeedback(param) {\n    let { errorCode, className } = param;\n    const [votedMap, setVotedMap] = (0, _react.useState)({});\n    const voted = votedMap[errorCode];\n    const hasVoted = voted !== undefined;\n    const disabled = false;\n    const handleFeedback = (0, _react.useCallback)(async (wasHelpful)=>{\n        // Optimistically set feedback state without loading/error states to keep implementation simple\n        setVotedMap((prev)=>({\n                ...prev,\n                [errorCode]: wasHelpful\n            }));\n        try {\n            const response = await fetch(( false || '') + \"/__nextjs_error_feedback?\" + new URLSearchParams({\n                errorCode,\n                wasHelpful: wasHelpful.toString()\n            }));\n            if (!response.ok) {\n                // Handle non-2xx HTTP responses here if needed\n                console.error('Failed to record feedback on the server.');\n            }\n        } catch (error) {\n            console.error('Failed to record feedback:', error);\n        }\n    }, [\n        errorCode\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        className: (0, _cx.cx)('error-feedback', className),\n        role: \"region\",\n        \"aria-label\": \"Error feedback\",\n        children: hasVoted ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n            className: \"error-feedback-thanks\",\n            role: \"status\",\n            \"aria-live\": \"polite\",\n            children: \"Thanks for your feedback!\"\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        href: \"https://nextjs.org/telemetry#error-feedback\",\n                        rel: \"noopener noreferrer\",\n                        target: \"_blank\",\n                        children: \"Was this helpful?\"\n                    })\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                    \"aria-disabled\": disabled ? 'true' : undefined,\n                    \"aria-label\": \"Mark as helpful\",\n                    onClick: disabled ? undefined : ()=>handleFeedback(true),\n                    className: (0, _cx.cx)('feedback-button', voted === true && 'voted'),\n                    title: disabled ? 'Feedback disabled due to setting NEXT_TELEMETRY_DISABLED' : undefined,\n                    type: \"button\",\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_thumbsup.ThumbsUp, {\n                        \"aria-hidden\": \"true\"\n                    })\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                    \"aria-disabled\": disabled ? 'true' : undefined,\n                    \"aria-label\": \"Mark as not helpful\",\n                    onClick: disabled ? undefined : ()=>handleFeedback(false),\n                    className: (0, _cx.cx)('feedback-button', voted === false && 'voted'),\n                    title: disabled ? 'Feedback disabled due to setting NEXT_TELEMETRY_DISABLED' : undefined,\n                    type: \"button\",\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_thumbsdown.ThumbsDown, {\n                        \"aria-hidden\": \"true\",\n                        // Optical alignment\n                        style: {\n                            translate: '1px 1px'\n                        }\n                    })\n                })\n            ]\n        })\n    });\n}\n_c = ErrorFeedback;\nconst styles = \"\\n  .error-feedback {\\n    display: flex;\\n    align-items: center;\\n    gap: 8px;\\n    white-space: nowrap;\\n    color: var(--color-gray-900);\\n  }\\n\\n  .error-feedback-thanks {\\n    height: var(--size-24);\\n    display: flex;\\n    align-items: center;\\n    padding-right: 4px; /* To match the 4px inner padding of the thumbs up and down icons */\\n  }\\n\\n  .feedback-button {\\n    background: none;\\n    border: none;\\n    border-radius: var(--rounded-md);\\n    width: var(--size-24);\\n    height: var(--size-24);\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    cursor: pointer;\\n\\n    &:focus {\\n      outline: var(--focus-ring);\\n    }\\n\\n    &:hover {\\n      background: var(--color-gray-alpha-100);\\n    }\\n\\n    &:active {\\n      background: var(--color-gray-alpha-200);\\n    }\\n  }\\n\\n  .feedback-button[aria-disabled='true'] {\\n    opacity: 0.7;\\n    cursor: not-allowed;\\n  }\\n\\n  .feedback-button.voted {\\n    background: var(--color-gray-alpha-200);\\n  }\\n\\n  .thumbs-up-icon,\\n  .thumbs-down-icon {\\n    color: var(--color-gray-900);\\n    width: var(--size-16);\\n    height: var(--size-16);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-feedback.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorFeedback\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-overlay-footer.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-overlay-footer.js ***!
  \**************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorOverlayFooter: function() {\n        return ErrorOverlayFooter;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _errorfeedback = __webpack_require__(/*! ./error-feedback/error-feedback */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.js\");\nfunction ErrorOverlayFooter(param) {\n    let { errorCode, footerMessage } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"footer\", {\n        className: \"error-overlay-footer\",\n        children: [\n            footerMessage ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                className: \"error-overlay-footer-message\",\n                children: footerMessage\n            }) : null,\n            errorCode ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorfeedback.ErrorFeedback, {\n                className: \"error-feedback\",\n                errorCode: errorCode\n            }) : null\n        ]\n    });\n}\n_c = ErrorOverlayFooter;\nconst styles = \"\\n  .error-overlay-footer {\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: space-between;\\n\\n    gap: 8px;\\n    padding: 12px;\\n    background: var(--color-background-200);\\n    border-top: 1px solid var(--color-gray-400);\\n  }\\n\\n  .error-feedback {\\n    margin-left: auto;\\n\\n    p {\\n      font-size: var(--size-14);\\n      font-weight: 500;\\n      margin: 0;\\n    }\\n  }\\n\\n  .error-overlay-footer-message {\\n    color: var(--color-gray-900);\\n    margin: 0;\\n    font-size: var(--size-14);\\n    font-weight: 400;\\n    line-height: var(--size-20);\\n  }\\n\\n  \" + _errorfeedback.styles + \"\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-overlay-footer.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-overlay-footer.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js ***!
  \**************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorOverlayLayout: function() {\n        return ErrorOverlayLayout;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _dialog = __webpack_require__(/*! ../../dialog */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/dialog/index.js\");\nconst _erroroverlaytoolbar = __webpack_require__(/*! ../error-overlay-toolbar/error-overlay-toolbar */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/error-overlay-toolbar.js\");\nconst _erroroverlayfooter = __webpack_require__(/*! ../error-overlay-footer/error-overlay-footer */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-overlay-footer.js\");\nconst _errormessage = __webpack_require__(/*! ../error-message/error-message */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-message/error-message.js\");\nconst _errortypelabel = __webpack_require__(/*! ../error-type-label/error-type-label */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-type-label/error-type-label.js\");\nconst _erroroverlaynav = __webpack_require__(/*! ../error-overlay-nav/error-overlay-nav */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-nav/error-overlay-nav.js\");\nconst _dialog1 = __webpack_require__(/*! ../dialog/dialog */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/dialog.js\");\nconst _header = __webpack_require__(/*! ../dialog/header */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/header.js\");\nconst _body = __webpack_require__(/*! ../dialog/body */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dialog/body.js\");\nconst _callstack = __webpack_require__(/*! ../call-stack/call-stack */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js\");\nconst _overlay = __webpack_require__(/*! ../overlay/overlay */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/overlay/overlay.js\");\nconst _erroroverlaybottomstack = __webpack_require__(/*! ../error-overlay-bottom-stack */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-bottom-stack/index.js\");\nconst _environmentnamelabel = __webpack_require__(/*! ../environment-name-label/environment-name-label */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/environment-name-label/environment-name-label.js\");\nconst _utils = __webpack_require__(/*! ../dev-tools-indicator/utils */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js\");\nfunction ErrorOverlayLayout(param) {\n    _s();\n    let { errorMessage, errorType, children, errorCode, error, debugInfo, isBuildError, onClose, versionInfo, runtimeErrors, activeIdx, setActiveIndex, footerMessage, isTurbopack, dialogResizerRef, // If it's not being passed, we should just render the component as it is being\n    // used without the context of a parent component that controls its state (e.g. Storybook).\n    rendered = true, transitionDurationMs } = param;\n    const animationProps = {\n        'data-rendered': rendered,\n        style: {\n            '--transition-duration': \"\" + transitionDurationMs + \"ms\"\n        }\n    };\n    const hasFooter = Boolean(footerMessage || errorCode);\n    const dialogRef = _react.useRef(null);\n    (0, _utils.useFocusTrap)(dialogRef, null, rendered);\n    var _runtimeErrors_length;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_overlay.ErrorOverlayOverlay, {\n        fixed: isBuildError,\n        ...animationProps,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n            \"data-nextjs-dialog-root\": true,\n            ref: dialogRef,\n            ...animationProps,\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(_dialog1.ErrorOverlayDialog, {\n                    onClose: onClose,\n                    dialogResizerRef: dialogResizerRef,\n                    \"data-has-footer\": hasFooter,\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(_dialog.DialogContent, {\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(_header.ErrorOverlayDialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                            className: \"nextjs__container_errors__error_title\",\n                                            // allow assertion in tests before error rating is implemented\n                                            \"data-nextjs-error-code\": errorCode,\n                                            children: [\n                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                                                    \"data-nextjs-error-label-group\": true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_errortypelabel.ErrorTypeLabel, {\n                                                            errorType: errorType\n                                                        }),\n                                                        error.environmentName && /*#__PURE__*/ (0, _jsxruntime.jsx)(_environmentnamelabel.EnvironmentNameLabel, {\n                                                            environmentName: error.environmentName\n                                                        })\n                                                    ]\n                                                }),\n                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_erroroverlaytoolbar.ErrorOverlayToolbar, {\n                                                    error: error,\n                                                    debugInfo: debugInfo\n                                                })\n                                            ]\n                                        }),\n                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_errormessage.ErrorMessage, {\n                                            errorMessage: errorMessage\n                                        })\n                                    ]\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_body.ErrorOverlayDialogBody, {\n                                    children: children\n                                })\n                            ]\n                        }),\n                        hasFooter && /*#__PURE__*/ (0, _jsxruntime.jsx)(_dialog.DialogFooter, {\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_erroroverlayfooter.ErrorOverlayFooter, {\n                                footerMessage: footerMessage,\n                                errorCode: errorCode\n                            })\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_erroroverlaybottomstack.ErrorOverlayBottomStack, {\n                            errorCount: (_runtimeErrors_length = runtimeErrors == null ? void 0 : runtimeErrors.length) != null ? _runtimeErrors_length : 0,\n                            activeIdx: activeIdx != null ? activeIdx : 0\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(_erroroverlaynav.ErrorOverlayNav, {\n                    runtimeErrors: runtimeErrors,\n                    activeIdx: activeIdx,\n                    setActiveIndex: setActiveIndex,\n                    versionInfo: versionInfo,\n                    isTurbopack: isTurbopack\n                })\n            ]\n        })\n    });\n}\n_s(ErrorOverlayLayout, \"PIKAI2iqVXWUNBzGqO4PRv0eqek=\");\n_c = ErrorOverlayLayout;\nconst styles = \"\\n  \" + _overlay.OVERLAY_STYLES + \"\\n  \" + _dialog1.DIALOG_STYLES + \"\\n  \" + _header.DIALOG_HEADER_STYLES + \"\\n  \" + _body.DIALOG_BODY_STYLES + \"\\n\\n  \" + _erroroverlaynav.styles + \"\\n  \" + _errortypelabel.styles + \"\\n  \" + _errormessage.styles + \"\\n  \" + _erroroverlaytoolbar.styles + \"\\n  \" + _callstack.CALL_STACK_STYLES + \"\\n\\n  [data-nextjs-error-label-group] {\\n    display: flex;\\n    align-items: center;\\n    gap: 8px;\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-overlay-layout.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-nav/error-overlay-nav.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-nav/error-overlay-nav.js ***!
  \********************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorOverlayNav: function() {\n        return ErrorOverlayNav;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _erroroverlaypagination = __webpack_require__(/*! ../error-overlay-pagination/error-overlay-pagination */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-pagination/error-overlay-pagination.js\");\nconst _versionstalenessinfo = __webpack_require__(/*! ../../version-staleness-info/version-staleness-info */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info.js\");\nfunction ErrorOverlayNav(param) {\n    let { runtimeErrors, activeIdx, setActiveIndex, versionInfo, isTurbopack } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-error-overlay-nav\": true,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(Notch, {\n                side: \"left\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_erroroverlaypagination.ErrorOverlayPagination, {\n                    runtimeErrors: runtimeErrors != null ? runtimeErrors : [],\n                    activeIdx: activeIdx != null ? activeIdx : 0,\n                    onActiveIndexChange: setActiveIndex != null ? setActiveIndex : ()=>{}\n                })\n            }),\n            versionInfo && /*#__PURE__*/ (0, _jsxruntime.jsx)(Notch, {\n                side: \"right\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_versionstalenessinfo.VersionStalenessInfo, {\n                    versionInfo: versionInfo,\n                    isTurbopack: isTurbopack\n                })\n            })\n        ]\n    });\n}\n_c = ErrorOverlayNav;\nconst styles = \"\\n  [data-nextjs-error-overlay-nav] {\\n    --notch-height: 2.625rem; /* 42px */\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n\\n    width: 100%;\\n\\n    outline: none;\\n    translate: 1px 1px;\\n    max-width: var(--next-dialog-max-width);\\n\\n    .error-overlay-notch {\\n      --stroke-color: var(--color-gray-400);\\n      --background-color: var(--color-background-100);\\n\\n      translate: -1px 0;\\n      width: auto;\\n      height: var(--notch-height);\\n      padding: 12px;\\n      background: var(--background-color);\\n      border: 1px solid var(--stroke-color);\\n      border-bottom: none;\\n      position: relative;\\n\\n      &[data-side='left'] {\\n        padding-right: 0;\\n        border-radius: var(--rounded-xl) 0 0 0;\\n\\n        .error-overlay-notch-tail {\\n          right: -54px;\\n        }\\n\\n        > *:not(.error-overlay-notch-tail) {\\n          margin-right: -10px;\\n        }\\n      }\\n\\n      &[data-side='right'] {\\n        padding-left: 0;\\n        border-radius: 0 var(--rounded-xl) 0 0;\\n\\n        .error-overlay-notch-tail {\\n          left: -54px;\\n          transform: rotateY(180deg);\\n        }\\n\\n        > *:not(.error-overlay-notch-tail) {\\n          margin-left: -12px;\\n        }\\n      }\\n\\n      .error-overlay-notch-tail {\\n        position: absolute;\\n        top: -1px;\\n        pointer-events: none;\\n        z-index: -1;\\n        height: calc(100% + 1px);\\n      }\\n    }\\n  }\\n\";\nfunction Notch(param) {\n    let { children, side = 'left' } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        className: \"error-overlay-notch\",\n        \"data-side\": side,\n        children: [\n            children,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(Tail, {})\n        ]\n    });\n}\n_c1 = Notch;\nfunction Tail() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n        width: \"60\",\n        height: \"42\",\n        viewBox: \"0 0 60 42\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"error-overlay-notch-tail\",\n        preserveAspectRatio: \"none\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"mask\", {\n                id: \"error_overlay_nav_mask0_2667_14687\",\n                style: {\n                    maskType: 'alpha'\n                },\n                maskUnits: \"userSpaceOnUse\",\n                x: \"0\",\n                y: \"-1\",\n                width: \"60\",\n                height: \"43\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"mask\", {\n                        id: \"error_overlay_nav_path_1_outside_1_2667_14687\",\n                        maskUnits: \"userSpaceOnUse\",\n                        x: \"0\",\n                        y: \"-1\",\n                        width: \"60\",\n                        height: \"43\",\n                        fill: \"black\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"rect\", {\n                                fill: \"white\",\n                                y: \"-1\",\n                                width: \"60\",\n                                height: \"43\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                                d: \"M1 0L8.0783 0C15.772 0 22.7836 4.41324 26.111 11.3501L34.8889 29.6498C38.2164 36.5868 45.228 41 52.9217 41H60H1L1 0Z\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        d: \"M1 0L8.0783 0C15.772 0 22.7836 4.41324 26.111 11.3501L34.8889 29.6498C38.2164 36.5868 45.228 41 52.9217 41H60H1L1 0Z\",\n                        fill: \"white\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        d: \"M1 0V-1H0V0L1 0ZM1 41H0V42H1V41ZM34.8889 29.6498L33.9873 30.0823L34.8889 29.6498ZM26.111 11.3501L27.0127 10.9177L26.111 11.3501ZM1 1H8.0783V-1H1V1ZM60 40H1V42H60V40ZM2 41V0L0 0L0 41H2ZM25.2094 11.7826L33.9873 30.0823L35.7906 29.2174L27.0127 10.9177L25.2094 11.7826ZM52.9217 42H60V40H52.9217V42ZM33.9873 30.0823C37.4811 37.3661 44.8433 42 52.9217 42V40C45.6127 40 38.9517 35.8074 35.7906 29.2174L33.9873 30.0823ZM8.0783 1C15.3873 1 22.0483 5.19257 25.2094 11.7826L27.0127 10.9177C23.5188 3.6339 16.1567 -1 8.0783 -1V1Z\",\n                        fill: \"black\",\n                        mask: \"url(#error_overlay_nav_path_1_outside_1_2667_14687)\"\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"g\", {\n                mask: \"url(#error_overlay_nav_mask0_2667_14687)\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"mask\", {\n                        id: \"error_overlay_nav_path_3_outside_2_2667_14687\",\n                        maskUnits: \"userSpaceOnUse\",\n                        x: \"-1\",\n                        y: \"0.0244141\",\n                        width: \"60\",\n                        height: \"43\",\n                        fill: \"black\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"rect\", {\n                                fill: \"white\",\n                                x: \"-1\",\n                                y: \"0.0244141\",\n                                width: \"60\",\n                                height: \"43\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                                d: \"M0 1.02441H7.0783C14.772 1.02441 21.7836 5.43765 25.111 12.3746L33.8889 30.6743C37.2164 37.6112 44.228 42.0244 51.9217 42.0244H59H0L0 1.02441Z\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        d: \"M0 1.02441H7.0783C14.772 1.02441 21.7836 5.43765 25.111 12.3746L33.8889 30.6743C37.2164 37.6112 44.228 42.0244 51.9217 42.0244H59H0L0 1.02441Z\",\n                        fill: \"var(--background-color)\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        d: \"M0 1.02441L0 0.0244141H-1V1.02441H0ZM0 42.0244H-1V43.0244H0L0 42.0244ZM33.8889 30.6743L32.9873 31.1068L33.8889 30.6743ZM25.111 12.3746L26.0127 11.9421L25.111 12.3746ZM0 2.02441H7.0783V0.0244141H0L0 2.02441ZM59 41.0244H0L0 43.0244H59V41.0244ZM1 42.0244L1 1.02441H-1L-1 42.0244H1ZM24.2094 12.8071L32.9873 31.1068L34.7906 30.2418L26.0127 11.9421L24.2094 12.8071ZM51.9217 43.0244H59V41.0244H51.9217V43.0244ZM32.9873 31.1068C36.4811 38.3905 43.8433 43.0244 51.9217 43.0244V41.0244C44.6127 41.0244 37.9517 36.8318 34.7906 30.2418L32.9873 31.1068ZM7.0783 2.02441C14.3873 2.02441 21.0483 6.21699 24.2094 12.8071L26.0127 11.9421C22.5188 4.65831 15.1567 0.0244141 7.0783 0.0244141V2.02441Z\",\n                        fill: \"var(--stroke-color)\",\n                        mask: \"url(#error_overlay_nav_path_3_outside_2_2667_14687)\"\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c2 = Tail;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-overlay-nav.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ErrorOverlayNav\");\n$RefreshReg$(_c1, \"Notch\");\n$RefreshReg$(_c2, \"Tail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-nav/error-overlay-nav.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-pagination/error-overlay-pagination.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-pagination/error-overlay-pagination.js ***!
  \**********************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorOverlayPagination: function() {\n        return ErrorOverlayPagination;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _leftarrow = __webpack_require__(/*! ../../../icons/left-arrow */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/left-arrow.js\");\nconst _rightarrow = __webpack_require__(/*! ../../../icons/right-arrow */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/icons/right-arrow.js\");\nfunction ErrorOverlayPagination(param) {\n    let { runtimeErrors, activeIdx, onActiveIndexChange } = param;\n    const handlePrevious = (0, _react.useCallback)(()=>(0, _react.startTransition)(()=>{\n            if (activeIdx > 0) {\n                onActiveIndexChange(Math.max(0, activeIdx - 1));\n            }\n        }), [\n        activeIdx,\n        onActiveIndexChange\n    ]);\n    const handleNext = (0, _react.useCallback)(()=>(0, _react.startTransition)(()=>{\n            if (activeIdx < runtimeErrors.length - 1) {\n                onActiveIndexChange(Math.max(0, Math.min(runtimeErrors.length - 1, activeIdx + 1)));\n            }\n        }), [\n        activeIdx,\n        runtimeErrors.length,\n        onActiveIndexChange\n    ]);\n    const buttonLeft = (0, _react.useRef)(null);\n    const buttonRight = (0, _react.useRef)(null);\n    const [nav, setNav] = (0, _react.useState)(null);\n    const onNav = (0, _react.useCallback)((el)=>{\n        setNav(el);\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (nav == null) {\n            return;\n        }\n        const root = nav.getRootNode();\n        const d = self.document;\n        function handler(e) {\n            if (e.key === 'ArrowLeft') {\n                e.preventDefault();\n                e.stopPropagation();\n                handlePrevious && handlePrevious();\n            } else if (e.key === 'ArrowRight') {\n                e.preventDefault();\n                e.stopPropagation();\n                handleNext && handleNext();\n            }\n        }\n        root.addEventListener('keydown', handler);\n        if (root !== d) {\n            d.addEventListener('keydown', handler);\n        }\n        return function() {\n            root.removeEventListener('keydown', handler);\n            if (root !== d) {\n                d.removeEventListener('keydown', handler);\n            }\n        };\n    }, [\n        nav,\n        handleNext,\n        handlePrevious\n    ]);\n    // Unlock focus for browsers like Firefox, that break all user focus if the\n    // currently focused item becomes disabled.\n    (0, _react.useEffect)(()=>{\n        if (nav == null) {\n            return;\n        }\n        const root = nav.getRootNode();\n        // Always true, but we do this for TypeScript:\n        if (root instanceof ShadowRoot) {\n            const a = root.activeElement;\n            if (activeIdx === 0) {\n                if (buttonLeft.current && a === buttonLeft.current) {\n                    buttonLeft.current.blur();\n                }\n            } else if (activeIdx === runtimeErrors.length - 1) {\n                if (buttonRight.current && a === buttonRight.current) {\n                    buttonRight.current.blur();\n                }\n            }\n        }\n    }, [\n        nav,\n        activeIdx,\n        runtimeErrors.length\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"nav\", {\n        className: \"error-overlay-pagination dialog-exclude-closing-from-outside-click\",\n        ref: onNav,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                ref: buttonLeft,\n                type: \"button\",\n                disabled: activeIdx === 0,\n                \"aria-disabled\": activeIdx === 0,\n                onClick: handlePrevious,\n                \"data-nextjs-dialog-error-previous\": true,\n                className: \"error-overlay-pagination-button\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_leftarrow.LeftArrow, {\n                    title: \"previous\",\n                    className: \"error-overlay-pagination-button-icon\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                className: \"error-overlay-pagination-count\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                        \"data-nextjs-dialog-error-index\": activeIdx,\n                        children: [\n                            activeIdx + 1,\n                            \"/\"\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                        \"data-nextjs-dialog-header-total-count\": true,\n                        children: runtimeErrors.length || 1\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                ref: buttonRight,\n                type: \"button\",\n                // If no errors or the last error is active, disable the button.\n                disabled: activeIdx >= runtimeErrors.length - 1,\n                \"aria-disabled\": activeIdx >= runtimeErrors.length - 1,\n                onClick: handleNext,\n                \"data-nextjs-dialog-error-next\": true,\n                className: \"error-overlay-pagination-button\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_rightarrow.RightArrow, {\n                    title: \"next\",\n                    className: \"error-overlay-pagination-button-icon\"\n                })\n            })\n        ]\n    });\n}\n_c = ErrorOverlayPagination;\nconst styles = \"\\n  .error-overlay-pagination {\\n    -webkit-font-smoothing: antialiased;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    gap: 8px;\\n    width: fit-content;\\n  }\\n\\n  .error-overlay-pagination-count {\\n    color: var(--color-gray-900);\\n    text-align: center;\\n    font-size: var(--size-14);\\n    font-weight: 500;\\n    line-height: var(--size-16);\\n    font-variant-numeric: tabular-nums;\\n  }\\n\\n  .error-overlay-pagination-button {\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n\\n    width: var(--size-24);\\n    height: var(--size-24);\\n    background: var(--color-gray-300);\\n    flex-shrink: 0;\\n\\n    border: none;\\n    border-radius: var(--rounded-full);\\n\\n    svg {\\n      width: var(--size-16);\\n      height: var(--size-16);\\n    }\\n\\n    &:focus-visible {\\n      outline: var(--focus-ring);\\n    }\\n\\n    &:not(:disabled):active {\\n      background: var(--color-gray-500);\\n    }\\n\\n    &:disabled {\\n      opacity: 0.5;\\n      cursor: not-allowed;\\n    }\\n  }\\n\\n  .error-overlay-pagination-button-icon {\\n    color: var(--color-gray-1000);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-overlay-pagination.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayPagination\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-pagination/error-overlay-pagination.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/copy-stack-trace-button.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/copy-stack-trace-button.js ***!
  \******************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"CopyStackTraceButton\", ({\n    enumerable: true,\n    get: function() {\n        return CopyStackTraceButton;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _copybutton = __webpack_require__(/*! ../../copy-button */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/copy-button/index.js\");\nfunction CopyStackTraceButton(param) {\n    let { error } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_copybutton.CopyButton, {\n        \"data-nextjs-data-runtime-error-copy-stack\": true,\n        className: \"copy-stack-trace-button\",\n        actionLabel: \"Copy Stack Trace\",\n        successLabel: \"Stack Trace Copied\",\n        content: error.stack || '',\n        disabled: !error.stack\n    });\n}\n_c = CopyStackTraceButton;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=copy-stack-trace-button.js.map\nvar _c;\n$RefreshReg$(_c, \"CopyStackTraceButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9lcnJvcnMvZXJyb3Itb3ZlcmxheS10b29sYmFyL2NvcHktc3RhY2stdHJhY2UtYnV0dG9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7d0RBRWdCQTs7O2VBQUFBOzs7O3dDQUZXO0FBRXBCLDhCQUE4QixLQUEyQjtJQUEzQixNQUFFQyxLQUFLLEVBQW9CLEdBQTNCO0lBQ25DLHFCQUNFLHFCQUFDQyxZQUFBQSxVQUFVO1FBQ1RDLDJDQUF5QztRQUN6Q0MsV0FBVTtRQUNWQyxhQUFZO1FBQ1pDLGNBQWE7UUFDYkMsU0FBU04sTUFBTU8sS0FBSyxJQUFJO1FBQ3hCQyxVQUFVLENBQUNSLE1BQU1PLEtBQUs7O0FBRzVCO0tBWGdCUiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXGNvbXBvbmVudHNcXGVycm9yc1xcZXJyb3Itb3ZlcmxheS10b29sYmFyXFxjb3B5LXN0YWNrLXRyYWNlLWJ1dHRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ29weUJ1dHRvbiB9IGZyb20gJy4uLy4uL2NvcHktYnV0dG9uJ1xuXG5leHBvcnQgZnVuY3Rpb24gQ29weVN0YWNrVHJhY2VCdXR0b24oeyBlcnJvciB9OiB7IGVycm9yOiBFcnJvciB9KSB7XG4gIHJldHVybiAoXG4gICAgPENvcHlCdXR0b25cbiAgICAgIGRhdGEtbmV4dGpzLWRhdGEtcnVudGltZS1lcnJvci1jb3B5LXN0YWNrXG4gICAgICBjbGFzc05hbWU9XCJjb3B5LXN0YWNrLXRyYWNlLWJ1dHRvblwiXG4gICAgICBhY3Rpb25MYWJlbD1cIkNvcHkgU3RhY2sgVHJhY2VcIlxuICAgICAgc3VjY2Vzc0xhYmVsPVwiU3RhY2sgVHJhY2UgQ29waWVkXCJcbiAgICAgIGNvbnRlbnQ9e2Vycm9yLnN0YWNrIHx8ICcnfVxuICAgICAgZGlzYWJsZWQ9eyFlcnJvci5zdGFja31cbiAgICAvPlxuICApXG59XG4iXSwibmFtZXMiOlsiQ29weVN0YWNrVHJhY2VCdXR0b24iLCJlcnJvciIsIkNvcHlCdXR0b24iLCJkYXRhLW5leHRqcy1kYXRhLXJ1bnRpbWUtZXJyb3ItY29weS1zdGFjayIsImNsYXNzTmFtZSIsImFjdGlvbkxhYmVsIiwic3VjY2Vzc0xhYmVsIiwiY29udGVudCIsInN0YWNrIiwiZGlzYWJsZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/copy-stack-trace-button.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/docs-link-button.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/docs-link-button.js ***!
  \***********************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"DocsLinkButton\", ({\n    enumerable: true,\n    get: function() {\n        return DocsLinkButton;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _ishydrationerror = __webpack_require__(/*! ../../../../../is-hydration-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/is-hydration-error.js\");\nconst _parseurlfromtext = __webpack_require__(/*! ../../../utils/parse-url-from-text */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/utils/parse-url-from-text.js\");\nconst docsURLAllowlist = [\n    'https://nextjs.org',\n    'https://react.dev'\n];\nfunction docsLinkMatcher(text) {\n    return docsURLAllowlist.some((url)=>text.startsWith(url));\n}\nfunction getDocsURLFromErrorMessage(text) {\n    const urls = (0, _parseurlfromtext.parseUrlFromText)(text, docsLinkMatcher);\n    if (urls.length === 0) {\n        return null;\n    }\n    const href = urls[0];\n    // Replace react hydration error link with nextjs hydration error link\n    if (href === _ishydrationerror.REACT_HYDRATION_ERROR_LINK) {\n        return _ishydrationerror.NEXTJS_HYDRATION_ERROR_LINK;\n    }\n    return href;\n}\nfunction DocsLinkButton(param) {\n    let { errorMessage } = param;\n    const docsURL = getDocsURLFromErrorMessage(errorMessage);\n    if (!docsURL) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n            title: \"No related documentation found\",\n            \"aria-label\": \"No related documentation found\",\n            className: \"docs-link-button\",\n            disabled: true,\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(DocsIcon, {\n                className: \"error-overlay-toolbar-button-icon\",\n                width: 14,\n                height: 14\n            })\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n        title: \"Go to related documentation\",\n        \"aria-label\": \"Go to related documentation\",\n        className: \"docs-link-button\",\n        href: docsURL,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(DocsIcon, {\n            className: \"error-overlay-toolbar-button-icon\",\n            width: 14,\n            height: 14\n        })\n    });\n}\n_c = DocsLinkButton;\nfunction DocsIcon(props) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"14\",\n        height: \"14\",\n        viewBox: \"0 0 14 14\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M0 .875h4.375C5.448.875 6.401 1.39 7 2.187A3.276 3.276 0 0 1 9.625.875H14v11.156H9.4c-.522 0-1.023.208-1.392.577l-.544.543h-.928l-.544-.543c-.369-.37-.87-.577-1.392-.577H0V.875zm6.344 3.281a1.969 1.969 0 0 0-1.969-1.968H1.312v8.53H4.6c.622 0 1.225.177 1.744.502V4.156zm1.312 7.064V4.156c0-1.087.882-1.968 1.969-1.968h3.063v8.53H9.4c-.622 0-1.225.177-1.744.502z\",\n            fill: \"currentColor\"\n        })\n    });\n}\n_c1 = DocsIcon;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=docs-link-button.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"DocsLinkButton\");\n$RefreshReg$(_c1, \"DocsIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/docs-link-button.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/error-overlay-toolbar.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/error-overlay-toolbar.js ***!
  \****************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ErrorOverlayToolbar: function() {\n        return ErrorOverlayToolbar;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _nodejsinspectorbutton = __webpack_require__(/*! ./nodejs-inspector-button */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/nodejs-inspector-button.js\");\nconst _copystacktracebutton = __webpack_require__(/*! ./copy-stack-trace-button */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/copy-stack-trace-button.js\");\nconst _docslinkbutton = __webpack_require__(/*! ./docs-link-button */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/docs-link-button.js\");\nfunction ErrorOverlayToolbar(param) {\n    let { error, debugInfo } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n        className: \"error-overlay-toolbar\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_copystacktracebutton.CopyStackTraceButton, {\n                error: error\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_docslinkbutton.DocsLinkButton, {\n                errorMessage: error.message\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_nodejsinspectorbutton.NodejsInspectorButton, {\n                devtoolsFrontendUrl: debugInfo == null ? void 0 : debugInfo.devtoolsFrontendUrl\n            })\n        ]\n    });\n}\n_c = ErrorOverlayToolbar;\nconst styles = \"\\n  .error-overlay-toolbar {\\n    display: flex;\\n    gap: 6px;\\n  }\\n\\n  .nodejs-inspector-button,\\n  .copy-stack-trace-button,\\n  .docs-link-button {\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n\\n    width: var(--size-28);\\n    height: var(--size-28);\\n    background: var(--color-background-100);\\n    background-clip: padding-box;\\n    border: 1px solid var(--color-gray-alpha-400);\\n    box-shadow: var(--shadow-small);\\n    border-radius: var(--rounded-full);\\n\\n    svg {\\n      width: var(--size-14);\\n      height: var(--size-14);\\n    }\\n\\n    &:focus {\\n      outline: var(--focus-ring);\\n    }\\n\\n    &:not(:disabled):hover {\\n      background: var(--color-gray-alpha-100);\\n    }\\n\\n    &:not(:disabled):active {\\n      background: var(--color-gray-alpha-200);\\n    }\\n\\n    &:disabled {\\n      background-color: var(--color-gray-100);\\n      cursor: not-allowed;\\n    }\\n  }\\n\\n  .error-overlay-toolbar-button-icon {\\n    color: var(--color-gray-900);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-overlay-toolbar.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlayToolbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/error-overlay-toolbar.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/nodejs-inspector-button.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/nodejs-inspector-button.js ***!
  \******************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"NodejsInspectorButton\", ({\n    enumerable: true,\n    get: function() {\n        return NodejsInspectorButton;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _copybutton = __webpack_require__(/*! ../../copy-button */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/copy-button/index.js\");\n// Inline this helper to avoid widely used across the codebase,\n// as for this feature the Chrome detector doesn't need to be super accurate.\nfunction isChrome() {\n    if (false) {}\n    const isChromium = 'chrome' in window && window.chrome;\n    const vendorName = window.navigator.vendor;\n    return isChromium !== null && isChromium !== undefined && vendorName === 'Google Inc.';\n}\nconst isChromeBrowser = isChrome();\nfunction NodeJsIcon(props) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n        width: \"14\",\n        height: \"14\",\n        viewBox: \"0 0 14 14\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"mask\", {\n                id: \"nodejs_icon_mask_a\",\n                style: {\n                    maskType: 'luminance'\n                },\n                maskUnits: \"userSpaceOnUse\",\n                x: \"0\",\n                y: \"0\",\n                width: \"14\",\n                height: \"14\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M6.67.089 1.205 3.256a.663.663 0 0 0-.33.573v6.339c0 .237.126.455.33.574l5.466 3.17a.66.66 0 0 0 .66 0l5.465-3.17a.664.664 0 0 0 .329-.574V3.829a.663.663 0 0 0-.33-.573L7.33.089a.663.663 0 0 0-.661 0\",\n                    fill: \"#fff\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"g\", {\n                mask: \"url(#nodejs_icon_mask_a)\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M18.648 2.717 3.248-4.86-4.648 11.31l15.4 7.58 7.896-16.174z\",\n                    fill: \"url(#nodejs_icon_linear_gradient_b)\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"mask\", {\n                id: \"nodejs_icon_mask_c\",\n                style: {\n                    maskType: 'luminance'\n                },\n                maskUnits: \"userSpaceOnUse\",\n                x: \"1\",\n                y: \"0\",\n                width: \"12\",\n                height: \"14\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M1.01 10.57a.663.663 0 0 0 .195.17l4.688 2.72.781.45a.66.66 0 0 0 .51.063l5.764-10.597a.653.653 0 0 0-.153-.122L9.216 1.18 7.325.087a.688.688 0 0 0-.171-.07L1.01 10.57z\",\n                    fill: \"#fff\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"g\", {\n                mask: \"url(#nodejs_icon_mask_c)\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M-5.647 4.958 5.226 19.734l14.38-10.667L8.734-5.71-5.647 4.958z\",\n                    fill: \"url(#nodejs_icon_linear_gradient_d)\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"g\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"mask\", {\n                        id: \"nodejs_icon_mask_e\",\n                        style: {\n                            maskType: 'luminance'\n                        },\n                        maskUnits: \"userSpaceOnUse\",\n                        x: \"1\",\n                        y: \"0\",\n                        width: \"13\",\n                        height: \"14\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                            d: \"M6.934.004A.665.665 0 0 0 6.67.09L1.22 3.247l5.877 10.746a.655.655 0 0 0 .235-.08l5.465-3.17a.665.665 0 0 0 .319-.453L7.126.015a.684.684 0 0 0-.189-.01\",\n                            fill: \"#fff\"\n                        })\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"g\", {\n                        mask: \"url(#nodejs_icon_mask_e)\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                            d: \"M1.22.002v13.992h11.894V.002H1.22z\",\n                            fill: \"url(#nodejs_icon_linear_gradient_f)\"\n                        })\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"defs\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"nodejs_icon_linear_gradient_b\",\n                        x1: \"10.943\",\n                        y1: \"-1.084\",\n                        x2: \"2.997\",\n                        y2: \"15.062\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".3\",\n                                stopColor: \"#3E863D\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".5\",\n                                stopColor: \"#55934F\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".8\",\n                                stopColor: \"#5AAD45\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"nodejs_icon_linear_gradient_d\",\n                        x1: \"-.145\",\n                        y1: \"12.431\",\n                        x2: \"14.277\",\n                        y2: \"1.818\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".57\",\n                                stopColor: \"#3E863D\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".72\",\n                                stopColor: \"#619857\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: \"#76AC64\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"nodejs_icon_linear_gradient_f\",\n                        x1: \"1.225\",\n                        y1: \"6.998\",\n                        x2: \"13.116\",\n                        y2: \"6.998\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".16\",\n                                stopColor: \"#6BBF47\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".38\",\n                                stopColor: \"#79B461\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".47\",\n                                stopColor: \"#75AC64\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".7\",\n                                stopColor: \"#659E5A\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".9\",\n                                stopColor: \"#3E863D\"\n                            })\n                        ]\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c = NodeJsIcon;\nfunction NodeJsDisabledIcon(props) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n        width: \"14\",\n        height: \"14\",\n        viewBox: \"0 0 14 14\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"mask\", {\n                id: \"nodejs_icon_mask_a\",\n                style: {\n                    maskType: 'luminance'\n                },\n                maskUnits: \"userSpaceOnUse\",\n                x: \"0\",\n                y: \"0\",\n                width: \"14\",\n                height: \"14\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M6.67.089 1.205 3.256a.663.663 0 0 0-.33.573v6.339c0 .237.126.455.33.574l5.466 3.17a.66.66 0 0 0 .66 0l5.465-3.17a.664.664 0 0 0 .329-.574V3.829a.663.663 0 0 0-.33-.573L7.33.089a.663.663 0 0 0-.661 0\",\n                    fill: \"#fff\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"g\", {\n                mask: \"url(#nodejs_icon_mask_a)\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M18.648 2.717 3.248-4.86-4.646 11.31l15.399 7.58 7.896-16.174z\",\n                    fill: \"url(#nodejs_icon_linear_gradient_b)\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"mask\", {\n                id: \"nodejs_icon_mask_c\",\n                style: {\n                    maskType: 'luminance'\n                },\n                maskUnits: \"userSpaceOnUse\",\n                x: \"1\",\n                y: \"0\",\n                width: \"12\",\n                height: \"15\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M1.01 10.571a.66.66 0 0 0 .195.172l4.688 2.718.781.451a.66.66 0 0 0 .51.063l5.764-10.597a.653.653 0 0 0-.153-.122L9.216 1.181 7.325.09a.688.688 0 0 0-.171-.07L1.01 10.572z\",\n                    fill: \"#fff\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"g\", {\n                mask: \"url(#nodejs_icon_mask_c)\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                    d: \"M-5.647 4.96 5.226 19.736 19.606 9.07 8.734-5.707-5.647 4.96z\",\n                    fill: \"url(#nodejs_icon_linear_gradient_d)\"\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"g\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"mask\", {\n                        id: \"nodejs_icon_mask_e\",\n                        style: {\n                            maskType: 'luminance'\n                        },\n                        maskUnits: \"userSpaceOnUse\",\n                        x: \"1\",\n                        y: \"0\",\n                        width: \"13\",\n                        height: \"14\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                            d: \"M6.935.003a.665.665 0 0 0-.264.085l-5.45 3.158 5.877 10.747a.653.653 0 0 0 .235-.082l5.465-3.17a.665.665 0 0 0 .319-.452L7.127.014a.684.684 0 0 0-.189-.01\",\n                            fill: \"#fff\"\n                        })\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"g\", {\n                        mask: \"url(#nodejs_icon_mask_e)\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                            d: \"M1.222.001v13.992h11.893V0H1.222z\",\n                            fill: \"url(#nodejs_icon_linear_gradient_f)\"\n                        })\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"defs\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"nodejs_icon_linear_gradient_b\",\n                        x1: \"10.944\",\n                        y1: \"-1.084\",\n                        x2: \"2.997\",\n                        y2: \"15.062\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".3\",\n                                stopColor: \"#676767\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".5\",\n                                stopColor: \"#858585\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".8\",\n                                stopColor: \"#989A98\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"nodejs_icon_linear_gradient_d\",\n                        x1: \"-.145\",\n                        y1: \"12.433\",\n                        x2: \"14.277\",\n                        y2: \"1.819\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".57\",\n                                stopColor: \"#747474\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".72\",\n                                stopColor: \"#707070\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: \"#929292\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"nodejs_icon_linear_gradient_f\",\n                        x1: \"1.226\",\n                        y1: \"6.997\",\n                        x2: \"13.117\",\n                        y2: \"6.997\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".16\",\n                                stopColor: \"#878787\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".38\",\n                                stopColor: \"#A9A9A9\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".47\",\n                                stopColor: \"#A5A5A5\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".7\",\n                                stopColor: \"#8F8F8F\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \".9\",\n                                stopColor: \"#626262\"\n                            })\n                        ]\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c1 = NodeJsDisabledIcon;\nconst label = 'Learn more about enabling Node.js inspector for server code with Chrome DevTools';\nfunction NodejsInspectorButton(param) {\n    let { devtoolsFrontendUrl } = param;\n    const content = devtoolsFrontendUrl || '';\n    const disabled = !content || !isChromeBrowser;\n    if (disabled) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            title: label,\n            \"aria-label\": label,\n            className: \"nodejs-inspector-button\",\n            href: \"https://nextjs.org/docs/app/building-your-application/configuring/debugging#server-side-code\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(NodeJsDisabledIcon, {\n                className: \"error-overlay-toolbar-button-icon\",\n                width: 14,\n                height: 14\n            })\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_copybutton.CopyButton, {\n        \"data-nextjs-data-runtime-error-copy-devtools-url\": true,\n        className: \"nodejs-inspector-button\",\n        actionLabel: 'Copy Chrome DevTools URL',\n        successLabel: \"Copied\",\n        content: content,\n        icon: /*#__PURE__*/ (0, _jsxruntime.jsx)(NodeJsIcon, {\n            className: \"error-overlay-toolbar-button-icon\",\n            width: 14,\n            height: 14\n        })\n    });\n}\n_c2 = NodejsInspectorButton;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=nodejs-inspector-button.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"NodeJsIcon\");\n$RefreshReg$(_c1, \"NodeJsDisabledIcon\");\n$RefreshReg$(_c2, \"NodejsInspectorButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/nodejs-inspector-button.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay/error-overlay.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay/error-overlay.js ***!
  \************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ErrorOverlay\", ({\n    enumerable: true,\n    get: function() {\n        return ErrorOverlay;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _builderror = __webpack_require__(/*! ../../../container/build-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/build-error.js\");\nconst _errors = __webpack_require__(/*! ../../../container/errors */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/errors.js\");\nconst _rootlayoutmissingtagserror = __webpack_require__(/*! ../../../container/root-layout-missing-tags-error */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/container/root-layout-missing-tags-error.js\");\nconst _usedelayedrender = __webpack_require__(/*! ../../../hooks/use-delayed-render */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-delayed-render.js\");\nconst transitionDurationMs = 200;\nfunction ErrorOverlay(param) {\n    let { state, runtimeErrors, isErrorOverlayOpen, setIsErrorOverlayOpen } = param;\n    var _state_rootLayoutMissingTags;\n    const isTurbopack = !!false;\n    // This hook lets us do an exit animation before unmounting the component\n    const { mounted, rendered } = (0, _usedelayedrender.useDelayedRender)(isErrorOverlayOpen, {\n        exitDelay: transitionDurationMs\n    });\n    const commonProps = {\n        rendered,\n        transitionDurationMs,\n        isTurbopack,\n        versionInfo: state.versionInfo\n    };\n    if (!!((_state_rootLayoutMissingTags = state.rootLayoutMissingTags) == null ? void 0 : _state_rootLayoutMissingTags.length)) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_rootlayoutmissingtagserror.RootLayoutMissingTagsError, {\n            ...commonProps,\n            // This is not a runtime error, forcedly display error overlay\n            rendered: true,\n            missingTags: state.rootLayoutMissingTags\n        });\n    }\n    if (state.buildError !== null) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_builderror.BuildError, {\n            ...commonProps,\n            message: state.buildError,\n            // This is not a runtime error, forcedly display error overlay\n            rendered: true\n        });\n    }\n    // No Runtime Errors.\n    if (!runtimeErrors.length) {\n        // Workaround React quirk that triggers \"Switch to client-side rendering\" if\n        // we return no Suspense boundary here.\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {});\n    }\n    if (!mounted) {\n        // Workaround React quirk that triggers \"Switch to client-side rendering\" if\n        // we return no Suspense boundary here.\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {});\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errors.Errors, {\n        ...commonProps,\n        debugInfo: state.debugInfo,\n        runtimeErrors: runtimeErrors,\n        onClose: ()=>{\n            setIsErrorOverlayOpen(false);\n        }\n    });\n}\n_c = ErrorOverlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-overlay.js.map\nvar _c;\n$RefreshReg$(_c, \"ErrorOverlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay/error-overlay.js\n"));

/***/ })

}]);