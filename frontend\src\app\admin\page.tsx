"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useTradingContext } from '@/contexts/TradingContext';
import type { AppSettings } from '@/lib/types';
import { AVAILABLE_STABLECOINS } from '@/lib/types';
import { useToast } from '@/hooks/use-toast';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Settings, BellRing, KeyRound, Bot, FileText, Home, Eye, EyeOff } from 'lucide-react';
import { SessionManager } from '@/components/admin/SessionManager';

export default function AdminPanelPage() {
  const { appSettings, dispatch, botSystemStatus } = useTradingContext();
  const [localSettings, setLocalSettings] = useState<Partial<AppSettings>>(appSettings);
  const [apiKey, setApiKey] = useState('iVIOLOnigRM31Qzm4UoLYsJo4QYIsd1XeXKztnwHfcijpWiAaWQKRsmx3NO7LrLA');
  const [apiSecret, setApiSecret] = useState('jzAnpgIFFv3Ypdhf4jEXljjbkBpfJE5W2aN0zrtypmD3RAjoh2vdQXMr66LOv5fp');
  const [showApiKey, setShowApiKey] = useState(false);
  const [showApiSecret, setShowApiSecret] = useState(false);
  const [telegramToken, setTelegramToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');
  const { toast } = useToast();
  const router = useRouter();

  useEffect(() => {
    setLocalSettings(appSettings);
  }, [appSettings]);

  const handleSettingsChange = (key: keyof AppSettings, value: string | number | boolean) => {
    setLocalSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleSaveAppSettings = () => {
    dispatch({ type: 'SET_APP_SETTINGS', payload: localSettings });
    toast({ title: "App Settings Saved", description: "Global application settings have been updated." });
  };

  const handleSaveApiKeys = async () => {
    try {
      localStorage.setItem('binance_api_key', apiKey);
      localStorage.setItem('binance_api_secret', apiSecret);
      console.log("API Keys Saved:", { apiKey: apiKey.substring(0, 10) + '...', apiSecret: apiSecret.substring(0, 10) + '...' });
      toast({ title: "API Keys Saved", description: "Binance API keys have been saved securely." });
    } catch (error) {
      toast({ title: "Error", description: "Failed to save API keys.", variant: "destructive" });
    }
  };

  const handleTestApiConnection = async () => {
    try {
      const response = await fetch('https://api.binance.com/api/v3/ping');
      if (response.ok) {
        toast({ title: "API Connection Test", description: "Successfully connected to Binance API!" });
      } else {
        toast({ title: "Connection Failed", description: "Unable to connect to Binance API.", variant: "destructive" });
      }
    } catch (error) {
      toast({ title: "Connection Error", description: "Network error while testing API connection.", variant: "destructive" });
    }
  };

  const handleSaveTelegramConfig = () => {
    try {
      localStorage.setItem('telegram_bot_token', telegramToken);
      localStorage.setItem('telegram_chat_id', telegramChatId);
      console.log("Telegram Config Saved:", { telegramToken: telegramToken.substring(0, 10) + '...', telegramChatId });
      toast({ title: "Telegram Config Saved", description: "Telegram settings have been saved successfully." });
    } catch (error) {
      toast({ title: "Error", description: "Failed to save Telegram configuration.", variant: "destructive" });
    }
  };

  const handleTestTelegram = async () => {
    if (!telegramToken || !telegramChatId) {
      toast({ title: "Missing Configuration", description: "Please enter both Telegram bot token and chat ID.", variant: "destructive" });
      return;
    }

    try {
      const response = await fetch(`https://api.telegram.org/bot${telegramToken}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: telegramChatId,
          text: '🤖 Test message from Pluto Trading Bot! Your Telegram integration is working correctly.'
        })
      });

      if (response.ok) {
        toast({ title: "Telegram Test Successful", description: "Test message sent successfully!" });
      } else {
        toast({ title: "Telegram Test Failed", description: "Failed to send test message. Check your token and chat ID.", variant: "destructive" });
      }
    } catch (error) {
      toast({ title: "Telegram Error", description: "Network error while testing Telegram integration.", variant: "destructive" });
    }
  };

  const adminTabs = [
    { value: "systemTools", label: "System Tools", icon: <Settings className="mr-2 h-4 w-4" /> },
    { value: "appSettings", label: "App Settings", icon: <BellRing className="mr-2 h-4 w-4" /> },
    { value: "apiKeys", label: "Exchange API Keys", icon: <KeyRound className="mr-2 h-4 w-4" /> },
    { value: "telegram", label: "Telegram Integration", icon: <Bot className="mr-2 h-4 w-4" /> },
    { value: "sessionManager", label: "Session Manager", icon: <FileText className="mr-2 h-4 w-4" /> },
  ];

  return (
    <div className="container mx-auto py-8 px-4">
      <Card className="border-2 border-border">
        <CardHeader className="flex flex-row justify-between items-center">
          <div>
            <CardTitle className="text-3xl font-bold text-primary">Admin Panel</CardTitle>
            <CardDescription>Manage global settings and tools for Pluto Trading Bot.</CardDescription>
          </div>
          <Button variant="outline" onClick={() => router.push('/dashboard')} className="btn-outline-neo">
            <Home className="mr-2 h-4 w-4" />
            Return to Dashboard
          </Button>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="systemTools" className="w-full">
            <ScrollArea className="pb-2">
              <TabsList className="bg-card border-border border-2 p-1">
                {adminTabs.map(tab => (
                   <TabsTrigger key={tab.value} value={tab.value} className="px-4 py-2 text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                     <div className="flex items-center">{tab.icon} {tab.label}</div>
                   </TabsTrigger>
                ))}
              </TabsList>
            </ScrollArea>

            <TabsContent value="systemTools" className="mt-6">
              <div className="space-y-6">
                <Card className="bg-card-foreground/5 border-border border-2">
                  <CardHeader><CardTitle>System Tools</CardTitle></CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-muted-foreground">Database Editor, Clean Duplicates, Export/Import, Backup/Restore, Diagnostics - (Placeholders for future implementation).</p>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      <Button variant="outline" className="btn-outline-neo" onClick={() => toast({title: "DB Editor Clicked"})}>View Database (Read-Only)</Button>
                      <Button variant="outline" className="btn-outline-neo" onClick={() => toast({title: "Export Orders Clicked"})}>Export Orders to Excel</Button>
                      <Button variant="outline" className="btn-outline-neo" onClick={() => toast({title: "Export History Clicked"})}>Export History to Excel</Button>
                      <Button variant="outline" className="btn-outline-neo" onClick={() => toast({title: "Backup DB Clicked"})}>Backup Database</Button>
                      <Button variant="outline" className="btn-outline-neo" onClick={() => toast({title: "Restore DB Clicked"})} disabled>Restore Database</Button>
                      <Button variant="outline" className="btn-outline-neo" onClick={() => toast({title: "Diagnostics Clicked"})}>Run System Diagnostics</Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="appSettings" className="mt-6">
              <Card className="bg-card-foreground/5 border-border border-2">
                <CardHeader><CardTitle>Application Settings</CardTitle></CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="preferredStablecoin">Preferred Stablecoin (for Swap Mode)</Label>
                    <Select
                      value={localSettings.preferredStablecoin}
                      onValueChange={(val) => handleSettingsChange('preferredStablecoin', val as string)}
                    >
                      <SelectTrigger id="preferredStablecoin"><SelectValue placeholder="Select Stablecoin" /></SelectTrigger>
                      <SelectContent>
                        {AVAILABLE_STABLECOINS.map(sc => <SelectItem key={sc} value={sc}>{sc}</SelectItem>)}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="priceUpdateIntervalMs">Price Update Interval (ms)</Label>
                    <Input
                      id="priceUpdateIntervalMs"
                      type="number"
                      value={localSettings.priceUpdateIntervalMs || 1000}
                      onChange={(e) => handleSettingsChange('priceUpdateIntervalMs', parseInt(e.target.value) || 1000)}
                    />
                  </div>
                  <Button onClick={handleSaveAppSettings} className="btn-neo">Save App Settings</Button>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="apiKeys" className="mt-6">
              <Card className="bg-card-foreground/5 border-border border-2">
                <CardHeader><CardTitle>Exchange API Keys (Binance)</CardTitle></CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">Configure your Binance API keys for real trading. Keys are stored securely in browser storage.</p>
                  <div>
                    <Label htmlFor="apiKey">API Key</Label>
                    <div className="relative">
                      <Input
                        id="apiKey"
                        type={showApiKey ? "text" : "password"}
                        value={apiKey}
                        onChange={(e) => setApiKey(e.target.value)}
                        placeholder="Enter your Binance API key"
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowApiKey(!showApiKey)}
                      >
                        {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="apiSecret">API Secret</Label>
                    <div className="relative">
                      <Input
                        id="apiSecret"
                        type={showApiSecret ? "text" : "password"}
                        value={apiSecret}
                        onChange={(e) => setApiSecret(e.target.value)}
                        placeholder="Enter your Binance API secret"
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowApiSecret(!showApiSecret)}
                      >
                        {showApiSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleSaveApiKeys} className="btn-neo">Save API Keys</Button>
                    <Button onClick={handleTestApiConnection} variant="outline" className="btn-outline-neo">Test Connection</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="telegram" className="mt-6">
              <div className="grid grid-cols-2 gap-6">
                <Card className="bg-card-foreground/5 border-border border-2">
                  <CardHeader>
                    <CardTitle>Telegram Configuration</CardTitle>
                    <CardDescription>Configure Telegram bot for real-time trading notifications.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="telegramToken">Telegram Bot Token</Label>
                      <Input
                        id="telegramToken"
                        type="password"
                        value={telegramToken}
                        onChange={(e) => setTelegramToken(e.target.value)}
                        placeholder="Enter your Telegram bot token"
                      />
                    </div>
                    <div>
                      <Label htmlFor="telegramChatId">Telegram Chat ID</Label>
                      <Input
                        id="telegramChatId"
                        value={telegramChatId}
                        onChange={(e) => setTelegramChatId(e.target.value)}
                        placeholder="Enter your Telegram chat ID"
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="notifyOnOrder" />
                      <Label htmlFor="notifyOnOrder">Notify on Order Execution</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="notifyOnErrors" />
                      <Label htmlFor="notifyOnErrors">Notify on Errors</Label>
                    </div>
                    <div className="flex gap-2">
                      <Button onClick={handleSaveTelegramConfig} className="btn-neo">
                        Save Telegram Config
                      </Button>
                      <Button onClick={handleTestTelegram} variant="outline" className="btn-outline-neo">
                        Test Telegram
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Right Column - Setup Guide */}
                <Card className="bg-card-foreground/5 border-border border-2">
                  <CardHeader>
                    <CardTitle>Setup Guide</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Step 1 */}
                    <div>
                      <h4 className="font-semibold text-yellow-500 mb-3">Step 1: Create a Telegram Bot</h4>
                      <ol className="text-sm space-y-1 list-decimal list-inside text-muted-foreground">
                        <li>Open Telegram and search for <code className="bg-muted px-1 rounded">@BotFather</code></li>
                        <li>Send <code className="bg-muted px-1 rounded">/newbot</code> command</li>
                        <li>Choose a name for your bot (e.g., "My Trading Bot")</li>
                        <li>Choose a username ending with "bot" (e.g., "mytradingbot")</li>
                        <li>Copy the bot token provided by BotFather</li>
                      </ol>
                    </div>

                    {/* Step 2 */}
                    <div>
                      <h4 className="font-semibold text-yellow-500 mb-3">Step 2: Get Your Chat ID</h4>
                      <ol className="text-sm space-y-1 list-decimal list-inside text-muted-foreground">
                        <li>Start a chat with your new bot</li>
                        <li>Send any message to the bot</li>
                        <li>Visit: <code className="bg-muted px-1 rounded text-xs">https://api.telegram.org/bot&lt;YOUR_BOT_TOKEN&gt;/getUpdates</code></li>
                        <li>Look for "chat":{"{"}id": in the response</li>
                        <li>Copy the chat ID number</li>
                      </ol>
                    </div>

                    {/* Step 3 */}
                    <div>
                      <h4 className="font-semibold text-yellow-500 mb-3">Step 3: Configure Bot</h4>
                      <ul className="text-sm space-y-1 list-disc list-inside text-muted-foreground">
                        <li>Paste the bot token in the "Telegram Bot Token" field</li>
                        <li>Paste the chat ID in the "Telegram Chat ID" field</li>
                        <li>Choose your notification preferences</li>
                        <li>Click "Save Telegram Config"</li>
                        <li>Test the connection with "Test Telegram"</li>
                      </ul>
                    </div>

                    {/* Pro Tip */}
                    <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-md p-4">
                      <div className="flex items-start gap-2">
                        <span className="text-yellow-500">💡</span>
                        <div>
                          <h5 className="font-semibold text-yellow-600 mb-1">Pro Tip:</h5>
                          <p className="text-sm text-muted-foreground">
                            Keep your bot token secure and never share it publicly. You can regenerate it anytime via
                            BotFather if needed.
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="sessionManager" className="mt-6">
              <SessionManager />
            </TabsContent>

          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
