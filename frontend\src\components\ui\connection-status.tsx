"use client";

import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ConnectionStatusProps {
  className?: string;
  onConnectionChange?: (isOnline: boolean) => void;
}

export function ConnectionStatus({ className, onConnectionChange }: ConnectionStatusProps) {
  const [isOnline, setIsOnline] = useState(true);
  const [lastChecked, setLastChecked] = useState<Date>(new Date());

  // Check internet connectivity by trying to fetch a small resource
  const checkConnection = async (): Promise<boolean> => {
    try {
      // Use multiple endpoints for better reliability
      const endpoints = [
        'https://www.google.com/favicon.ico',
        'https://httpbin.org/status/200',
        'https://jsonplaceholder.typicode.com/posts/1'
      ];

      // Try to fetch from any of the endpoints with a timeout
      const promises = endpoints.map(url => 
        fetch(url, { 
          method: 'HEAD',
          mode: 'no-cors',
          cache: 'no-cache',
          signal: AbortSignal.timeout(5000) // 5 second timeout
        })
      );

      await Promise.any(promises);
      return true;
    } catch (error) {
      console.warn('Connection check failed:', error);
      return false;
    }
  };

  // Alternative method using navigator.onLine with additional verification
  const verifyConnection = async (): Promise<boolean> => {
    // First check navigator.onLine (basic browser connectivity)
    if (!navigator.onLine) {
      return false;
    }

    // Then verify with actual network request
    return await checkConnection();
  };

  const updateConnectionStatus = async () => {
    const online = await verifyConnection();
    setIsOnline(online);
    setLastChecked(new Date());
    
    // Notify parent component of connection change
    if (onConnectionChange) {
      onConnectionChange(online);
    }
  };

  useEffect(() => {
    // Initial check
    updateConnectionStatus();

    // Set up periodic checks every 30 seconds
    const interval = setInterval(updateConnectionStatus, 30000);

    // Listen to browser online/offline events
    const handleOnline = () => {
      console.log('Browser detected online');
      updateConnectionStatus();
    };

    const handleOffline = () => {
      console.log('Browser detected offline');
      setIsOnline(false);
      setLastChecked(new Date());
      if (onConnectionChange) {
        onConnectionChange(false);
      }
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Listen for visibility change (when user switches tabs/minimizes)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible, check connection
        updateConnectionStatus();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup
    return () => {
      clearInterval(interval);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [onConnectionChange]);

  return (
    <div className={cn(
      "flex items-center space-x-2 px-3 py-1 rounded-md text-sm font-medium transition-colors",
      isOnline 
        ? "bg-green-100 text-green-800 border border-green-200" 
        : "bg-red-100 text-red-800 border border-red-200",
      className
    )}>
      {isOnline ? (
        <Wifi className="h-4 w-4" />
      ) : (
        <WifiOff className="h-4 w-4" />
      )}
      <span>{isOnline ? 'Online' : 'Offline'}</span>
      <span className="text-xs opacity-70">
        {lastChecked.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </span>
    </div>
  );
}
