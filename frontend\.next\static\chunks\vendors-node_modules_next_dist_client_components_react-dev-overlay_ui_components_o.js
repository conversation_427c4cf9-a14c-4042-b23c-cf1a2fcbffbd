"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_o"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js ***!
  \*********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    lock: function() {\n        return lock;\n    },\n    unlock: function() {\n        return unlock;\n    }\n});\nlet previousBodyPaddingRight;\nlet previousBodyOverflowSetting;\nlet activeLocks = 0;\nfunction lock() {\n    setTimeout(()=>{\n        if (activeLocks++ > 0) {\n            return;\n        }\n        const scrollBarGap = window.innerWidth - document.documentElement.clientWidth;\n        if (scrollBarGap > 0) {\n            previousBodyPaddingRight = document.body.style.paddingRight;\n            document.body.style.paddingRight = \"\" + scrollBarGap + \"px\";\n        }\n        previousBodyOverflowSetting = document.body.style.overflow;\n        document.body.style.overflow = 'hidden';\n    });\n}\nfunction unlock() {\n    setTimeout(()=>{\n        if (activeLocks === 0 || --activeLocks !== 0) {\n            return;\n        }\n        if (previousBodyPaddingRight !== undefined) {\n            document.body.style.paddingRight = previousBodyPaddingRight;\n            previousBodyPaddingRight = undefined;\n        }\n        if (previousBodyOverflowSetting !== undefined) {\n            document.body.style.overflow = previousBodyOverflowSetting;\n            previousBodyOverflowSetting = undefined;\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=body-locker.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Overlay\", ({\n    enumerable: true,\n    get: function() {\n        return _overlay.Overlay;\n    }\n}));\nconst _overlay = __webpack_require__(/*! ./overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9vdmVybGF5L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7MkNBQVNBOzs7ZUFBQUEsU0FBQUEsT0FBTzs7O3FDQUFRIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcY29tcG9uZW50c1xcb3ZlcmxheVxcaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IE92ZXJsYXkgfSBmcm9tICcuL292ZXJsYXknXG4iXSwibmFtZXMiOlsiT3ZlcmxheSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js ***!
  \*****************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Overlay\", ({\n    enumerable: true,\n    get: function() {\n        return Overlay;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _bodylocker = __webpack_require__(/*! ./body-locker */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js\");\nconst Overlay = function Overlay(param) {\n    _s();\n    let { className, children, fixed, ...props } = param;\n    _react.useEffect({\n        \"Overlay.useEffect\": ()=>{\n            (0, _bodylocker.lock)();\n            return ({\n                \"Overlay.useEffect\": ()=>{\n                    (0, _bodylocker.unlock)();\n                }\n            })[\"Overlay.useEffect\"];\n        }\n    }[\"Overlay.useEffect\"], []);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-dialog-overlay\": true,\n        className: className,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"data-nextjs-dialog-backdrop\": true,\n                \"data-nextjs-dialog-backdrop-fixed\": fixed ? true : undefined\n            }),\n            children\n        ]\n    });\n};\n_s(Overlay, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Overlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=overlay.js.map\nvar _c;\n$RefreshReg$(_c, \"Overlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js ***!
  \****************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst styles = \"\\n  [data-nextjs-dialog-overlay] {\\n    position: fixed;\\n    top: 0;\\n    right: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 9000;\\n\\n    display: flex;\\n    align-content: center;\\n    align-items: center;\\n    flex-direction: column;\\n    padding: 10vh 15px 0;\\n  }\\n\\n  @media (max-height: 812px) {\\n    [data-nextjs-dialog-overlay] {\\n      padding: 15px 15px 0;\\n    }\\n  }\\n\\n  [data-nextjs-dialog-backdrop] {\\n    position: fixed;\\n    top: 0;\\n    right: 0;\\n    bottom: 0;\\n    left: 0;\\n    background-color: var(--color-backdrop);\\n    backdrop-filter: blur(10px);\\n    pointer-events: all;\\n    z-index: -1;\\n  }\\n\\n  [data-nextjs-dialog-backdrop-fixed] {\\n    cursor: not-allowed;\\n    -webkit-backdrop-filter: blur(8px);\\n    backdrop-filter: blur(8px);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9vdmVybGF5L3N0eWxlcy5qcyIsIm1hcHBpbmdzIjoiOzs7OzBDQXlDU0E7OztlQUFBQTs7O0FBekNULE1BQU1BLFNBQVUiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xccmVhY3QtZGV2LW92ZXJsYXlcXHVpXFxjb21wb25lbnRzXFxvdmVybGF5XFxzdHlsZXMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHN0eWxlcyA9IGBcbiAgW2RhdGEtbmV4dGpzLWRpYWxvZy1vdmVybGF5XSB7XG4gICAgcG9zaXRpb246IGZpeGVkO1xuICAgIHRvcDogMDtcbiAgICByaWdodDogMDtcbiAgICBib3R0b206IDA7XG4gICAgbGVmdDogMDtcbiAgICB6LWluZGV4OiA5MDAwO1xuXG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1jb250ZW50OiBjZW50ZXI7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIHBhZGRpbmc6IDEwdmggMTVweCAwO1xuICB9XG5cbiAgQG1lZGlhIChtYXgtaGVpZ2h0OiA4MTJweCkge1xuICAgIFtkYXRhLW5leHRqcy1kaWFsb2ctb3ZlcmxheV0ge1xuICAgICAgcGFkZGluZzogMTVweCAxNXB4IDA7XG4gICAgfVxuICB9XG5cbiAgW2RhdGEtbmV4dGpzLWRpYWxvZy1iYWNrZHJvcF0ge1xuICAgIHBvc2l0aW9uOiBmaXhlZDtcbiAgICB0b3A6IDA7XG4gICAgcmlnaHQ6IDA7XG4gICAgYm90dG9tOiAwO1xuICAgIGxlZnQ6IDA7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tY29sb3ItYmFja2Ryb3ApO1xuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcbiAgICBwb2ludGVyLWV2ZW50czogYWxsO1xuICAgIHotaW5kZXg6IC0xO1xuICB9XG5cbiAgW2RhdGEtbmV4dGpzLWRpYWxvZy1iYWNrZHJvcC1maXhlZF0ge1xuICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7XG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoOHB4KTtcbiAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoOHB4KTtcbiAgfVxuYFxuXG5leHBvcnQgeyBzdHlsZXMgfVxuIl0sIm5hbWVzIjpbInN0eWxlcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/shadow-portal.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/shadow-portal.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ShadowPortal\", ({\n    enumerable: true,\n    get: function() {\n        return ShadowPortal;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _reactdom = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\nconst _shared = __webpack_require__(/*! ../../shared */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nfunction ShadowPortal(param) {\n    _s();\n    let { children } = param;\n    let portalNode = _react.useRef(null);\n    let shadowNode = _react.useRef(null);\n    let [, forceUpdate] = _react.useState();\n    _react.useLayoutEffect({\n        \"ShadowPortal.useLayoutEffect\": ()=>{\n            const ownerDocument = document;\n            portalNode.current = ownerDocument.createElement('nextjs-portal');\n            // load default color preference from localstorage\n            if (typeof localStorage !== 'undefined') {\n                const theme = localStorage.getItem(_shared.STORAGE_KEY_THEME);\n                if (theme === 'dark') {\n                    portalNode.current.classList.add('dark');\n                    portalNode.current.classList.remove('light');\n                } else if (theme === 'light') {\n                    portalNode.current.classList.remove('dark');\n                    portalNode.current.classList.add('light');\n                }\n            }\n            shadowNode.current = portalNode.current.attachShadow({\n                mode: 'open'\n            });\n            ownerDocument.body.appendChild(portalNode.current);\n            forceUpdate({});\n            return ({\n                \"ShadowPortal.useLayoutEffect\": ()=>{\n                    if (portalNode.current && portalNode.current.ownerDocument) {\n                        portalNode.current.ownerDocument.body.removeChild(portalNode.current);\n                    }\n                }\n            })[\"ShadowPortal.useLayoutEffect\"];\n        }\n    }[\"ShadowPortal.useLayoutEffect\"], []);\n    return shadowNode.current ? /*#__PURE__*/ (0, _reactdom.createPortal)(children, shadowNode.current) : null;\n}\n_s(ShadowPortal, \"P7YL0rn/sjH62F7+OsEXN5GMw3U=\");\n_c = ShadowPortal;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=shadow-portal.js.map\nvar _c;\n$RefreshReg$(_c, \"ShadowPortal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/shadow-portal.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js ***!
  \*********************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    lock: function() {\n        return lock;\n    },\n    unlock: function() {\n        return unlock;\n    }\n});\nlet previousBodyPaddingRight;\nlet previousBodyOverflowSetting;\nlet activeLocks = 0;\nfunction lock() {\n    setTimeout(()=>{\n        if (activeLocks++ > 0) {\n            return;\n        }\n        const scrollBarGap = window.innerWidth - document.documentElement.clientWidth;\n        if (scrollBarGap > 0) {\n            previousBodyPaddingRight = document.body.style.paddingRight;\n            document.body.style.paddingRight = \"\" + scrollBarGap + \"px\";\n        }\n        previousBodyOverflowSetting = document.body.style.overflow;\n        document.body.style.overflow = 'hidden';\n    });\n}\nfunction unlock() {\n    setTimeout(()=>{\n        if (activeLocks === 0 || --activeLocks !== 0) {\n            return;\n        }\n        if (previousBodyPaddingRight !== undefined) {\n            document.body.style.paddingRight = previousBodyPaddingRight;\n            previousBodyPaddingRight = undefined;\n        }\n        if (previousBodyOverflowSetting !== undefined) {\n            document.body.style.overflow = previousBodyOverflowSetting;\n            previousBodyOverflowSetting = undefined;\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=body-locker.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Overlay\", ({\n    enumerable: true,\n    get: function() {\n        return _overlay.Overlay;\n    }\n}));\nconst _overlay = __webpack_require__(/*! ./overlay */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9vdmVybGF5L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7MkNBQVNBOzs7ZUFBQUEsU0FBQUEsT0FBTzs7O3FDQUFRIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcY29tcG9uZW50c1xcb3ZlcmxheVxcaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IE92ZXJsYXkgfSBmcm9tICcuL292ZXJsYXknXG4iXSwibmFtZXMiOlsiT3ZlcmxheSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js ***!
  \*****************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Overlay\", ({\n    enumerable: true,\n    get: function() {\n        return Overlay;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _bodylocker = __webpack_require__(/*! ./body-locker */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js\");\nconst Overlay = function Overlay(param) {\n    _s();\n    let { className, children, fixed, ...props } = param;\n    _react.useEffect({\n        \"Overlay.useEffect\": ()=>{\n            (0, _bodylocker.lock)();\n            return ({\n                \"Overlay.useEffect\": ()=>{\n                    (0, _bodylocker.unlock)();\n                }\n            })[\"Overlay.useEffect\"];\n        }\n    }[\"Overlay.useEffect\"], []);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-dialog-overlay\": true,\n        className: className,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"data-nextjs-dialog-backdrop\": true,\n                \"data-nextjs-dialog-backdrop-fixed\": fixed ? true : undefined\n            }),\n            children\n        ]\n    });\n};\n_s(Overlay, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Overlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=overlay.js.map\nvar _c;\n$RefreshReg$(_c, \"Overlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js ***!
  \****************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst styles = \"\\n  [data-nextjs-dialog-overlay] {\\n    position: fixed;\\n    top: 0;\\n    right: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 9000;\\n\\n    display: flex;\\n    align-content: center;\\n    align-items: center;\\n    flex-direction: column;\\n    padding: 10vh 15px 0;\\n  }\\n\\n  @media (max-height: 812px) {\\n    [data-nextjs-dialog-overlay] {\\n      padding: 15px 15px 0;\\n    }\\n  }\\n\\n  [data-nextjs-dialog-backdrop] {\\n    position: fixed;\\n    top: 0;\\n    right: 0;\\n    bottom: 0;\\n    left: 0;\\n    background-color: var(--color-backdrop);\\n    backdrop-filter: blur(10px);\\n    pointer-events: all;\\n    z-index: -1;\\n  }\\n\\n  [data-nextjs-dialog-backdrop-fixed] {\\n    cursor: not-allowed;\\n    -webkit-backdrop-filter: blur(8px);\\n    backdrop-filter: blur(8px);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvdWkvY29tcG9uZW50cy9vdmVybGF5L3N0eWxlcy5qcyIsIm1hcHBpbmdzIjoiOzs7OzBDQXlDU0E7OztlQUFBQTs7O0FBekNULE1BQU1BLFNBQVUiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xccmVhY3QtZGV2LW92ZXJsYXlcXHVpXFxjb21wb25lbnRzXFxvdmVybGF5XFxzdHlsZXMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHN0eWxlcyA9IGBcbiAgW2RhdGEtbmV4dGpzLWRpYWxvZy1vdmVybGF5XSB7XG4gICAgcG9zaXRpb246IGZpeGVkO1xuICAgIHRvcDogMDtcbiAgICByaWdodDogMDtcbiAgICBib3R0b206IDA7XG4gICAgbGVmdDogMDtcbiAgICB6LWluZGV4OiA5MDAwO1xuXG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1jb250ZW50OiBjZW50ZXI7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIHBhZGRpbmc6IDEwdmggMTVweCAwO1xuICB9XG5cbiAgQG1lZGlhIChtYXgtaGVpZ2h0OiA4MTJweCkge1xuICAgIFtkYXRhLW5leHRqcy1kaWFsb2ctb3ZlcmxheV0ge1xuICAgICAgcGFkZGluZzogMTVweCAxNXB4IDA7XG4gICAgfVxuICB9XG5cbiAgW2RhdGEtbmV4dGpzLWRpYWxvZy1iYWNrZHJvcF0ge1xuICAgIHBvc2l0aW9uOiBmaXhlZDtcbiAgICB0b3A6IDA7XG4gICAgcmlnaHQ6IDA7XG4gICAgYm90dG9tOiAwO1xuICAgIGxlZnQ6IDA7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tY29sb3ItYmFja2Ryb3ApO1xuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcbiAgICBwb2ludGVyLWV2ZW50czogYWxsO1xuICAgIHotaW5kZXg6IC0xO1xuICB9XG5cbiAgW2RhdGEtbmV4dGpzLWRpYWxvZy1iYWNrZHJvcC1maXhlZF0ge1xuICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7XG4gICAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoOHB4KTtcbiAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoOHB4KTtcbiAgfVxuYFxuXG5leHBvcnQgeyBzdHlsZXMgfVxuIl0sIm5hbWVzIjpbInN0eWxlcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/shadow-portal.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/shadow-portal.js ***!
  \***************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ShadowPortal\", ({\n    enumerable: true,\n    get: function() {\n        return ShadowPortal;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _reactdom = __webpack_require__(/*! react-dom */ \"(pages-dir-browser)/./node_modules/react-dom/index.js\");\nconst _shared = __webpack_require__(/*! ../../shared */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nfunction ShadowPortal(param) {\n    _s();\n    let { children } = param;\n    let portalNode = _react.useRef(null);\n    let shadowNode = _react.useRef(null);\n    let [, forceUpdate] = _react.useState();\n    _react.useLayoutEffect({\n        \"ShadowPortal.useLayoutEffect\": ()=>{\n            const ownerDocument = document;\n            portalNode.current = ownerDocument.createElement('nextjs-portal');\n            // load default color preference from localstorage\n            if (typeof localStorage !== 'undefined') {\n                const theme = localStorage.getItem(_shared.STORAGE_KEY_THEME);\n                if (theme === 'dark') {\n                    portalNode.current.classList.add('dark');\n                    portalNode.current.classList.remove('light');\n                } else if (theme === 'light') {\n                    portalNode.current.classList.remove('dark');\n                    portalNode.current.classList.add('light');\n                }\n            }\n            shadowNode.current = portalNode.current.attachShadow({\n                mode: 'open'\n            });\n            ownerDocument.body.appendChild(portalNode.current);\n            forceUpdate({});\n            return ({\n                \"ShadowPortal.useLayoutEffect\": ()=>{\n                    if (portalNode.current && portalNode.current.ownerDocument) {\n                        portalNode.current.ownerDocument.body.removeChild(portalNode.current);\n                    }\n                }\n            })[\"ShadowPortal.useLayoutEffect\"];\n        }\n    }[\"ShadowPortal.useLayoutEffect\"], []);\n    return shadowNode.current ? /*#__PURE__*/ (0, _reactdom.createPortal)(children, shadowNode.current) : null;\n}\n_s(ShadowPortal, \"P7YL0rn/sjH62F7+OsEXN5GMw3U=\");\n_c = ShadowPortal;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=shadow-portal.js.map\nvar _c;\n$RefreshReg$(_c, \"ShadowPortal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/shadow-portal.js\n"));

/***/ })

}]);