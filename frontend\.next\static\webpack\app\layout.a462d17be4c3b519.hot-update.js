"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"033412eb7207\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDMzNDEyZWI3MjA3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiApi: () => (/* binding */ aiApi),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   sessionApi: () => (/* binding */ sessionApi),\n/* harmony export */   tradingApi: () => (/* binding */ tradingApi),\n/* harmony export */   userApi: () => (/* binding */ userApi)\n/* harmony export */ });\n// API utility functions for backend communication\n// Configure base URL - can be overridden if needed for different environments\nconst API_BASE_URL = \"http://localhost:5000\" || 0;\nconsole.log('API Base URL:', API_BASE_URL); // Log the URL to help with debugging\n// Generic fetch wrapper with error handling\nasync function fetchWithAuth(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    // Add auth token if available\n    const token = localStorage.getItem('plutoAuthToken');\n    const headers = {\n        'Content-Type': 'application/json',\n        ...token ? {\n            'Authorization': \"Bearer \".concat(token)\n        } : {},\n        ...options.headers\n    };\n    try {\n        // Add timeout to fetch request\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 10000); // 10 second timeout\n        const response = await fetch(url, {\n            ...options,\n            headers,\n            signal: controller.signal\n        }).finally(()=>clearTimeout(timeoutId));\n        // Handle unauthorized responses\n        if (response.status === 401) {\n            localStorage.removeItem('plutoAuth');\n            localStorage.removeItem('plutoAuthToken');\n            localStorage.removeItem('plutoUser');\n            if (true) {\n                window.location.href = '/login';\n            }\n            throw new Error('Authentication expired. Please login again.');\n        }\n        // Parse JSON response (safely)\n        let data;\n        const contentType = response.headers.get('content-type');\n        if (contentType && contentType.includes('application/json')) {\n            data = await response.json();\n        } else {\n            // Handle non-JSON responses\n            const text = await response.text();\n            try {\n                data = JSON.parse(text);\n            } catch (e) {\n                // If it's not parseable JSON, use the text as is\n                data = {\n                    message: text\n                };\n            }\n        }\n        // Handle API errors\n        if (!response.ok) {\n            console.error('API error response:', {\n                status: response.status,\n                statusText: response.statusText,\n                url: response.url,\n                data: data\n            });\n            // Provide more specific error messages\n            if (response.status === 422) {\n                throw new Error(data.error || data.message || 'Validation error: Please check your input data');\n            } else if (response.status === 401) {\n                throw new Error('Authentication failed. Please log in again.');\n            } else if (response.status === 403) {\n                throw new Error('Access denied. You do not have permission for this action.');\n            } else if (response.status === 404) {\n                throw new Error('Resource not found.');\n            } else if (response.status >= 500) {\n                throw new Error('Server error. Please try again later.');\n            }\n            throw new Error(data.error || data.message || \"API error: \".concat(response.status));\n        }\n        return data;\n    } catch (error) {\n        // Handle network errors specifically\n        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {\n            console.error('Network error - Is the backend server running?:', error);\n            throw new Error('Cannot connect to server. Please check if the backend is running.');\n        }\n        // Handle timeout\n        if (error.name === 'AbortError') {\n            console.error('Request timeout:', error);\n            throw new Error('Request timed out. Server may be unavailable.');\n        }\n        console.error('API request failed:', error);\n        throw error;\n    }\n}\n// Auth API functions\nconst authApi = {\n    login: async (username, password)=>{\n        try {\n            // Use retry mechanism for login attempts\n            const data = await retryWithBackoff(async ()=>{\n                return await fetchWithAuth('/auth/login', {\n                    method: 'POST',\n                    body: JSON.stringify({\n                        username,\n                        password\n                    })\n                });\n            });\n            // The backend returns access_token in the response\n            if (data && data.access_token) {\n                localStorage.setItem('plutoAuthToken', data.access_token);\n                localStorage.setItem('plutoAuth', 'true');\n                // Also store user data if available\n                if (data.user) {\n                    localStorage.setItem('plutoUser', JSON.stringify(data.user));\n                }\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error('Login API error:', error);\n            return false;\n        }\n    },\n    register: async (username, password, email)=>{\n        // Use retry mechanism for registration attempts\n        return retryWithBackoff(async ()=>{\n            return await fetchWithAuth('/auth/register', {\n                method: 'POST',\n                body: JSON.stringify({\n                    username,\n                    password,\n                    email\n                })\n            });\n        });\n    },\n    logout: async ()=>{\n        localStorage.removeItem('plutoAuth');\n        localStorage.removeItem('plutoAuthToken');\n        localStorage.removeItem('plutoUser');\n        return true;\n    }\n};\n// Trading API functions\nconst tradingApi = {\n    getConfig: async (configId)=>{\n        return fetchWithAuth(configId ? \"/trading/config/\".concat(configId) : '/trading/config');\n    },\n    saveConfig: async (config)=>{\n        return fetchWithAuth('/trading/config', {\n            method: 'POST',\n            body: JSON.stringify(config)\n        });\n    },\n    updateConfig: async (configId, config)=>{\n        return fetchWithAuth(\"/trading/config/\".concat(configId), {\n            method: 'PUT',\n            body: JSON.stringify(config)\n        });\n    },\n    startBot: async (configId)=>{\n        return fetchWithAuth(\"/trading/bot/start/\".concat(configId), {\n            method: 'POST'\n        });\n    },\n    stopBot: async (configId)=>{\n        return fetchWithAuth(\"/trading/bot/stop/\".concat(configId), {\n            method: 'POST'\n        });\n    },\n    getBotStatus: async (configId)=>{\n        return fetchWithAuth(\"/trading/bot/status/\".concat(configId));\n    },\n    getTradeHistory: async (configId)=>{\n        const params = configId ? \"?configId=\".concat(configId) : '';\n        return fetchWithAuth(\"/trading/history\".concat(params));\n    },\n    getBalances: async ()=>{\n        return fetchWithAuth('/trading/balances');\n    },\n    getMarketPrice: async (symbol)=>{\n        return fetchWithAuth(\"/trading/market-data/\".concat(symbol));\n    },\n    getTradingPairs: async function() {\n        let exchange = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'binance';\n        return fetchWithAuth(\"/trading/exchange/trading-pairs?exchange=\".concat(exchange));\n    },\n    getCryptocurrencies: async function() {\n        let exchange = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'binance';\n        return fetchWithAuth(\"/trading/exchange/cryptocurrencies?exchange=\".concat(exchange));\n    }\n};\n// User API functions\nconst userApi = {\n    getProfile: async ()=>{\n        return fetchWithAuth('/user/profile');\n    },\n    updateProfile: async (profileData)=>{\n        return fetchWithAuth('/user/profile', {\n            method: 'PUT',\n            body: JSON.stringify(profileData)\n        });\n    },\n    saveApiKeys: async (exchangeData)=>{\n        return fetchWithAuth('/user/apikeys', {\n            method: 'POST',\n            body: JSON.stringify(exchangeData)\n        });\n    },\n    getApiKeys: async ()=>{\n        return fetchWithAuth('/user/apikeys');\n    }\n};\n// AI API functions\nconst aiApi = {\n    getTradingSuggestion: async (data)=>{\n        return fetchWithAuth('/ai/trading-suggestion', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n};\n// Session API functions\nconst sessionApi = {\n    getAllSessions: async function() {\n        let includeInactive = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        return fetchWithAuth(\"/sessions/?include_inactive=\".concat(includeInactive));\n    },\n    createSession: async (sessionData)=>{\n        return fetchWithAuth('/sessions/', {\n            method: 'POST',\n            body: JSON.stringify(sessionData)\n        });\n    },\n    getSession: async (sessionId)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId));\n    },\n    updateSession: async (sessionId, sessionData)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId), {\n            method: 'PUT',\n            body: JSON.stringify(sessionData)\n        });\n    },\n    deleteSession: async (sessionId)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId), {\n            method: 'DELETE'\n        });\n    },\n    activateSession: async (sessionId)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId, \"/activate\"), {\n            method: 'POST'\n        });\n    },\n    getSessionHistory: async (sessionId)=>{\n        return fetchWithAuth(\"/sessions/\".concat(sessionId, \"/history\"));\n    },\n    getActiveSession: async ()=>{\n        return fetchWithAuth('/sessions/active');\n    }\n};\n// Add a retry mechanism for transient connection issues\nconst retryWithBackoff = async function(fn) {\n    let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n    let retries = 0;\n    const execute = async ()=>{\n        try {\n            return await fn();\n        } catch (error) {\n            // Only retry on network errors, not on 4xx/5xx responses\n            if (error instanceof TypeError && error.message.includes('Failed to fetch') || error.name === 'AbortError') {\n                if (retries < maxRetries) {\n                    const delay = Math.pow(2, retries) * 500; // Exponential backoff\n                    console.log(\"Retrying after \".concat(delay, \"ms (attempt \").concat(retries + 1, \"/\").concat(maxRetries, \")...\"));\n                    retries++;\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                    return execute();\n                }\n            }\n            throw error;\n        }\n    };\n    return execute();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});