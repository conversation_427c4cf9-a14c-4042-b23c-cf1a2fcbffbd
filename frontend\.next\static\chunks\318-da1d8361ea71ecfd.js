"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[318],{25731:(t,e,o)=>{o.d(e,{Rk:()=>i,ZQ:()=>a,oc:()=>c});let r="http://localhost:5000";async function n(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o="".concat(r).concat(t),n=localStorage.getItem("plutoAuthToken"),a={"Content-Type":"application/json",...n?{Authorization:"Bearer ".concat(n)}:{},...e.headers};try{let t;let r=new AbortController,n=setTimeout(()=>r.abort(),1e4),c=await fetch(o,{...e,headers:a,signal:r.signal}).finally(()=>clearTimeout(n));if(401===c.status)throw localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),window.location.href="/login",Error("Authentication expired. Please login again.");let i=c.headers.get("content-type");if(i&&i.includes("application/json"))t=await c.json();else{let e=await c.text();try{t=JSON.parse(e)}catch(o){t={message:e}}}if(!c.ok)throw console.error("API error response:",t),Error(t.error||t.message||"API error: ".concat(c.status));return t}catch(t){if(t instanceof TypeError&&t.message.includes("Failed to fetch"))throw console.error("Network error - Is the backend server running?:",t),Error("Cannot connect to server. Please check if the backend is running.");if("AbortError"===t.name)throw console.error("Request timeout:",t),Error("Request timed out. Server may be unavailable.");throw console.error("API request failed:",t),t}}console.log("API Base URL:",r);let a={login:async(t,e)=>{try{let o=await s(async()=>await n("/auth/login",{method:"POST",body:JSON.stringify({username:t,password:e})}));if(o&&o.access_token)return localStorage.setItem("plutoAuthToken",o.access_token),localStorage.setItem("plutoAuth","true"),o.user&&localStorage.setItem("plutoUser",JSON.stringify(o.user)),!0;return!1}catch(t){return console.error("Login API error:",t),!1}},register:async(t,e,o)=>s(async()=>await n("/auth/register",{method:"POST",body:JSON.stringify({username:t,password:e,email:o})})),logout:async()=>(localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),!0)},c={getConfig:async t=>n(t?"/trading/config/".concat(t):"/trading/config"),saveConfig:async t=>n("/trading/config",{method:"POST",body:JSON.stringify(t)}),updateConfig:async(t,e)=>n("/trading/config/".concat(t),{method:"PUT",body:JSON.stringify(e)}),startBot:async t=>n("/trading/bot/start/".concat(t),{method:"POST"}),stopBot:async t=>n("/trading/bot/stop/".concat(t),{method:"POST"}),getBotStatus:async t=>n("/trading/bot/status/".concat(t)),getTradeHistory:async t=>n("/trading/history".concat(t?"?configId=".concat(t):"")),getBalances:async()=>n("/trading/balances"),getMarketPrice:async t=>n("/trading/market-data/".concat(t)),getTradingPairs:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"binance";return n("/trading/exchange/trading-pairs?exchange=".concat(t))},getCryptocurrencies:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"binance";return n("/trading/exchange/cryptocurrencies?exchange=".concat(t))}},i={getAllSessions:async function(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return n("/sessions/?include_inactive=".concat(t))},createSession:async t=>n("/sessions/",{method:"POST",body:JSON.stringify(t)}),getSession:async t=>n("/sessions/".concat(t)),updateSession:async(t,e)=>n("/sessions/".concat(t),{method:"PUT",body:JSON.stringify(e)}),deleteSession:async t=>n("/sessions/".concat(t),{method:"DELETE"}),activateSession:async t=>n("/sessions/".concat(t,"/activate"),{method:"POST"}),getSessionHistory:async t=>n("/sessions/".concat(t,"/history")),getActiveSession:async()=>n("/sessions/active")},s=async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,o=0,r=async()=>{try{return await t()}catch(t){if((t instanceof TypeError&&t.message.includes("Failed to fetch")||"AbortError"===t.name)&&o<e){let t=500*Math.pow(2,o);return console.log("Retrying after ".concat(t,"ms (attempt ").concat(o+1,"/").concat(e,")...")),o++,await new Promise(e=>setTimeout(e,t)),r()}throw t}};return r()}},29348:(t,e,o)=>{o.d(e,{Oh:()=>r,Ql:()=>c,hg:()=>n,vA:()=>a});let r={soundAlertsEnabled:!0,alertOnOrderExecution:!0,alertOnError:!0,soundOrderExecution:"/sounds/order-executed.mp3",soundError:"/sounds/error.mp3",clearOrderHistoryOnStart:!1},n=["BTC","ETH","ADA","SOL","DOGE","LINK","MATIC","DOT","AVAX","XRP","LTC","BCH","BNB","SHIB"],a={BTC:["USDT","USDC","FDUSD","EUR"],ETH:["USDT","USDC","FDUSD","BTC","EUR"],ADA:["USDT","USDC","BTC","ETH"],SOL:["USDT","USDC","BTC","ETH"]},c=["USDT","USDC","FDUSD","DAI"]},59434:(t,e,o)=>{o.d(e,{cn:()=>a});var r=o(52596),n=o(39688);function a(){for(var t=arguments.length,e=Array(t),o=0;o<t;o++)e[o]=arguments[o];return(0,n.QP)((0,r.$)(e))}},77213:(t,e,o)=>{o.d(e,{TradingProvider:()=>P,U:()=>w});var r=o(95155),n=o(12115),a=o(29348),c=o(79737),i=o(87481),s=o(25731),l=o(84553);class d{static getInstance(){return d.instance||(d.instance=new d),d.instance}setupEventListeners(){window.addEventListener("online",this.handleOnline.bind(this)),window.addEventListener("offline",this.handleOffline.bind(this)),document.addEventListener("visibilitychange",()=>{document.hidden||this.checkConnection()})}handleOnline(){console.log("\uD83C\uDF10 Network: Back online"),this.isOnline=!0,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.notifyListeners(!0)}handleOffline(){console.log("\uD83C\uDF10 Network: Gone offline"),this.isOnline=!1,this.notifyListeners(!1)}async checkConnection(){try{let t=(await fetch("/api/health",{method:"HEAD",cache:"no-cache",signal:AbortSignal.timeout(5e3)})).ok;return t!==this.isOnline&&(this.isOnline=t,this.notifyListeners(t),t&&(this.lastOnlineTime=Date.now(),this.reconnectAttempts=0)),t}catch(t){return this.isOnline&&(this.isOnline=!1,this.notifyListeners(!1)),!1}}startPeriodicCheck(){let t=setInterval(()=>{this.checkConnection()},3e4);this.periodicInterval=t}cleanup(){this.periodicInterval&&clearInterval(this.periodicInterval),this.listeners.clear()}notifyListeners(t){this.listeners.forEach(e=>{try{e(t)}catch(t){console.error("Error in network status listener:",t)}})}addListener(t){return this.listeners.add(t),()=>{this.listeners.delete(t)}}getStatus(){return{isOnline:this.isOnline,lastOnlineTime:this.lastOnlineTime,reconnectAttempts:this.reconnectAttempts}}async forceCheck(){return await this.checkConnection()}async attemptReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts)return console.log("\uD83C\uDF10 Network: Max reconnect attempts reached"),!1;this.reconnectAttempts++;let t=Math.min(this.reconnectInterval*Math.pow(2,this.reconnectAttempts-1),3e4);console.log("\uD83C\uDF10 Network: Attempting reconnect ".concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts," in ").concat(t,"ms")),await new Promise(e=>setTimeout(e,t));let e=await this.checkConnection();return!e&&this.reconnectAttempts<this.maxReconnectAttempts&&setTimeout(()=>this.attemptReconnect(),1e3),e}constructor(){this.isOnline=navigator.onLine,this.listeners=new Set,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=5e3,this.setupEventListeners(),this.startPeriodicCheck()}}class u{static getInstance(){return u.instance||(u.instance=new u),u.instance}setupNetworkListener(){this.networkMonitor.addListener(t=>{t&&this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving on network reconnection"),this.saveFunction(),this.lastSaveTime=Date.now())})}setupBeforeUnloadListener(){window.addEventListener("beforeunload",()=>{this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving before page unload"),this.saveFunction())}),document.addEventListener("visibilitychange",()=>{document.hidden&&this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving on tab switch"),this.saveFunction(),this.lastSaveTime=Date.now())})}enable(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3e4;this.saveFunction=t,this.intervalMs=e,this.isEnabled=!0,this.stop(),this.saveInterval=setInterval(()=>{this.isEnabled&&this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(console.log("\uD83D\uDCBE Auto-save: Periodic save"),this.saveFunction(),this.lastSaveTime=Date.now())},this.intervalMs),console.log("\uD83D\uDCBE Auto-save: Enabled with ".concat(e,"ms interval"))}disable(){this.isEnabled=!1,this.stop(),console.log("\uD83D\uDCBE Auto-save: Disabled")}stop(){this.saveInterval&&(clearInterval(this.saveInterval),this.saveInterval=null)}saveNow(){this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(console.log("\uD83D\uDCBE Auto-save: Manual save triggered"),this.saveFunction(),this.lastSaveTime=Date.now())}getStatus(){return{isEnabled:this.isEnabled,lastSaveTime:this.lastSaveTime,intervalMs:this.intervalMs,isOnline:this.networkMonitor.getStatus().isOnline}}constructor(){this.saveInterval=null,this.saveFunction=null,this.intervalMs=3e4,this.isEnabled=!0,this.lastSaveTime=0,this.networkMonitor=d.getInstance(),this.setupNetworkListener(),this.setupBeforeUnloadListener()}}class p{static getInstance(){return p.instance||(p.instance=new p),p.instance}startMonitoring(){"memory"in performance&&(this.checkInterval=setInterval(()=>{this.checkMemoryUsage()},6e4))}checkMemoryUsage(){if("memory"in performance){let t=performance.memory,e=t.usedJSHeapSize;this.notifyListeners(t),e>this.criticalThreshold?(console.warn("\uD83E\uDDE0 Memory: Critical memory usage detected:",{used:"".concat((e/1024/1024).toFixed(2),"MB"),total:"".concat((t.totalJSHeapSize/1024/1024).toFixed(2),"MB"),limit:"".concat((t.jsHeapSizeLimit/1024/1024).toFixed(2),"MB")}),"gc"in window&&window.gc()):e>this.warningThreshold&&console.log("\uD83E\uDDE0 Memory: High memory usage:",{used:"".concat((e/1024/1024).toFixed(2),"MB"),total:"".concat((t.totalJSHeapSize/1024/1024).toFixed(2),"MB")})}}notifyListeners(t){this.listeners.forEach(e=>{try{e(t)}catch(t){console.error("Error in memory monitor listener:",t)}})}addListener(t){return this.listeners.add(t),()=>this.listeners.delete(t)}getMemoryUsage(){return"memory"in performance?performance.memory:null}stop(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null)}constructor(){this.checkInterval=null,this.warningThreshold=0x6400000,this.criticalThreshold=0xc800000,this.listeners=new Set,this.startMonitoring()}}let y=t=>1,S=async t=>{try{let o="".concat(t.crypto1).concat(t.crypto2).toUpperCase();try{let e=await fetch("https://api.binance.com/api/v3/ticker/price?symbol=".concat(o));if(e.ok){let o=await e.json(),r=parseFloat(o.price);if(r>0)return console.log("✅ Price fetched from Binance: ".concat(t.crypto1,"/").concat(t.crypto2," = ").concat(r)),r}}catch(t){console.warn("Binance API failed, trying alternative...",t)}try{let o=g(t.crypto1),r=g(t.crypto2);if(o&&r){let n=await fetch("https://api.coingecko.com/api/v3/simple/price?ids=".concat(o,"&vs_currencies=").concat(r));if(n.ok){var e;let a=await n.json(),c=null===(e=a[o])||void 0===e?void 0:e[r];if(c>0)return console.log("✅ Price fetched from CoinGecko: ".concat(t.crypto1,"/").concat(t.crypto2," = ").concat(c)),c}}}catch(t){console.warn("CoinGecko API failed, using mock price...",t)}let r=m(t);return console.log("⚠️ Using mock price: ".concat(t.crypto1,"/").concat(t.crypto2," = ").concat(r)),r}catch(e){return console.error("Error fetching market price:",e),m(t)}},g=t=>({BTC:"bitcoin",ETH:"ethereum",SOL:"solana",ADA:"cardano",DOT:"polkadot",MATIC:"matic-network",AVAX:"avalanche-2",LINK:"chainlink",UNI:"uniswap",USDT:"tether",USDC:"usd-coin",BUSD:"binance-usd",DAI:"dai"})[t.toUpperCase()]||null,h=t=>({BTC:109e3,ETH:4e3,SOL:240,ADA:1.2,DOGE:.4,LINK:25,MATIC:.5,DOT:8,AVAX:45,SHIB:3e-5,XRP:2.5,LTC:110,BCH:500,UNI:15,AAVE:180,MKR:1800,SNX:3.5,COMP:85,YFI:8500,SUSHI:2.1,"1INCH":.65,CRV:.85,UMA:3.2,ATOM:12,NEAR:6.5,ALGO:.35,ICP:14,HBAR:.28,APT:12.5,TON:5.8,FTM:.95,ONE:.025,FIL:8.5,TRX:.25,ETC:35,VET:.055,QNT:125,LDO:2.8,CRO:.18,LUNC:15e-5,MANA:.85,SAND:.75,AXS:8.5,ENJ:.45,CHZ:.12,THETA:2.1,FLOW:1.2,XTZ:1.8,EOS:1.1,GRT:.28,BAT:.35,ZEC:45,DASH:35,LRC:.45,ZRX:.65,KNC:.85,REN:.15,BAND:2.5,STORJ:.85,NMR:25,ANT:8.5,BNT:.95,MLN:35,REP:15,IOTX:.065,ZIL:.045,ICX:.35,QTUM:4.5,ONT:.45,WAVES:3.2,LSK:1.8,NANO:1.5,SC:.008,DGB:.025,RVN:.035,BTT:15e-7,WIN:15e-5,HOT:.0035,DENT:.0018,NPXS:85e-5,FUN:.0085,CELR:.025,USDT:1,USDC:1,FDUSD:1,BUSD:1,DAI:1})[t.toUpperCase()]||100,m=t=>{let e=h(t.crypto1),o=h(t.crypto2),r=e/o*(1+(Math.random()-.5)*.02);return console.log("\uD83D\uDCCA Fallback price calculation: ".concat(t.crypto1," ($").concat(e,") / ").concat(t.crypto2," ($").concat(o,") = ").concat(r.toFixed(6))),r},f={tradingMode:"SimpleSpot",crypto1:a.hg[0],crypto2:(a.vA[a.hg[0]]||["USDT","USDC","BTC"])[0],baseBid:100,multiplier:1.005,numDigits:4,slippagePercent:.2,incomeSplitCrypto1Percent:50,incomeSplitCrypto2Percent:50,preferredStablecoin:a.Ql[0]},T={config:f,targetPriceRows:[],orderHistory:[],appSettings:a.Oh,currentMarketPrice:y(f),botSystemStatus:"Stopped",crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0,backendStatus:"unknown",connectionStatus:"online"},v=new Map,C="pluto_trading_state",E=t=>{try{{let e={config:t.config,targetPriceRows:t.targetPriceRows,orderHistory:t.orderHistory,appSettings:t.appSettings,currentMarketPrice:t.currentMarketPrice,crypto1Balance:t.crypto1Balance,crypto2Balance:t.crypto2Balance,stablecoinBalance:t.stablecoinBalance,botSystemStatus:t.botSystemStatus,timestamp:Date.now()};localStorage.setItem(C,JSON.stringify(e))}}catch(t){console.error("Failed to save state to localStorage:",t)}},D=()=>{try{{let t=localStorage.getItem(C);if(t){let e=JSON.parse(t);if(e.timestamp&&Date.now()-e.timestamp<864e5)return e}}}catch(t){console.error("Failed to load state from localStorage:",t)}return null},A=(t,e)=>{switch(e.type){case"SET_CONFIG":let o={...t.config,...e.payload};if(e.payload.crypto1||e.payload.crypto2)return{...t,config:o,currentMarketPrice:y(o)};return{...t,config:o};case"SET_TARGET_PRICE_ROWS":return{...t,targetPriceRows:e.payload.sort((t,e)=>t.targetPrice-e.targetPrice).map((t,e)=>({...t,counter:e+1}))};case"ADD_TARGET_PRICE_ROW":{let o=[...t.targetPriceRows,e.payload].sort((t,e)=>t.targetPrice-e.targetPrice).map((t,e)=>({...t,counter:e+1}));return{...t,targetPriceRows:o}}case"UPDATE_TARGET_PRICE_ROW":{let o=t.targetPriceRows.map(t=>t.id===e.payload.id?e.payload:t).sort((t,e)=>t.targetPrice-e.targetPrice).map((t,e)=>({...t,counter:e+1}));return{...t,targetPriceRows:o}}case"REMOVE_TARGET_PRICE_ROW":{let o=t.targetPriceRows.filter(t=>t.id!==e.payload).sort((t,e)=>t.targetPrice-e.targetPrice).map((t,e)=>({...t,counter:e+1}));return{...t,targetPriceRows:o}}case"ADD_ORDER_HISTORY_ENTRY":return{...t,orderHistory:[e.payload,...t.orderHistory]};case"CLEAR_ORDER_HISTORY":return{...t,orderHistory:[]};case"SET_APP_SETTINGS":return{...t,appSettings:{...t.appSettings,...e.payload}};case"SET_MARKET_PRICE":return{...t,currentMarketPrice:e.payload};case"FLUCTUATE_MARKET_PRICE":{if(t.currentMarketPrice<=0)return t;let e=(Math.random()-.5)*.006,o=t.currentMarketPrice*(1+e);return{...t,currentMarketPrice:o>0?o:t.currentMarketPrice}}case"UPDATE_BALANCES":return{...t,crypto1Balance:void 0!==e.payload.crypto1?e.payload.crypto1:t.crypto1Balance,crypto2Balance:void 0!==e.payload.crypto2?e.payload.crypto2:t.crypto2Balance,stablecoinBalance:void 0!==e.payload.stablecoin?e.payload.stablecoin:t.stablecoinBalance};case"UPDATE_STABLECOIN_BALANCE":return{...t,stablecoinBalance:e.payload};case"RESET_SESSION":let r={...t.config};return{...T,config:r,appSettings:{...t.appSettings},currentMarketPrice:y(r)};case"SET_BACKEND_STATUS":return{...t,backendStatus:e.payload};case"SET_CONNECTION_STATUS":if("offline"===e.payload&&"Running"===t.botSystemStatus)return console.warn("\uD83D\uDD34 Connection lost - stopping bot automatically"),{...t,connectionStatus:e.payload,botSystemStatus:"Stopped"};return{...t,connectionStatus:e.payload};case"SET_BALANCES":return{...t,crypto1Balance:e.payload.crypto1,crypto2Balance:e.payload.crypto2};case"SYSTEM_START_BOT_INITIATE":return{...t,botSystemStatus:"WarmingUp"};case"SYSTEM_COMPLETE_WARMUP":return{...t,botSystemStatus:"Running"};case"SYSTEM_STOP_BOT":return{...t,botSystemStatus:"Stopped"};case"SYSTEM_RESET_BOT":let n=t.targetPriceRows.map(e=>({...e,status:"Free",orderLevel:0,valueLevel:t.config.baseBid,crypto1AmountHeld:void 0,originalCostCrypto2:void 0,crypto1Var:void 0,crypto2Var:void 0,lastActionTimestamp:void 0}));return v.clear(),{...t,botSystemStatus:"Stopped",targetPriceRows:n,orderHistory:[]};case"SET_TARGET_PRICE_ROWS":return{...t,targetPriceRows:e.payload};default:return t}},b=(0,n.createContext)(void 0),P=t=>{let{children:e}=t,[o,a]=(0,n.useReducer)(A,(()=>{let t=D();return t?{...T,...t}:T})()),{toast:y}=(0,i.dj)(),g=(0,n.useRef)(null),m=(0,n.useCallback)(async()=>{try{let t=await S(o.config);a({type:"SET_MARKET_PRICE",payload:t})}catch(t){console.error("Failed to fetch market price:",t)}},[o.config,a]);(0,n.useEffect)(()=>{m();let t=setInterval(()=>{a({type:"FLUCTUATE_MARKET_PRICE"})},2e3);return()=>{clearInterval(t)}},[m,a]),(0,n.useEffect)(()=>{g.current=new Audio},[]);let f=(0,n.useCallback)(t=>{if(o.appSettings.soundAlertsEnabled&&g.current){let e;"soundOrderExecution"===t&&o.appSettings.alertOnOrderExecution?e=o.appSettings.soundOrderExecution:"soundError"===t&&o.appSettings.alertOnError&&(e=o.appSettings.soundError),e&&(g.current.src=e,g.current.currentTime=0,g.current.play().then(()=>{setTimeout(()=>{g.current&&(g.current.pause(),g.current.currentTime=0)},2e3)}).catch(t=>console.error("Error playing sound:",t)))}},[o.appSettings]),v=(0,n.useCallback)(async t=>{try{let e=localStorage.getItem("telegram_bot_token"),o=localStorage.getItem("telegram_chat_id");if(!e||!o){console.log("Telegram not configured - skipping notification");return}let r=await fetch("https://api.telegram.org/bot".concat(e,"/sendMessage"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:o,text:t,parse_mode:"HTML"})});r.ok||console.error("Failed to send Telegram notification:",r.statusText)}catch(t){console.error("Error sending Telegram notification:",t)}},[]);(0,n.useEffect)(()=>{},[o.config.crypto1,o.config.crypto2]);let C=(0,n.useCallback)(t=>{t&&Array.isArray(t)&&a({type:"SET_TARGET_PRICE_ROWS",payload:[...t].filter(t=>!isNaN(t)&&t>0).sort((t,e)=>t-e).map((t,e)=>{let r=o.targetPriceRows.find(e=>e.targetPrice===t);return r?{...r,counter:e+1}:{id:(0,c.A)(),counter:e+1,status:"Free",orderLevel:0,valueLevel:o.config.baseBid,targetPrice:t}})})},[o.targetPriceRows,o.config.baseBid,a]);(0,n.useEffect)(()=>{if("Running"!==o.botSystemStatus||"online"!==o.connectionStatus||0===o.targetPriceRows.length||o.currentMarketPrice<=0)return;let{config:t,currentMarketPrice:e,targetPriceRows:r,crypto1Balance:n,crypto2Balance:i}=o,s=[...r].sort((t,e)=>t.targetPrice-e.targetPrice),l=n,d=i,u=0;console.log("\uD83D\uDE80 CONTINUOUS TRADING: Price $".concat(e.toFixed(2)," | Targets: ").concat(s.length," | Balance: $").concat(d," ").concat(t.crypto2));let p=s.filter(o=>Math.abs(e-o.targetPrice)/e*100<=t.slippagePercent);p.length>0&&console.log("\uD83C\uDFAF TARGETS IN RANGE (\xb1".concat(t.slippagePercent,"%):"),p.map(t=>"Counter ".concat(t.counter," (").concat(t.status,")")));for(let o=0;o<s.length;o++){let r=s[o];if(Math.abs(e-r.targetPrice)/e*100<=t.slippagePercent){if("SimpleSpot"===t.tradingMode){if("Free"===r.status){let o=r.valueLevel;if(d>=o){let n=o/e;a({type:"UPDATE_TARGET_PRICE_ROW",payload:{...r,status:"Full",orderLevel:r.orderLevel+1,valueLevel:t.baseBid*Math.pow(t.multiplier,r.orderLevel+1),crypto1AmountHeld:n,originalCostCrypto2:o,crypto1Var:n,crypto2Var:-o}}),a({type:"UPDATE_BALANCES",payload:{crypto1:l+n,crypto2:d-o}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(t.crypto1,"/").concat(t.crypto2),crypto1:t.crypto1,orderType:"BUY",amountCrypto1:n,avgPrice:e,valueCrypto2:o,price1:e,crypto1Symbol:t.crypto1||"",crypto2Symbol:t.crypto2||""}}),console.log("✅ BUY: Counter ".concat(r.counter," bought ").concat(n.toFixed(6)," ").concat(t.crypto1," at $").concat(e.toFixed(2))),y({title:"BUY Executed",description:"Counter ".concat(r.counter,": ").concat(n.toFixed(6)," ").concat(t.crypto1),duration:2e3}),f("soundOrderExecution"),v("\uD83D\uDFE2 <b>BUY EXECUTED</b>\n"+"\uD83D\uDCCA Counter: ".concat(r.counter,"\n")+"\uD83D\uDCB0 Amount: ".concat(n.toFixed(6)," ").concat(t.crypto1,"\n")+"\uD83D\uDCB5 Price: $".concat(e.toFixed(2),"\n")+"\uD83D\uDCB8 Cost: $".concat(o.toFixed(2)," ").concat(t.crypto2,"\n")+"\uD83D\uDCC8 Mode: Simple Spot"),u++,d-=o,l+=n}}let o=r.counter,n=s.find(t=>t.counter===o-1);if(n&&"Full"===n.status&&n.crypto1AmountHeld&&n.originalCostCrypto2){let r=n.crypto1AmountHeld,i=r*e,s=i-n.originalCostCrypto2,p=e>0?s*t.incomeSplitCrypto1Percent/100/e:0;a({type:"UPDATE_TARGET_PRICE_ROW",payload:{...n,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:t.baseBid*Math.pow(t.multiplier,n.orderLevel),crypto1Var:-r,crypto2Var:i}}),a({type:"UPDATE_BALANCES",payload:{crypto1:l-r,crypto2:d+i}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(t.crypto1,"/").concat(t.crypto2),crypto1:t.crypto1,orderType:"SELL",amountCrypto1:r,avgPrice:e,valueCrypto2:i,price1:e,crypto1Symbol:t.crypto1||"",crypto2Symbol:t.crypto2||"",realizedProfitLossCrypto2:s,realizedProfitLossCrypto1:p}}),console.log("✅ SELL: Counter ".concat(o-1," sold ").concat(r.toFixed(6)," ").concat(t.crypto1,". Profit: $").concat(s.toFixed(2))),y({title:"SELL Executed",description:"Counter ".concat(o-1,": Profit $").concat(s.toFixed(2)),duration:2e3}),f("soundOrderExecution");let S=s>0?"\uD83D\uDCC8":s<0?"\uD83D\uDCC9":"➖";v("\uD83D\uDD34 <b>SELL EXECUTED</b>\n"+"\uD83D\uDCCA Counter: ".concat(o-1,"\n")+"\uD83D\uDCB0 Amount: ".concat(r.toFixed(6)," ").concat(t.crypto1,"\n")+"\uD83D\uDCB5 Price: $".concat(e.toFixed(2),"\n")+"\uD83D\uDCB8 Received: $".concat(i.toFixed(2)," ").concat(t.crypto2,"\n")+"".concat(S," Profit: $").concat(s.toFixed(2)," ").concat(t.crypto2,"\n")+"\uD83D\uDCC8 Mode: Simple Spot"),u++,l-=r,d+=i}}else if("StablecoinSwap"===t.tradingMode){if("Free"===r.status){let e=r.valueLevel;if(d>=e){let o=h(t.crypto2||"USDT")/h(t.preferredStablecoin||"USDT"),n=e*o,i=h(t.crypto1||"BTC")/h(t.preferredStablecoin||"USDT"),s=n/i,p=r.orderLevel+1,S=t.baseBid*Math.pow(t.multiplier,p);a({type:"UPDATE_TARGET_PRICE_ROW",payload:{...r,status:"Full",orderLevel:p,valueLevel:S,crypto1AmountHeld:s,originalCostCrypto2:e,crypto1Var:s,crypto2Var:-e}}),a({type:"UPDATE_BALANCES",payload:{crypto1:l+s,crypto2:d-e}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(t.crypto2,"/").concat(t.preferredStablecoin),crypto1:t.crypto2,orderType:"SELL",amountCrypto1:e,avgPrice:o,valueCrypto2:n,price1:o,crypto1Symbol:t.crypto2||"",crypto2Symbol:t.preferredStablecoin||""}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(t.crypto1,"/").concat(t.preferredStablecoin),crypto1:t.crypto1,orderType:"BUY",amountCrypto1:s,avgPrice:i,valueCrypto2:n,price1:i,crypto1Symbol:t.crypto1||"",crypto2Symbol:t.preferredStablecoin||""}}),console.log("✅ STABLECOIN BUY: Counter ".concat(r.counter," | Step 1: Sold ").concat(e," ").concat(t.crypto2," → ").concat(n.toFixed(2)," ").concat(t.preferredStablecoin," | Step 2: Bought ").concat(s.toFixed(6)," ").concat(t.crypto1," | Level: ").concat(r.orderLevel," → ").concat(p)),y({title:"BUY Executed (Stablecoin)",description:"Counter ".concat(r.counter,": ").concat(s.toFixed(6)," ").concat(t.crypto1," via ").concat(t.preferredStablecoin),duration:2e3}),f("soundOrderExecution"),v("\uD83D\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\n"+"\uD83D\uDCCA Counter: ".concat(r.counter,"\n")+"\uD83D\uDD04 Step 1: Sold ".concat(e.toFixed(2)," ").concat(t.crypto2," → ").concat(n.toFixed(2)," ").concat(t.preferredStablecoin,"\n")+"\uD83D\uDD04 Step 2: Bought ".concat(s.toFixed(6)," ").concat(t.crypto1,"\n")+"\uD83D\uDCCA Level: ".concat(r.orderLevel," → ").concat(p,"\n")+"\uD83D\uDCC8 Mode: Stablecoin Swap"),u++,d-=e,l+=s}}let e=r.counter,o=s.find(t=>t.counter===e-1);if(o&&"Full"===o.status&&o.crypto1AmountHeld&&o.originalCostCrypto2){let r=o.crypto1AmountHeld,n=h(t.crypto1||"BTC")/h(t.preferredStablecoin||"USDT"),i=r*n,s=h(t.crypto2||"USDT")/h(t.preferredStablecoin||"USDT"),p=i/s,S=p-o.originalCostCrypto2,g=n>0?S*t.incomeSplitCrypto1Percent/100/n:0;a({type:"UPDATE_TARGET_PRICE_ROW",payload:{...o,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:t.baseBid*Math.pow(t.multiplier,o.orderLevel),crypto1Var:0,crypto2Var:0}}),a({type:"UPDATE_BALANCES",payload:{crypto1:l-r,crypto2:d+p}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(t.crypto1,"/").concat(t.preferredStablecoin),crypto1:t.crypto1,orderType:"SELL",amountCrypto1:r,avgPrice:n,valueCrypto2:i,price1:n,crypto1Symbol:t.crypto1||"",crypto2Symbol:t.preferredStablecoin||""}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(t.crypto2,"/").concat(t.preferredStablecoin),crypto1:t.crypto2,orderType:"BUY",amountCrypto1:p,avgPrice:s,valueCrypto2:i,price1:s,crypto1Symbol:t.crypto2||"",crypto2Symbol:t.preferredStablecoin||"",realizedProfitLossCrypto2:S,realizedProfitLossCrypto1:g}}),console.log("✅ STABLECOIN SELL: Counter ".concat(e-1," | Step A: Sold ").concat(r.toFixed(6)," ").concat(t.crypto1," → ").concat(i.toFixed(2)," ").concat(t.preferredStablecoin," | Step B: Bought ").concat(p.toFixed(2)," ").concat(t.crypto2," | Profit: ").concat(S.toFixed(2)," ").concat(t.crypto2," | Level: ").concat(o.orderLevel," (unchanged)")),y({title:"SELL Executed (Stablecoin)",description:"Counter ".concat(e-1,": Profit ").concat(S.toFixed(2)," ").concat(t.crypto2," via ").concat(t.preferredStablecoin),duration:2e3}),f("soundOrderExecution");let m=S>0?"\uD83D\uDCC8":S<0?"\uD83D\uDCC9":"➖";v("\uD83D\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\n"+"\uD83D\uDCCA Counter: ".concat(e-1,"\n")+"\uD83D\uDD04 Step A: Sold ".concat(r.toFixed(6)," ").concat(t.crypto1," → ").concat(i.toFixed(2)," ").concat(t.preferredStablecoin,"\n")+"\uD83D\uDD04 Step B: Bought ".concat(p.toFixed(2)," ").concat(t.crypto2,"\n")+"".concat(m," Profit: ").concat(S.toFixed(2)," ").concat(t.crypto2,"\n")+"\uD83D\uDCCA Level: ".concat(o.orderLevel," (unchanged)\n")+"\uD83D\uDCC8 Mode: Stablecoin Swap"),u++,l-=r,d+=p}}}}u>0&&console.log("\uD83C\uDFAF CYCLE COMPLETE: ".concat(u," actions taken at price $").concat(e.toFixed(2)))},[o.botSystemStatus,o.currentMarketPrice,o.targetPriceRows,o.config,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,a,y,f,v]);let P=(0,n.useCallback)(()=>o.targetPriceRows&&Array.isArray(o.targetPriceRows)?o.targetPriceRows.map(t=>{let e,r;let n=o.currentMarketPrice||0,a=t.targetPrice||0;if("Full"===t.status&&t.crypto1AmountHeld&&t.originalCostCrypto2){let a=n*t.crypto1AmountHeld-t.originalCostCrypto2;r=a*o.config.incomeSplitCrypto2Percent/100,n>0&&(e=a*o.config.incomeSplitCrypto1Percent/100/n)}return{...t,currentPrice:n,priceDifference:a-n,priceDifferencePercent:n>0?(a-n)/n*100:0,potentialProfitCrypto1:o.config.incomeSplitCrypto1Percent/100*t.valueLevel/(a||1),potentialProfitCrypto2:o.config.incomeSplitCrypto2Percent/100*t.valueLevel,percentFromActualPrice:n&&a?(n/a-1)*100:0,incomeCrypto1:e,incomeCrypto2:r}}).sort((t,e)=>e.targetPrice-t.targetPrice):[],[o.targetPriceRows,o.currentMarketPrice,o.config.incomeSplitCrypto1Percent,o.config.incomeSplitCrypto2Percent,o.config.baseBid,o.config.multiplier]),w=(0,n.useCallback)(async t=>{try{var e;let r={name:"".concat(t.crypto1,"/").concat(t.crypto2," ").concat(t.tradingMode),tradingMode:t.tradingMode,crypto1:t.crypto1,crypto2:t.crypto2,baseBid:t.baseBid,multiplier:t.multiplier,numDigits:t.numDigits,slippagePercent:t.slippagePercent,incomeSplitCrypto1Percent:t.incomeSplitCrypto1Percent,incomeSplitCrypto2Percent:t.incomeSplitCrypto2Percent,preferredStablecoin:t.preferredStablecoin,targetPrices:o.targetPriceRows.map(t=>t.targetPrice)},n=await s.oc.saveConfig(r);return console.log("✅ Config saved to backend:",n),(null===(e=n.config)||void 0===e?void 0:e.id)||null}catch(t){return console.error("❌ Failed to save config to backend:",t),y({title:"Backend Error",description:"Failed to save configuration to backend",variant:"destructive",duration:3e3}),null}},[o.targetPriceRows,y]),B=(0,n.useCallback)(async t=>{try{let e=await s.oc.startBot(t);return console.log("✅ Bot started on backend:",e),y({title:"Bot Started",description:"Trading bot started successfully on backend",duration:3e3}),!0}catch(t){return console.error("❌ Failed to start bot on backend:",t),y({title:"Backend Error",description:"Failed to start bot on backend",variant:"destructive",duration:3e3}),!1}},[y]),R=(0,n.useCallback)(async t=>{try{let e=await s.oc.stopBot(t);return console.log("✅ Bot stopped on backend:",e),y({title:"Bot Stopped",description:"Trading bot stopped successfully on backend",duration:3e3}),!0}catch(t){return console.error("❌ Failed to stop bot on backend:",t),y({title:"Backend Error",description:"Failed to stop bot on backend",variant:"destructive",duration:3e3}),!1}},[y]),I=(0,n.useCallback)(async()=>{let t="http://localhost:5000";if(!t){console.error("Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed."),a({type:"SET_BACKEND_STATUS",payload:"offline"});return}try{let e=await fetch("".concat(t,"/health/"));if(!e.ok){console.error("Backend health check failed with status: ".concat(e.status," ").concat(e.statusText));let t=await e.text().catch(()=>"Could not read response text.");console.error("Backend health check response body:",t)}a({type:"SET_BACKEND_STATUS",payload:e.ok?"online":"offline"})}catch(e){a({type:"SET_BACKEND_STATUS",payload:"offline"}),console.error("Backend connectivity check failed. Error details:",e),e.cause&&console.error("Fetch error cause:",e.cause),console.error("Attempted to fetch API URL:","".concat(t,"/health/"))}},[a]);(0,n.useEffect)(()=>{I()},[I]),(0,n.useEffect)(()=>{E(o)},[o]),(0,n.useEffect)(()=>{"WarmingUp"===o.botSystemStatus&&(console.log("Bot is Warming Up... Immediate execution enabled."),a({type:"SYSTEM_COMPLETE_WARMUP"}),console.log("Bot is now Running immediately."))},[o.botSystemStatus,a]),(0,n.useEffect)(()=>{let t=d.getInstance(),e=u.getInstance(),r=p.getInstance(),n=l.C.getInstance(),a=t.addListener(t=>{console.log("\uD83C\uDF10 Network status changed: ".concat(t?"Online":"Offline")),t?y({title:"Network Reconnected",description:"Connection restored. Auto-saving enabled.",duration:3e3}):y({title:"Network Disconnected",description:"You are currently offline. Data will be saved locally.",variant:"destructive",duration:5e3})}),c=r.addListener(t=>{let e=t.usedJSHeapSize/1024/1024;e>150&&(console.warn("\uD83E\uDDE0 High memory usage: ".concat(e.toFixed(2),"MB")),y({title:"High Memory Usage",description:"Memory usage is high (".concat(e.toFixed(0),"MB). Consider refreshing the page."),variant:"destructive",duration:5e3}))});return e.enable(()=>{try{let t=n.getCurrentSessionId();t&&n.saveSession(t,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,"Running"===o.botSystemStatus),E(o)}catch(t){console.error("Auto-save failed:",t)}},3e4),()=>{a(),c(),e.disable()}},[o,y]),(0,n.useEffect)(()=>{u.getInstance().saveNow()},[o.botSystemStatus]);let O=(0,n.useCallback)(t=>{a({type:"SET_CONNECTION_STATUS",payload:t})},[a]),k={...o,dispatch:a,setTargetPrices:C,getDisplayOrders:P,checkBackendStatus:I,fetchMarketPrice:m,setConnectionStatus:O,startBackendBot:B,stopBackendBot:R,saveConfigToBackend:w,backendStatus:o.backendStatus,connectionStatus:o.connectionStatus,botSystemStatus:o.botSystemStatus,isBotActive:"Running"===o.botSystemStatus};return(0,r.jsx)(b.Provider,{value:k,children:e})},w=()=>{let t=(0,n.useContext)(b);if(void 0===t)throw Error("useTradingContext must be used within a TradingProvider");return t}},84553:(t,e,o)=>{o.d(e,{C:()=>i});var r=o(79737),n=o(25731);let a="pluto_trading_sessions",c="pluto_current_session";class i{static getInstance(){return i.instance||(i.instance=new i),i.instance}async checkBackendConnection(){try{let t=new AbortController,e=setTimeout(()=>t.abort(),1500),o=await fetch("http://localhost:5000/",{method:"GET",signal:t.signal});if(clearTimeout(e),o.status<500)this.useBackend=!0,console.log("✅ Session Manager: Backend connection established");else throw Error("Backend returned server error")}catch(t){this.useBackend=!1,console.warn("⚠️ Session Manager: Backend unavailable, using localStorage fallback"),console.warn("\uD83D\uDCA1 To enable backend features, start the backend server: python run.py")}}loadSessionsFromStorage(){try{let t=localStorage.getItem(a),e=localStorage.getItem(c);if(t){let e=JSON.parse(t);this.sessions=new Map(Object.entries(e))}this.currentSessionId=e}catch(t){console.error("Failed to load sessions from storage:",t)}}saveSessionsToStorage(){try{let t=Object.fromEntries(this.sessions);localStorage.setItem(a,JSON.stringify(t)),this.currentSessionId&&localStorage.setItem(c,this.currentSessionId)}catch(t){console.error("Failed to save sessions to storage:",t)}}async createNewSession(t,e){if(this.useBackend)try{let o=(await n.Rk.createSession({name:t,config:e,targetPriceRows:[],currentMarketPrice:0,crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0})).session.id;return console.log("✅ Session created on backend:",o),o}catch(t){console.error("❌ Failed to create session on backend, falling back to localStorage:",t),this.useBackend=!1}let o=(0,r.A)(),a=Date.now();return this.sessions.set(o,{id:o,name:t,config:e,targetPriceRows:[],orderHistory:[],currentMarketPrice:0,crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0,createdAt:a,lastModified:a,isActive:!1,runtime:0}),this.saveSessionsToStorage(),o}saveSession(t,e,o,r,n,a,c,i){let s=arguments.length>8&&void 0!==arguments[8]&&arguments[8];try{let l=this.sessions.get(t);if(!l)return console.error("Session not found:",t),!1;let d={...l,config:e,targetPriceRows:[...o],orderHistory:[...r],currentMarketPrice:n,crypto1Balance:a,crypto2Balance:c,stablecoinBalance:i,isActive:s,lastModified:Date.now(),runtime:l.runtime+(Date.now()-l.lastModified)};return this.sessions.set(t,d),this.saveSessionsToStorage(),!0}catch(t){return console.error("Failed to save session:",t),!1}}loadSession(t){return this.sessions.get(t)||null}deleteSession(t){let e=this.sessions.delete(t);return e&&(this.currentSessionId===t&&(this.currentSessionId=null,localStorage.removeItem(c)),this.saveSessionsToStorage()),e}getAllSessions(){return Array.from(this.sessions.values()).map(t=>({id:t.id,name:t.name,pair:"".concat(t.config.crypto1,"/").concat(t.config.crypto2),createdAt:t.createdAt,lastModified:t.lastModified,isActive:t.isActive,runtime:t.runtime,totalTrades:t.orderHistory.length,totalProfitLoss:t.orderHistory.filter(t=>"SELL"===t.orderType&&void 0!==t.realizedProfitLossCrypto2).reduce((t,e)=>t+(e.realizedProfitLossCrypto2||0),0)}))}setCurrentSession(t){this.sessions.has(t)&&(this.currentSessionId=t,localStorage.setItem(c,t))}getCurrentSessionId(){return this.currentSessionId}exportSessionToJSON(t){let e=this.sessions.get(t);return e?JSON.stringify(e,null,2):null}importSessionFromJSON(t){try{let e=JSON.parse(t),o=(0,r.A)(),n={...e,id:o,isActive:!1,lastModified:Date.now()};return this.sessions.set(o,n),this.saveSessionsToStorage(),o}catch(t){return console.error("Failed to import session:",t),null}}renameSession(t,e){let o=this.sessions.get(t);return!!o&&(o.name=e,o.lastModified=Date.now(),this.sessions.set(t,o),this.saveSessionsToStorage(),!0)}getSessionHistory(t){let e=this.sessions.get(t);return e?[...e.orderHistory]:[]}exportSessionToCSV(t){let e=this.sessions.get(t);return e?["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...e.orderHistory.map(t=>{var o,r,n,a,c,i,s;return[new Date(t.timestamp).toISOString().split("T")[0],new Date(t.timestamp).toTimeString().split(" ")[0],t.pair,t.crypto1Symbol,t.orderType,(null===(o=t.amountCrypto1)||void 0===o?void 0:o.toFixed(e.config.numDigits))||"",(null===(r=t.avgPrice)||void 0===r?void 0:r.toFixed(e.config.numDigits))||"",(null===(n=t.valueCrypto2)||void 0===n?void 0:n.toFixed(e.config.numDigits))||"",(null===(a=t.price1)||void 0===a?void 0:a.toFixed(e.config.numDigits))||"",t.crypto1Symbol,(null===(c=t.price2)||void 0===c?void 0:c.toFixed(e.config.numDigits))||"",t.crypto2Symbol,(null===(i=t.realizedProfitLossCrypto1)||void 0===i?void 0:i.toFixed(e.config.numDigits))||"",(null===(s=t.realizedProfitLossCrypto2)||void 0===s?void 0:s.toFixed(e.config.numDigits))||""].join(",")})].join("\n"):null}clearAllSessions(){this.sessions.clear(),this.currentSessionId=null,localStorage.removeItem(a),localStorage.removeItem(c)}enableAutoSave(t,e){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e4,r=setInterval(()=>{let o=e();this.saveSession(t,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,o.isActive)},o);return()=>clearInterval(r)}constructor(){this.sessions=new Map,this.currentSessionId=null,this.useBackend=!0,this.loadSessionsFromStorage(),this.useBackend=!1,setTimeout(()=>{this.checkBackendConnection().catch(()=>{})},1e3)}}},87481:(t,e,o)=>{o.d(e,{dj:()=>p});var r=o(12115);let n=0,a=new Map,c=t=>{if(a.has(t))return;let e=setTimeout(()=>{a.delete(t),d({type:"REMOVE_TOAST",toastId:t})},1e6);a.set(t,e)},i=(t,e)=>{switch(e.type){case"ADD_TOAST":return{...t,toasts:[e.toast,...t.toasts].slice(0,1)};case"UPDATE_TOAST":return{...t,toasts:t.toasts.map(t=>t.id===e.toast.id?{...t,...e.toast}:t)};case"DISMISS_TOAST":{let{toastId:o}=e;return o?c(o):t.toasts.forEach(t=>{c(t.id)}),{...t,toasts:t.toasts.map(t=>t.id===o||void 0===o?{...t,open:!1}:t)}}case"REMOVE_TOAST":if(void 0===e.toastId)return{...t,toasts:[]};return{...t,toasts:t.toasts.filter(t=>t.id!==e.toastId)}}},s=[],l={toasts:[]};function d(t){l=i(l,t),s.forEach(t=>{t(l)})}function u(t){let{...e}=t,o=(n=(n+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>d({type:"DISMISS_TOAST",toastId:o});return d({type:"ADD_TOAST",toast:{...e,id:o,open:!0,onOpenChange:t=>{t||r()}}}),{id:o,dismiss:r,update:t=>d({type:"UPDATE_TOAST",toast:{...t,id:o}})}}function p(){let[t,e]=r.useState(l);return r.useEffect(()=>(s.push(e),()=>{let t=s.indexOf(e);t>-1&&s.splice(t,1)}),[t]),{...t,toast:u,dismiss:t=>d({type:"DISMISS_TOAST",toastId:t})}}}}]);