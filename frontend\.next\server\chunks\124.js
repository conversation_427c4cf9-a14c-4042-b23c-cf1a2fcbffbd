exports.id=124,exports.ids=[124],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var o=r(49384),n=r(82348);function i(...e){return(0,n.QP)((0,o.$)(e))}},5551:(e,t,r)=>{"use strict";r.d(t,{C:()=>s});var o=r(74112),n=r(62185);let i="pluto_current_session";class s{constructor(){this.sessions=new Map,this.currentSessionId=null,this.useBackend=!0,this.loadSessionsFromStorage(),this.useBackend=!1,setTimeout(()=>{this.checkBackendConnection().catch(()=>{})},1e3)}static getInstance(){return s.instance||(s.instance=new s),s.instance}async checkBackendConnection(){try{let e=new AbortController,t=setTimeout(()=>e.abort(),1500),r=await fetch("http://localhost:5000/",{method:"GET",signal:e.signal});if(clearTimeout(t),r.status<500)this.useBackend=!0,console.log("✅ Session Manager: Backend connection established");else throw Error("Backend returned server error")}catch(e){this.useBackend=!1,console.warn("⚠️ Session Manager: Backend unavailable, using localStorage fallback"),console.warn("\uD83D\uDCA1 To enable backend features, start the backend server: python run.py")}}loadSessionsFromStorage(){try{return}catch(e){console.error("Failed to load sessions from storage:",e)}}saveSessionsToStorage(){try{return}catch(e){console.error("Failed to save sessions to storage:",e)}}async createNewSession(e,t){if(this.useBackend)try{let r=(await n.Rk.createSession({name:e,config:t,targetPriceRows:[],currentMarketPrice:0,crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0})).session.id;return console.log("✅ Session created on backend:",r),r}catch(e){console.error("❌ Failed to create session on backend, falling back to localStorage:",e),this.useBackend=!1}let r=(0,o.A)(),i=Date.now();return this.sessions.set(r,{id:r,name:e,config:t,targetPriceRows:[],orderHistory:[],currentMarketPrice:0,crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0,createdAt:i,lastModified:i,isActive:!1,runtime:0}),this.saveSessionsToStorage(),r}saveSession(e,t,r,o,n,i,s,a,c=!1){try{let l=this.sessions.get(e);if(!l)return console.error("Session not found:",e),!1;let d={...l,config:t,targetPriceRows:[...r],orderHistory:[...o],currentMarketPrice:n,crypto1Balance:i,crypto2Balance:s,stablecoinBalance:a,isActive:c,lastModified:Date.now(),runtime:l.runtime+(Date.now()-l.lastModified)};return this.sessions.set(e,d),this.saveSessionsToStorage(),!0}catch(e){return console.error("Failed to save session:",e),!1}}loadSession(e){return this.sessions.get(e)||null}deleteSession(e){let t=this.sessions.delete(e);return t&&(this.currentSessionId===e&&(this.currentSessionId=null,localStorage.removeItem(i)),this.saveSessionsToStorage()),t}getAllSessions(){return Array.from(this.sessions.values()).map(e=>({id:e.id,name:e.name,pair:`${e.config.crypto1}/${e.config.crypto2}`,createdAt:e.createdAt,lastModified:e.lastModified,isActive:e.isActive,runtime:e.runtime,totalTrades:e.orderHistory.length,totalProfitLoss:e.orderHistory.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0)}))}setCurrentSession(e){this.sessions.has(e)&&(this.currentSessionId=e,localStorage.setItem(i,e))}getCurrentSessionId(){return this.currentSessionId}exportSessionToJSON(e){let t=this.sessions.get(e);return t?JSON.stringify(t,null,2):null}importSessionFromJSON(e){try{let t=JSON.parse(e),r=(0,o.A)(),n={...t,id:r,isActive:!1,lastModified:Date.now()};return this.sessions.set(r,n),this.saveSessionsToStorage(),r}catch(e){return console.error("Failed to import session:",e),null}}renameSession(e,t){let r=this.sessions.get(e);return!!r&&(r.name=t,r.lastModified=Date.now(),this.sessions.set(e,r),this.saveSessionsToStorage(),!0)}getSessionHistory(e){let t=this.sessions.get(e);return t?[...t.orderHistory]:[]}exportSessionToCSV(e){let t=this.sessions.get(e);return t?["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...t.orderHistory.map(e=>[new Date(e.timestamp).toISOString().split("T")[0],new Date(e.timestamp).toTimeString().split(" ")[0],e.pair,e.crypto1Symbol,e.orderType,e.amountCrypto1?.toFixed(t.config.numDigits)||"",e.avgPrice?.toFixed(t.config.numDigits)||"",e.valueCrypto2?.toFixed(t.config.numDigits)||"",e.price1?.toFixed(t.config.numDigits)||"",e.crypto1Symbol,e.price2?.toFixed(t.config.numDigits)||"",e.crypto2Symbol,e.realizedProfitLossCrypto1?.toFixed(t.config.numDigits)||"",e.realizedProfitLossCrypto2?.toFixed(t.config.numDigits)||""].join(","))].join("\n"):null}clearAllSessions(){this.sessions.clear(),this.currentSessionId=null,localStorage.removeItem("pluto_trading_sessions"),localStorage.removeItem(i)}enableAutoSave(e,t,r=3e4){let o=setInterval(()=>{let r=t();this.saveSession(e,r.config,r.targetPriceRows,r.orderHistory,r.currentMarketPrice,r.crypto1Balance,r.crypto2Balance,r.stablecoinBalance,r.isActive)},r);return()=>clearInterval(o)}}},8641:(e,t,r)=>{"use strict";r.d(t,{AIProvider:()=>c,f:()=>l});var o=r(60687),n=r(43210),i=r(6475);let s=(0,i.createServerReference)("406b51c45f1e93f88b682f8a8fff734ab97ae270e5",i.callServer,void 0,i.findSourceMapURL,"suggestTradingMode"),a=(0,n.createContext)(void 0),c=({children:e})=>{let[t,r]=(0,n.useState)(null),[i,c]=(0,n.useState)(!1),[l,d]=(0,n.useState)(null),u=async e=>{c(!0),d(null),r(null);try{let t=await s(e);r(t)}catch(e){d(e instanceof Error?e.message:"An unknown error occurred during AI suggestion."),console.error("Error fetching trading mode suggestion:",e)}finally{c(!1)}};return(0,o.jsx)(a.Provider,{value:{suggestion:t,isLoading:i,error:l,getTradingModeSuggestion:u},children:e})},l=()=>{let e=(0,n.useContext)(a);if(void 0===e)throw Error("useAIContext must be used within an AIProvider");return e}},10074:(e,t,r)=>{Promise.resolve().then(r.bind(r,14947)),Promise.resolve().then(r.bind(r,8641)),Promise.resolve().then(r.bind(r,63213)),Promise.resolve().then(r.bind(r,78895))},14947:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>f});var o=r(60687),n=r(29867),i=r(43210),s=r(47313),a=r(24224),c=r(78726),l=r(4780);let d=s.Kq,u=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(s.LM,{ref:r,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));u.displayName=s.LM.displayName;let p=(0,a.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),y=i.forwardRef(({className:e,variant:t,...r},n)=>(0,o.jsx)(s.bL,{ref:n,className:(0,l.cn)(p({variant:t}),e),...r}));y.displayName=s.bL.displayName,i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(s.rc,{ref:r,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=s.rc.displayName;let g=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(s.bm,{ref:r,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,o.jsx)(c.A,{className:"h-4 w-4"})}));g.displayName=s.bm.displayName;let h=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(s.hE,{ref:r,className:(0,l.cn)("text-sm font-semibold",e),...t}));h.displayName=s.hE.displayName;let m=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(s.VY,{ref:r,className:(0,l.cn)("text-sm opacity-90",e),...t}));function f(){let{toasts:e}=(0,n.dj)();return(0,o.jsxs)(d,{children:[e.map(function({id:e,title:t,description:r,action:n,...i}){return(0,o.jsxs)(y,{...i,children:[(0,o.jsxs)("div",{className:"grid gap-1",children:[t&&(0,o.jsx)(h,{children:t}),r&&(0,o.jsx)(m,{children:r})]}),n,(0,o.jsx)(g,{})]},e)}),(0,o.jsx)(u,{})]})}m.displayName=s.VY.displayName},16949:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},26443:(e,t,r)=>{"use strict";r.d(t,{AIProvider:()=>n});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call AIProvider() from the server but AIProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AIContext.tsx","AIProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useAIContext() from the server but useAIContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AIContext.tsx","useAIContext")},29131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AuthContext.tsx","useAuth")},29867:(e,t,r)=>{"use strict";r.d(t,{dj:()=>p});var o=r(43210);let n=0,i=new Map,s=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},a=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?s(r):e.toasts.forEach(e=>{s(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[],l={toasts:[]};function d(e){l=a(l,e),c.forEach(e=>{e(l)})}function u({...e}){let t=(n=(n+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>d({type:"DISMISS_TOAST",toastId:t});return d({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function p(){let[e,t]=o.useState(l);return o.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);e>-1&&c.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},47002:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=47002,e.exports=t},47506:(e,t,r)=>{"use strict";r.d(t,{TradingProvider:()=>n});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call TradingProvider() from the server but TradingProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\TradingContext.tsx","TradingProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useTradingContext() from the server but useTradingContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\TradingContext.tsx","useTradingContext")},55280:(e,t,r)=>{"use strict";r.d(t,{Oh:()=>o,Ql:()=>s,hg:()=>n,vA:()=>i});let o={soundAlertsEnabled:!0,alertOnOrderExecution:!0,alertOnError:!0,soundOrderExecution:"/sounds/order-executed.mp3",soundError:"/sounds/error.mp3",clearOrderHistoryOnStart:!1},n=["BTC","ETH","ADA","SOL","DOGE","LINK","MATIC","DOT","AVAX","XRP","LTC","BCH","BNB","SHIB"],i={BTC:["USDT","USDC","FDUSD","EUR"],ETH:["USDT","USDC","FDUSD","BTC","EUR"],ADA:["USDT","USDC","BTC","ETH"],SOL:["USDT","USDC","BTC","ETH"]},s=["USDT","USDC","FDUSD","DAI"]},58805:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},61135:()=>{},62185:(e,t,r)=>{"use strict";r.d(t,{Rk:()=>a,ZQ:()=>i,oc:()=>s});let o="http://localhost:5000";async function n(e,t={}){let r=`${o}${e}`,i=localStorage.getItem("plutoAuthToken"),s={"Content-Type":"application/json",...i?{Authorization:`Bearer ${i}`}:{},...t.headers};try{let e;let o=new AbortController,n=setTimeout(()=>o.abort(),1e4),i=await fetch(r,{...t,headers:s,signal:o.signal}).finally(()=>clearTimeout(n));if(401===i.status)throw localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),Error("Authentication expired. Please login again.");let a=i.headers.get("content-type");if(a&&a.includes("application/json"))e=await i.json();else{let t=await i.text();try{e=JSON.parse(t)}catch(r){e={message:t}}}if(!i.ok)throw console.error("API error response:",e),Error(e.error||e.message||`API error: ${i.status}`);return e}catch(e){if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw console.error("Network error - Is the backend server running?:",e),Error("Cannot connect to server. Please check if the backend is running.");if("AbortError"===e.name)throw console.error("Request timeout:",e),Error("Request timed out. Server may be unavailable.");throw console.error("API request failed:",e),e}}console.log("API Base URL:",o);let i={login:async(e,t)=>{try{let r=await c(async()=>await n("/auth/login",{method:"POST",body:JSON.stringify({username:e,password:t})}));if(r&&r.access_token)return localStorage.setItem("plutoAuthToken",r.access_token),localStorage.setItem("plutoAuth","true"),r.user&&localStorage.setItem("plutoUser",JSON.stringify(r.user)),!0;return!1}catch(e){return console.error("Login API error:",e),!1}},register:async(e,t,r)=>c(async()=>await n("/auth/register",{method:"POST",body:JSON.stringify({username:e,password:t,email:r})})),logout:async()=>(localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),!0)},s={getConfig:async e=>n(e?`/trading/config/${e}`:"/trading/config"),saveConfig:async e=>n("/trading/config",{method:"POST",body:JSON.stringify(e)}),updateConfig:async(e,t)=>n(`/trading/config/${e}`,{method:"PUT",body:JSON.stringify(t)}),startBot:async e=>n(`/trading/bot/start/${e}`,{method:"POST"}),stopBot:async e=>n(`/trading/bot/stop/${e}`,{method:"POST"}),getBotStatus:async e=>n(`/trading/bot/status/${e}`),getTradeHistory:async e=>{let t=e?`?configId=${e}`:"";return n(`/trading/history${t}`)},getBalances:async()=>n("/trading/balances"),getMarketPrice:async e=>n(`/trading/market-data/${e}`),getTradingPairs:async(e="binance")=>n(`/trading/exchange/trading-pairs?exchange=${e}`),getCryptocurrencies:async(e="binance")=>n(`/trading/exchange/cryptocurrencies?exchange=${e}`)},a={getAllSessions:async(e=!0)=>n(`/sessions/?include_inactive=${e}`),createSession:async e=>n("/sessions/",{method:"POST",body:JSON.stringify(e)}),getSession:async e=>n(`/sessions/${e}`),updateSession:async(e,t)=>n(`/sessions/${e}`,{method:"PUT",body:JSON.stringify(t)}),deleteSession:async e=>n(`/sessions/${e}`,{method:"DELETE"}),activateSession:async e=>n(`/sessions/${e}/activate`,{method:"POST"}),getSessionHistory:async e=>n(`/sessions/${e}/history`),getActiveSession:async()=>n("/sessions/active")},c=async(e,t=3)=>{let r=0,o=async()=>{try{return await e()}catch(e){if((e instanceof TypeError&&e.message.includes("Failed to fetch")||"AbortError"===e.name)&&r<t){let e=500*Math.pow(2,r);return console.log(`Retrying after ${e}ms (attempt ${r+1}/${t})...`),r++,await new Promise(t=>setTimeout(t,e)),o()}throw e}};return o()}},63213:(e,t,r)=>{"use strict";r.d(t,{A:()=>d,AuthProvider:()=>l});var o=r(60687),n=r(43210),i=r(16189),s=r(11516),a=r(62185);let c=(0,n.createContext)(void 0),l=({children:e})=>{let[t,r]=(0,n.useState)(!1),[l,d]=(0,n.useState)(!0),u=(0,i.useRouter)(),p=(0,i.usePathname)();(0,n.useEffect)(()=>{let e=localStorage.getItem("plutoAuth"),t=localStorage.getItem("plutoAuthToken");"true"===e&&t&&r(!0),d(!1)},[]),(0,n.useEffect)(()=>{l||t||"/login"===p?!l&&t&&"/login"===p&&u.push("/dashboard"):u.push("/login")},[t,l,p,u]);let y=async(e,t)=>{d(!0);try{if(await a.ZQ.login(e,t))return r(!0),u.push("/dashboard"),!0;return r(!1),!1}catch(e){return console.error("Login failed:",e),r(!1),!1}finally{d(!1)}},g=async()=>{try{await a.ZQ.logout()}catch(e){console.error("Logout error:",e)}finally{r(!1),u.push("/login")}};return l&&!p?.startsWith("/_next/static/")?(0,o.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,o.jsx)(s.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,o.jsx)("p",{className:"ml-4 text-xl",children:"Loading Pluto..."})]}):t||"/login"===p||p?.startsWith("/_next/static/")?(0,o.jsx)(c.Provider,{value:{isAuthenticated:t,login:y,logout:g,isLoading:l},children:e}):(0,o.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,o.jsx)(s.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,o.jsx)("p",{className:"ml-4 text-xl",children:"Redirecting to login..."})]})},d=()=>{let e=(0,n.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},78895:(e,t,r)=>{"use strict";r.d(t,{TradingProvider:()=>P,U:()=>w});var o=r(60687),n=r(43210),i=r(55280),s=r(74112),a=r(29867),c=r(62185),l=r(5551);class d{constructor(){this.isOnline=navigator.onLine,this.listeners=new Set,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=5e3,this.setupEventListeners(),this.startPeriodicCheck()}static getInstance(){return d.instance||(d.instance=new d),d.instance}setupEventListeners(){window.addEventListener("online",this.handleOnline.bind(this)),window.addEventListener("offline",this.handleOffline.bind(this)),document.addEventListener("visibilitychange",()=>{document.hidden||this.checkConnection()})}handleOnline(){console.log("\uD83C\uDF10 Network: Back online"),this.isOnline=!0,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.notifyListeners(!0)}handleOffline(){console.log("\uD83C\uDF10 Network: Gone offline"),this.isOnline=!1,this.notifyListeners(!1)}async checkConnection(){try{let e=(await fetch("/api/health",{method:"HEAD",cache:"no-cache",signal:AbortSignal.timeout(5e3)})).ok;return e!==this.isOnline&&(this.isOnline=e,this.notifyListeners(e),e&&(this.lastOnlineTime=Date.now(),this.reconnectAttempts=0)),e}catch(e){return this.isOnline&&(this.isOnline=!1,this.notifyListeners(!1)),!1}}startPeriodicCheck(){let e=setInterval(()=>{this.checkConnection()},3e4);this.periodicInterval=e}cleanup(){this.periodicInterval&&clearInterval(this.periodicInterval),this.listeners.clear()}notifyListeners(e){this.listeners.forEach(t=>{try{t(e)}catch(e){console.error("Error in network status listener:",e)}})}addListener(e){return this.listeners.add(e),()=>{this.listeners.delete(e)}}getStatus(){return{isOnline:this.isOnline,lastOnlineTime:this.lastOnlineTime,reconnectAttempts:this.reconnectAttempts}}async forceCheck(){return await this.checkConnection()}async attemptReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts)return console.log("\uD83C\uDF10 Network: Max reconnect attempts reached"),!1;this.reconnectAttempts++;let e=Math.min(this.reconnectInterval*Math.pow(2,this.reconnectAttempts-1),3e4);console.log(`🌐 Network: Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${e}ms`),await new Promise(t=>setTimeout(t,e));let t=await this.checkConnection();return!t&&this.reconnectAttempts<this.maxReconnectAttempts&&setTimeout(()=>this.attemptReconnect(),1e3),t}}class u{constructor(){this.saveInterval=null,this.saveFunction=null,this.intervalMs=3e4,this.isEnabled=!0,this.lastSaveTime=0,this.networkMonitor=d.getInstance(),this.setupNetworkListener(),this.setupBeforeUnloadListener()}static getInstance(){return u.instance||(u.instance=new u),u.instance}setupNetworkListener(){this.networkMonitor.addListener(e=>{e&&this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving on network reconnection"),this.saveFunction(),this.lastSaveTime=Date.now())})}setupBeforeUnloadListener(){window.addEventListener("beforeunload",()=>{this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving before page unload"),this.saveFunction())}),document.addEventListener("visibilitychange",()=>{document.hidden&&this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving on tab switch"),this.saveFunction(),this.lastSaveTime=Date.now())})}enable(e,t=3e4){this.saveFunction=e,this.intervalMs=t,this.isEnabled=!0,this.stop(),this.saveInterval=setInterval(()=>{this.isEnabled&&this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(console.log("\uD83D\uDCBE Auto-save: Periodic save"),this.saveFunction(),this.lastSaveTime=Date.now())},this.intervalMs),console.log(`💾 Auto-save: Enabled with ${t}ms interval`)}disable(){this.isEnabled=!1,this.stop(),console.log("\uD83D\uDCBE Auto-save: Disabled")}stop(){this.saveInterval&&(clearInterval(this.saveInterval),this.saveInterval=null)}saveNow(){this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(console.log("\uD83D\uDCBE Auto-save: Manual save triggered"),this.saveFunction(),this.lastSaveTime=Date.now())}getStatus(){return{isEnabled:this.isEnabled,lastSaveTime:this.lastSaveTime,intervalMs:this.intervalMs,isOnline:this.networkMonitor.getStatus().isOnline}}}class p{constructor(){this.checkInterval=null,this.warningThreshold=0x6400000,this.criticalThreshold=0xc800000,this.listeners=new Set,this.startMonitoring()}static getInstance(){return p.instance||(p.instance=new p),p.instance}startMonitoring(){"memory"in performance&&(this.checkInterval=setInterval(()=>{this.checkMemoryUsage()},6e4))}checkMemoryUsage(){if("memory"in performance){let e=performance.memory,t=e.usedJSHeapSize;this.notifyListeners(e),t>this.criticalThreshold?(console.warn("\uD83E\uDDE0 Memory: Critical memory usage detected:",{used:`${(t/1024/1024).toFixed(2)}MB`,total:`${(e.totalJSHeapSize/1024/1024).toFixed(2)}MB`,limit:`${(e.jsHeapSizeLimit/1024/1024).toFixed(2)}MB`}),"gc"in window&&window.gc()):t>this.warningThreshold&&console.log("\uD83E\uDDE0 Memory: High memory usage:",{used:`${(t/1024/1024).toFixed(2)}MB`,total:`${(e.totalJSHeapSize/1024/1024).toFixed(2)}MB`})}}notifyListeners(e){this.listeners.forEach(t=>{try{t(e)}catch(e){console.error("Error in memory monitor listener:",e)}})}addListener(e){return this.listeners.add(e),()=>this.listeners.delete(e)}getMemoryUsage(){return"memory"in performance?performance.memory:null}stop(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null)}}let y=e=>1,g=async e=>{try{let t=`${e.crypto1}${e.crypto2}`.toUpperCase();try{let r=await fetch(`https://api.binance.com/api/v3/ticker/price?symbol=${t}`);if(r.ok){let t=await r.json(),o=parseFloat(t.price);if(o>0)return console.log(`✅ Price fetched from Binance: ${e.crypto1}/${e.crypto2} = ${o}`),o}}catch(e){console.warn("Binance API failed, trying alternative...",e)}try{let t=h(e.crypto1),r=h(e.crypto2);if(t&&r){let o=await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${t}&vs_currencies=${r}`);if(o.ok){let n=await o.json(),i=n[t]?.[r];if(i>0)return console.log(`✅ Price fetched from CoinGecko: ${e.crypto1}/${e.crypto2} = ${i}`),i}}}catch(e){console.warn("CoinGecko API failed, using mock price...",e)}let r=f(e);return console.log(`⚠️ Using mock price: ${e.crypto1}/${e.crypto2} = ${r}`),r}catch(t){return console.error("Error fetching market price:",t),f(e)}},h=e=>({BTC:"bitcoin",ETH:"ethereum",SOL:"solana",ADA:"cardano",DOT:"polkadot",MATIC:"matic-network",AVAX:"avalanche-2",LINK:"chainlink",UNI:"uniswap",USDT:"tether",USDC:"usd-coin",BUSD:"binance-usd",DAI:"dai"})[e.toUpperCase()]||null,m=e=>({BTC:109e3,ETH:4e3,SOL:240,ADA:1.2,DOGE:.4,LINK:25,MATIC:.5,DOT:8,AVAX:45,SHIB:3e-5,XRP:2.5,LTC:110,BCH:500,UNI:15,AAVE:180,MKR:1800,SNX:3.5,COMP:85,YFI:8500,SUSHI:2.1,"1INCH":.65,CRV:.85,UMA:3.2,ATOM:12,NEAR:6.5,ALGO:.35,ICP:14,HBAR:.28,APT:12.5,TON:5.8,FTM:.95,ONE:.025,FIL:8.5,TRX:.25,ETC:35,VET:.055,QNT:125,LDO:2.8,CRO:.18,LUNC:15e-5,MANA:.85,SAND:.75,AXS:8.5,ENJ:.45,CHZ:.12,THETA:2.1,FLOW:1.2,XTZ:1.8,EOS:1.1,GRT:.28,BAT:.35,ZEC:45,DASH:35,LRC:.45,ZRX:.65,KNC:.85,REN:.15,BAND:2.5,STORJ:.85,NMR:25,ANT:8.5,BNT:.95,MLN:35,REP:15,IOTX:.065,ZIL:.045,ICX:.35,QTUM:4.5,ONT:.45,WAVES:3.2,LSK:1.8,NANO:1.5,SC:.008,DGB:.025,RVN:.035,BTT:15e-7,WIN:15e-5,HOT:.0035,DENT:.0018,NPXS:85e-5,FUN:.0085,CELR:.025,USDT:1,USDC:1,FDUSD:1,BUSD:1,DAI:1})[e.toUpperCase()]||100,f=e=>{let t=m(e.crypto1),r=m(e.crypto2),o=t/r*(1+(Math.random()-.5)*.02);return console.log(`📊 Fallback price calculation: ${e.crypto1} ($${t}) / ${e.crypto2} ($${r}) = ${o.toFixed(6)}`),o},S={tradingMode:"SimpleSpot",crypto1:i.hg[0],crypto2:(i.vA[i.hg[0]]||["USDT","USDC","BTC"])[0],baseBid:100,multiplier:1.005,numDigits:4,slippagePercent:.2,incomeSplitCrypto1Percent:50,incomeSplitCrypto2Percent:50,preferredStablecoin:i.Ql[0]},v={config:S,targetPriceRows:[],orderHistory:[],appSettings:i.Oh,currentMarketPrice:y(S),botSystemStatus:"Stopped",crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0,backendStatus:"unknown",connectionStatus:"online"},b=new Map,T=e=>{},A=()=>null,C=(e,t)=>{switch(t.type){case"SET_CONFIG":let r={...e.config,...t.payload};if(t.payload.crypto1||t.payload.crypto2)return{...e,config:r,currentMarketPrice:y(r)};return{...e,config:r};case"SET_TARGET_PRICE_ROWS":return{...e,targetPriceRows:t.payload.sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}))};case"ADD_TARGET_PRICE_ROW":{let r=[...e.targetPriceRows,t.payload].sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:r}}case"UPDATE_TARGET_PRICE_ROW":{let r=e.targetPriceRows.map(e=>e.id===t.payload.id?t.payload:e).sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:r}}case"REMOVE_TARGET_PRICE_ROW":{let r=e.targetPriceRows.filter(e=>e.id!==t.payload).sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:r}}case"ADD_ORDER_HISTORY_ENTRY":return{...e,orderHistory:[t.payload,...e.orderHistory]};case"CLEAR_ORDER_HISTORY":return{...e,orderHistory:[]};case"SET_APP_SETTINGS":return{...e,appSettings:{...e.appSettings,...t.payload}};case"SET_MARKET_PRICE":return{...e,currentMarketPrice:t.payload};case"FLUCTUATE_MARKET_PRICE":{if(e.currentMarketPrice<=0)return e;let t=(Math.random()-.5)*.006,r=e.currentMarketPrice*(1+t);return{...e,currentMarketPrice:r>0?r:e.currentMarketPrice}}case"UPDATE_BALANCES":return{...e,crypto1Balance:void 0!==t.payload.crypto1?t.payload.crypto1:e.crypto1Balance,crypto2Balance:void 0!==t.payload.crypto2?t.payload.crypto2:e.crypto2Balance,stablecoinBalance:void 0!==t.payload.stablecoin?t.payload.stablecoin:e.stablecoinBalance};case"UPDATE_STABLECOIN_BALANCE":return{...e,stablecoinBalance:t.payload};case"RESET_SESSION":let o={...e.config};return{...v,config:o,appSettings:{...e.appSettings},currentMarketPrice:y(o)};case"SET_BACKEND_STATUS":return{...e,backendStatus:t.payload};case"SET_CONNECTION_STATUS":if("offline"===t.payload&&"Running"===e.botSystemStatus)return console.warn("\uD83D\uDD34 Connection lost - stopping bot automatically"),{...e,connectionStatus:t.payload,botSystemStatus:"Stopped"};return{...e,connectionStatus:t.payload};case"SET_BALANCES":return{...e,crypto1Balance:t.payload.crypto1,crypto2Balance:t.payload.crypto2};case"SYSTEM_START_BOT_INITIATE":return{...e,botSystemStatus:"WarmingUp"};case"SYSTEM_COMPLETE_WARMUP":return{...e,botSystemStatus:"Running"};case"SYSTEM_STOP_BOT":return{...e,botSystemStatus:"Stopped"};case"SYSTEM_RESET_BOT":let n=e.targetPriceRows.map(t=>({...t,status:"Free",orderLevel:0,valueLevel:e.config.baseBid,crypto1AmountHeld:void 0,originalCostCrypto2:void 0,crypto1Var:void 0,crypto2Var:void 0,lastActionTimestamp:void 0}));return b.clear(),{...e,botSystemStatus:"Stopped",targetPriceRows:n,orderHistory:[]};case"SET_TARGET_PRICE_ROWS":return{...e,targetPriceRows:t.payload};default:return e}},E=(0,n.createContext)(void 0),P=({children:e})=>{let[t,r]=(0,n.useReducer)(C,(()=>{let e=A();return e?{...v,...e}:v})()),{toast:i}=(0,a.dj)(),y=(0,n.useRef)(null),h=(0,n.useCallback)(async()=>{try{let e=await g(t.config);r({type:"SET_MARKET_PRICE",payload:e})}catch(e){console.error("Failed to fetch market price:",e)}},[t.config,r]);(0,n.useEffect)(()=>{h();let e=setInterval(()=>{r({type:"FLUCTUATE_MARKET_PRICE"})},2e3);return()=>{clearInterval(e)}},[h,r]),(0,n.useEffect)(()=>{},[]);let f=(0,n.useCallback)(e=>{if(t.appSettings.soundAlertsEnabled&&y.current){let r;"soundOrderExecution"===e&&t.appSettings.alertOnOrderExecution?r=t.appSettings.soundOrderExecution:"soundError"===e&&t.appSettings.alertOnError&&(r=t.appSettings.soundError),r&&(y.current.src=r,y.current.currentTime=0,y.current.play().then(()=>{setTimeout(()=>{y.current&&(y.current.pause(),y.current.currentTime=0)},2e3)}).catch(e=>console.error("Error playing sound:",e)))}},[t.appSettings]),S=(0,n.useCallback)(async e=>{try{let t=localStorage.getItem("telegram_bot_token"),r=localStorage.getItem("telegram_chat_id");if(!t||!r){console.log("Telegram not configured - skipping notification");return}let o=await fetch(`https://api.telegram.org/bot${t}/sendMessage`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:r,text:e,parse_mode:"HTML"})});o.ok||console.error("Failed to send Telegram notification:",o.statusText)}catch(e){console.error("Error sending Telegram notification:",e)}},[]);(0,n.useEffect)(()=>{},[t.config.crypto1,t.config.crypto2]);let b=(0,n.useCallback)(e=>{e&&Array.isArray(e)&&r({type:"SET_TARGET_PRICE_ROWS",payload:[...e].filter(e=>!isNaN(e)&&e>0).sort((e,t)=>e-t).map((e,r)=>{let o=t.targetPriceRows.find(t=>t.targetPrice===e);return o?{...o,counter:r+1}:{id:(0,s.A)(),counter:r+1,status:"Free",orderLevel:0,valueLevel:t.config.baseBid,targetPrice:e}})})},[t.targetPriceRows,t.config.baseBid,r]);(0,n.useEffect)(()=>{if("Running"!==t.botSystemStatus||"online"!==t.connectionStatus||0===t.targetPriceRows.length||t.currentMarketPrice<=0)return;let{config:e,currentMarketPrice:o,targetPriceRows:n,crypto1Balance:a,crypto2Balance:c}=t,l=[...n].sort((e,t)=>e.targetPrice-t.targetPrice),d=a,u=c,p=0;console.log(`🚀 CONTINUOUS TRADING: Price $${o.toFixed(2)} | Targets: ${l.length} | Balance: $${u} ${e.crypto2}`);let y=l.filter(t=>Math.abs(o-t.targetPrice)/o*100<=e.slippagePercent);y.length>0&&console.log(`🎯 TARGETS IN RANGE (\xb1${e.slippagePercent}%):`,y.map(e=>`Counter ${e.counter} (${e.status})`));for(let t=0;t<l.length;t++){let n=l[t];if(Math.abs(o-n.targetPrice)/o*100<=e.slippagePercent){if("SimpleSpot"===e.tradingMode){if("Free"===n.status){let t=n.valueLevel;if(u>=t){let a=t/o;r({type:"UPDATE_TARGET_PRICE_ROW",payload:{...n,status:"Full",orderLevel:n.orderLevel+1,valueLevel:e.baseBid*Math.pow(e.multiplier,n.orderLevel+1),crypto1AmountHeld:a,originalCostCrypto2:t,crypto1Var:a,crypto2Var:-t}}),r({type:"UPDATE_BALANCES",payload:{crypto1:d+a,crypto2:u-t}}),r({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,s.A)(),timestamp:Date.now(),pair:`${e.crypto1}/${e.crypto2}`,crypto1:e.crypto1,orderType:"BUY",amountCrypto1:a,avgPrice:o,valueCrypto2:t,price1:o,crypto1Symbol:e.crypto1||"",crypto2Symbol:e.crypto2||""}}),console.log(`✅ BUY: Counter ${n.counter} bought ${a.toFixed(6)} ${e.crypto1} at $${o.toFixed(2)}`),i({title:"BUY Executed",description:`Counter ${n.counter}: ${a.toFixed(6)} ${e.crypto1}`,duration:2e3}),f("soundOrderExecution"),S(`🟢 <b>BUY EXECUTED</b>
📊 Counter: ${n.counter}
💰 Amount: ${a.toFixed(6)} ${e.crypto1}
💵 Price: $${o.toFixed(2)}
💸 Cost: $${t.toFixed(2)} ${e.crypto2}
📈 Mode: Simple Spot`),p++,u-=t,d+=a}}let t=n.counter,a=l.find(e=>e.counter===t-1);if(a&&"Full"===a.status&&a.crypto1AmountHeld&&a.originalCostCrypto2){let n=a.crypto1AmountHeld,c=n*o,l=c-a.originalCostCrypto2,y=o>0?l*e.incomeSplitCrypto1Percent/100/o:0;r({type:"UPDATE_TARGET_PRICE_ROW",payload:{...a,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:e.baseBid*Math.pow(e.multiplier,a.orderLevel),crypto1Var:-n,crypto2Var:c}}),r({type:"UPDATE_BALANCES",payload:{crypto1:d-n,crypto2:u+c}}),r({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,s.A)(),timestamp:Date.now(),pair:`${e.crypto1}/${e.crypto2}`,crypto1:e.crypto1,orderType:"SELL",amountCrypto1:n,avgPrice:o,valueCrypto2:c,price1:o,crypto1Symbol:e.crypto1||"",crypto2Symbol:e.crypto2||"",realizedProfitLossCrypto2:l,realizedProfitLossCrypto1:y}}),console.log(`✅ SELL: Counter ${t-1} sold ${n.toFixed(6)} ${e.crypto1}. Profit: $${l.toFixed(2)}`),i({title:"SELL Executed",description:`Counter ${t-1}: Profit $${l.toFixed(2)}`,duration:2e3}),f("soundOrderExecution");let g=l>0?"\uD83D\uDCC8":l<0?"\uD83D\uDCC9":"➖";S(`🔴 <b>SELL EXECUTED</b>
📊 Counter: ${t-1}
💰 Amount: ${n.toFixed(6)} ${e.crypto1}
💵 Price: $${o.toFixed(2)}
💸 Received: $${c.toFixed(2)} ${e.crypto2}
${g} Profit: $${l.toFixed(2)} ${e.crypto2}
📈 Mode: Simple Spot`),p++,d-=n,u+=c}}else if("StablecoinSwap"===e.tradingMode){if("Free"===n.status){let t=n.valueLevel;if(u>=t){let o=m(e.crypto2||"USDT")/m(e.preferredStablecoin||"USDT"),a=t*o,c=m(e.crypto1||"BTC")/m(e.preferredStablecoin||"USDT"),l=a/c,y=n.orderLevel+1,g=e.baseBid*Math.pow(e.multiplier,y);r({type:"UPDATE_TARGET_PRICE_ROW",payload:{...n,status:"Full",orderLevel:y,valueLevel:g,crypto1AmountHeld:l,originalCostCrypto2:t,crypto1Var:l,crypto2Var:-t}}),r({type:"UPDATE_BALANCES",payload:{crypto1:d+l,crypto2:u-t}}),r({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,s.A)(),timestamp:Date.now(),pair:`${e.crypto2}/${e.preferredStablecoin}`,crypto1:e.crypto2,orderType:"SELL",amountCrypto1:t,avgPrice:o,valueCrypto2:a,price1:o,crypto1Symbol:e.crypto2||"",crypto2Symbol:e.preferredStablecoin||""}}),r({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,s.A)(),timestamp:Date.now(),pair:`${e.crypto1}/${e.preferredStablecoin}`,crypto1:e.crypto1,orderType:"BUY",amountCrypto1:l,avgPrice:c,valueCrypto2:a,price1:c,crypto1Symbol:e.crypto1||"",crypto2Symbol:e.preferredStablecoin||""}}),console.log(`✅ STABLECOIN BUY: Counter ${n.counter} | Step 1: Sold ${t} ${e.crypto2} → ${a.toFixed(2)} ${e.preferredStablecoin} | Step 2: Bought ${l.toFixed(6)} ${e.crypto1} | Level: ${n.orderLevel} → ${y}`),i({title:"BUY Executed (Stablecoin)",description:`Counter ${n.counter}: ${l.toFixed(6)} ${e.crypto1} via ${e.preferredStablecoin}`,duration:2e3}),f("soundOrderExecution"),S(`🟢 <b>BUY EXECUTED (Stablecoin Swap)</b>
📊 Counter: ${n.counter}
🔄 Step 1: Sold ${t.toFixed(2)} ${e.crypto2} → ${a.toFixed(2)} ${e.preferredStablecoin}
🔄 Step 2: Bought ${l.toFixed(6)} ${e.crypto1}
📊 Level: ${n.orderLevel} → ${y}
📈 Mode: Stablecoin Swap`),p++,u-=t,d+=l}}let t=n.counter,o=l.find(e=>e.counter===t-1);if(o&&"Full"===o.status&&o.crypto1AmountHeld&&o.originalCostCrypto2){let n=o.crypto1AmountHeld,a=m(e.crypto1||"BTC")/m(e.preferredStablecoin||"USDT"),c=n*a,l=m(e.crypto2||"USDT")/m(e.preferredStablecoin||"USDT"),y=c/l,g=y-o.originalCostCrypto2,h=a>0?g*e.incomeSplitCrypto1Percent/100/a:0;r({type:"UPDATE_TARGET_PRICE_ROW",payload:{...o,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:e.baseBid*Math.pow(e.multiplier,o.orderLevel),crypto1Var:0,crypto2Var:0}}),r({type:"UPDATE_BALANCES",payload:{crypto1:d-n,crypto2:u+y}}),r({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,s.A)(),timestamp:Date.now(),pair:`${e.crypto1}/${e.preferredStablecoin}`,crypto1:e.crypto1,orderType:"SELL",amountCrypto1:n,avgPrice:a,valueCrypto2:c,price1:a,crypto1Symbol:e.crypto1||"",crypto2Symbol:e.preferredStablecoin||""}}),r({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,s.A)(),timestamp:Date.now(),pair:`${e.crypto2}/${e.preferredStablecoin}`,crypto1:e.crypto2,orderType:"BUY",amountCrypto1:y,avgPrice:l,valueCrypto2:c,price1:l,crypto1Symbol:e.crypto2||"",crypto2Symbol:e.preferredStablecoin||"",realizedProfitLossCrypto2:g,realizedProfitLossCrypto1:h}}),console.log(`✅ STABLECOIN SELL: Counter ${t-1} | Step A: Sold ${n.toFixed(6)} ${e.crypto1} → ${c.toFixed(2)} ${e.preferredStablecoin} | Step B: Bought ${y.toFixed(2)} ${e.crypto2} | Profit: ${g.toFixed(2)} ${e.crypto2} | Level: ${o.orderLevel} (unchanged)`),i({title:"SELL Executed (Stablecoin)",description:`Counter ${t-1}: Profit ${g.toFixed(2)} ${e.crypto2} via ${e.preferredStablecoin}`,duration:2e3}),f("soundOrderExecution");let v=g>0?"\uD83D\uDCC8":g<0?"\uD83D\uDCC9":"➖";S(`🔴 <b>SELL EXECUTED (Stablecoin Swap)</b>
📊 Counter: ${t-1}
🔄 Step A: Sold ${n.toFixed(6)} ${e.crypto1} → ${c.toFixed(2)} ${e.preferredStablecoin}
🔄 Step B: Bought ${y.toFixed(2)} ${e.crypto2}
${v} Profit: ${g.toFixed(2)} ${e.crypto2}
📊 Level: ${o.orderLevel} (unchanged)
📈 Mode: Stablecoin Swap`),p++,d-=n,u+=y}}}}p>0&&console.log(`🎯 CYCLE COMPLETE: ${p} actions taken at price $${o.toFixed(2)}`)},[t.botSystemStatus,t.currentMarketPrice,t.targetPriceRows,t.config,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,r,i,f,S]);let P=(0,n.useCallback)(()=>t.targetPriceRows&&Array.isArray(t.targetPriceRows)?t.targetPriceRows.map(e=>{let r,o;let n=t.currentMarketPrice||0,i=e.targetPrice||0;if("Full"===e.status&&e.crypto1AmountHeld&&e.originalCostCrypto2){let i=n*e.crypto1AmountHeld-e.originalCostCrypto2;o=i*t.config.incomeSplitCrypto2Percent/100,n>0&&(r=i*t.config.incomeSplitCrypto1Percent/100/n)}return{...e,currentPrice:n,priceDifference:i-n,priceDifferencePercent:n>0?(i-n)/n*100:0,potentialProfitCrypto1:t.config.incomeSplitCrypto1Percent/100*e.valueLevel/(i||1),potentialProfitCrypto2:t.config.incomeSplitCrypto2Percent/100*e.valueLevel,percentFromActualPrice:n&&i?(n/i-1)*100:0,incomeCrypto1:r,incomeCrypto2:o}}).sort((e,t)=>t.targetPrice-e.targetPrice):[],[t.targetPriceRows,t.currentMarketPrice,t.config.incomeSplitCrypto1Percent,t.config.incomeSplitCrypto2Percent,t.config.baseBid,t.config.multiplier]),w=(0,n.useCallback)(async e=>{try{let r={name:`${e.crypto1}/${e.crypto2} ${e.tradingMode}`,tradingMode:e.tradingMode,crypto1:e.crypto1,crypto2:e.crypto2,baseBid:e.baseBid,multiplier:e.multiplier,numDigits:e.numDigits,slippagePercent:e.slippagePercent,incomeSplitCrypto1Percent:e.incomeSplitCrypto1Percent,incomeSplitCrypto2Percent:e.incomeSplitCrypto2Percent,preferredStablecoin:e.preferredStablecoin,targetPrices:t.targetPriceRows.map(e=>e.targetPrice)},o=await c.oc.saveConfig(r);return console.log("✅ Config saved to backend:",o),o.config?.id||null}catch(e){return console.error("❌ Failed to save config to backend:",e),i({title:"Backend Error",description:"Failed to save configuration to backend",variant:"destructive",duration:3e3}),null}},[t.targetPriceRows,i]),x=(0,n.useCallback)(async e=>{try{let t=await c.oc.startBot(e);return console.log("✅ Bot started on backend:",t),i({title:"Bot Started",description:"Trading bot started successfully on backend",duration:3e3}),!0}catch(e){return console.error("❌ Failed to start bot on backend:",e),i({title:"Backend Error",description:"Failed to start bot on backend",variant:"destructive",duration:3e3}),!1}},[i]),$=(0,n.useCallback)(async e=>{try{let t=await c.oc.stopBot(e);return console.log("✅ Bot stopped on backend:",t),i({title:"Bot Stopped",description:"Trading bot stopped successfully on backend",duration:3e3}),!0}catch(e){return console.error("❌ Failed to stop bot on backend:",e),i({title:"Backend Error",description:"Failed to stop bot on backend",variant:"destructive",duration:3e3}),!1}},[i]),R=(0,n.useCallback)(async()=>{let e="http://localhost:5000";if(!e){console.error("Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed."),r({type:"SET_BACKEND_STATUS",payload:"offline"});return}try{let t=await fetch(`${e}/health/`);if(!t.ok){console.error(`Backend health check failed with status: ${t.status} ${t.statusText}`);let e=await t.text().catch(()=>"Could not read response text.");console.error("Backend health check response body:",e)}r({type:"SET_BACKEND_STATUS",payload:t.ok?"online":"offline"})}catch(t){r({type:"SET_BACKEND_STATUS",payload:"offline"}),console.error("Backend connectivity check failed. Error details:",t),t.cause&&console.error("Fetch error cause:",t.cause),console.error("Attempted to fetch API URL:",`${e}/health/`)}},[r]);(0,n.useEffect)(()=>{R()},[R]),(0,n.useEffect)(()=>{T(t)},[t]),(0,n.useEffect)(()=>{"WarmingUp"===t.botSystemStatus&&(console.log("Bot is Warming Up... Immediate execution enabled."),r({type:"SYSTEM_COMPLETE_WARMUP"}),console.log("Bot is now Running immediately."))},[t.botSystemStatus,r]),(0,n.useEffect)(()=>{let e=d.getInstance(),r=u.getInstance(),o=p.getInstance(),n=l.C.getInstance(),s=e.addListener(e=>{console.log(`🌐 Network status changed: ${e?"Online":"Offline"}`),e?i({title:"Network Reconnected",description:"Connection restored. Auto-saving enabled.",duration:3e3}):i({title:"Network Disconnected",description:"You are currently offline. Data will be saved locally.",variant:"destructive",duration:5e3})}),a=o.addListener(e=>{let t=e.usedJSHeapSize/1024/1024;t>150&&(console.warn(`🧠 High memory usage: ${t.toFixed(2)}MB`),i({title:"High Memory Usage",description:`Memory usage is high (${t.toFixed(0)}MB). Consider refreshing the page.`,variant:"destructive",duration:5e3}))});return r.enable(()=>{try{let e=n.getCurrentSessionId();e&&n.saveSession(e,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,"Running"===t.botSystemStatus),T(t)}catch(e){console.error("Auto-save failed:",e)}},3e4),()=>{s(),a(),r.disable()}},[t,i]),(0,n.useEffect)(()=>{u.getInstance().saveNow()},[t.botSystemStatus]);let I=(0,n.useCallback)(e=>{r({type:"SET_CONNECTION_STATUS",payload:e})},[r]),k={...t,dispatch:r,setTargetPrices:b,getDisplayOrders:P,checkBackendStatus:R,fetchMarketPrice:h,setConnectionStatus:I,startBackendBot:x,stopBackendBot:$,saveConfigToBackend:w,backendStatus:t.backendStatus,connectionStatus:t.connectionStatus,botSystemStatus:t.botSystemStatus,isBotActive:"Running"===t.botSystemStatus};return(0,o.jsx)(E.Provider,{value:k,children:e})},w=()=>{let e=(0,n.useContext)(E);if(void 0===e)throw Error("useTradingContext must be used within a TradingProvider");return e}},79737:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\components\\ui\\toaster.tsx","Toaster")},86514:(e,t,r)=>{Promise.resolve().then(r.bind(r,79737)),Promise.resolve().then(r.bind(r,26443)),Promise.resolve().then(r.bind(r,29131)),Promise.resolve().then(r.bind(r,47506))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>d});var o=r(37413),n=r(7653),i=r.n(n);r(61135);var s=r(79737),a=r(29131),c=r(47506),l=r(26443);let d={title:"Pluto Trading Bot",description:"Simulated cryptocurrency trading bot with Neo Brutalist UI."};function u({children:e}){return(0,o.jsx)("html",{lang:"en",className:i().variable,suppressHydrationWarning:!0,children:(0,o.jsxs)("body",{className:"font-sans antialiased",suppressHydrationWarning:!0,children:[" ",(0,o.jsx)(a.AuthProvider,{children:(0,o.jsx)(c.TradingProvider,{children:(0,o.jsxs)(l.AIProvider,{children:[e,(0,o.jsx)(s.Toaster,{})]})})})]})})}},95911:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"406b51c45f1e93f88b682f8a8fff734ab97ae270e5":()=>d});var o=r(91199);r(42087);var n=r(37612),i=r(56758);let s=(0,n.genkit)({plugins:[(0,i.YF)()],model:"googleai/gemini-2.0-flash"});var a=r(33331);let c=n.z.object({riskTolerance:n.z.string().describe("The user risk tolerance, can be low, medium, or high."),preferredCryptocurrencies:n.z.string().describe("The user preferred cryptocurrencies, comma separated."),investmentGoals:n.z.string().describe("The user investment goals, such as long term investment or short term profit.")}),l=n.z.object({suggestedMode:n.z.enum(["Simple Spot","Stablecoin Swap"]).describe("The suggested trading mode."),reason:n.z.string().describe("The reason for the suggestion.")});async function d(e){return p(e)}let u=s.definePrompt({name:"tradingModeSuggestionPrompt",input:{schema:c},output:{schema:l},prompt:`You are an expert in trading mode selection. You will suggest the most suitable trading mode (Simple Spot or Stablecoin Swap) based on the user's risk tolerance, preferred cryptocurrencies, and investment goals.

Risk Tolerance: {{{riskTolerance}}}
Preferred Cryptocurrencies: {{{preferredCryptocurrencies}}}
Investment Goals: {{{investmentGoals}}}

Consider the following:

*   Simple Spot Mode is suitable for users who are comfortable with higher risk and are looking for short term profits.
*   Stablecoin Swap Mode is suitable for users who are risk averse and are looking for long term investment.

Based on the information above, suggest a trading mode and explain your reasoning.`}),p=s.defineFlow({name:"suggestTradingModeFlow",inputSchema:c,outputSchema:l},async e=>{let{output:t}=await u(e);return t});(0,a.D)([d]),(0,o.A)(d,"406b51c45f1e93f88b682f8a8fff734ab97ae270e5",null)}};