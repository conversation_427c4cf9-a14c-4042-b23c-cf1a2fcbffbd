"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/analytics/page",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    async checkBackendConnection() {\n        try {\n            // Try a simple ping to the backend\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 1500); // 1.5 second timeout\n            const response = await fetch('http://localhost:5000/', {\n                method: 'GET',\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.status < 500) {\n                this.useBackend = true;\n                console.log('✅ Session Manager: Backend connection established');\n            } else {\n                throw new Error('Backend returned server error');\n            }\n        } catch (error) {\n            this.useBackend = false;\n            console.warn('⚠️ Session Manager: Backend unavailable, using localStorage fallback');\n            console.warn('💡 To enable backend features, start the backend server: python run.py');\n        }\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            const currentSessionId = localStorage.getItem(CURRENT_SESSION_KEY);\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            if (this.currentSessionId) {\n                localStorage.setItem(CURRENT_SESSION_KEY, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSession(name, config) {\n        if (this.useBackend) {\n            try {\n                // Validate config before sending\n                if (!config.crypto1 || !config.crypto2) {\n                    throw new Error('Invalid config: crypto1 and crypto2 are required');\n                }\n                const sessionData = {\n                    name,\n                    config: {\n                        tradingMode: config.tradingMode,\n                        crypto1: config.crypto1,\n                        crypto2: config.crypto2,\n                        baseBid: config.baseBid,\n                        multiplier: config.multiplier,\n                        numDigits: config.numDigits,\n                        slippagePercent: config.slippagePercent,\n                        incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                        incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                        preferredStablecoin: config.preferredStablecoin\n                    },\n                    targetPriceRows: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0\n                };\n                console.log('📤 Creating session with data:', sessionData);\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                const sessionId = response.session.id;\n                console.log('✅ Session created on backend:', sessionId);\n                return sessionId;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend:', error);\n                console.error('❌ Error details:', error.message);\n                // Don't fall back to localStorage for validation errors\n                if (error.message.includes('Validation error') || error.message.includes('422')) {\n                    throw error;\n                }\n                console.log('📱 Falling back to localStorage');\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: 10,\n            crypto2Balance: 100000,\n            stablecoinBalance: 0,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: session.runtime + (Date.now() - session.lastModified)\n            };\n            this.sessions.set(sessionId, updatedSession);\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    deleteSession(sessionId) {\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                localStorage.removeItem(CURRENT_SESSION_KEY);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: session.runtime,\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            localStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        localStorage.removeItem(CURRENT_SESSION_KEY);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.loadSessionsFromStorage();\n        // Start with localStorage mode, check backend in background\n        this.useBackend = false;\n        // Check backend connection in background without blocking\n        setTimeout(()=>{\n            this.checkBackendConnection().catch(()=>{\n            // Silently fail and continue with localStorage\n            });\n        }, 1000);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});