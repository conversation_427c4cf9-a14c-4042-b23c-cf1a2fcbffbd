[{"name": "generate-buildid", "duration": 560, "timestamp": 51050769985, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750267622880, "traceId": "cca038ed79a07689"}, {"name": "load-custom-routes", "duration": 644, "timestamp": 51050770818, "id": 5, "parentId": 1, "tags": {}, "startTime": 1750267622880, "traceId": "cca038ed79a07689"}, {"name": "create-dist-dir", "duration": 867, "timestamp": 51050917449, "id": 6, "parentId": 1, "tags": {}, "startTime": 1750267623027, "traceId": "cca038ed79a07689"}, {"name": "create-pages-mapping", "duration": 430, "timestamp": 51050944481, "id": 7, "parentId": 1, "tags": {}, "startTime": 1750267623054, "traceId": "cca038ed79a07689"}, {"name": "collect-app-paths", "duration": 4290, "timestamp": 51050944995, "id": 8, "parentId": 1, "tags": {}, "startTime": 1750267623055, "traceId": "cca038ed79a07689"}, {"name": "create-app-mapping", "duration": 1606, "timestamp": 51050949332, "id": 9, "parentId": 1, "tags": {}, "startTime": 1750267623059, "traceId": "cca038ed79a07689"}, {"name": "public-dir-conflict-check", "duration": 1225, "timestamp": 51050952476, "id": 10, "parentId": 1, "tags": {}, "startTime": 1750267623062, "traceId": "cca038ed79a07689"}, {"name": "generate-routes-manifest", "duration": 10189, "timestamp": 51050954225, "id": 11, "parentId": 1, "tags": {}, "startTime": 1750267623064, "traceId": "cca038ed79a07689"}, {"name": "create-entrypoints", "duration": 120176, "timestamp": 51051046040, "id": 14, "parentId": 1, "tags": {}, "startTime": 1750267623156, "traceId": "cca038ed79a07689"}, {"name": "generate-webpack-config", "duration": 1293454, "timestamp": 51051166291, "id": 15, "parentId": 13, "tags": {}, "startTime": 1750267623276, "traceId": "cca038ed79a07689"}, {"name": "next-trace-entrypoint-plugin", "duration": 3841, "timestamp": 51053059902, "id": 17, "parentId": 16, "tags": {}, "startTime": 1750267625170, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 1938853, "timestamp": 51053076175, "id": 21, "parentId": 18, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1750267625186, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 1984151, "timestamp": 51053075205, "id": 19, "parentId": 18, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1750267625185, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 2390962, "timestamp": 51053076197, "id": 22, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fpage&name=app%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&appPaths=%2Fadmin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750267625186, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 2390963, "timestamp": 51053076219, "id": 23, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750267625186, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 2390950, "timestamp": 51053076237, "id": 24, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Flogin%2Fpage&name=app%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&appPaths=%2Flogin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750267625186, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 2390936, "timestamp": 51053076254, "id": 25, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fdashboard%2Fpage&name=app%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&appPaths=%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750267625186, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 2390919, "timestamp": 51053076274, "id": 26, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fdashboard%2Fanalytics%2Fpage&name=app%2Fdashboard%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fanalytics%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&appPaths=%2Fdashboard%2Fanalytics%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750267625186, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 2390898, "timestamp": 51053076298, "id": 27, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fdashboard%2Fhistory%2Fpage&name=app%2Fdashboard%2Fhistory%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fhistory%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&appPaths=%2Fdashboard%2Fhistory%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750267625186, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 2402918, "timestamp": 51053076124, "id": 20, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750267625186, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 2504493, "timestamp": 51053076552, "id": 28, "parentId": 18, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1750267625186, "traceId": "cca038ed79a07689"}, {"name": "build-module-tsx", "duration": 109602, "timestamp": 51056395781, "id": 75, "parentId": 16, "tags": {"name": "E:\\bot\\tradingbot_final\\frontend\\src\\components\\dashboard\\SessionAwareAnalytics.tsx", "layer": "ssr"}, "startTime": 1750267628505, "traceId": "cca038ed79a07689"}, {"name": "make", "duration": 7129384, "timestamp": 51053073830, "id": 18, "parentId": 16, "tags": {}, "startTime": 1750267625183, "traceId": "cca038ed79a07689"}, {"name": "get-entries", "duration": 5323, "timestamp": 51060205522, "id": 84, "parentId": 83, "tags": {}, "startTime": 1750267632315, "traceId": "cca038ed79a07689"}, {"name": "node-file-trace-plugin", "duration": 151574, "timestamp": 51060216712, "id": 85, "parentId": 83, "tags": {"traceEntryCount": "16"}, "startTime": 1750267632326, "traceId": "cca038ed79a07689"}, {"name": "collect-traced-files", "duration": 912, "timestamp": 51060368309, "id": 86, "parentId": 83, "tags": {}, "startTime": 1750267632478, "traceId": "cca038ed79a07689"}, {"name": "finish-modules", "duration": 164054, "timestamp": 51060205180, "id": 83, "parentId": 17, "tags": {}, "startTime": 1750267632315, "traceId": "cca038ed79a07689"}, {"name": "chunk-graph", "duration": 141303, "timestamp": 51060849822, "id": 88, "parentId": 87, "tags": {}, "startTime": 1750267632959, "traceId": "cca038ed79a07689"}, {"name": "optimize-modules", "duration": 129, "timestamp": 51060992132, "id": 90, "parentId": 87, "tags": {}, "startTime": 1750267633102, "traceId": "cca038ed79a07689"}, {"name": "optimize-chunks", "duration": 153832, "timestamp": 51060992650, "id": 91, "parentId": 87, "tags": {}, "startTime": 1750267633102, "traceId": "cca038ed79a07689"}, {"name": "optimize-tree", "duration": 254, "timestamp": 51061146642, "id": 92, "parentId": 87, "tags": {}, "startTime": 1750267633256, "traceId": "cca038ed79a07689"}, {"name": "optimize-chunk-modules", "duration": 228836, "timestamp": 51061147050, "id": 93, "parentId": 87, "tags": {}, "startTime": 1750267633257, "traceId": "cca038ed79a07689"}, {"name": "optimize", "duration": 384520, "timestamp": 51060991693, "id": 89, "parentId": 87, "tags": {}, "startTime": 1750267633101, "traceId": "cca038ed79a07689"}, {"name": "module-hash", "duration": 151025, "timestamp": 51061520066, "id": 94, "parentId": 87, "tags": {}, "startTime": 1750267633630, "traceId": "cca038ed79a07689"}, {"name": "code-generation", "duration": 977364, "timestamp": 51061671270, "id": 95, "parentId": 87, "tags": {}, "startTime": 1750267633781, "traceId": "cca038ed79a07689"}, {"name": "hash", "duration": 22431, "timestamp": 51062665459, "id": 96, "parentId": 87, "tags": {}, "startTime": 1750267634775, "traceId": "cca038ed79a07689"}, {"name": "code-generation-jobs", "duration": 551, "timestamp": 51062687884, "id": 97, "parentId": 87, "tags": {}, "startTime": 1750267634798, "traceId": "cca038ed79a07689"}, {"name": "module-assets", "duration": 1380, "timestamp": 51062688332, "id": 98, "parentId": 87, "tags": {}, "startTime": 1750267634798, "traceId": "cca038ed79a07689"}, {"name": "create-chunk-assets", "duration": 11206, "timestamp": 51062689765, "id": 99, "parentId": 87, "tags": {}, "startTime": 1750267634799, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 76054, "timestamp": 51062744984, "id": 101, "parentId": 100, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1750267634855, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 75650, "timestamp": 51062745408, "id": 102, "parentId": 100, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1750267634855, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 75624, "timestamp": 51062745437, "id": 103, "parentId": 100, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1750267634855, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 75612, "timestamp": 51062745452, "id": 104, "parentId": 100, "tags": {"name": "../app/admin/page.js", "cache": "HIT"}, "startTime": 1750267634855, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 75599, "timestamp": 51062745468, "id": 105, "parentId": 100, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1750267634855, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 75588, "timestamp": 51062745483, "id": 106, "parentId": 100, "tags": {"name": "../app/login/page.js", "cache": "HIT"}, "startTime": 1750267634855, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 75578, "timestamp": 51062745497, "id": 107, "parentId": 100, "tags": {"name": "../app/dashboard/page.js", "cache": "HIT"}, "startTime": 1750267634855, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 581, "timestamp": 51062820498, "id": 109, "parentId": 100, "tags": {"name": "../app/dashboard/history/page.js", "cache": "HIT"}, "startTime": 1750267634930, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 524, "timestamp": 51062820557, "id": 110, "parentId": 100, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1750267634930, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 308, "timestamp": 51062820776, "id": 111, "parentId": 100, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1750267634930, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 230, "timestamp": 51062820857, "id": 112, "parentId": 100, "tags": {"name": "204.js", "cache": "HIT"}, "startTime": 1750267634930, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 199, "timestamp": 51062820891, "id": 113, "parentId": 100, "tags": {"name": "303.js", "cache": "HIT"}, "startTime": 1750267634931, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 183, "timestamp": 51062820909, "id": 114, "parentId": 100, "tags": {"name": "585.js", "cache": "HIT"}, "startTime": 1750267634931, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 171, "timestamp": 51062820923, "id": 115, "parentId": 100, "tags": {"name": "118.js", "cache": "HIT"}, "startTime": 1750267634931, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 156, "timestamp": 51062820940, "id": 116, "parentId": 100, "tags": {"name": "191.js", "cache": "HIT"}, "startTime": 1750267634931, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 143, "timestamp": 51062820956, "id": 117, "parentId": 100, "tags": {"name": "112.js", "cache": "HIT"}, "startTime": 1750267634931, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 130, "timestamp": 51062820973, "id": 118, "parentId": 100, "tags": {"name": "525.js", "cache": "HIT"}, "startTime": 1750267634931, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 117, "timestamp": 51062820988, "id": 119, "parentId": 100, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1750267634931, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 104, "timestamp": 51062821004, "id": 120, "parentId": 100, "tags": {"name": "124.js", "cache": "HIT"}, "startTime": 1750267634931, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 92, "timestamp": 51062821020, "id": 121, "parentId": 100, "tags": {"name": "241.js", "cache": "HIT"}, "startTime": 1750267634931, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 751392, "timestamp": 51062745510, "id": 108, "parentId": 100, "tags": {"name": "../app/dashboard/analytics/page.js", "cache": "MISS"}, "startTime": 1750267634855, "traceId": "cca038ed79a07689"}, {"name": "minify-webpack-plugin-optimize", "duration": 790580, "timestamp": 51062706349, "id": 100, "parentId": 16, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1750267634816, "traceId": "cca038ed79a07689"}, {"name": "css-minimizer-plugin", "duration": 222, "timestamp": 51063497166, "id": 122, "parentId": 16, "tags": {}, "startTime": 1750267635607, "traceId": "cca038ed79a07689"}, {"name": "create-trace-assets", "duration": 3133, "timestamp": 51063497809, "id": 123, "parentId": 17, "tags": {}, "startTime": 1750267635607, "traceId": "cca038ed79a07689"}, {"name": "create-trace-assets", "duration": 1788, "timestamp": 51063501245, "id": 124, "parentId": 17, "tags": {}, "startTime": 1750267635611, "traceId": "cca038ed79a07689"}, {"name": "seal", "duration": 2886438, "timestamp": 51060666484, "id": 87, "parentId": 16, "tags": {}, "startTime": 1750267632776, "traceId": "cca038ed79a07689"}, {"name": "webpack-compilation", "duration": 10564463, "timestamp": 51053055357, "id": 16, "parentId": 13, "tags": {"name": "server"}, "startTime": 1750267625165, "traceId": "cca038ed79a07689"}, {"name": "emit", "duration": 58030, "timestamp": 51063620695, "id": 125, "parentId": 13, "tags": {}, "startTime": 1750267635730, "traceId": "cca038ed79a07689"}, {"name": "webpack-close", "duration": 540876, "timestamp": 51063689085, "id": 126, "parentId": 13, "tags": {"name": "server"}, "startTime": 1750267635799, "traceId": "cca038ed79a07689"}, {"name": "webpack-generate-error-stats", "duration": 7672, "timestamp": 51064230124, "id": 127, "parentId": 126, "tags": {}, "startTime": 1750267636340, "traceId": "cca038ed79a07689"}, {"name": "make", "duration": 674, "timestamp": 51064269018, "id": 129, "parentId": 128, "tags": {}, "startTime": 1750267636379, "traceId": "cca038ed79a07689"}, {"name": "chunk-graph", "duration": 67, "timestamp": 51064272053, "id": 131, "parentId": 130, "tags": {}, "startTime": 1750267636382, "traceId": "cca038ed79a07689"}, {"name": "optimize-modules", "duration": 16, "timestamp": 51064272230, "id": 133, "parentId": 130, "tags": {}, "startTime": 1750267636382, "traceId": "cca038ed79a07689"}, {"name": "optimize-chunks", "duration": 123, "timestamp": 51064272349, "id": 134, "parentId": 130, "tags": {}, "startTime": 1750267636382, "traceId": "cca038ed79a07689"}, {"name": "optimize-tree", "duration": 21, "timestamp": 51064272604, "id": 135, "parentId": 130, "tags": {}, "startTime": 1750267636382, "traceId": "cca038ed79a07689"}, {"name": "optimize-chunk-modules", "duration": 120, "timestamp": 51064272750, "id": 136, "parentId": 130, "tags": {}, "startTime": 1750267636382, "traceId": "cca038ed79a07689"}, {"name": "optimize", "duration": 786, "timestamp": 51064272157, "id": 132, "parentId": 130, "tags": {}, "startTime": 1750267636382, "traceId": "cca038ed79a07689"}, {"name": "module-hash", "duration": 47, "timestamp": 51064273383, "id": 137, "parentId": 130, "tags": {}, "startTime": 1750267636383, "traceId": "cca038ed79a07689"}, {"name": "code-generation", "duration": 55, "timestamp": 51064274396, "id": 138, "parentId": 130, "tags": {}, "startTime": 1750267636384, "traceId": "cca038ed79a07689"}, {"name": "hash", "duration": 510, "timestamp": 51064274951, "id": 139, "parentId": 130, "tags": {}, "startTime": 1750267636385, "traceId": "cca038ed79a07689"}, {"name": "code-generation-jobs", "duration": 226, "timestamp": 51064275459, "id": 140, "parentId": 130, "tags": {}, "startTime": 1750267636385, "traceId": "cca038ed79a07689"}, {"name": "module-assets", "duration": 64, "timestamp": 51064275641, "id": 141, "parentId": 130, "tags": {}, "startTime": 1750267636385, "traceId": "cca038ed79a07689"}, {"name": "create-chunk-assets", "duration": 70, "timestamp": 51064275722, "id": 142, "parentId": 130, "tags": {}, "startTime": 1750267636385, "traceId": "cca038ed79a07689"}, {"name": "minify-js", "duration": 74, "timestamp": 51064285254, "id": 144, "parentId": 143, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1750267636395, "traceId": "cca038ed79a07689"}, {"name": "minify-webpack-plugin-optimize", "duration": 2011, "timestamp": 51064283337, "id": 143, "parentId": 128, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1750267636393, "traceId": "cca038ed79a07689"}, {"name": "css-minimizer-plugin", "duration": 15, "timestamp": 51064285509, "id": 145, "parentId": 128, "tags": {}, "startTime": 1750267636395, "traceId": "cca038ed79a07689"}, {"name": "seal", "duration": 20634, "timestamp": 51064271667, "id": 130, "parentId": 128, "tags": {}, "startTime": 1750267636381, "traceId": "cca038ed79a07689"}, {"name": "webpack-compilation", "duration": 28821, "timestamp": 51064263827, "id": 128, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1750267636373, "traceId": "cca038ed79a07689"}, {"name": "emit", "duration": 3601, "timestamp": 51064292801, "id": 146, "parentId": 13, "tags": {}, "startTime": 1750267636402, "traceId": "cca038ed79a07689"}, {"name": "webpack-close", "duration": 369, "timestamp": 51064297068, "id": 147, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1750267636407, "traceId": "cca038ed79a07689"}, {"name": "webpack-generate-error-stats", "duration": 1078, "timestamp": 51064297452, "id": 148, "parentId": 147, "tags": {}, "startTime": 1750267636407, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 1143068, "timestamp": 51064336556, "id": 155, "parentId": 150, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1750267636446, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 1312995, "timestamp": 51064336592, "id": 156, "parentId": 150, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1750267636446, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 1396931, "timestamp": 51064336439, "id": 154, "parentId": 150, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1750267636446, "traceId": "cca038ed79a07689"}, {"name": "build-module-tsx", "duration": 79234, "timestamp": 51065826349, "id": 168, "parentId": 149, "tags": {"name": "E:\\bot\\tradingbot_final\\frontend\\src\\components\\dashboard\\SessionAwareAnalytics.tsx", "layer": "app-pages-browser"}, "startTime": 1750267637936, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 1682199, "timestamp": 51064336404, "id": 153, "parentId": 150, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1750267636446, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 1693897, "timestamp": 51064336774, "id": 163, "parentId": 150, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1750267636446, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 1701308, "timestamp": 51064336323, "id": 152, "parentId": 150, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1750267636446, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 1714283, "timestamp": 51064336739, "id": 160, "parentId": 150, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1750267636446, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 1714288, "timestamp": 51064336756, "id": 161, "parentId": 150, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1750267636446, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 1714419, "timestamp": 51064336646, "id": 157, "parentId": 150, "tags": {"request": "E:\\bot\\tradingbot_final\\frontend\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1750267636446, "traceId": "cca038ed79a07689"}, {"name": "add-entry", "duration": 1714944, "timestamp": 51064336135, "id": 151, "parentId": 150, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1750267636446, "traceId": "cca038ed79a07689"}]