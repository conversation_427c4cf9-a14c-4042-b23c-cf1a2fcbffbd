"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_det"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/initialize-for-app-router.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/initialize-for-app-router.js ***!
  \********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"initializeDevBuildIndicatorForAppRouter\", ({\n    enumerable: true,\n    get: function() {\n        return initializeDevBuildIndicatorForAppRouter;\n    }\n}));\nconst _devbuildindicator = __webpack_require__(/*! ./internal/dev-build-indicator */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\");\nconst initializeDevBuildIndicatorForAppRouter = ()=>{\n    if (false) {}\n    _devbuildindicator.devBuildIndicator.initialize();\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=initialize-for-app-router.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2Rldi9kZXYtYnVpbGQtaW5kaWNhdG9yL2luaXRpYWxpemUtZm9yLWFwcC1yb3V0ZXIuanMiLCJtYXBwaW5ncyI6Ijs7OzsyRUFHYUE7OztlQUFBQTs7OytDQUhxQjtBQUczQixNQUFNQSwwQ0FBMEM7SUFDckQsSUFBSSxLQUFpQyxFQUFFLEVBRXRDO0lBRURJLG1CQUFBQSxpQkFBaUIsQ0FBQ0MsVUFBVTtBQUM5QiIsInNvdXJjZXMiOlsiRTpcXHNyY1xcY2xpZW50XFxkZXZcXGRldi1idWlsZC1pbmRpY2F0b3JcXGluaXRpYWxpemUtZm9yLWFwcC1yb3V0ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGV2QnVpbGRJbmRpY2F0b3IgfSBmcm9tICcuL2ludGVybmFsL2Rldi1idWlsZC1pbmRpY2F0b3InXG5cbi8qKiBJbnRlZ3JhdGVzIHRoZSBnZW5lcmljIGRldiBidWlsZCBpbmRpY2F0b3Igd2l0aCB0aGUgQXBwIFJvdXRlci4gKi9cbmV4cG9ydCBjb25zdCBpbml0aWFsaXplRGV2QnVpbGRJbmRpY2F0b3JGb3JBcHBSb3V0ZXIgPSAoKSA9PiB7XG4gIGlmICghcHJvY2Vzcy5lbnYuX19ORVhUX0RFVl9JTkRJQ0FUT1IpIHtcbiAgICByZXR1cm5cbiAgfVxuXG4gIGRldkJ1aWxkSW5kaWNhdG9yLmluaXRpYWxpemUoKVxufVxuIl0sIm5hbWVzIjpbImluaXRpYWxpemVEZXZCdWlsZEluZGljYXRvckZvckFwcFJvdXRlciIsInByb2Nlc3MiLCJlbnYiLCJfX05FWFRfREVWX0lORElDQVRPUiIsImRldkJ1aWxkSW5kaWNhdG9yIiwiaW5pdGlhbGl6ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/initialize-for-app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js ***!
  \***********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"devBuildIndicator\", ({\n    enumerable: true,\n    get: function() {\n        return devBuildIndicator;\n    }\n}));\nconst _initialize = __webpack_require__(/*! ./initialize */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js\");\nconst NOOP = ()=>{};\n_c = NOOP;\nconst devBuildIndicator = {\n    /** Shows build indicator when Next.js is compiling. Requires initialize() first. */ show: NOOP,\n    /** Hides build indicator when Next.js finishes compiling. Requires initialize() first. */ hide: NOOP,\n    /** Sets up the build indicator UI component. Call this before using show/hide. */ initialize: _initialize.initialize\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-build-indicator.js.map\nvar _c;\n$RefreshReg$(_c, \"NOOP\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2Rldi9kZXYtYnVpbGQtaW5kaWNhdG9yL2ludGVybmFsL2Rldi1idWlsZC1pbmRpY2F0b3IuanMiLCJtYXBwaW5ncyI6Ijs7OztxREFJYUE7OztlQUFBQTs7O3dDQUpjO0FBRTNCLGFBQWEsS0FBTztLQUFkQztBQUVDLE1BQU1ELG9CQUFvQjtJQUMvQixrRkFBa0YsR0FDbEZFLE1BQU1EO0lBQ04sd0ZBQXdGLEdBQ3hGRSxNQUFNRjtJQUNOLGdGQUFnRixHQUNoRkcsWUFBQUEsWUFBQUEsVUFBVTtBQUNaIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGRldlxcZGV2LWJ1aWxkLWluZGljYXRvclxcaW50ZXJuYWxcXGRldi1idWlsZC1pbmRpY2F0b3IudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW5pdGlhbGl6ZSB9IGZyb20gJy4vaW5pdGlhbGl6ZSdcblxuY29uc3QgTk9PUCA9ICgpID0+IHt9XG5cbmV4cG9ydCBjb25zdCBkZXZCdWlsZEluZGljYXRvciA9IHtcbiAgLyoqIFNob3dzIGJ1aWxkIGluZGljYXRvciB3aGVuIE5leHQuanMgaXMgY29tcGlsaW5nLiBSZXF1aXJlcyBpbml0aWFsaXplKCkgZmlyc3QuICovXG4gIHNob3c6IE5PT1AsXG4gIC8qKiBIaWRlcyBidWlsZCBpbmRpY2F0b3Igd2hlbiBOZXh0LmpzIGZpbmlzaGVzIGNvbXBpbGluZy4gUmVxdWlyZXMgaW5pdGlhbGl6ZSgpIGZpcnN0LiAqL1xuICBoaWRlOiBOT09QLFxuICAvKiogU2V0cyB1cCB0aGUgYnVpbGQgaW5kaWNhdG9yIFVJIGNvbXBvbmVudC4gQ2FsbCB0aGlzIGJlZm9yZSB1c2luZyBzaG93L2hpZGUuICovXG4gIGluaXRpYWxpemUsXG59XG4iXSwibmFtZXMiOlsiZGV2QnVpbGRJbmRpY2F0b3IiLCJOT09QIiwic2hvdyIsImhpZGUiLCJpbml0aWFsaXplIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js ***!
  \*****************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"handleDevBuildIndicatorHmrEvents\", ({\n    enumerable: true,\n    get: function() {\n        return handleDevBuildIndicatorHmrEvents;\n    }\n}));\nconst _hotreloadertypes = __webpack_require__(/*! ../../../../server/dev/hot-reloader-types */ \"(app-pages-browser)/./node_modules/next/dist/server/dev/hot-reloader-types.js\");\nconst _devbuildindicator = __webpack_require__(/*! ./dev-build-indicator */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\");\nconst handleDevBuildIndicatorHmrEvents = (obj)=>{\n    try {\n        if (!('action' in obj)) {\n            return;\n        }\n        // eslint-disable-next-line default-case\n        switch(obj.action){\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILDING:\n                _devbuildindicator.devBuildIndicator.show();\n                break;\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SYNC:\n                _devbuildindicator.devBuildIndicator.hide();\n                break;\n        }\n    } catch (e) {}\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=handle-dev-build-indicator-hmr-events.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js ***!
  \**************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/*\n * Singleton store to track whether the app is currently being built\n * Used by the dev tools indicator of the new overlay to show build status\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    initialize: function() {\n        return initialize;\n    },\n    useIsDevBuilding: function() {\n        return useIsDevBuilding;\n    }\n});\nconst _devbuildindicator = __webpack_require__(/*! ./dev-build-indicator */ \"(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nlet isVisible = false;\nlet listeners = [];\nconst subscribe = (listener)=>{\n    listeners.push(listener);\n    return ()=>{\n        listeners = listeners.filter((l)=>l !== listener);\n    };\n};\nconst getSnapshot = ()=>isVisible;\nfunction useIsDevBuilding() {\n    return (0, _react.useSyncExternalStore)(subscribe, getSnapshot);\n}\nfunction initialize() {\n    _devbuildindicator.devBuildIndicator.show = ()=>{\n        isVisible = true;\n        listeners.forEach((listener)=>listener());\n    };\n    _devbuildindicator.devBuildIndicator.hide = ()=>{\n        isVisible = false;\n        listeners.forEach((listener)=>listener());\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=initialize.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/detect-domain-locale.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/client/detect-domain-locale.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"detectDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return detectDomainLocale;\n    }\n}));\nconst detectDomainLocale = function() {\n    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n        args[_key] = arguments[_key];\n    }\n    if (false) {}\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=detect-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2RldGVjdC1kb21haW4tbG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7c0RBRWFBOzs7ZUFBQUE7OztBQUFOLE1BQU1BLHFCQUFnQztxQ0FBSUMsT0FBQUEsSUFBQUEsTUFBQUEsT0FBQUEsT0FBQUEsR0FBQUEsT0FBQUEsTUFBQUEsT0FBQUE7UUFBQUEsSUFBQUEsQ0FBQUEsS0FBQUEsR0FBQUEsU0FBQUEsQ0FBQUEsS0FBQUE7O0lBQy9DLElBQUlDLEtBQStCLEVBQUUsRUFJcEM7QUFDSCIsInNvdXJjZXMiOlsiRTpcXGJvdFxcc3JjXFxjbGllbnRcXGRldGVjdC1kb21haW4tbG9jYWxlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgZGV0ZWN0RG9tYWluTG9jYWxlIGFzIEZuIH0gZnJvbSAnLi4vc2hhcmVkL2xpYi9pMThuL2RldGVjdC1kb21haW4tbG9jYWxlJ1xuXG5leHBvcnQgY29uc3QgZGV0ZWN0RG9tYWluTG9jYWxlOiB0eXBlb2YgRm4gPSAoLi4uYXJncykgPT4ge1xuICBpZiAocHJvY2Vzcy5lbnYuX19ORVhUX0kxOE5fU1VQUE9SVCkge1xuICAgIHJldHVybiByZXF1aXJlKCcuLi9zaGFyZWQvbGliL2kxOG4vZGV0ZWN0LWRvbWFpbi1sb2NhbGUnKS5kZXRlY3REb21haW5Mb2NhbGUoXG4gICAgICAuLi5hcmdzXG4gICAgKVxuICB9XG59XG4iXSwibmFtZXMiOlsiZGV0ZWN0RG9tYWluTG9jYWxlIiwiYXJncyIsInByb2Nlc3MiLCJlbnYiLCJfX05FWFRfSTE4Tl9TVVBQT1JUIiwicmVxdWlyZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/detect-domain-locale.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/initialize-for-page-router.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/initialize-for-page-router.js ***!
  \*********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"initializeDevBuildIndicatorForPageRouter\", ({\n    enumerable: true,\n    get: function() {\n        return initializeDevBuildIndicatorForPageRouter;\n    }\n}));\nconst _websocket = __webpack_require__(/*! ../../components/react-dev-overlay/pages/websocket */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/pages/websocket.js\");\nconst _devbuildindicator = __webpack_require__(/*! ./internal/dev-build-indicator */ \"(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\");\nconst _handledevbuildindicatorhmrevents = __webpack_require__(/*! ./internal/handle-dev-build-indicator-hmr-events */ \"(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js\");\nconst initializeDevBuildIndicatorForPageRouter = ()=>{\n    if (false) {}\n    _devbuildindicator.devBuildIndicator.initialize();\n    // Add message listener specifically for Pages Router to handle lifecycle events\n    // related to dev builds (building, built, sync)\n    (0, _websocket.addMessageListener)(_handledevbuildindicatorhmrevents.handleDevBuildIndicatorHmrEvents);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=initialize-for-page-router.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2Rldi9kZXYtYnVpbGQtaW5kaWNhdG9yL2luaXRpYWxpemUtZm9yLXBhZ2Utcm91dGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7NEVBS2FBOzs7ZUFBQUE7Ozt1Q0FMc0I7K0NBQ0Q7OERBQ2U7QUFHMUMsTUFBTUEsMkNBQTJDO0lBQ3RELElBQUksS0FBaUMsRUFBRSxFQUV0QztJQUVESSxtQkFBQUEsaUJBQWlCLENBQUNDLFVBQVU7SUFFNUIsZ0ZBQWdGO0lBQ2hGLGdEQUFnRDtJQUNoREMsQ0FBQUEsR0FBQUEsV0FBQUEsa0JBQUFBLEVBQW1CQyxrQ0FBQUEsZ0NBQWdDO0FBQ3JEIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGRldlxcZGV2LWJ1aWxkLWluZGljYXRvclxcaW5pdGlhbGl6ZS1mb3ItcGFnZS1yb3V0ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYWRkTWVzc2FnZUxpc3RlbmVyIH0gZnJvbSAnLi4vLi4vY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9wYWdlcy93ZWJzb2NrZXQnXG5pbXBvcnQgeyBkZXZCdWlsZEluZGljYXRvciB9IGZyb20gJy4vaW50ZXJuYWwvZGV2LWJ1aWxkLWluZGljYXRvcidcbmltcG9ydCB7IGhhbmRsZURldkJ1aWxkSW5kaWNhdG9ySG1yRXZlbnRzIH0gZnJvbSAnLi9pbnRlcm5hbC9oYW5kbGUtZGV2LWJ1aWxkLWluZGljYXRvci1obXItZXZlbnRzJ1xuXG4vKiogSW50ZWdyYXRlcyB0aGUgZ2VuZXJpYyBkZXYgYnVpbGQgaW5kaWNhdG9yIHdpdGggdGhlIFBhZ2VzIFJvdXRlci4gKi9cbmV4cG9ydCBjb25zdCBpbml0aWFsaXplRGV2QnVpbGRJbmRpY2F0b3JGb3JQYWdlUm91dGVyID0gKCkgPT4ge1xuICBpZiAoIXByb2Nlc3MuZW52Ll9fTkVYVF9ERVZfSU5ESUNBVE9SKSB7XG4gICAgcmV0dXJuXG4gIH1cblxuICBkZXZCdWlsZEluZGljYXRvci5pbml0aWFsaXplKClcblxuICAvLyBBZGQgbWVzc2FnZSBsaXN0ZW5lciBzcGVjaWZpY2FsbHkgZm9yIFBhZ2VzIFJvdXRlciB0byBoYW5kbGUgbGlmZWN5Y2xlIGV2ZW50c1xuICAvLyByZWxhdGVkIHRvIGRldiBidWlsZHMgKGJ1aWxkaW5nLCBidWlsdCwgc3luYylcbiAgYWRkTWVzc2FnZUxpc3RlbmVyKGhhbmRsZURldkJ1aWxkSW5kaWNhdG9ySG1yRXZlbnRzKVxufVxuIl0sIm5hbWVzIjpbImluaXRpYWxpemVEZXZCdWlsZEluZGljYXRvckZvclBhZ2VSb3V0ZXIiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX0RFVl9JTkRJQ0FUT1IiLCJkZXZCdWlsZEluZGljYXRvciIsImluaXRpYWxpemUiLCJhZGRNZXNzYWdlTGlzdGVuZXIiLCJoYW5kbGVEZXZCdWlsZEluZGljYXRvckhtckV2ZW50cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/initialize-for-page-router.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js ***!
  \***********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"devBuildIndicator\", ({\n    enumerable: true,\n    get: function() {\n        return devBuildIndicator;\n    }\n}));\nconst _initialize = __webpack_require__(/*! ./initialize */ \"(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js\");\nconst NOOP = ()=>{};\n_c = NOOP;\nconst devBuildIndicator = {\n    /** Shows build indicator when Next.js is compiling. Requires initialize() first. */ show: NOOP,\n    /** Hides build indicator when Next.js finishes compiling. Requires initialize() first. */ hide: NOOP,\n    /** Sets up the build indicator UI component. Call this before using show/hide. */ initialize: _initialize.initialize\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-build-indicator.js.map\nvar _c;\n$RefreshReg$(_c, \"NOOP\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2Rldi9kZXYtYnVpbGQtaW5kaWNhdG9yL2ludGVybmFsL2Rldi1idWlsZC1pbmRpY2F0b3IuanMiLCJtYXBwaW5ncyI6Ijs7OztxREFJYUE7OztlQUFBQTs7O3dDQUpjO0FBRTNCLGFBQWEsS0FBTztLQUFkQztBQUVDLE1BQU1ELG9CQUFvQjtJQUMvQixrRkFBa0YsR0FDbEZFLE1BQU1EO0lBQ04sd0ZBQXdGLEdBQ3hGRSxNQUFNRjtJQUNOLGdGQUFnRixHQUNoRkcsWUFBQUEsWUFBQUEsVUFBVTtBQUNaIiwic291cmNlcyI6WyJFOlxcc3JjXFxjbGllbnRcXGRldlxcZGV2LWJ1aWxkLWluZGljYXRvclxcaW50ZXJuYWxcXGRldi1idWlsZC1pbmRpY2F0b3IudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW5pdGlhbGl6ZSB9IGZyb20gJy4vaW5pdGlhbGl6ZSdcblxuY29uc3QgTk9PUCA9ICgpID0+IHt9XG5cbmV4cG9ydCBjb25zdCBkZXZCdWlsZEluZGljYXRvciA9IHtcbiAgLyoqIFNob3dzIGJ1aWxkIGluZGljYXRvciB3aGVuIE5leHQuanMgaXMgY29tcGlsaW5nLiBSZXF1aXJlcyBpbml0aWFsaXplKCkgZmlyc3QuICovXG4gIHNob3c6IE5PT1AsXG4gIC8qKiBIaWRlcyBidWlsZCBpbmRpY2F0b3Igd2hlbiBOZXh0LmpzIGZpbmlzaGVzIGNvbXBpbGluZy4gUmVxdWlyZXMgaW5pdGlhbGl6ZSgpIGZpcnN0LiAqL1xuICBoaWRlOiBOT09QLFxuICAvKiogU2V0cyB1cCB0aGUgYnVpbGQgaW5kaWNhdG9yIFVJIGNvbXBvbmVudC4gQ2FsbCB0aGlzIGJlZm9yZSB1c2luZyBzaG93L2hpZGUuICovXG4gIGluaXRpYWxpemUsXG59XG4iXSwibmFtZXMiOlsiZGV2QnVpbGRJbmRpY2F0b3IiLCJOT09QIiwic2hvdyIsImhpZGUiLCJpbml0aWFsaXplIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js ***!
  \*****************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"handleDevBuildIndicatorHmrEvents\", ({\n    enumerable: true,\n    get: function() {\n        return handleDevBuildIndicatorHmrEvents;\n    }\n}));\nconst _hotreloadertypes = __webpack_require__(/*! ../../../../server/dev/hot-reloader-types */ \"(pages-dir-browser)/./node_modules/next/dist/server/dev/hot-reloader-types.js\");\nconst _devbuildindicator = __webpack_require__(/*! ./dev-build-indicator */ \"(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\");\nconst handleDevBuildIndicatorHmrEvents = (obj)=>{\n    try {\n        if (!('action' in obj)) {\n            return;\n        }\n        // eslint-disable-next-line default-case\n        switch(obj.action){\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILDING:\n                _devbuildindicator.devBuildIndicator.show();\n                break;\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n            case _hotreloadertypes.HMR_ACTIONS_SENT_TO_BROWSER.SYNC:\n                _devbuildindicator.devBuildIndicator.hide();\n                break;\n        }\n    } catch (e) {}\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=handle-dev-build-indicator-hmr-events.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js ***!
  \**************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/*\n * Singleton store to track whether the app is currently being built\n * Used by the dev tools indicator of the new overlay to show build status\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    initialize: function() {\n        return initialize;\n    },\n    useIsDevBuilding: function() {\n        return useIsDevBuilding;\n    }\n});\nconst _devbuildindicator = __webpack_require__(/*! ./dev-build-indicator */ \"(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/dev-build-indicator.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nlet isVisible = false;\nlet listeners = [];\nconst subscribe = (listener)=>{\n    listeners.push(listener);\n    return ()=>{\n        listeners = listeners.filter((l)=>l !== listener);\n    };\n};\nconst getSnapshot = ()=>isVisible;\nfunction useIsDevBuilding() {\n    return (0, _react.useSyncExternalStore)(subscribe, getSnapshot);\n}\nfunction initialize() {\n    _devbuildindicator.devBuildIndicator.show = ()=>{\n        isVisible = true;\n        listeners.forEach((listener)=>listener());\n    };\n    _devbuildindicator.devBuildIndicator.hide = ()=>{\n        isVisible = false;\n        listeners.forEach((listener)=>listener());\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=initialize.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js\n"));

/***/ })

}]);