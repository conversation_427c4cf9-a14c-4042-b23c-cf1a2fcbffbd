"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_dev-tools-in-13e6d335"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.js ***!
  \**************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMinimumLoadingTimeMultiple\", ({\n    enumerable: true,\n    get: function() {\n        return useMinimumLoadingTimeMultiple;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction useMinimumLoadingTimeMultiple(isLoadingTrigger, interval) {\n    if (interval === void 0) interval = 750;\n    const [isLoading, setIsLoading] = (0, _react.useState)(false);\n    const loadStartTimeRef = (0, _react.useRef)(null);\n    const timeoutIdRef = (0, _react.useRef)(null);\n    (0, _react.useEffect)(()=>{\n        // Clear any pending timeout to avoid overlap\n        if (timeoutIdRef.current) {\n            clearTimeout(timeoutIdRef.current);\n            timeoutIdRef.current = null;\n        }\n        if (isLoadingTrigger) {\n            // If we enter \"loading\" state, record start time if not already\n            if (loadStartTimeRef.current === null) {\n                loadStartTimeRef.current = Date.now();\n            }\n            setIsLoading(true);\n        } else {\n            // If we're exiting the \"loading\" state:\n            if (loadStartTimeRef.current === null) {\n                // No start time was recorded, so just stop loading immediately\n                setIsLoading(false);\n            } else {\n                // How long we've been \"loading\"\n                const timeDiff = Date.now() - loadStartTimeRef.current;\n                // Next multiple of `interval` after `timeDiff`\n                const nextMultiple = interval * Math.ceil(timeDiff / interval);\n                // Remaining time needed to reach that multiple\n                const remainingTime = nextMultiple - timeDiff;\n                if (remainingTime > 0) {\n                    // If not yet at that multiple, schedule the final step\n                    timeoutIdRef.current = setTimeout(()=>{\n                        setIsLoading(false);\n                        loadStartTimeRef.current = null;\n                    }, remainingTime);\n                } else {\n                    // We're already past the multiple boundary\n                    setIsLoading(false);\n                    loadStartTimeRef.current = null;\n                }\n            }\n        }\n        // Cleanup when effect is about to re-run or component unmounts\n        return ()=>{\n            if (timeoutIdRef.current) {\n                clearTimeout(timeoutIdRef.current);\n            }\n        };\n    }, [\n        isLoadingTrigger,\n        interval\n    ]);\n    return isLoading;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-minimum-loading-time-multiple.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js ***!
  \**********************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    MENU_CURVE: function() {\n        return MENU_CURVE;\n    },\n    MENU_DURATION_MS: function() {\n        return MENU_DURATION_MS;\n    },\n    useClickOutside: function() {\n        return useClickOutside;\n    },\n    useFocusTrap: function() {\n        return useFocusTrap;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction useFocusTrap(rootRef, triggerRef, active, onOpenFocus) {\n    (0, _react.useEffect)(()=>{\n        let rootNode = null;\n        function onTab(e) {\n            if (e.key !== 'Tab' || rootNode === null) {\n                return;\n            }\n            const [firstFocusableNode, lastFocusableNode] = getFocusableNodes(rootNode);\n            const activeElement = getActiveElement(rootNode);\n            if (e.shiftKey) {\n                if (activeElement === firstFocusableNode) {\n                    lastFocusableNode == null ? void 0 : lastFocusableNode.focus();\n                    e.preventDefault();\n                }\n            } else {\n                if (activeElement === lastFocusableNode) {\n                    firstFocusableNode == null ? void 0 : firstFocusableNode.focus();\n                    e.preventDefault();\n                }\n            }\n        }\n        const id = setTimeout(()=>{\n            // Grab this on next tick to ensure the content is mounted\n            rootNode = rootRef.current;\n            if (active) {\n                if (onOpenFocus) {\n                    onOpenFocus();\n                } else {\n                    rootNode == null ? void 0 : rootNode.focus();\n                }\n                rootNode == null ? void 0 : rootNode.addEventListener('keydown', onTab);\n            } else {\n                const activeElement = getActiveElement(rootNode);\n                // Only restore focus if the focus was previously on the content.\n                // This avoids us accidentally focusing on mount when the\n                // user could want to interact with their own app instead.\n                if (triggerRef && (rootNode == null ? void 0 : rootNode.contains(activeElement))) {\n                    var _triggerRef_current;\n                    (_triggerRef_current = triggerRef.current) == null ? void 0 : _triggerRef_current.focus();\n                }\n            }\n        });\n        return ()=>{\n            clearTimeout(id);\n            rootNode == null ? void 0 : rootNode.removeEventListener('keydown', onTab);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        active\n    ]);\n}\nfunction getActiveElement(node) {\n    const root = node == null ? void 0 : node.getRootNode();\n    return root instanceof ShadowRoot ? root == null ? void 0 : root.activeElement : null;\n}\nfunction getFocusableNodes(node) {\n    const focusableElements = node.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n    if (!focusableElements) return [];\n    return [\n        focusableElements[0],\n        focusableElements[focusableElements.length - 1]\n    ];\n}\nfunction useClickOutside(rootRef, triggerRef, active, close) {\n    (0, _react.useEffect)(()=>{\n        if (!active) {\n            return;\n        }\n        function handleClickOutside(event) {\n            var _rootRef_current, _triggerRef_current;\n            if (!(((_rootRef_current = rootRef.current) == null ? void 0 : _rootRef_current.getBoundingClientRect()) ? event.clientX >= rootRef.current.getBoundingClientRect().left && event.clientX <= rootRef.current.getBoundingClientRect().right && event.clientY >= rootRef.current.getBoundingClientRect().top && event.clientY <= rootRef.current.getBoundingClientRect().bottom : false) && !(((_triggerRef_current = triggerRef.current) == null ? void 0 : _triggerRef_current.getBoundingClientRect()) ? event.clientX >= triggerRef.current.getBoundingClientRect().left && event.clientX <= triggerRef.current.getBoundingClientRect().right && event.clientY >= triggerRef.current.getBoundingClientRect().top && event.clientY <= triggerRef.current.getBoundingClientRect().bottom : false)) {\n                close();\n            }\n        }\n        function handleKeyDown(event) {\n            if (event.key === 'Escape') {\n                close();\n            }\n        }\n        document.addEventListener('mousedown', handleClickOutside);\n        document.addEventListener('keydown', handleKeyDown);\n        return ()=>{\n            document.removeEventListener('mousedown', handleClickOutside);\n            document.removeEventListener('keydown', handleKeyDown);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        active\n    ]);\n}\nconst MENU_DURATION_MS = 200;\nconst MENU_CURVE = 'cubic-bezier(0.175, 0.885, 0.32, 1.1)';\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.js ***!
  \**************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMinimumLoadingTimeMultiple\", ({\n    enumerable: true,\n    get: function() {\n        return useMinimumLoadingTimeMultiple;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nfunction useMinimumLoadingTimeMultiple(isLoadingTrigger, interval) {\n    if (interval === void 0) interval = 750;\n    const [isLoading, setIsLoading] = (0, _react.useState)(false);\n    const loadStartTimeRef = (0, _react.useRef)(null);\n    const timeoutIdRef = (0, _react.useRef)(null);\n    (0, _react.useEffect)(()=>{\n        // Clear any pending timeout to avoid overlap\n        if (timeoutIdRef.current) {\n            clearTimeout(timeoutIdRef.current);\n            timeoutIdRef.current = null;\n        }\n        if (isLoadingTrigger) {\n            // If we enter \"loading\" state, record start time if not already\n            if (loadStartTimeRef.current === null) {\n                loadStartTimeRef.current = Date.now();\n            }\n            setIsLoading(true);\n        } else {\n            // If we're exiting the \"loading\" state:\n            if (loadStartTimeRef.current === null) {\n                // No start time was recorded, so just stop loading immediately\n                setIsLoading(false);\n            } else {\n                // How long we've been \"loading\"\n                const timeDiff = Date.now() - loadStartTimeRef.current;\n                // Next multiple of `interval` after `timeDiff`\n                const nextMultiple = interval * Math.ceil(timeDiff / interval);\n                // Remaining time needed to reach that multiple\n                const remainingTime = nextMultiple - timeDiff;\n                if (remainingTime > 0) {\n                    // If not yet at that multiple, schedule the final step\n                    timeoutIdRef.current = setTimeout(()=>{\n                        setIsLoading(false);\n                        loadStartTimeRef.current = null;\n                    }, remainingTime);\n                } else {\n                    // We're already past the multiple boundary\n                    setIsLoading(false);\n                    loadStartTimeRef.current = null;\n                }\n            }\n        }\n        // Cleanup when effect is about to re-run or component unmounts\n        return ()=>{\n            if (timeoutIdRef.current) {\n                clearTimeout(timeoutIdRef.current);\n            }\n        };\n    }, [\n        isLoadingTrigger,\n        interval\n    ]);\n    return isLoading;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-minimum-loading-time-multiple.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js ***!
  \**********************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    MENU_CURVE: function() {\n        return MENU_CURVE;\n    },\n    MENU_DURATION_MS: function() {\n        return MENU_DURATION_MS;\n    },\n    useClickOutside: function() {\n        return useClickOutside;\n    },\n    useFocusTrap: function() {\n        return useFocusTrap;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nfunction useFocusTrap(rootRef, triggerRef, active, onOpenFocus) {\n    (0, _react.useEffect)(()=>{\n        let rootNode = null;\n        function onTab(e) {\n            if (e.key !== 'Tab' || rootNode === null) {\n                return;\n            }\n            const [firstFocusableNode, lastFocusableNode] = getFocusableNodes(rootNode);\n            const activeElement = getActiveElement(rootNode);\n            if (e.shiftKey) {\n                if (activeElement === firstFocusableNode) {\n                    lastFocusableNode == null ? void 0 : lastFocusableNode.focus();\n                    e.preventDefault();\n                }\n            } else {\n                if (activeElement === lastFocusableNode) {\n                    firstFocusableNode == null ? void 0 : firstFocusableNode.focus();\n                    e.preventDefault();\n                }\n            }\n        }\n        const id = setTimeout(()=>{\n            // Grab this on next tick to ensure the content is mounted\n            rootNode = rootRef.current;\n            if (active) {\n                if (onOpenFocus) {\n                    onOpenFocus();\n                } else {\n                    rootNode == null ? void 0 : rootNode.focus();\n                }\n                rootNode == null ? void 0 : rootNode.addEventListener('keydown', onTab);\n            } else {\n                const activeElement = getActiveElement(rootNode);\n                // Only restore focus if the focus was previously on the content.\n                // This avoids us accidentally focusing on mount when the\n                // user could want to interact with their own app instead.\n                if (triggerRef && (rootNode == null ? void 0 : rootNode.contains(activeElement))) {\n                    var _triggerRef_current;\n                    (_triggerRef_current = triggerRef.current) == null ? void 0 : _triggerRef_current.focus();\n                }\n            }\n        });\n        return ()=>{\n            clearTimeout(id);\n            rootNode == null ? void 0 : rootNode.removeEventListener('keydown', onTab);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        active\n    ]);\n}\nfunction getActiveElement(node) {\n    const root = node == null ? void 0 : node.getRootNode();\n    return root instanceof ShadowRoot ? root == null ? void 0 : root.activeElement : null;\n}\nfunction getFocusableNodes(node) {\n    const focusableElements = node.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n    if (!focusableElements) return [];\n    return [\n        focusableElements[0],\n        focusableElements[focusableElements.length - 1]\n    ];\n}\nfunction useClickOutside(rootRef, triggerRef, active, close) {\n    (0, _react.useEffect)(()=>{\n        if (!active) {\n            return;\n        }\n        function handleClickOutside(event) {\n            var _rootRef_current, _triggerRef_current;\n            if (!(((_rootRef_current = rootRef.current) == null ? void 0 : _rootRef_current.getBoundingClientRect()) ? event.clientX >= rootRef.current.getBoundingClientRect().left && event.clientX <= rootRef.current.getBoundingClientRect().right && event.clientY >= rootRef.current.getBoundingClientRect().top && event.clientY <= rootRef.current.getBoundingClientRect().bottom : false) && !(((_triggerRef_current = triggerRef.current) == null ? void 0 : _triggerRef_current.getBoundingClientRect()) ? event.clientX >= triggerRef.current.getBoundingClientRect().left && event.clientX <= triggerRef.current.getBoundingClientRect().right && event.clientY >= triggerRef.current.getBoundingClientRect().top && event.clientY <= triggerRef.current.getBoundingClientRect().bottom : false)) {\n                close();\n            }\n        }\n        function handleKeyDown(event) {\n            if (event.key === 'Escape') {\n                close();\n            }\n        }\n        document.addEventListener('mousedown', handleClickOutside);\n        document.addEventListener('keydown', handleKeyDown);\n        return ()=>{\n            document.removeEventListener('mousedown', handleClickOutside);\n            document.removeEventListener('keydown', handleKeyDown);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        active\n    ]);\n}\nconst MENU_DURATION_MS = 200;\nconst MENU_CURVE = 'cubic-bezier(0.175, 0.885, 0.32, 1.1)';\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js\n"));

/***/ })

}]);