(()=>{var t={};t.id=754,t.ids=[754],t.modules={22:(t,e,r)=>{var n=r(75254),o=r(20623),i=r(48169),a=r(40542),c=r(45058);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):c(t)}},643:t=>{"use strict";t.exports=require("node:perf_hooks")},658:(t,e,r)=>{t.exports=r(41547)(r(85718),"Map")},1566:(t,e,r)=>{var n=r(89167),o=r(658),i=r(30401),a=r(34772),c=r(17830),u=r(29395),l=r(12290),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(n),v=l(o),m=l(i),b=l(a),g=l(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=d||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=h)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return d;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},1707:(t,e,r)=>{var n=r(35142),o=r(46436);t.exports=function(t,e){e=n(e,t);for(var r=0,i=e.length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},1708:t=>{"use strict";t.exports=require("node:process")},1944:t=>{t.exports=function(){return!1}},2408:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},2896:(t,e,r)=>{var n=r(81488),o=r(59467);t.exports=function(t,e){return null!=t&&o(t,e,n)}},2984:(t,e,r)=>{var n=r(49227);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},3105:t=>{t.exports=function(t){return t.split("")}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4080:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\analytics\\page.tsx","default")},4573:t=>{"use strict";t.exports=require("node:buffer")},4999:(t,e,r)=>{t.exports=r(85718).Uint8Array},5231:(t,e,r)=>{var n=r(29395),o=r(55048);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},5359:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},5566:(t,e,r)=>{var n=r(41011),o=r(34117),i=r(66713),a=r(42403);t.exports=function(t){return function(e){var r=o(e=a(e))?i(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},6053:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},6330:t=>{t.exports=function(){return[]}},7252:t=>{"use strict";t.exports=require("express")},7383:(t,e,r)=>{var n=r(67009),o=r(32269),i=r(38428),a=r(55048);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(o(r)&&i(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},7651:(t,e,r)=>{var n=r(82038),o=r(52931),i=r(32269);t.exports=function(t){return i(t)?n(t):o(t)}},8336:(t,e,r)=>{var n=r(45803);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},8751:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(82614).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},8852:(t,e,r)=>{var n=r(1707);t.exports=function(t){return function(e){return n(e,t)}}},10090:(t,e,r)=>{var n=r(80458),o=r(89624),i=r(47282),a=i&&i.isTypedArray;t.exports=a?o(a):n},10653:(t,e,r)=>{var n=r(21456),o=r(63979),i=r(7651);t.exports=function(t){return n(t,i,o)}},10663:t=>{t.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new o(n,i||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,o,i,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,u=Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||o&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},11424:(t,e,r)=>{var n=r(47603);t.exports=r(66400)(n)},11539:(t,e,r)=>{var n=r(37643),o=r(55048),i=r(49227),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return a;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||l.test(t)?s(t.slice(2),r?2:8):c.test(t)?a:+t}},12290:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},12344:(t,e,r)=>{t.exports=r(65984)()},12412:t=>{"use strict";t.exports=require("assert")},14675:t=>{t.exports=function(t){return function(){return t}}},14985:t=>{"use strict";t.exports=require("dns")},15331:(t,e,r)=>{Promise.resolve().then(r.bind(r,38278))},15451:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},15871:(t,e,r)=>{var n=r(36341),o=r(27467);t.exports=function t(e,r,i,a,c){return e===r||(null!=e&&null!=r&&(o(e)||o(r))?n(e,r,i,a,t,c):e!=e&&r!=r)}},15883:(t,e,r)=>{var n=r(2984),o=r(46063),i=r(48169);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},15909:(t,e,r)=>{var n=r(87506),o=r(66930),i=r(658);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},16141:t=>{"use strict";t.exports=require("node:zlib")},16698:t=>{"use strict";t.exports=require("node:async_hooks")},16854:t=>{t.exports=function(t){return this.__data__.has(t)}},17518:(t,e,r)=>{var n=r(21367),o=r(1707),i=r(22),a=r(54765),c=r(43378),u=r(89624),l=r(65727),s=r(48169),f=r(40542);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,u(i)),c(a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},17830:(t,e,r)=>{t.exports=r(41547)(r(85718),"WeakMap")},18234:(t,e,r)=>{var n=r(91290),o=r(22),i=r(84482),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:i(r);return u<0&&(u=a(c+u,0)),n(t,o(e,3),u)}},19063:t=>{"use strict";t.exports=require("require-in-the-middle")},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19185:t=>{"use strict";t.exports=require("dgram")},19771:t=>{"use strict";t.exports=require("process")},19976:(t,e,r)=>{var n=r(8336);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=+(r.size!=o),this}},20540:(t,e,r)=>{var n=r(55048),o=r(70151),i=r(11539),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=s}function x(){var t,r,n,i=o();if(g(i))return O(i);p=setTimeout(x,(t=i-h,r=i-d,n=e-t,v?c(n,s-r):n))}function O(t){return(p=void 0,m&&u)?b(t):(u=l=void 0,f)}function w(){var t,r=o(),n=g(r);if(u=arguments,l=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),w.cancel=function(){void 0!==p&&clearTimeout(p),d=0,u=h=l=p=void 0},w.flush=function(){return void 0===p?f:O(o())},w}},20623:(t,e,r)=>{var n=r(15871),o=r(40491),i=r(2896),a=r(67619),c=r(34883),u=r(41132),l=r(46436);t.exports=function(t,e){return a(t)&&c(e)?u(l(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},21367:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},21456:(t,e,r)=>{var n=r(41693),o=r(40542);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},21592:(t,e,r)=>{var n=r(42205),o=r(61837);t.exports=function(t,e){return n(o(t,e),1)}},21630:(t,e,r)=>{var n=r(10653),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,c){var u=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!u)return!1;for(var f=s;f--;){var p=l[f];if(!(u?p in e:o.call(e,p)))return!1}var h=c.get(t),d=c.get(e);if(h&&d)return h==e&&d==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<s;){var m=t[p=l[f]],b=e[p];if(i)var g=u?i(b,m,p,e,t,c):i(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,i,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,O=e.constructor;x!=O&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof O&&O instanceof O)&&(y=!1)}return c.delete(t),c.delete(e),y}},21820:t=>{"use strict";t.exports=require("os")},22964:(t,e,r)=>{t.exports=r(23729)(r(18234))},23729:(t,e,r)=>{var n=r(22),o=r(32269),i=r(7651);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!o(e)){var u=n(r,3);e=i(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},25118:t=>{t.exports=function(t){return this.__data__.has(t)}},27006:(t,e,r)=>{var n=r(46328),o=r(99525),i=r(58276);t.exports=function(t,e,r,a,c,u){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=u.get(t),h=u.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,u):a(m,b,d,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,u))){y=!1;break}}return u.delete(t),u.delete(e),y}},27467:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},27669:t=>{t.exports=function(){this.__data__=[],this.size=0}},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},28837:(t,e,r)=>{var n=r(57797),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},28977:(t,e,r)=>{var n=r(11539),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},29021:t=>{"use strict";t.exports=require("fs")},29205:(t,e,r)=>{var n=r(8336);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=+!!e,e}},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(t,e,r)=>{var n=r(79474),o=r(70222),i=r(84713),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},29508:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).get(t)}},29632:(t,e,r)=>{"use strict";t.exports=r(97668)},30316:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},30401:(t,e,r)=>{t.exports=r(41547)(r(85718),"Promise")},30854:(t,e,r)=>{var n=r(66930),o=r(658),i=r(95746);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},32269:(t,e,r)=>{var n=r(5231),o=r(69619);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},33873:t=>{"use strict";t.exports=require("path")},34117:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},34452:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34631:t=>{"use strict";t.exports=require("tls")},34746:t=>{t.exports=function(t){return this.__data__.get(t)}},34772:(t,e,r)=>{t.exports=r(41547)(r(85718),"Set")},34883:(t,e,r)=>{var n=r(55048);t.exports=function(t){return t==t&&!n(t)}},34990:(t,e,r)=>{t.exports=r(87321)()},35142:(t,e,r)=>{var n=r(40542),o=r(67619),i=r(51449),a=r(42403);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},35163:(t,e,r)=>{var n=r(15451),o=r(27467),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable;t.exports=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!c.call(t,"callee")}},35697:(t,e,r)=>{var n=r(79474),o=r(4999),i=r(67009),a=r(27006),c=r(59774),u=r(2408),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new o(t),new o(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=c;case"[object Set]":var d=1&n;if(h||(h=u),t.size!=e.size&&!d)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},35800:(t,e,r)=>{var n=r(57797);t.exports=function(t){return n(this.__data__,t)>-1}},36315:(t,e,r)=>{var n=r(22),o=r(92662);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},36341:(t,e,r)=>{var n=r(67200),o=r(27006),i=r(35697),a=r(21630),c=r(1566),u=r(40542),l=r(80329),s=r(10090),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),O=g?p:c(e);x=x==f?h:x,O=O==f?h:O;var w=x==h,j=O==h,S=x==O;if(S&&l(t)){if(!l(e))return!1;b=!0,w=!1}if(S&&!w)return m||(m=new n),b||s(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var P=w&&d.call(t,"__wrapped__"),A=j&&d.call(e,"__wrapped__");if(P||A){var E=P?t.value():t,k=A?e.value():e;return m||(m=new n),v(E,k,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},36959:t=>{t.exports=function(){}},37067:t=>{"use strict";t.exports=require("node:http")},37456:t=>{t.exports=function(t){return null==t}},37575:(t,e,r)=>{var n=r(66930);t.exports=function(){this.__data__=new n,this.size=0}},37643:(t,e,r)=>{var n=r(6053),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},37830:t=>{"use strict";t.exports=require("node:stream/web")},38278:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>dG});var n={};r.r(n),r.d(n,{scaleBand:()=>nM,scaleDiverging:()=>function t(){var e=iv(cC()(o6));return e.copy=function(){return cM(e,t())},nj.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=iP(cC()).domain([.1,1,10]);return e.copy=function(){return cM(e,t()).base(e.base())},nj.apply(e,arguments)},scaleDivergingPow:()=>cD,scaleDivergingSqrt:()=>cN,scaleDivergingSymlog:()=>function t(){var e=ik(cC());return e.copy=function(){return cM(e,t()).constant(e.constant())},nj.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,o3),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,o3):[0,1],iv(n)},scaleImplicit:()=>nE,scaleLinear:()=>im,scaleLog:()=>function t(){let e=iP(ie()).domain([1,10]);return e.copy=()=>it(e,t()).base(e.base()),nw.apply(e,arguments),e},scaleOrdinal:()=>nk,scalePoint:()=>n_,scalePow:()=>iD,scaleQuantile:()=>function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,r=oh){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[oy(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(ol),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},nw.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[oy(i,t,0,o)]:e}function u(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},c.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return i.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},nw.apply(iv(c),arguments)},scaleRadial:()=>function t(){var e,r=ir(),n=[0,1],o=!1;function i(t){var n,i=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return r.invert(iI(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,o3)).map(iI)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},nw.apply(i,arguments),iv(i)},scaleSequential:()=>function t(){var e=iv(ck()(o6));return e.copy=function(){return cM(e,t())},nj.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=iP(ck()).domain([1,10]);return e.copy=function(){return cM(e,t()).base(e.base())},nj.apply(e,arguments)},scaleSequentialPow:()=>c_,scaleSequentialQuantile:()=>function t(){var e=[],r=o6;function n(t){if(null!=t&&!isNaN(t*=1))return r((oy(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(ol),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return iL(t);if(e>=1)return iB(t);var n,o=(n-1)*e,i=Math.floor(o),a=iB((function t(e,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=r&&r<=o))return e;for(i=void 0===i?iR:function(t=ol){if(t===ol)return iR;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(i);o>n;){if(o-n>600){let a=o-n+1,c=r-n+1,u=Math.log(a),l=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+s)),p=Math.min(o,Math.floor(r+(a-c)*l/a+s));t(e,r,f,p,i)}let a=e[r],c=n,u=o;for(iz(e,n,r),i(e[o],a)>0&&iz(e,n,o);c<u;){for(iz(e,c,u),++c,--u;0>i(e[c],a);)++c;for(;i(e[u],a)>0;)--u}0===i(e[n],a)?iz(e,n,u):iz(e,++u,o),u<=r&&(n=u+1),r<=u&&(o=u-1)}return e})(t,i).subarray(0,i+1));return a+(iL(t.subarray(i+1))-a)*(o-i)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},nj.apply(n,arguments)},scaleSequentialSqrt:()=>cT,scaleSequentialSymlog:()=>function t(){var e=ik(ck());return e.copy=function(){return cM(e,t()).constant(e.constant())},nj.apply(e,arguments)},scaleSqrt:()=>iN,scaleSymlog:()=>function t(){var e=ik(ie());return e.copy=function(){return it(e,t()).constant(e.constant())},nw.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[oy(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(o=Math.min((r=Array.from(t)).length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},nw.apply(i,arguments)},scaleTime:()=>cA,scaleUtc:()=>cE,tickFormat:()=>iy});var o=r(60687),i=r(43210),a=r.n(i),c=r(37079),u=r(70428),l=r(44493),s=r(15079),f=r(96834),p=r(35950),h=r(78895),d=r(5551),y=r(8751),v=r(82614);let m=(0,v.A)("Percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]),b=(0,v.A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),g=(0,v.A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var x=r(15036),O=r(49384),w=r(45603),j=r.n(w),S=r(63866),P=r.n(S),A=r(77822),E=r.n(A),k=r(40491),M=r.n(k),_=r(93490),T=r.n(_),C=function(t){return 0===t?0:t>0?1:-1},D=function(t){return P()(t)&&t.indexOf("%")===t.length-1},N=function(t){return T()(t)&&!E()(t)},I=function(t){return N(t)||P()(t)},B=0,L=function(t){var e=++B;return"".concat(t||"").concat(e)},R=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!N(t)&&!P()(t))return n;if(D(t)){var i=t.indexOf("%");r=e*parseFloat(t.slice(0,i))/100}else r=+t;return E()(r)&&(r=n),o&&r>e&&(r=e),r},z=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},U=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1},q=function(t,e){return N(t)&&N(e)?function(r){return t+r*(e-t)}:function(){return e}};function F(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):M()(t,e))===r}):null}var $=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},W=r(37456),X=r.n(W),H=r(5231),V=r.n(H),G=r(55048),Y=r.n(G),K=r(29632);function Z(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}function J(t){return(J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Q=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],tt=["points","pathLength"],te={svg:["viewBox","children"],polygon:tt,polyline:tt},tr=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],tn=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,i.isValidElement)(t)&&(r=t.props),!Y()(r))return null;var n={};return Object.keys(r).forEach(function(t){tr.includes(t)&&(n[t]=e||function(e){return r[t](r,e)})}),n},to=function(t,e,r){if(!Y()(t)||"object"!==J(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];tr.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t){return i(e,r,t),null})}),n},ti=["children"],ta=["children"];function tc(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function tu(t){return(tu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tl={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},ts=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},tf=null,tp=null,th=function t(e){if(e===tf&&Array.isArray(tp))return tp;var r=[];return i.Children.forEach(e,function(e){X()(e)||((0,K.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),tp=r,tf=e,r};function td(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return ts(t)}):[ts(e)],th(t).forEach(function(t){var e=M()(t,"type.displayName")||M()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function ty(t,e){var r=td(t,e);return r&&r[0]}var tv=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!N(r)&&!(r<=0)&&!!N(n)&&!(n<=0)},tm=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tb=function(t,e,r,n){var o,i=null!==(o=null==te?void 0:te[n])&&void 0!==o?o:[];return!V()(t)&&(n&&i.includes(e)||Q.includes(e))||r&&tr.includes(e)},tg=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,i.isValidElement)(t)&&(n=t.props),!Y()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;tb(null===(i=n)||void 0===i?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},tx=function t(e,r){if(e===r)return!0;var n=i.Children.count(e);if(n!==i.Children.count(r))return!1;if(0===n)return!0;if(1===n)return tO(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var a=e[o],c=r[o];if(Array.isArray(a)||Array.isArray(c)){if(!t(a,c))return!1}else if(!tO(a,c))return!1}return!0},tO=function(t,e){if(X()(t)&&X()(e))return!0;if(!X()(t)&&!X()(e)){var r=t.props||{},n=r.children,o=tc(r,ti),i=e.props||{},a=i.children,c=tc(i,ta);if(n&&a)return Z(o,c)&&tx(n,a);if(!n&&!a)return Z(o,c)}return!1},tw=function(t,e){var r=[],n={};return th(t).forEach(function(t,o){var i;if((i=t)&&i.type&&P()(i.type)&&tm.indexOf(i.type)>=0)r.push(t);else if(t){var a=ts(t.type),c=e[a]||{},u=c.handler,l=c.once;if(u&&(!l||!n[a])){var s=u(t,a,o);r.push(s),n[a]=!0}}}),r},tj=function(t){var e=t&&t.type;return e&&tl[e]?tl[e]:null};function tS(t){return(tS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tP(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=tS(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tS(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tS(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tE(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tk=(0,i.forwardRef)(function(t,e){var r,n=t.aspect,o=t.initialDimension,c=void 0===o?{width:-1,height:-1}:o,u=t.width,l=void 0===u?"100%":u,s=t.height,f=void 0===s?"100%":s,p=t.minWidth,h=void 0===p?0:p,d=t.minHeight,y=t.maxHeight,v=t.children,m=t.debounce,b=void 0===m?0:m,g=t.id,x=t.className,w=t.onResize,S=t.style,P=(0,i.useRef)(null),A=(0,i.useRef)();A.current=w,(0,i.useImperativeHandle)(e,function(){return Object.defineProperty(P.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),P.current},configurable:!0})});var E=function(t){if(Array.isArray(t))return t}(r=(0,i.useState)({containerWidth:c.width,containerHeight:c.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return tE(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tE(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),k=E[0],M=E[1],_=(0,i.useCallback)(function(t,e){M(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,i.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;_(n,o),null===(e=A.current)||void 0===e||e.call(A,n,o)};b>0&&(t=j()(t,b,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=P.current.getBoundingClientRect();return _(r.width,r.height),e.observe(P.current),function(){e.disconnect()}},[_,b]);var T=(0,i.useMemo)(function(){var t=k.containerWidth,e=k.containerHeight;if(t<0||e<0)return null;$(D(l)||D(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",l,f),$(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=D(l)?t:l,o=D(f)?e:f;n&&n>0&&(r?o=r/n:o&&(r=o*n),y&&o>y&&(o=y)),$(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,l,f,h,d,n);var c=!Array.isArray(v)&&ts(v.type).endsWith("Chart");return a().Children.map(v,function(t){return a().isValidElement(t)?(0,i.cloneElement)(t,tA({width:r,height:o},c?{style:tA({height:"100%",width:"100%",maxHeight:o,maxWidth:r},t.props.style)}:{})):t})},[n,v,f,y,d,h,k,l]);return a().createElement("div",{id:g?"".concat(g):void 0,className:(0,O.A)("recharts-responsive-container",x),style:tA(tA({},void 0===S?{}:S),{},{width:l,height:f,minWidth:h,minHeight:d,maxHeight:y}),ref:P},T)}),tM=r(34990),t_=r.n(tM),tT=r(85938),tC=r.n(tT);function tD(t,e){if(!t)throw Error("Invariant failed")}var tN=["children","width","height","viewBox","className","style","title","desc"];function tI(){return(tI=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tB(t){var e=t.children,r=t.width,n=t.height,o=t.viewBox,i=t.className,c=t.style,u=t.title,l=t.desc,s=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tN),f=o||{width:r,height:n,x:0,y:0},p=(0,O.A)("recharts-surface",i);return a().createElement("svg",tI({},tg(s,!0,"svg"),{className:p,width:r,height:n,style:c,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),a().createElement("title",null,u),a().createElement("desc",null,l),e)}var tL=["children","className"];function tR(){return(tR=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tz=a().forwardRef(function(t,e){var r=t.children,n=t.className,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tL),i=(0,O.A)("recharts-layer",n);return a().createElement("g",tR({className:i},tg(o,!0),{ref:e}),r)});function tU(t){return(tU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tq(){return(tq=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tF(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function t$(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t$(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=tU(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tU(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t$(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tX(t){return Array.isArray(t)&&I(t[0])&&I(t[1])?t.join(" ~ "):t}var tH=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,o=t.itemStyle,i=void 0===o?{}:o,c=t.labelStyle,u=t.payload,l=t.formatter,s=t.itemSorter,f=t.wrapperClassName,p=t.labelClassName,h=t.label,d=t.labelFormatter,y=t.accessibilityLayer,v=tW({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),m=tW({margin:0},void 0===c?{}:c),b=!X()(h),g=b?h:"",x=(0,O.A)("recharts-default-tooltip",f),w=(0,O.A)("recharts-tooltip-label",p);return b&&d&&null!=u&&(g=d(h,u)),a().createElement("div",tq({className:x,style:v},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),a().createElement("p",{className:w,style:m},a().isValidElement(g)?g:"".concat(g)),function(){if(u&&u.length){var t=(s?tC()(u,s):u).map(function(t,e){if("none"===t.type)return null;var n=tW({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},i),o=t.formatter||l||tX,c=t.value,s=t.name,f=c,p=s;if(o&&null!=f&&null!=p){var h=o(c,s,t,e,u);if(Array.isArray(h)){var d=function(t){if(Array.isArray(t))return t}(h)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(h,2)||function(t,e){if(t){if("string"==typeof t)return tF(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tF(t,e)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=d[0],p=d[1]}else f=h}return a().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},I(p)?a().createElement("span",{className:"recharts-tooltip-item-name"},p):null,I(p)?a().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,a().createElement("span",{className:"recharts-tooltip-item-value"},f),a().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return a().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function tV(t){return(tV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tG(t,e,r){var n;return(n=function(t,e){if("object"!=tV(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==tV(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var tY="recharts-tooltip-wrapper",tK={visibility:"hidden"};function tZ(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(i&&N(i[n]))return i[n];var s=r[n]-c-o,f=r[n]+o;return e[n]?a[n]?s:f:a[n]?s<u[n]?Math.max(f,u[n]):Math.max(s,u[n]):f+c>u[n]+l?Math.max(s,u[n]):Math.max(f,u[n])}function tJ(t){return(tJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t0(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tQ(Object(r),!0).forEach(function(e){t3(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function t1(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(t1=function(){return!!t})()}function t2(t){return(t2=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function t5(t,e){return(t5=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function t3(t,e,r){return(e=t4(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function t4(t){var e=function(t,e){if("object"!=tJ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tJ(e)?e:e+""}var t6=function(t){var e;function r(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r);for(var t,e,n,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r,n=[].concat(i),e=t2(e),t3(t=function(t,e){if(e&&("object"===tJ(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,t1()?Reflect.construct(e,n||[],t2(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),t3(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&t5(t,e)}(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,o,i,c,u,l,s,f,p,h,d,y,v,m,b,g,x=this,w=this.props,j=w.active,S=w.allowEscapeViewBox,P=w.animationDuration,A=w.animationEasing,E=w.children,k=w.coordinate,M=w.hasPayload,_=w.isAnimationActive,T=w.offset,C=w.position,D=w.reverseDirection,I=w.useTranslate3d,B=w.viewBox,L=w.wrapperStyle,R=(p=(t={allowEscapeViewBox:S,coordinate:k,offsetTopLeft:T,position:C,reverseDirection:D,tooltipBox:this.state.lastBoundingBox,useTranslate3d:I,viewBox:B}).allowEscapeViewBox,h=t.coordinate,d=t.offsetTopLeft,y=t.position,v=t.reverseDirection,m=t.tooltipBox,b=t.useTranslate3d,g=t.viewBox,m.height>0&&m.width>0&&h?(r=(e={translateX:s=tZ({allowEscapeViewBox:p,coordinate:h,key:"x",offsetTopLeft:d,position:y,reverseDirection:v,tooltipDimension:m.width,viewBox:g,viewBoxDimension:g.width}),translateY:f=tZ({allowEscapeViewBox:p,coordinate:h,key:"y",offsetTopLeft:d,position:y,reverseDirection:v,tooltipDimension:m.height,viewBox:g,viewBoxDimension:g.height}),useTranslate3d:b}).translateX,n=e.translateY,l={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):l=tK,{cssProperties:l,cssClasses:(i=(o={translateX:s,translateY:f,coordinate:h}).coordinate,c=o.translateX,u=o.translateY,(0,O.A)(tY,tG(tG(tG(tG({},"".concat(tY,"-right"),N(c)&&i&&N(i.x)&&c>=i.x),"".concat(tY,"-left"),N(c)&&i&&N(i.x)&&c<i.x),"".concat(tY,"-bottom"),N(u)&&i&&N(i.y)&&u>=i.y),"".concat(tY,"-top"),N(u)&&i&&N(i.y)&&u<i.y)))}),z=R.cssClasses,U=R.cssProperties,q=t0(t0({transition:_&&j?"transform ".concat(P,"ms ").concat(A):void 0},U),{},{pointerEvents:"none",visibility:!this.state.dismissed&&j&&M?"visible":"hidden",position:"absolute",top:0,left:0},L);return a().createElement("div",{tabIndex:-1,className:z,style:q,ref:function(t){x.wrapperNode=t}},E)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,t4(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent),t8={isSsr:!0,get:function(t){return t8[t]},set:function(t,e){if("string"==typeof t)t8[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){t8[e]=t[e]})}}},t7=r(36315),t9=r.n(t7);function et(t,e,r){return!0===e?t9()(t,r):V()(e)?t9()(t,e):t}function ee(t){return(ee="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function er(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function en(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?er(Object(r),!0).forEach(function(e){ec(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):er(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function eo(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eo=function(){return!!t})()}function ei(t){return(ei=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ea(t,e){return(ea=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ec(t,e,r){return(e=eu(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eu(t){var e=function(t,e){if("object"!=ee(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ee(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ee(e)?e:e+""}function el(t){return t.dataKey}var es=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=ei(t),function(t,e){if(e&&("object"===ee(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,eo()?Reflect.construct(t,e||[],ei(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ea(t,e)}(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,c=r.animationEasing,u=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=h?h:[];s&&x.length&&(x=et(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,el));var O=x.length>0;return a().createElement(t6,{allowEscapeViewBox:o,animationDuration:i,animationEasing:c,isAnimationActive:f,active:n,coordinate:l,hasPayload:O,offset:p,position:y,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(t=en(en({},this.props),{},{payload:x}),a().isValidElement(u)?a().cloneElement(u,t):"function"==typeof u?a().createElement(u,t):a().createElement(tH,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eu(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);ec(es,"displayName","Tooltip"),ec(es,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!t8.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var ef=r(69433),ep=r.n(ef);let eh=Math.cos,ed=Math.sin,ey=Math.sqrt,ev=Math.PI,em=2*ev,eb={draw(t,e){let r=ey(e/ev);t.moveTo(r,0),t.arc(0,0,r,0,em)}},eg=ey(1/3),ex=2*eg,eO=ed(ev/10)/ed(7*ev/10),ew=ed(em/10)*eO,ej=-eh(em/10)*eO,eS=ey(3),eP=ey(3)/2,eA=1/ey(12),eE=(eA/2+1)*3;function ek(t){return function(){return t}}let eM=Math.PI,e_=2*eM,eT=e_-1e-6;function eC(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class eD{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?eC:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return eC;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,n,o){if(t*=1,e*=1,r*=1,n*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,c=r-t,u=n-e,l=i-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>1e-6){if(Math.abs(s*c-u*l)>1e-6&&o){let p=r-i,h=n-a,d=c*c+u*u,y=Math.sqrt(d),v=Math.sqrt(f),m=o*Math.tan((eM-Math.acos((d+f-(p*p+h*h))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${t+b*l},${e+b*s}`,this._append`A${o},${o},0,0,${+(s*p>l*h)},${this._x1=t+g*c},${this._y1=e+g*u}`}else this._append`L${this._x1=t},${this._y1=e}`}}arc(t,e,r,n,o,i){if(t*=1,e*=1,r*=1,i=!!i,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${u},${l}`:(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-l)>1e-6)&&this._append`L${u},${l}`,r&&(f<0&&(f=f%e_+e_),f>eT?this._append`A${r},${r},0,1,${s},${t-a},${e-c}A${r},${r},0,1,${s},${this._x1=u},${this._y1=l}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=eM)},${s},${this._x1=t+r*Math.cos(o)},${this._y1=e+r*Math.sin(o)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function eN(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new eD(e)}function eI(t){return(eI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eD.prototype,ey(3),ey(3);var eB=["type","size","sizeType"];function eL(){return(eL=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eR(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ez(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eR(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=eI(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eI(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eR(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var eU={symbolCircle:eb,symbolCross:{draw(t,e){let r=ey(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=ey(e/ex),n=r*eg;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=ey(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=ey(.8908130915292852*e),n=ew*r,o=ej*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){let i=em*e/5,a=eh(i),c=ed(i);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*o,c*n+a*o)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-ey(e/(3*eS));t.moveTo(0,2*r),t.lineTo(-eS*r,-r),t.lineTo(eS*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=ey(e/eE),n=r/2,o=r*eA,i=r*eA+r,a=-n;t.moveTo(n,o),t.lineTo(n,i),t.lineTo(a,i),t.lineTo(-.5*n-eP*o,eP*n+-.5*o),t.lineTo(-.5*n-eP*i,eP*n+-.5*i),t.lineTo(-.5*a-eP*i,eP*a+-.5*i),t.lineTo(-.5*n+eP*o,-.5*o-eP*n),t.lineTo(-.5*n+eP*i,-.5*i-eP*n),t.lineTo(-.5*a+eP*i,-.5*i-eP*a),t.closePath()}}},eq=Math.PI/180,eF=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*eq;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},e$=function(t){var e,r=t.type,n=void 0===r?"circle":r,o=t.size,i=void 0===o?64:o,c=t.sizeType,u=void 0===c?"area":c,l=ez(ez({},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,eB)),{},{type:n,size:i,sizeType:u}),s=l.className,f=l.cx,p=l.cy,h=tg(l,!0);return f===+f&&p===+p&&i===+i?a().createElement("path",eL({},h,{className:(0,O.A)("recharts-symbols",s),transform:"translate(".concat(f,", ").concat(p,")"),d:(e=eU["symbol".concat(ep()(n))]||eb,(function(t,e){let r=null,n=eN(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:ek(t||eb),e="function"==typeof e?e:ek(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:ek(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:ek(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o})().type(e).size(eF(i,u,n))())})):null};function eW(t){return(eW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eX(){return(eX=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}e$.registerSymbol=function(t,e){eU["symbol".concat(ep()(t))]=e};function eV(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eV=function(){return!!t})()}function eG(t){return(eG=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eY(t,e){return(eY=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eK(t,e,r){return(e=eZ(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eZ(t){var e=function(t,e){if("object"!=eW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eW(e)?e:e+""}var eJ=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=eG(t),function(t,e){if(e&&("object"===eW(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,eV()?Reflect.construct(t,e||[],eG(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&eY(t,e)}(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,o=t.inactive?e:t.color;if("plainline"===t.type)return a().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return a().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return a().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a().isValidElement(t.legendIcon)){var i=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eH(Object(r),!0).forEach(function(e){eK(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete i.legendIcon,a().cloneElement(t.legendIcon,i)}return a().createElement(e$,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,o=e.layout,i=e.formatter,c=e.inactiveColor,u={x:0,y:0,width:32,height:32},l={display:"horizontal"===o?"inline-block":"block",marginRight:10},s={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var o=e.formatter||i,f=(0,O.A)(eK(eK({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var p=V()(e.value)?null:e.value;$(!V()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var h=e.inactive?c:e.color;return a().createElement("li",eX({className:f,style:l,key:"legend-item-".concat(r)},to(t.props,e,r)),a().createElement(tB,{width:n,height:n,viewBox:u,style:s},t.renderIcon(e)),a().createElement("span",{className:"recharts-legend-item-text",style:{color:h}},o?o(p,e,r):p))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?a().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eZ(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);function eQ(t){return(eQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eK(eJ,"displayName","Legend"),eK(eJ,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var e0=["ref"];function e1(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function e2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?e1(Object(r),!0).forEach(function(e){e8(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e1(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function e5(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,e7(n.key),n)}}function e3(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(e3=function(){return!!t})()}function e4(t){return(e4=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function e6(t,e){return(e6=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function e8(t,e,r){return(e=e7(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function e7(t){var e=function(t,e){if("object"!=eQ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eQ(e)?e:e+""}function e9(t){return t.value}var rt=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=e4(e),e8(t=function(t,e){if(e&&("object"===eQ(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,e3()?Reflect.construct(e,r||[],e4(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&e6(t,e)}(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?e2({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),e2(e2({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,o=e.height,i=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,l=e2(e2({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return a().createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(a().isValidElement(t))return a().cloneElement(t,e);if("function"==typeof t)return a().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,e0);return a().createElement(eJ,r)}(r,e2(e2({},this.props),{},{payload:et(u,c,e9)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=e2(e2({},this.defaultProps),t.props).layout;return"vertical"===r&&N(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&e5(n.prototype,e),r&&e5(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function re(){return(re=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}e8(rt,"displayName","Legend"),e8(rt,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var rr=function(t){var e=t.cx,r=t.cy,n=t.r,o=t.className,i=(0,O.A)("recharts-dot",o);return e===+e&&r===+r&&n===+n?a().createElement("circle",re({},tg(t,!1),tn(t),{className:i,cx:e,cy:r,r:n})):null},rn=r(87955),ro=r.n(rn),ri=Object.getOwnPropertyNames,ra=Object.getOwnPropertySymbols,rc=Object.prototype.hasOwnProperty;function ru(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function rl(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var c=t(e,r,n);return o.delete(e),o.delete(r),c}}function rs(t){return ri(t).concat(ra(t))}var rf=Object.hasOwn||function(t,e){return rc.call(t,e)};function rp(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var rh=Object.getOwnPropertyDescriptor,rd=Object.keys;function ry(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function rv(t,e){return rp(t.getTime(),e.getTime())}function rm(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function rb(t,e){return t===e}function rg(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,h=o.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}function rx(t,e,r){var n=rd(t),o=n.length;if(rd(e).length!==o)return!1;for(;o-- >0;)if(!rE(t,e,r,n[o]))return!1;return!0}function rO(t,e,r){var n,o,i,a=rs(t),c=a.length;if(rs(e).length!==c)return!1;for(;c-- >0;)if(!rE(t,e,r,n=a[c])||(o=rh(t,n),i=rh(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function rw(t,e){return rp(t.valueOf(),e.valueOf())}function rj(t,e){return t.source===e.source&&t.flags===e.flags}function rS(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),l=!1,s=0;(o=u.next())&&!o.done;){if(!a[s]&&r.equals(n.value,o.value,n.value,o.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function rP(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function rA(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function rE(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||rf(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var rk=Array.isArray,rM="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,r_=Object.assign,rT=Object.prototype.toString.call.bind(Object.prototype.toString),rC=rD();function rD(t){void 0===t&&(t={});var e,r,n,o,i,a,c,u,l,s,f,p,h,d=t.circular,y=t.createInternalComparator,v=t.createState,m=t.strict,b=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?rO:ry,areDatesEqual:rv,areErrorsEqual:rm,areFunctionsEqual:rb,areMapsEqual:n?ru(rg,rO):rg,areNumbersEqual:rp,areObjectsEqual:n?rO:rx,arePrimitiveWrappersEqual:rw,areRegExpsEqual:rj,areSetsEqual:n?ru(rS,rO):rS,areTypedArraysEqual:n?rO:rP,areUrlsEqual:rA};if(r&&(o=r_({},o,r(o))),e){var i=rl(o.areArraysEqual),a=rl(o.areMapsEqual),c=rl(o.areObjectsEqual),u=rl(o.areSetsEqual);o=r_({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return o}(t)).areArraysEqual,n=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,l=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,h=e.areUrlsEqual,function(t,e,d){if(t===e)return!0;if(null==t||null==e)return!1;var y=typeof t;if(y!==typeof e)return!1;if("object"!==y)return"number"===y?c(t,e,d):"function"===y&&i(t,e,d);var v=t.constructor;if(v!==e.constructor)return!1;if(v===Object)return u(t,e,d);if(rk(t))return r(t,e,d);if(null!=rM&&rM(t))return p(t,e,d);if(v===Date)return n(t,e,d);if(v===RegExp)return s(t,e,d);if(v===Map)return a(t,e,d);if(v===Set)return f(t,e,d);var m=rT(t);return"[object Date]"===m?n(t,e,d):"[object RegExp]"===m?s(t,e,d):"[object Map]"===m?a(t,e,d):"[object Set]"===m?f(t,e,d):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,d):"[object URL]"===m?h(t,e,d):"[object Error]"===m?o(t,e,d):"[object Arguments]"===m?u(t,e,d):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&l(t,e,d)}),g=y?y(b):function(t,e,r,n,o,i,a){return b(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:o,meta:c.meta,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:void 0!==d&&d,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function rN(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>e)t(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function rI(t){return(rI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rB(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function rL(t){return(rL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rR(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rz(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rR(Object(r),!0).forEach(function(e){rU(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rR(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rU(t,e,r){var n;return(n=function(t,e){if("object"!==rL(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rL(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}rD({strict:!0}),rD({circular:!0}),rD({circular:!0,strict:!0}),rD({createInternalComparator:function(){return rp}}),rD({strict:!0,createInternalComparator:function(){return rp}}),rD({circular:!0,createInternalComparator:function(){return rp}}),rD({circular:!0,createInternalComparator:function(){return rp},strict:!0});var rq=function(t){return t},rF=function(t,e){return Object.keys(e).reduce(function(r,n){return rz(rz({},r),{},rU({},n,t(n,e[n])))},{})},r$=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},rW=function(t,e,r,n,o,i,a,c){};function rX(t,e){if(t){if("string"==typeof t)return rH(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rH(t,e)}}function rH(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rV=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},rG=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},rY=function(t,e){return function(r){return rG(rV(t,e),r)}},rK=function(){for(var t,e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,c=1,u=1;break;case"ease":i=.25,a=.1,c=.25,u=1;break;case"ease-in":i=.42,a=0,c=1,u=1;break;case"ease-out":i=.42,a=0,c=.58,u=1;break;case"ease-in-out":i=0,a=0,c=.58,u=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(s,4)||rX(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],c=f[2],u=f[3]}else rW(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}rW([i,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=rY(i,c),h=rY(a,u),d=(t=i,e=c,function(r){var n;return rG([].concat(function(t){if(Array.isArray(t))return rH(t)}(n=rV(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||rX(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o,i=p(r)-e,a=d(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return h(r)};return y.isStepper=!1,y},rZ=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,c=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(i)?[e,0]:[c,i]};return c.isStepper=!0,c.dt=a,c},rJ=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return rK(n);case"spring":return rZ();default:if("cubic-bezier"===n.split("(")[0])return rK(n);rW(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(rW(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function rQ(t){return(rQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r0(t){return function(t){if(Array.isArray(t))return r4(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||r3(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r1(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?r1(Object(r),!0).forEach(function(e){r5(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):r1(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function r5(t,e,r){var n;return(n=function(t,e){if("object"!==rQ(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rQ(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function r3(t,e){if(t){if("string"==typeof t)return r4(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r4(t,e)}}function r4(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var r6=function(t,e,r){return t+(e-t)*r},r8=function(t){return t.from!==t.to},r7=function t(e,r,n){var o=rF(function(t,r){if(r8(r)){var n,o=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(n,2)||r3(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return r2(r2({},r),{},{from:i,velocity:a})}return r},r);return n<1?rF(function(t,e){return r8(e)?r2(r2({},e),{},{velocity:r6(e.velocity,o[t].velocity,n),from:r6(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};let r9=function(t,e,r,n,o){var i,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return r2(r2({},r),{},r5({},n,[t[n],e[n]]))},{}),l=c.reduce(function(r,n){return r2(r2({},r),{},r5({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;l=r7(r,l,a),o(r2(r2(r2({},t),e),rF(function(t,e){return e.from},l))),i=n,Object.values(l).filter(r8).length&&(s=requestAnimationFrame(f))}:function(i){a||(a=i);var c=(i-a)/n,l=rF(function(t,e){return r6.apply(void 0,r0(e).concat([r(c)]))},u);if(o(r2(r2(r2({},t),e),l)),c<1)s=requestAnimationFrame(f);else{var p=rF(function(t,e){return r6.apply(void 0,r0(e).concat([r(1)]))},u);o(r2(r2(r2({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function nt(t){return(nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var ne=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function nr(t){return function(t){if(Array.isArray(t))return nn(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return nn(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nn(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function no(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ni(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?no(Object(r),!0).forEach(function(e){na(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):no(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function na(t,e,r){return(e=nc(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nc(t){var e=function(t,e){if("object"!==nt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==nt(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===nt(e)?e:String(e)}function nu(t,e){return(nu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function nl(t,e){if(e&&("object"===nt(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return ns(t)}function ns(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function nf(t){return(nf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var np=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&nu(t,e)}(o,t);var e,r,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=nf(o);return t=e?Reflect.construct(r,arguments,nf(this).constructor):r.apply(this,arguments),nl(this,t)});function o(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o);var r=n.call(this,t,e),i=r.props,a=i.isActive,c=i.attributeName,u=i.from,l=i.to,s=i.steps,f=i.children,p=i.duration;if(r.handleStyleChange=r.handleStyleChange.bind(ns(r)),r.changeStyle=r.changeStyle.bind(ns(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),nl(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},nl(r);r.state={style:c?na({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var l={style:o?na({},o,a):a};this.state&&u&&(o&&u[o]!==a||!o&&u!==a)&&this.setState(l);return}if(!rC(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?c:t.to;if(this.state&&u){var p={style:o?na({},o,f):f};(o&&u[o]!==f||!o&&u!==f)&&this.setState(p)}this.runAnimation(ni(ni({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=r9(r,n,rJ(i),o,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},o,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,c=i.duration;return this.manager.start([o].concat(nr(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(nr(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:i,easing:c}),i]);var h=r$(p,i,c),d=ni(ni(ni({},f.style),u),{},{transition:h});return[].concat(nr(t),[d,i,s]).filter(rq)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){if(!this.manager){var e,r,n,o;this.manager=(r=function(){return null},n=!1,o=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var o=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return rB(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rB(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){rN(t.bind(null,a),i);return}t(i),rN(t.bind(null,a));return}"object"===rI(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,o(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}})}var i=t.begin,a=t.duration,c=t.attributeName,u=t.to,l=t.easing,s=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,h=t.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof h||"spring"===l){this.runJSAnimation(t);return}if(p.length>1){this.runStepAnimation(t);return}var y=c?na({},c,u):u,v=r$(Object.keys(y),a,l);d.start([s,i,ni(ni({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),n=(t.attributeName,t.easing,t.isActive),o=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,ne)),c=i.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!n||0===c||r<=0)return e;var l=function(t){var e=t.props,r=e.style,n=e.className;return(0,i.cloneElement)(t,ni(ni({},o),{},{style:ni(ni({},void 0===r?{}:r),u),className:n}))};return 1===c?l(i.Children.only(e)):a().createElement("div",null,i.Children.map(e,function(t){return l(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,nc(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(i.PureComponent);function nh(t){return(nh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nd(){return(nd=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ny(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nm(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nv(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=nh(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nh(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nh(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nv(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}np.displayName="Animate",np.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},np.propTypes={from:ro().oneOfType([ro().object,ro().string]),to:ro().oneOfType([ro().object,ro().string]),attributeName:ro().string,duration:ro().number,begin:ro().number,easing:ro().oneOfType([ro().string,ro().func]),steps:ro().arrayOf(ro().shape({duration:ro().number.isRequired,style:ro().object.isRequired,easing:ro().oneOfType([ro().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),ro().func]),properties:ro().arrayOf("string"),onAnimationEnd:ro().func})),children:ro().oneOfType([ro().node,ro().func]),isActive:ro().bool,canBegin:ro().bool,onAnimationEnd:ro().func,shouldReAnimate:ro().bool,onAnimationStart:ro().func,onAnimationReStart:ro().func};var nb=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),i+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),i+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),i+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},ng=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+c),f=Math.max(i,i+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},nx={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},nO=function(t){var e,r=nm(nm({},nx),t),n=(0,i.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,i.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return ny(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ny(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=o[0],u=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.width,p=r.height,h=r.radius,d=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isAnimationActive,g=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||0===f||0===p)return null;var x=(0,O.A)("recharts-rectangle",d);return g?a().createElement(np,{canBegin:c>0,from:{width:f,height:p,x:l,y:s},to:{width:f,height:p,x:l,y:s},duration:v,animationEasing:y,isActive:g},function(t){var e=t.width,o=t.height,i=t.x,u=t.y;return a().createElement(np,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,isActive:b,easing:y},a().createElement("path",nd({},tg(r,!0),{className:x,d:nb(i,u,e,o,h),ref:n})))}):a().createElement("path",nd({},tg(r,!0),{className:x,d:nb(l,s,f,p,h)}))};function nw(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function nj(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class nS extends Map{constructor(t,e=nA){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(nP(this,t))}has(t){return super.has(nP(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function nP({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function nA(t){return null!==t&&"object"==typeof t?t.valueOf():t}let nE=Symbol("implicit");function nk(){var t=new nS,e=[],r=[],n=nE;function o(o){let i=t.get(o);if(void 0===i){if(n!==nE)return n;t.set(o,i=e.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new nS,r))t.has(n)||t.set(n,e.push(n)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return nk(e,r).unknown(n)},nw.apply(o,arguments),o}function nM(){var t,e,r=nk().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,c=!1,u=0,l=0,s=.5;function f(){var r=n().length,f=a<i,p=f?a:i,h=f?i:a;t=(h-p)/Math.max(1,r-u+2*l),c&&(t=Math.floor(t)),p+=(h-p-t*(r-u))*s,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var d=(function(t,e,r){t*=1,e*=1,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=Array(o);++n<o;)i[n]=t+n*r;return i})(r).map(function(e){return p+t*e});return o(f?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([i,a]=t,i*=1,a*=1,f()):[i,a]},r.rangeRound=function(t){return[i,a]=t,i*=1,a*=1,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,l=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return nM(n(),[i,a]).round(c).paddingInner(u).paddingOuter(l).align(s)},nw.apply(f(),arguments)}function n_(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(nM.apply(null,arguments).paddingInner(1))}function nT(t){return(nT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nC(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nD(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nC(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=nT(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nT(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nT(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nC(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nN(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nI={widthCache:{},cacheCount:0},nB={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nL="recharts_measurement_span",nR=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||t8.isSsr)return{width:0,height:0};var n=(Object.keys(e=nD({},r)).forEach(function(t){e[t]||delete e[t]}),e),o=JSON.stringify({text:t,copyStyle:n});if(nI.widthCache[o])return nI.widthCache[o];try{var i=document.getElementById(nL);i||((i=document.createElement("span")).setAttribute("id",nL),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=nD(nD({},nB),n);Object.assign(i.style,a),i.textContent="".concat(t);var c=i.getBoundingClientRect(),u={width:c.width,height:c.height};return nI.widthCache[o]=u,++nI.cacheCount>2e3&&(nI.cacheCount=0,nI.widthCache={}),u}catch(t){return{width:0,height:0}}};function nz(t){return(nz="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nU(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nq(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nq(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nq(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nF(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=nz(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nz(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nz(e)?e:e+""}(n.key),n)}}var n$=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nW=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nX=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nH=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nV={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},nG=Object.keys(nV),nY=function(){var t,e;function r(t,e){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,r),this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||nX.test(e)||(this.num=NaN,this.unit=""),nG.includes(e)&&(this.num=t*nV[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=nU(null!==(e=nH.exec(t))&&void 0!==e?e:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],t&&nF(r.prototype,t),e&&nF(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function nK(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=nU(null!==(r=n$.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],c=nY.parse(null!=o?o:""),u=nY.parse(null!=a?a:""),l="*"===i?c.multiply(u):c.divide(u);if(l.isNaN())return"NaN";e=e.replace(n$,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=nU(null!==(s=nW.exec(e))&&void 0!==s?s:[],4),p=f[1],h=f[2],d=f[3],y=nY.parse(null!=p?p:""),v=nY.parse(null!=d?d:""),m="+"===h?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";e=e.replace(nW,m.toString())}return e}var nZ=/\(([^()]*)\)/;function nJ(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=nU(nZ.exec(e),2)[1];e=e.replace(nZ,nK(r))}return e}(e),e=nK(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var nQ=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],n0=["dx","dy","angle","className","breakAll"];function n1(){return(n1=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function n2(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function n5(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return n3(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n3(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n3(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var n4=/[ \f\n\r\t\v\u2028\u2029]+/,n6=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];X()(e)||(o=r?e.toString().split(""):e.toString().split(n4));var i=o.map(function(t){return{word:t,width:nR(t,n).width}}),a=r?0:nR("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(t){return null}},n8=function(t,e,r,n,o){var i,a=t.maxLines,c=t.children,u=t.style,l=t.breakAll,s=N(a),f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||o||c.width+a+r<Number(n))?(c.words.push(i),c.width+=a+r):t.push({words:[i],width:a}),t},[])},p=f(e);if(!s)return p;for(var h=function(t){var e=f(n6({breakAll:l,style:u,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},d=0,y=c.length-1,v=0;d<=y&&v<=c.length-1;){var m=Math.floor((d+y)/2),b=n5(h(m-1),2),g=b[0],x=b[1],O=n5(h(m),1)[0];if(g||O||(d=m+1),g&&O&&(y=m-1),!g&&O){i=x;break}v++}return i||p},n7=function(t){return[{words:X()(t)?[]:t.toString().split(n4)}]},n9=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!t8.isSsr){var c=n6({breakAll:i,children:n,style:o});if(!c)return n7(n);var u=c.wordsWithComputedWidth,l=c.spaceWidth;return n8({breakAll:i,children:n,maxLines:a,style:o},u,l,e,r)}return n7(n)},ot="#808080",oe=function(t){var e,r=t.x,n=void 0===r?0:r,o=t.y,c=void 0===o?0:o,u=t.lineHeight,l=void 0===u?"1em":u,s=t.capHeight,f=void 0===s?"0.71em":s,p=t.scaleToFit,h=void 0!==p&&p,d=t.textAnchor,y=t.verticalAnchor,v=t.fill,m=void 0===v?ot:v,b=n2(t,nQ),g=(0,i.useMemo)(function(){return n9({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:h,style:b.style,width:b.width})},[b.breakAll,b.children,b.maxLines,h,b.style,b.width]),x=b.dx,w=b.dy,j=b.angle,S=b.className,P=b.breakAll,A=n2(b,n0);if(!I(n)||!I(c))return null;var E=n+(N(x)?x:0),k=c+(N(w)?w:0);switch(void 0===y?"end":y){case"start":e=nJ("calc(".concat(f,")"));break;case"middle":e=nJ("calc(".concat((g.length-1)/2," * -").concat(l," + (").concat(f," / 2))"));break;default:e=nJ("calc(".concat(g.length-1," * -").concat(l,")"))}var M=[];if(h){var _=g[0].width,T=b.width;M.push("scale(".concat((N(T)?T/_:1)/_,")"))}return j&&M.push("rotate(".concat(j,", ").concat(E,", ").concat(k,")")),M.length&&(A.transform=M.join(" ")),a().createElement("text",n1({},tg(A,!0),{x:E,y:k,className:(0,O.A)("recharts-text",S),textAnchor:void 0===d?"start":d,fill:m.includes("url")?ot:m}),g.map(function(t,r){var n=t.words.join(P?"":" ");return a().createElement("tspan",{x:E,dy:0===r?e:l,key:"".concat(n,"-").concat(r)},n)}))};let or=Math.sqrt(50),on=Math.sqrt(10),oo=Math.sqrt(2);function oi(t,e,r){let n,o,i;let a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),u=a/Math.pow(10,c),l=u>=or?10:u>=on?5:u>=oo?2:1;return(c<0?(n=Math.round(t*(i=Math.pow(10,-c)/l)),o=Math.round(e*i),n/i<t&&++n,o/i>e&&--o,i=-i):(n=Math.round(t/(i=Math.pow(10,c)*l)),o=Math.round(e/i),n*i<t&&++n,o*i>e&&--o),o<n&&.5<=r&&r<2)?oi(t,e,2*r):[n,o,i]}function oa(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[o,i,a]=n?oi(e,t,r):oi(t,e,r);if(!(i>=o))return[];let c=i-o+1,u=Array(c);if(n){if(a<0)for(let t=0;t<c;++t)u[t]=-((i-t)/a);else for(let t=0;t<c;++t)u[t]=(i-t)*a}else if(a<0)for(let t=0;t<c;++t)u[t]=-((o+t)/a);else for(let t=0;t<c;++t)u[t]=(o+t)*a;return u}function oc(t,e,r){return oi(t*=1,e*=1,r*=1)[2]}function ou(t,e,r){e*=1,t*=1,r*=1;let n=e<t,o=n?oc(e,t,r):oc(t,e,r);return(n?-1:1)*(o<0?-(1/o):o)}function ol(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function os(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function of(t){let e,r,n;function o(t,n,i=0,a=t.length){if(i<a){if(0!==e(n,n))return a;do{let e=i+a>>>1;0>r(t[e],n)?i=e+1:a=e}while(i<a)}return i}return 2!==t.length?(e=ol,r=(e,r)=>ol(t(e),r),n=(e,r)=>t(e)-r):(e=t===ol||t===os?t:op,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){let a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>=r(t[e],n)?o=e+1:i=e}while(o<i)}return o}}}function op(){return 0}function oh(t){return null===t?NaN:+t}let od=of(ol),oy=od.right;function ov(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function om(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function ob(){}od.left,of(oh).center;var og="\\s*([+-]?\\d+)\\s*",ox="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",oO="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",ow=/^#([0-9a-f]{3,8})$/,oj=RegExp(`^rgb\\(${og},${og},${og}\\)$`),oS=RegExp(`^rgb\\(${oO},${oO},${oO}\\)$`),oP=RegExp(`^rgba\\(${og},${og},${og},${ox}\\)$`),oA=RegExp(`^rgba\\(${oO},${oO},${oO},${ox}\\)$`),oE=RegExp(`^hsl\\(${ox},${oO},${oO}\\)$`),ok=RegExp(`^hsla\\(${ox},${oO},${oO},${ox}\\)$`),oM={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function o_(){return this.rgb().formatHex()}function oT(){return this.rgb().formatRgb()}function oC(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=ow.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?oD(e):3===r?new oB(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?oN(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?oN(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=oj.exec(t))?new oB(e[1],e[2],e[3],1):(e=oS.exec(t))?new oB(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=oP.exec(t))?oN(e[1],e[2],e[3],e[4]):(e=oA.exec(t))?oN(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=oE.exec(t))?oF(e[1],e[2]/100,e[3]/100,1):(e=ok.exec(t))?oF(e[1],e[2]/100,e[3]/100,e[4]):oM.hasOwnProperty(t)?oD(oM[t]):"transparent"===t?new oB(NaN,NaN,NaN,0):null}function oD(t){return new oB(t>>16&255,t>>8&255,255&t,1)}function oN(t,e,r,n){return n<=0&&(t=e=r=NaN),new oB(t,e,r,n)}function oI(t,e,r,n){var o;return 1==arguments.length?((o=t)instanceof ob||(o=oC(o)),o)?new oB((o=o.rgb()).r,o.g,o.b,o.opacity):new oB:new oB(t,e,r,null==n?1:n)}function oB(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function oL(){return`#${oq(this.r)}${oq(this.g)}${oq(this.b)}`}function oR(){let t=oz(this.opacity);return`${1===t?"rgb(":"rgba("}${oU(this.r)}, ${oU(this.g)}, ${oU(this.b)}${1===t?")":`, ${t})`}`}function oz(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function oU(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function oq(t){return((t=oU(t))<16?"0":"")+t.toString(16)}function oF(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new oW(t,e,r,n)}function o$(t){if(t instanceof oW)return new oW(t.h,t.s,t.l,t.opacity);if(t instanceof ob||(t=oC(t)),!t)return new oW;if(t instanceof oW)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,c=i-o,u=(i+o)/2;return c?(a=e===i?(r-n)/c+(r<n)*6:r===i?(n-e)/c+2:(e-r)/c+4,c/=u<.5?i+o:2-i-o,a*=60):c=u>0&&u<1?0:a,new oW(a,c,u,t.opacity)}function oW(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function oX(t){return(t=(t||0)%360)<0?t+360:t}function oH(t){return Math.max(0,Math.min(1,t||0))}function oV(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function oG(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}ov(ob,oC,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:o_,formatHex:o_,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return o$(this).formatHsl()},formatRgb:oT,toString:oT}),ov(oB,oI,om(ob,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oB(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oB(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new oB(oU(this.r),oU(this.g),oU(this.b),oz(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:oL,formatHex:oL,formatHex8:function(){return`#${oq(this.r)}${oq(this.g)}${oq(this.b)}${oq((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:oR,toString:oR})),ov(oW,function(t,e,r,n){return 1==arguments.length?o$(t):new oW(t,e,r,null==n?1:n)},om(ob,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oW(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oW(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new oB(oV(t>=240?t-240:t+120,o,n),oV(t,o,n),oV(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new oW(oX(this.h),oH(this.s),oH(this.l),oz(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=oz(this.opacity);return`${1===t?"hsl(":"hsla("}${oX(this.h)}, ${100*oH(this.s)}%, ${100*oH(this.l)}%${1===t?")":`, ${t})`}`}}));let oY=t=>()=>t;function oK(t,e){var r,n,o=e-t;return o?(r=t,n=o,function(t){return r+t*n}):oY(isNaN(t)?e:t)}let oZ=function t(e){var r,n=1==(r=+e)?oK:function(t,e){var n,o,i;return e-t?(n=t,o=e,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(t){return Math.pow(n+t*o,i)}):oY(isNaN(t)?e:t)};function o(t,e){var r=n((t=oI(t)).r,(e=oI(e)).r),o=n(t.g,e.g),i=n(t.b,e.b),a=oK(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function oJ(t){return function(e){var r,n,o=e.length,i=Array(o),a=Array(o),c=Array(o);for(r=0;r<o;++r)n=oI(e[r]),i[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return i=t(i),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=c(t),n+""}}}oJ(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,c=n<e-1?t[n+2]:2*i-o;return oG((r-n/e)*e,a,o,i,c)}}),oJ(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return oG((r-n/e)*e,o,i,a,c)}});function oQ(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var o0=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,o1=RegExp(o0.source,"g");function o2(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?oY(e):("number"===o?oQ:"string"===o?(n=oC(e))?(e=n,oZ):function(t,e){var r,n,o,i,a,c=o0.lastIndex=o1.lastIndex=0,u=-1,l=[],s=[];for(t+="",e+="";(o=o0.exec(t))&&(i=o1.exec(e));)(a=i.index)>c&&(a=e.slice(c,a),l[u]?l[u]+=a:l[++u]=a),(o=o[0])===(i=i[0])?l[u]?l[u]+=i:l[++u]=i:(l[++u]=null,s.push({i:u,x:oQ(o,i)})),c=o1.lastIndex;return c<e.length&&(a=e.slice(c),l[u]?l[u]+=a:l[++u]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof oC?oZ:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=o2(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},o={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=o2(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}:oQ:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}})(t,e)}function o5(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function o3(t){return+t}var o4=[0,1];function o6(t){return t}function o8(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function o7(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=o8(o,n),i=r(a,i)):(n=o8(n,o),i=r(i,a)),function(t){return i(n(t))}}function o9(t,e,r){var n=Math.min(t.length,e.length)-1,o=Array(n),i=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=o8(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=oy(t,e,1,n)-1;return i[r](o[r](e))}}function it(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function ie(){var t,e,r,n,o,i,a=o4,c=o4,u=o2,l=o6;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==o6&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?o9:o7,o=i=null,f}function f(e){return null==e||isNaN(e*=1)?r:(o||(o=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(c,a.map(t),oQ)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,o3),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=o5,s()},f.clamp=function(t){return arguments.length?(l=!!t||o6,s()):l!==o6},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function ir(){return ie()(o6,o6)}var io=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ii(t){var e;if(!(e=io.exec(t)))throw Error("invalid format: "+t);return new ia({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function ia(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function ic(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function iu(t){return(t=ic(Math.abs(t)))?t[1]:NaN}function il(t,e){var r=ic(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}ii.prototype=ia.prototype,ia.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let is={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>il(100*t,e),r:il,s:function(t,e){var r=ic(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(cU=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+ic(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function ip(t){return t}var ih=Array.prototype.map,id=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function iy(t,e,r,n){var o,i,a,c=ou(t,e,r);switch((n=ii(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(e));return null==n.precision&&!isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(iu(u)/3)))-iu(Math.abs(c))))&&(n.precision=a),c$(n,u);case"":case"e":case"g":case"p":case"r":null==n.precision&&!isNaN(a=Math.max(0,iu(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(o=Math.abs(o=c)))-iu(o))+1)&&(n.precision=a-("e"===n.type));break;case"f":case"%":null==n.precision&&!isNaN(a=Math.max(0,-iu(Math.abs(c))))&&(n.precision=a-("%"===n.type)*2)}return cF(n)}function iv(t){var e=t.domain;return t.ticks=function(t){var r=e();return oa(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return iy(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,c=i.length-1,u=i[a],l=i[c],s=10;for(l<u&&(o=u,u=l,l=o,o=a,a=c,c=o);s-- >0;){if((o=oc(u,l,r))===n)return i[a]=u,i[c]=l,e(i);if(o>0)u=Math.floor(u/o)*o,l=Math.ceil(l/o)*o;else if(o<0)u=Math.ceil(u*o)/o,l=Math.floor(l*o)/o;else break;n=o}return t},t}function im(){var t=ir();return t.copy=function(){return it(t,im())},nw.apply(t,arguments),iv(t)}function ib(t,e){t=t.slice();var r,n=0,o=t.length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function ig(t){return Math.log(t)}function ix(t){return Math.exp(t)}function iO(t){return-Math.log(-t)}function iw(t){return-Math.exp(-t)}function ij(t){return isFinite(t)?+("1e"+t):t<0?0:t}function iS(t){return(e,r)=>-t(-e,r)}function iP(t){let e,r;let n=t(ig,ix),o=n.domain,i=10;function a(){var a,c;return e=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(c=i)?ij:c===Math.E?Math.exp:t=>Math.pow(c,t),o()[0]<0?(e=iS(e),r=iS(r),t(iO,iw)):t(ig,ix),n}return n.base=function(t){return arguments.length?(i=+t,a()):i},n.domain=function(t){return arguments.length?(o(t),a()):o()},n.ticks=t=>{let n,a;let c=o(),u=c[0],l=c[c.length-1],s=l<u;s&&([u,l]=[l,u]);let f=e(u),p=e(l),h=null==t?10:+t,d=[];if(!(i%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),u>0){for(;f<=p;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}}else for(;f<=p;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}2*d.length<h&&(d=oa(u,l,h))}else d=oa(f,p,Math.min(p-f,h)).map(r);return s?d.reverse():d},n.tickFormat=(t,o)=>{if(null==t&&(t=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=ii(o)).precision||(o.trim=!0),o=cF(o)),t===1/0)return o;let a=Math.max(1,i*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*i<i-.5&&(n*=i),n<=a?o(t):""}},n.nice=()=>o(ib(o(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function iA(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function iE(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function ik(t){var e=1,r=t(iA(1),iE(e));return r.constant=function(r){return arguments.length?t(iA(e=+r),iE(e)):e},iv(r)}function iM(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function i_(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function iT(t){return t<0?-t*t:t*t}function iC(t){var e=t(o6,o6),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(o6,o6):.5===r?t(i_,iT):t(iM(r),iM(1/r)):r},iv(e)}function iD(){var t=iC(ie());return t.copy=function(){return it(t,iD()).exponent(t.exponent())},nw.apply(t,arguments),t}function iN(){return iD.apply(null,arguments).exponent(.5)}function iI(t){return Math.sign(t)*t*t}function iB(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function iL(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}cF=(cq=function(t){var e,r,n,o=void 0===t.grouping||void 0===t.thousands?ip:(e=ih.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,c=e[0],u=0;o>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),i.push(t.substring(o-=c,o+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return i.reverse().join(r)}),i=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?ip:(n=ih.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),l=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=ii(t)).fill,r=t.align,n=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):is[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?i:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?l:"",O=is[b],w=/[defgprs%]/.test(b);function j(t){var i,a,l,p=g,j=x;if("c"===b)j=O(t)+j,t="";else{var S=(t*=1)<0||1/t<0;if(t=isNaN(t)?f:O(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==n&&(S=!1),p=(S?"("===n?n:s:"-"===n||"("===n?"":n)+p,j=("s"===b?id[8+cU/3]:"")+j+(S&&"("===n?")":""),w){for(i=-1,a=t.length;++i<a;)if(48>(l=t.charCodeAt(i))||l>57){j=(46===l?c+t.slice(i+1):t.slice(i))+j,t=t.slice(0,i);break}}}y&&!h&&(t=o(t,1/0));var P=p.length+t.length+j.length,A=P<d?Array(d-P+1).join(e):"";switch(y&&h&&(t=o(A+t,A.length?d-j.length:1/0),A=""),r){case"<":t=p+t+j+A;break;case"=":t=p+A+t+j;break;case"^":t=A.slice(0,P=A.length>>1)+p+t+j+A.slice(P);break;default:t=A+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=ii(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(iu(e)/3))),o=Math.pow(10,-n),i=id[8+n/3];return function(t){return r(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,c$=cq.formatPrefix;function iR(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function iz(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let iU=new Date,iq=new Date;function iF(t,e,r,n){function o(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{let e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{let a;let c=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return c;do c.push(a=new Date(+r)),e(r,i),t(r);while(a<r&&r<n);return c},o.filter=r=>iF(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t){if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}}),r&&(o.count=(e,n)=>(iU.setTime(+e),iq.setTime(+n),t(iU),t(iq),Math.floor(r(iU,iq))),o.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?o.filter(n?e=>n(e)%t==0:e=>o.count(0,e)%t==0):o:null),o}let i$=iF(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);i$.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?iF(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):i$:null,i$.range;let iW=iF(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());iW.range;let iX=iF(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());iX.range;let iH=iF(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());iH.range;let iV=iF(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());iV.range;let iG=iF(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());iG.range;let iY=iF(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);iY.range;let iK=iF(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);iK.range;let iZ=iF(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function iJ(t){return iF(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}iZ.range;let iQ=iJ(0),i0=iJ(1),i1=iJ(2),i2=iJ(3),i5=iJ(4),i3=iJ(5),i4=iJ(6);function i6(t){return iF(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}iQ.range,i0.range,i1.range,i2.range,i5.range,i3.range,i4.range;let i8=i6(0),i7=i6(1),i9=i6(2),at=i6(3),ae=i6(4),ar=i6(5),an=i6(6);i8.range,i7.range,i9.range,at.range,ae.range,ar.range,an.range;let ao=iF(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());ao.range;let ai=iF(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());ai.range;let aa=iF(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());aa.every=t=>isFinite(t=Math.floor(t))&&t>0?iF(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,aa.range;let ac=iF(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function au(t,e,r,n,o,i){let a=[[iW,1,1e3],[iW,5,5e3],[iW,15,15e3],[iW,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let o=Math.abs(r-e)/n,i=of(([,,t])=>t).right(a,o);if(i===a.length)return t.every(ou(e/31536e6,r/31536e6,n));if(0===i)return i$.every(Math.max(ou(e,r,n),1));let[c,u]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let o=r&&"function"==typeof r.range?r:c(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},c]}ac.every=t=>isFinite(t=Math.floor(t))&&t>0?iF(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,ac.range;let[al,as]=au(ac,ai,i8,iZ,iG,iH),[af,ap]=au(aa,ao,iQ,iY,iV,iX);function ah(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function ad(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function ay(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var av={"-":"",_:" ",0:"0"},am=/^\s*\d+/,ab=/^%/,ag=/[\\^$*+?|[\]().{}]/g;function ax(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?Array(r-i+1).join(e)+o:o)}function aO(t){return t.replace(ag,"\\$&")}function aw(t){return RegExp("^(?:"+t.map(aO).join("|")+")","i")}function aj(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function aS(t,e,r){var n=am.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function aP(t,e,r){var n=am.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function aA(t,e,r){var n=am.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function aE(t,e,r){var n=am.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function ak(t,e,r){var n=am.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function aM(t,e,r){var n=am.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function a_(t,e,r){var n=am.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aT(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aC(t,e,r){var n=am.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function aD(t,e,r){var n=am.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function aN(t,e,r){var n=am.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function aI(t,e,r){var n=am.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function aB(t,e,r){var n=am.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function aL(t,e,r){var n=am.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function aR(t,e,r){var n=am.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function az(t,e,r){var n=am.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function aU(t,e,r){var n=am.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function aq(t,e,r){var n=ab.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function aF(t,e,r){var n=am.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function a$(t,e,r){var n=am.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function aW(t,e){return ax(t.getDate(),e,2)}function aX(t,e){return ax(t.getHours(),e,2)}function aH(t,e){return ax(t.getHours()%12||12,e,2)}function aV(t,e){return ax(1+iY.count(aa(t),t),e,3)}function aG(t,e){return ax(t.getMilliseconds(),e,3)}function aY(t,e){return aG(t,e)+"000"}function aK(t,e){return ax(t.getMonth()+1,e,2)}function aZ(t,e){return ax(t.getMinutes(),e,2)}function aJ(t,e){return ax(t.getSeconds(),e,2)}function aQ(t){var e=t.getDay();return 0===e?7:e}function a0(t,e){return ax(iQ.count(aa(t)-1,t),e,2)}function a1(t){var e=t.getDay();return e>=4||0===e?i5(t):i5.ceil(t)}function a2(t,e){return t=a1(t),ax(i5.count(aa(t),t)+(4===aa(t).getDay()),e,2)}function a5(t){return t.getDay()}function a3(t,e){return ax(i0.count(aa(t)-1,t),e,2)}function a4(t,e){return ax(t.getFullYear()%100,e,2)}function a6(t,e){return ax((t=a1(t)).getFullYear()%100,e,2)}function a8(t,e){return ax(t.getFullYear()%1e4,e,4)}function a7(t,e){var r=t.getDay();return ax((t=r>=4||0===r?i5(t):i5.ceil(t)).getFullYear()%1e4,e,4)}function a9(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+ax(e/60|0,"0",2)+ax(e%60,"0",2)}function ct(t,e){return ax(t.getUTCDate(),e,2)}function ce(t,e){return ax(t.getUTCHours(),e,2)}function cr(t,e){return ax(t.getUTCHours()%12||12,e,2)}function cn(t,e){return ax(1+iK.count(ac(t),t),e,3)}function co(t,e){return ax(t.getUTCMilliseconds(),e,3)}function ci(t,e){return co(t,e)+"000"}function ca(t,e){return ax(t.getUTCMonth()+1,e,2)}function cc(t,e){return ax(t.getUTCMinutes(),e,2)}function cu(t,e){return ax(t.getUTCSeconds(),e,2)}function cl(t){var e=t.getUTCDay();return 0===e?7:e}function cs(t,e){return ax(i8.count(ac(t)-1,t),e,2)}function cf(t){var e=t.getUTCDay();return e>=4||0===e?ae(t):ae.ceil(t)}function cp(t,e){return t=cf(t),ax(ae.count(ac(t),t)+(4===ac(t).getUTCDay()),e,2)}function ch(t){return t.getUTCDay()}function cd(t,e){return ax(i7.count(ac(t)-1,t),e,2)}function cy(t,e){return ax(t.getUTCFullYear()%100,e,2)}function cv(t,e){return ax((t=cf(t)).getUTCFullYear()%100,e,2)}function cm(t,e){return ax(t.getUTCFullYear()%1e4,e,4)}function cb(t,e){var r=t.getUTCDay();return ax((t=r>=4||0===r?ae(t):ae.ceil(t)).getUTCFullYear()%1e4,e,4)}function cg(){return"+0000"}function cx(){return"%"}function cO(t){return+t}function cw(t){return Math.floor(+t/1e3)}function cj(t){return new Date(t)}function cS(t){return t instanceof Date?+t:+new Date(+t)}function cP(t,e,r,n,o,i,a,c,u,l){var s=ir(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function O(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,cS)):p().map(cj)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?O:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(ib(r,t)):s},s.copy=function(){return it(s,cP(t,e,r,n,o,i,a,c,u,l))},s}function cA(){return nw.apply(cP(af,ap,aa,ao,iQ,iY,iV,iX,iW,cX).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cE(){return nw.apply(cP(al,as,ac,ai,i8,iK,iG,iH,iW,cH).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function ck(){var t,e,r,n,o,i=0,a=1,c=o6,u=!1;function l(e){return null==e||isNaN(e*=1)?o:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i*=1),e=n(a*=1),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(o2),l.rangeRound=s(o5),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function cM(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function c_(){var t=iC(ck());return t.copy=function(){return cM(t,c_()).exponent(t.exponent())},nj.apply(t,arguments)}function cT(){return c_.apply(null,arguments).exponent(.5)}function cC(){var t,e,r,n,o,i,a,c=0,u=.5,l=1,s=1,f=o6,p=!1;function h(t){return isNaN(t*=1)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=o2);for(var r=0,n=e.length-1,o=e[0],i=Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=i(c*=1),e=i(u*=1),r=i(l*=1),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(o2),h.rangeRound=d(o5),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function cD(){var t=iC(cC());return t.copy=function(){return cM(t,cD()).exponent(t.exponent())},nj.apply(t,arguments)}function cN(){return cD.apply(null,arguments).exponent(.5)}function cI(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],c=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function cB(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function cL(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function cR(t,e){return t[e]}function cz(t){let e=[];return e.key=t,e}cX=(cW=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=aw(o),s=aj(o),f=aw(i),p=aj(i),h=aw(a),d=aj(a),y=aw(c),v=aj(c),m=aw(u),b=aj(u),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:aW,e:aW,f:aY,g:a6,G:a7,H:aX,I:aH,j:aV,L:aG,m:aK,M:aZ,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:cO,s:cw,S:aJ,u:aQ,U:a0,V:a2,w:a5,W:a3,x:null,X:null,y:a4,Y:a8,Z:a9,"%":cx},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:ct,e:ct,f:ci,g:cv,G:cb,H:ce,I:cr,j:cn,L:co,m:ca,M:cc,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:cO,s:cw,S:cu,u:cl,U:cs,V:cp,w:ch,W:cd,x:null,X:null,y:cy,Y:cm,Z:cg,"%":cx},O={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:aN,e:aN,f:aU,g:a_,G:aM,H:aB,I:aB,j:aI,L:az,m:aD,M:aL,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:aC,Q:aF,s:a$,S:aR,u:aP,U:aA,V:aE,w:aS,W:ak,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:a_,Y:aM,Z:aT,"%":aq};function w(t,e){return function(r){var n,o,i,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(o=av[n=t.charAt(++c)])?n=t.charAt(++c):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,o,i=ay(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!e||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=ad(ay(i.y,0,1))).getUTCDay())>4||0===o?i7.ceil(n):i7(n),n=iK.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=ah(ay(i.y,0,1))).getDay())>4||0===o?i0.ceil(n):i0(n),n=iY.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),o="Z"in i?ad(ay(i.y,0,1)).getUTCDay():ah(ay(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,ad(i)):ah(i)}}function S(t,e,r,n){for(var o,i,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(o=e.charCodeAt(a++))){if(!(i=O[(o=e.charAt(a++))in av?e.charAt(a++):o])||(n=i(t,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=w(r,g),g.X=w(n,g),g.c=w(e,g),x.x=w(r,x),x.X=w(n,x),x.c=w(e,x),{format:function(t){var e=w(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,cW.parse,cH=cW.utcFormat,cW.utcParse,Array.prototype.slice;var cU,cq,cF,c$,cW,cX,cH,cV,cG,cY=r(90453),cK=r.n(cY),cZ=r(15883),cJ=r.n(cZ),cQ=r(21592),c0=r.n(cQ),c1=r(71967),c2=r.n(c1),c5=!0,c3="[DecimalError] ",c4=c3+"Invalid argument: ",c6=c3+"Exponent out of range: ",c8=Math.floor,c7=Math.pow,c9=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ut=c8(1286742750677284.5),ue={};function ur(t,e){var r,n,o,i,a,c,u,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),c5?up(e,f):e;if(u=t.d,l=e.d,a=t.e,o=e.e,u=u.slice(),i=a-o){for(i<0?(n=u,i=-i,c=l.length):(n=l,o=a,c=u.length),i>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=u.length)-(i=l.length)<0&&(i=c,n=l,l=u,u=n),r=0;i;)r=(u[--i]=u[i]+l[i]+r)/1e7|0,u[i]%=1e7;for(r&&(u.unshift(r),++o),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=o,c5?up(e,f):e}function un(t,e,r){if(t!==~~t||t<e||t>r)throw Error(c4+t)}function uo(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=ul(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=ul(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}ue.absoluteValue=ue.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},ue.comparedTo=ue.cmp=function(t){var e,r,n,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(o=t.d.length)?n:o;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},ue.decimalPlaces=ue.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},ue.dividedBy=ue.div=function(t){return ui(this,new this.constructor(t))},ue.dividedToIntegerBy=ue.idiv=function(t){var e=this.constructor;return up(ui(this,new e(t),0,1),e.precision)},ue.equals=ue.eq=function(t){return!this.cmp(t)},ue.exponent=function(){return uc(this)},ue.greaterThan=ue.gt=function(t){return this.cmp(t)>0},ue.greaterThanOrEqualTo=ue.gte=function(t){return this.cmp(t)>=0},ue.isInteger=ue.isint=function(){return this.e>this.d.length-2},ue.isNegative=ue.isneg=function(){return this.s<0},ue.isPositive=ue.ispos=function(){return this.s>0},ue.isZero=function(){return 0===this.s},ue.lessThan=ue.lt=function(t){return 0>this.cmp(t)},ue.lessThanOrEqualTo=ue.lte=function(t){return 1>this.cmp(t)},ue.logarithm=ue.log=function(t){var e,r=this.constructor,n=r.precision,o=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(cG))throw Error(c3+"NaN");if(this.s<1)throw Error(c3+(this.s?"NaN":"-Infinity"));return this.eq(cG)?new r(0):(c5=!1,e=ui(us(this,o),us(t,o),o),c5=!0,up(e,n))},ue.minus=ue.sub=function(t){return t=new this.constructor(t),this.s==t.s?uh(this,t):ur(this,(t.s=-t.s,t))},ue.modulo=ue.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(c3+"NaN");return this.s?(c5=!1,e=ui(this,t,0,1).times(t),c5=!0,this.minus(e)):up(new r(this),n)},ue.naturalExponential=ue.exp=function(){return ua(this)},ue.naturalLogarithm=ue.ln=function(){return us(this)},ue.negated=ue.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},ue.plus=ue.add=function(t){return t=new this.constructor(t),this.s==t.s?ur(this,t):uh(this,(t.s=-t.s,t))},ue.precision=ue.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(c4+t);if(e=uc(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},ue.squareRoot=ue.sqrt=function(){var t,e,r,n,o,i,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(c3+"NaN")}for(t=uc(this),c5=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=uo(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=c8((t+1)/2)-(t<0||t%2),n=new c(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(o.toString()),o=a=(r=c.precision)+3;;)if(n=(i=n).plus(ui(this,i,a+2)).times(.5),uo(i.d).slice(0,a)===(e=uo(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(up(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=e)break;a+=4}return c5=!0,up(n,r)},ue.times=ue.mul=function(t){var e,r,n,o,i,a,c,u,l,s=this.constructor,f=this.d,p=(t=new s(t)).d;if(!this.s||!t.s)return new s(0);for(t.s*=this.s,r=this.e+t.e,(u=f.length)<(l=p.length)&&(i=f,f=p,p=i,a=u,u=l,l=a),i=[],n=a=u+l;n--;)i.push(0);for(n=l;--n>=0;){for(e=0,o=u+n;o>n;)c=i[o]+p[n]*f[o-n-1]+e,i[o--]=c%1e7|0,e=c/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,c5?up(t,s.precision):t},ue.toDecimalPlaces=ue.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(un(t,0,1e9),void 0===e?e=n.rounding:un(e,0,8),up(r,t+uc(r)+1,e))},ue.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=ud(n,!0):(un(t,0,1e9),void 0===e?e=o.rounding:un(e,0,8),r=ud(n=up(new o(n),t+1,e),!0,t+1)),r},ue.toFixed=function(t,e){var r,n,o=this.constructor;return void 0===t?ud(this):(un(t,0,1e9),void 0===e?e=o.rounding:un(e,0,8),r=ud((n=up(new o(this),t+uc(this)+1,e)).abs(),!1,t+uc(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},ue.toInteger=ue.toint=function(){var t=this.constructor;return up(new t(this),uc(this)+1,t.rounding)},ue.toNumber=function(){return+this},ue.toPower=ue.pow=function(t){var e,r,n,o,i,a,c=this,u=c.constructor,l=+(t=new u(t));if(!t.s)return new u(cG);if(!(c=new u(c)).s){if(t.s<1)throw Error(c3+"Infinity");return c}if(c.eq(cG))return c;if(n=u.precision,t.eq(cG))return up(c,n);if(a=(e=t.e)>=(r=t.d.length-1),i=c.s,a){if((r=l<0?-l:l)<=0x1fffffffffffff){for(o=new u(cG),e=Math.ceil(n/7+4),c5=!1;r%2&&uy((o=o.times(c)).d,e),0!==(r=c8(r/2));)uy((c=c.times(c)).d,e);return c5=!0,t.s<0?new u(cG).div(o):up(o,n)}}else if(i<0)throw Error(c3+"NaN");return i=i<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,c5=!1,o=t.times(us(c,n+12)),c5=!0,(o=ua(o)).s=i,o},ue.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?(r=uc(o),n=ud(o,r<=i.toExpNeg||r>=i.toExpPos)):(un(t,1,1e9),void 0===e?e=i.rounding:un(e,0,8),r=uc(o=up(new i(o),t,e)),n=ud(o,t<=r||r<=i.toExpNeg,t)),n},ue.toSignificantDigits=ue.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(un(t,1,1e9),void 0===e?e=r.rounding:un(e,0,8)),up(new r(this),t,e)},ue.toString=ue.valueOf=ue.val=ue.toJSON=ue[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=uc(this),e=this.constructor;return ud(this,t<=e.toExpNeg||t>=e.toExpPos)};var ui=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var c,u,l,s,f,p,h,d,y,v,m,b,g,x,O,w,j,S,P=n.constructor,A=n.s==o.s?1:-1,E=n.d,k=o.d;if(!n.s)return new P(n);if(!o.s)throw Error(c3+"Division by zero");for(l=0,u=n.e-o.e,j=k.length,O=E.length,d=(h=new P(A)).d=[];k[l]==(E[l]||0);)++l;if(k[l]>(E[l]||0)&&--u,(b=null==i?i=P.precision:a?i+(uc(n)-uc(o))+1:i)<0)return new P(0);if(b=b/7+2|0,l=0,1==j)for(s=0,k=k[0],b++;(l<O||s)&&b--;l++)g=1e7*s+(E[l]||0),d[l]=g/k|0,s=g%k|0;else{for((s=1e7/(k[0]+1)|0)>1&&(k=t(k,s),E=t(E,s),j=k.length,O=E.length),x=j,v=(y=E.slice(0,j)).length;v<j;)y[v++]=0;(S=k.slice()).unshift(0),w=k[0],k[1]>=1e7/2&&++w;do s=0,(c=e(k,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(s=m/w|0)>1?(s>=1e7&&(s=1e7-1),p=(f=t(k,s)).length,v=y.length,1==(c=e(f,y,p,v))&&(s--,r(f,j<p?S:k,p))):(0==s&&(c=s=1),f=k.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(k,y,j,v))<1&&(s++,r(y,j<v?S:k,v))),v=y.length):0===c&&(s++,y=[0]),d[l++]=s,c&&y[0]?y[v++]=E[x]||0:(y=[E[x]],v=1);while((x++<O||void 0!==y[0])&&b--)}return d[0]||d.shift(),h.e=u,up(h,a?i+uc(h)+1:i)}}();function ua(t,e){var r,n,o,i,a,c=0,u=0,l=t.constructor,s=l.precision;if(uc(t)>16)throw Error(c6+uc(t));if(!t.s)return new l(cG);for(null==e?(c5=!1,a=s):a=e,i=new l(.03125);t.abs().gte(.1);)t=t.times(i),u+=5;for(a+=Math.log(c7(2,u))/Math.LN10*2+5|0,r=n=o=new l(cG),l.precision=a;;){if(n=up(n.times(t),a),r=r.times(++c),uo((i=o.plus(ui(n,r,a))).d).slice(0,a)===uo(o.d).slice(0,a)){for(;u--;)o=up(o.times(o),a);return l.precision=s,null==e?(c5=!0,up(o,s)):o}o=i}}function uc(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function uu(t,e,r){if(e>t.LN10.sd())throw c5=!0,r&&(t.precision=r),Error(c3+"LN10 precision limit exceeded");return up(new t(t.LN10),e)}function ul(t){for(var e="";t--;)e+="0";return e}function us(t,e){var r,n,o,i,a,c,u,l,s,f=1,p=t,h=p.d,d=p.constructor,y=d.precision;if(p.s<1)throw Error(c3+(p.s?"NaN":"-Infinity"));if(p.eq(cG))return new d(0);if(null==e?(c5=!1,l=y):l=e,p.eq(10))return null==e&&(c5=!0),uu(d,l);if(d.precision=l+=10,n=(r=uo(h)).charAt(0),!(15e14>Math.abs(i=uc(p))))return u=uu(d,l+2,y).times(i+""),p=us(new d(n+"."+r.slice(1)),l-10).plus(u),d.precision=y,null==e?(c5=!0,up(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=uo((p=p.times(t)).d)).charAt(0),f++;for(i=uc(p),n>1?(p=new d("0."+r),i++):p=new d(n+"."+r.slice(1)),c=a=p=ui(p.minus(cG),p.plus(cG),l),s=up(p.times(p),l),o=3;;){if(a=up(a.times(s),l),uo((u=c.plus(ui(a,new d(o),l))).d).slice(0,l)===uo(c.d).slice(0,l))return c=c.times(2),0!==i&&(c=c.plus(uu(d,l+2,y).times(i+""))),c=ui(c,new d(f),l),d.precision=y,null==e?(c5=!0,up(c,y)):c;c=u,o+=2}}function uf(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,t.e=c8((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),c5&&(t.e>ut||t.e<-ut))throw Error(c6+r)}else t.s=0,t.e=0,t.d=[0];return t}function up(t,e,r){var n,o,i,a,c,u,l,s,f=t.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(i=f.length))return t;for(a=1,l=i=f[s];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=l/(i=c7(10,a-o-1))%10|0,u=e<0||void 0!==f[s+1]||l%i,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?o>0?l/c7(10,a-o):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(i=uc(t),f.length=1,e=e-i-1,f[0]=c7(10,(7-e%7)%7),t.e=c8(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,i=1,s--):(f.length=s+1,i=c7(10,7-n),f[s]=o>0?(l/c7(10,a-o)%c7(10,o)|0)*i:0),u)for(;;){if(0==s){1e7==(f[0]+=i)&&(f[0]=1,++t.e);break}if(f[s]+=i,1e7!=f[s])break;f[s--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(c5&&(t.e>ut||t.e<-ut))throw Error(c6+uc(t));return t}function uh(t,e){var r,n,o,i,a,c,u,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),c5?up(e,h):e;if(u=t.d,f=e.d,n=e.e,l=t.e,u=u.slice(),a=l-n){for((s=a<0)?(r=u,a=-a,c=f.length):(r=f,n=l,c=u.length),a>(o=Math.max(Math.ceil(h/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((s=(o=u.length)<(c=f.length))&&(c=o),o=0;o<c;o++)if(u[o]!=f[o]){s=u[o]<f[o];break}a=0}for(s&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,o=f.length-c;o>0;--o)u[c++]=0;for(o=f.length;o>a;){if(u[--o]<f[o]){for(i=o;i&&0===u[--i];)u[i]=1e7-1;--u[i],u[o]+=1e7}u[o]-=f[o]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,c5?up(e,h):e):new p(0)}function ud(t,e,r){var n,o=uc(t),i=uo(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+ul(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+ul(-o-1)+i,r&&(n=r-a)>0&&(i+=ul(n))):o>=a?(i+=ul(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+ul(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=ul(n))),t.s<0?"-"+i:i}function uy(t,e){if(t.length>e)return t.length=e,!0}function uv(t){if(!t||"object"!=typeof t)throw Error(c3+"Object expected");var e,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(c8(n)===n&&n>=o[e+1]&&n<=o[e+2])this[r]=n;else throw Error(c4+r+": "+n)}if(void 0!==(n=t[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(c4+r+": "+n)}return this}var cV=function t(e){var r,n,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(c4+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return uf(this,t.toString())}if("string"!=typeof t)throw Error(c4+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,c9.test(t))uf(this,t);else throw Error(c4+t)}if(i.prototype=ue,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=uv,void 0===e&&(e={}),e)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});cG=new cV(1);let um=cV;function ub(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var ug=function(t){return t},ux={},uO=function(t){return t===ux},uw=function(t){return function e(){return 0==arguments.length||1==arguments.length&&uO(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},uj=function(t){return function t(e,r){return 1===e?r:uw(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==ux}).length;return a>=e?r.apply(void 0,o):t(e-a,uw(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return uO(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return ub(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return ub(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ub(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},uS=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},uP=uj(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),uA=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return ug;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},uE=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},uk=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};uj(function(t,e,r){var n=+t;return n+r*(+e-n)}),uj(function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)}),uj(function(t,e,r){var n=e-+t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let uM={rangeStep:function(t,e,r){for(var n=new um(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new um(t).abs().log(10).toNumber())+1}};function u_(t){return function(t){if(Array.isArray(t))return uD(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||uC(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uT(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}}(t,e)||uC(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uC(t,e){if(t){if("string"==typeof t)return uD(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uD(t,e)}}function uD(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uN(t){var e=uT(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function uI(t,e,r){if(t.lte(0))return new um(0);var n=uM.getDigitCount(t.toNumber()),o=new um(10).pow(n),i=t.div(o),a=1!==n?.05:.1,c=new um(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?c:new um(Math.ceil(c))}function uB(t,e,r){var n=1,o=new um(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new um(10).pow(uM.getDigitCount(t)-1),o=new um(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new um(Math.floor(t)))}else 0===t?o=new um(Math.floor((e-1)/2)):r||(o=new um(Math.floor(t)));var a=Math.floor((e-1)/2);return uA(uP(function(t){return o.add(new um(t-a).mul(n)).toNumber()}),uS)(0,e)}var uL=uk(function(t){var e=uT(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=uT(uN([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(u_(uS(0,o-1).map(function(){return 1/0}))):[].concat(u_(uS(0,o-1).map(function(){return-1/0})),[l]);return r>n?uE(s):s}if(u===l)return uB(u,o,i);var f=function t(e,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new um(0),tickMin:new um(0),tickMax:new um(0)};var c=uI(new um(r).sub(e).div(n-1),o,a),u=Math.ceil((i=e<=0&&r>=0?new um(0):(i=new um(e).add(r).div(2)).sub(new um(i).mod(c))).sub(e).div(c).toNumber()),l=Math.ceil(new um(r).sub(i).div(c).toNumber()),s=u+l+1;return s>n?t(e,r,n,o,a+1):(s<n&&(l=r>0?l+(n-s):l,u=r>0?u:u+(n-s)),{step:c,tickMin:i.sub(new um(u).mul(c)),tickMax:i.add(new um(l).mul(c))})}(u,l,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=uM.rangeStep(h,d.add(new um(.1).mul(p)),p);return r>n?uE(y):y});uk(function(t){var e=uT(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=uT(uN([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return uB(u,o,i);var s=uI(new um(l).sub(u).div(a-1),i,0),f=uA(uP(function(t){return new um(u).add(new um(t).mul(s)).toNumber()}),uS)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?uE(f):f});var uR=uk(function(t,e){var r=uT(t,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=uT(uN([n,o]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,o];if(c===u)return[c];var l=Math.max(e,2),s=uI(new um(u).sub(c).div(l-1),i,0),f=[].concat(u_(uM.rangeStep(new um(c),new um(u).sub(new um(.99).mul(s)),s)),[u]);return n>o?uE(f):f}),uz=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function uU(t){return(uU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uq(){return(uq=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function uF(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u$(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(u$=function(){return!!t})()}function uW(t){return(uW=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function uX(t,e){return(uX=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uH(t,e,r){return(e=uV(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uV(t){var e=function(t,e){if("object"!=uU(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uU(e)?e:e+""}var uG=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=uW(t),function(t,e){if(e&&("object"===uU(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,u$()?Reflect.construct(t,e||[],uW(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&uX(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,o=t.dataKey,i=t.data,c=t.dataPointFormatter,u=t.xAxis,l=t.yAxis,s=tg(function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,uz),!1);"x"===this.props.direction&&"number"!==u.type&&tD(!1);var f=i.map(function(t){var i,f,p=c(t,o),h=p.x,d=p.y,y=p.value,v=p.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return uF(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uF(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=b[0],f=b[1]}else i=f=v;if("vertical"===r){var g=u.scale,x=d+e,O=x+n,w=x-n,j=g(y-i),S=g(y+f);m.push({x1:S,y1:O,x2:S,y2:w}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:O,x2:j,y2:w})}else if("horizontal"===r){var P=l.scale,A=h+e,E=A-n,k=A+n,M=P(y-i),_=P(y+f);m.push({x1:E,y1:_,x2:k,y2:_}),m.push({x1:A,y1:M,x2:A,y2:_}),m.push({x1:E,y1:M,x2:k,y2:M})}return a().createElement(tz,uq({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},s),m.map(function(t){return a().createElement("line",uq({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return a().createElement(tz,{className:"recharts-errorBars"},f)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uV(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function uY(t){return(uY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uK(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uZ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uK(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=uY(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uY(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uK(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}uH(uG,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),uH(uG,"displayName","ErrorBar");var uJ=function(t){var e,r=t.children,n=t.formattedGraphicalItems,o=t.legendWidth,i=t.legendContent,a=ty(r,rt);if(!a)return null;var c=rt.defaultProps,u=void 0!==c?uZ(uZ({},c),a.props):{};return e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(n||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?uZ(uZ({},r),e.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:u.iconType||a||"square",color:u7(e),value:i||o,payload:n}}),uZ(uZ(uZ({},u),rt.getWithHeight(a,o)),{},{payload:e,item:a})};function uQ(t){return(uQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u0(t){return function(t){if(Array.isArray(t))return u1(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return u1(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u1(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u1(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u2(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u5(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u2(Object(r),!0).forEach(function(e){u3(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u2(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function u3(t,e,r){var n;return(n=function(t,e){if("object"!=uQ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==uQ(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function u4(t,e,r){return X()(t)||X()(e)?r:I(e)?M()(t,e,r):V()(e)?e(t):r}function u6(t,e,r,n){var o=c0()(t,function(t){return u4(t,e)});if("number"===r){var i=o.filter(function(t){return N(t)||parseFloat(t)});return i.length?[cJ()(i),cK()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!X()(t)}):o).map(function(t){return I(t)||t instanceof Date?t:""})}var u8=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var c=o.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(C(s-l)!==C(f-s)){var h=[];if(C(f-s)===C(c[1]-c[0])){p=f;var d=s+c[1]-c[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+c[1]-c[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},u7=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?u5(u5({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},u9=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var l=o[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return ts(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?u5(u5({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var O=X()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:X()(O)?void 0:R(O,r,0)})}}return i},lt=function(t){var e,r=t.barGap,n=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,c=t.maxBarSize,u=a.length;if(u<1)return null;var l=R(r,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/u,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=o&&(h-=(u-1)*l,l=0),h>=o&&p>0&&(f=!0,p*=.9,h=u*p);var d={offset:((o-h)/2>>0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(u0(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=R(n,o,0,!0);o-2*y-(u-1)*l<=0&&(l=0);var v=(o-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(u0(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},le=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=uJ({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(c){var u=n||{},l=u.width,s=u.height,f=c.align,p=c.verticalAlign,h=c.layout;if(("vertical"===h||"horizontal"===h&&"middle"===p)&&"center"!==f&&N(t[f]))return u5(u5({},t),{},u3({},f,t[f]+(l||0)));if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==p&&N(t[p]))return u5(u5({},t),{},u3({},p,t[p]+(s||0)))}return t},lr=function(t,e,r,n,o){var i=td(e.props.children,uG).filter(function(t){var e;return e=t.props.direction,!!X()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(i&&i.length){var a=i.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=u4(e,r);if(X()(n))return t;var o=Array.isArray(n)?[cJ()(n),cK()(n)]:[n,n],i=a.reduce(function(t,r){var n=u4(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},ln=function(t,e,r,n,o){var i=e.map(function(e){return lr(t,e,r,o,n)}).filter(function(t){return!X()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},lo=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&lr(t,e,i,n)||u6(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},li=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},la=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var o,i,a=t.map(function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate});return o||a.push(e),i||a.push(r),a},lc=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*C(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(o?o.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!E()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}})},lu=new WeakMap,ll=function(t,e){if("function"!=typeof e)return t;lu.has(t)||lu.set(t,new WeakMap);var r=lu.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},ls=function(t,e,r){var o=t.scale,i=t.type,a=t.layout,c=t.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===c?{scale:nM(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:im(),realScaleType:"linear"}:"category"===i&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:n_(),realScaleType:"point"}:"category"===i?{scale:nM(),realScaleType:"band"}:{scale:im(),realScaleType:"linear"};if(P()(o)){var u="scale".concat(ep()(o));return{scale:(n[u]||n_)(),realScaleType:n[u]?u:"point"}}return V()(o)?{scale:o}:{scale:n_(),realScaleType:"point"}},lf=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}},lp=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},lh=function(t,e){if(!e||2!==e.length||!N(e[0])||!N(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!N(t[0])||t[0]<r)&&(o[0]=r),(!N(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},ld={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=E()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}cI(t,e)}},none:cI,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}cI(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<o;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,u&&(i-=l/u)}r[a-1][1]+=r[a-1][0]=i,cI(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=E()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},ly=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=ld[r];return(function(){var t=ek([]),e=cL,r=cI,n=cR;function o(o){var i,a,c=Array.from(t.apply(this,arguments),cz),u=c.length,l=-1;for(let t of o)for(i=0,++l;i<u;++i)(c[i][l]=[0,+n(t,c[i].key,l,o)]).data=t;for(i=0,a=cB(e(c));i<u;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:ek(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:ek(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?cL:"function"==typeof t?t:ek(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?cI:t,o):r},o})().keys(n).value(function(t,e){return+u4(t,e,0)}).order(cL).offset(o)(t)},lv=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?u5(u5({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(I(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[L("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return u5(u5({},t),{},u3({},c,u))},{});return Object.keys(a).reduce(function(e,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,i){var a=c.stackGroups[i];return u5(u5({},e),{},u3({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:ly(t,a.items,o)}))},{})),u5(u5({},e),{},u3({},i,c))},{})},lm=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var l=uL(u,o,a);return t.domain([cJ()(l),cK()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:uR(t.domain(),o,a)}:null};function lb(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!X()(o[e.dataKey])){var c=F(r,"value",o[e.dataKey]);if(c)return c.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var u=u4(o,X()(a)?e.dataKey:a);return X()(u)?null:e.scale(u)}var lg=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=u4(i,e.dataKey,e.domain[a]);return X()(c)?null:e.scale(c)-o/2+n},lx=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},lO=function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?u5(u5({},t.type.defaultProps),t.props):t.props).stackId;if(I(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},lw=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[cJ()(e.concat([t[0]]).filter(N)),cK()(e.concat([t[1]]).filter(N))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},lj=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lS=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lP=function(t,e,r){if(V()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(N(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(lj.test(t[0])){var o=+lj.exec(t[0])[1];n[0]=e[0]-o}else V()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(N(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(lS.test(t[1])){var i=+lS.exec(t[1])[1];n[1]=e[1]+i}else V()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},lA=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=tC()(e,function(t){return t.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],l=o[a-1];i=Math.min((u.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},lE=function(t,e,r){return!t||!t.length||c2()(t,M()(r,"type.defaultProps.domain"))?e:t},lk=function(t,e){var r=t.type.defaultProps?u5(u5({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return u5(u5({},tg(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:u7(t),value:u4(e,n),type:c,payload:e,chartType:u,hide:l})};function lM(t){return(lM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l_(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lT(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l_(Object(r),!0).forEach(function(e){lC(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l_(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lC(t,e,r){var n;return(n=function(t,e){if("object"!=lM(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lM(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var lD=["Webkit","Moz","O","ms"],lN=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=lD.reduce(function(t,n){return lT(lT({},t),{},lC({},n+r,e))},{});return n[t]=e,n};function lI(t){return(lI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lB(){return(lB=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lL(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lR(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lL(Object(r),!0).forEach(function(e){l$(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lL(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lz(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lW(n.key),n)}}function lU(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(lU=function(){return!!t})()}function lq(t){return(lq=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lF(t,e){return(lF=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function l$(t,e,r){return(e=lW(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lW(t){var e=function(t,e){if("object"!=lI(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lI(e)?e:e+""}var lX=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=n_().domain(t_()(0,c)).range([o,o+i-a]),l=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}},lH=function(t){return t.changedTouches&&!!t.changedTouches.length},lV=function(t){var e,r;function n(t){var e,r,o;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[t],r=lq(r),l$(e=function(t,e){if(e&&("object"===lI(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,lU()?Reflect.construct(r,o||[],lq(this).constructor):r.apply(this,o)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),l$(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),l$(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})}),e.detachDragEndListener()}),l$(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),l$(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),l$(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),l$(e,"handleSlideDragStart",function(t){var r=lH(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&lF(t,e)}(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=Math.min(e,r),l=Math.max(e,r),s=n.getIndexInRange(o,u),f=n.getIndexInRange(o,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=u4(r[t],o,t);return V()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=lH(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||!!(o<i)&&b===t||"endX"===n&&(o>i?b%p==0:m%p==0)||!!(o>i)&&b===t};this.setState(l$(l$({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var l=u+t;if(-1!==l&&!(l>=o.length)){var s=o[l];("startX"!==e||!(s>=a))&&("endX"!==e||!(s<=i))&&this.setState(l$({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.fill,c=t.stroke;return a().createElement("rect",{stroke:c,fill:i,x:e,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,c=t.data,u=t.children,l=t.padding,s=i.Children.only(u);return s?a().cloneElement(s,{x:e,y:r,width:n,height:o,margin:l,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,o,i=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,d=c.startIndex,y=c.endIndex,v=Math.max(t,this.props.x),m=lR(lR({},tg(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null===(r=h[d])||void 0===r?void 0:r.name,", Max value: ").concat(null===(o=h[y])||void 0===o?void 0:o.name);return a().createElement(tz,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,o=r.height,i=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return a().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:u,y:n,width:l,height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,o=t.height,i=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return a().createElement(tz,{className:"recharts-brush-texts"},a().createElement(oe,lB({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:n+o/2},f),this.getTextOfTick(e)),a().createElement(oe,lB({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+i+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,o=t.x,i=t.y,c=t.width,u=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,v=s.isTravellerFocused;if(!e||!e.length||!N(o)||!N(i)||!N(c)||!N(u)||c<=0||u<=0)return null;var m=(0,O.A)("recharts-brush",r),b=1===a().Children.count(n),g=lN("userSelect","none");return a().createElement(tz,{className:m,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),b&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||v||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,o=t.height,i=t.stroke,c=Math.floor(r+o/2)-1;return a().createElement(a().Fragment,null,a().createElement("rect",{x:e,y:r,width:n,height:o,fill:i,stroke:"none"}),a().createElement("line",{x1:e+1,y1:c,x2:e+n-1,y2:c,fill:"none",stroke:"#fff"}),a().createElement("line",{x1:e+1,y1:c+2,x2:e+n-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):V()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return lR({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?lX({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);t[i]>e?o=i:n=i}return e>=t[o]?o:n}}],e&&lz(n.prototype,e),r&&lz(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function lG(t){return(lG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lY(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lK(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lY(Object(r),!0).forEach(function(e){(function(t,e,r){var n;(n=function(t,e){if("object"!=lG(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lG(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r})(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lY(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}l$(lV,"displayName","Brush"),l$(lV,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var lZ=Math.PI/180,lJ=function(t,e,r,n){return{x:t+Math.cos(-lZ*n)*r,y:e+Math.sin(-lZ*n)*r}},lQ=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},l0=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=lQ({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=Math.acos((r-o)/a);return n>i&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},l1=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},l2=function(t,e){var r,n=l0({x:t.x,y:t.y},e),o=n.radius,i=n.angle,a=e.innerRadius,c=e.outerRadius;if(o<a||o>c)return!1;if(0===o)return!0;var u=l1(e),l=u.startAngle,s=u.endAngle,f=i;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?lK(lK({},e),{},{radius:o,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null};function l5(t){return(l5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var l3=["offset"];function l4(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function l6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l8(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l6(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=l5(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l5(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function l7(){return(l7=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var l9=function(t){var e=t.value,r=t.formatter,n=X()(t.children)?e:t.children;return V()(r)?r(n):n},st=function(t,e,r){var n,o,i=t.position,c=t.viewBox,u=t.offset,l=t.className,s=c.cx,f=c.cy,p=c.innerRadius,h=c.outerRadius,d=c.startAngle,y=c.endAngle,v=c.clockWise,m=(p+h)/2,b=C(y-d)*Math.min(Math.abs(y-d),360),g=b>=0?1:-1;"insideStart"===i?(n=d+g*u,o=v):"insideEnd"===i?(n=y-g*u,o=!v):"end"===i&&(n=y+g*u,o=v),o=b<=0?o:!o;var x=lJ(s,f,m,n),w=lJ(s,f,m,n+(o?1:-1)*359),j="M".concat(x.x,",").concat(x.y,"\n    A").concat(m,",").concat(m,",0,1,").concat(+!o,",\n    ").concat(w.x,",").concat(w.y),S=X()(t.id)?L("recharts-radial-line-"):t.id;return a().createElement("text",l7({},r,{dominantBaseline:"central",className:(0,O.A)("recharts-radial-bar-label",l)}),a().createElement("defs",null,a().createElement("path",{id:S,d:j})),a().createElement("textPath",{xlinkHref:"#".concat(S)},e))},se=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e.cx,i=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=lJ(o,i,c+r,u),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=lJ(o,i,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},sr=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e.x,a=e.y,c=e.width,u=e.height,l=u>=0?1:-1,s=l*n,f=l>0?"end":"start",p=l>0?"start":"end",h=c>=0?1:-1,d=h*n,y=h>0?"end":"start",v=h>0?"start":"end";if("top"===o)return l8(l8({},{x:i+c/2,y:a-l*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===o)return l8(l8({},{x:i+c/2,y:a+u+s,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===o){var m={x:i-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"};return l8(l8({},m),r?{width:Math.max(m.x-r.x,0),height:u}:{})}if("right"===o){var b={x:i+c+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"};return l8(l8({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:u}:{})}var g=r?{width:c,height:u}:{};return"insideLeft"===o?l8({x:i+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===o?l8({x:i+c-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===o?l8({x:i+c/2,y:a+s,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===o?l8({x:i+c/2,y:a+u-s,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===o?l8({x:i+d,y:a+s,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===o?l8({x:i+c-d,y:a+s,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===o?l8({x:i+d,y:a+u-s,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===o?l8({x:i+c-d,y:a+u-s,textAnchor:y,verticalAnchor:f},g):Y()(o)&&(N(o.x)||D(o.x))&&(N(o.y)||D(o.y))?l8({x:i+R(o.x,c),y:a+R(o.y,u),textAnchor:"end",verticalAnchor:"end"},g):l8({x:i+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function sn(t){var e,r=t.offset,n=l8({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,l3)),o=n.viewBox,c=n.position,u=n.value,l=n.children,s=n.content,f=n.className,p=n.textBreakAll;if(!o||X()(u)&&X()(l)&&!(0,i.isValidElement)(s)&&!V()(s))return null;if((0,i.isValidElement)(s))return(0,i.cloneElement)(s,n);if(V()(s)){if(e=(0,i.createElement)(s,n),(0,i.isValidElement)(e))return e}else e=l9(n);var h="cx"in o&&N(o.cx),d=tg(n,!0);if(h&&("insideStart"===c||"insideEnd"===c||"end"===c))return st(n,e,d);var y=h?se(n):sr(n);return a().createElement(oe,l7({className:(0,O.A)("recharts-label",void 0===f?"":f)},d,y,{breakAll:p}),e)}sn.displayName="Label";var so=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,d=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(N(d)&&N(y)){if(N(s)&&N(f))return{x:s,y:f,width:d,height:y};if(N(p)&&N(h))return{x:p,y:h,width:d,height:y}}return N(s)&&N(f)?{x:s,y:f,width:0,height:0}:N(e)&&N(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};sn.parseViewBox=so,sn.renderCallByParent=function(t,e){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var c=t.children,u=so(t),l=td(c,sn).map(function(t,r){return(0,i.cloneElement)(t,{viewBox:e||u,key:"label-".concat(r)})});if(!o)return l;return[(r=t.label,n=e||u,r?!0===r?a().createElement(sn,{key:"label-implicit",viewBox:n}):I(r)?a().createElement(sn,{key:"label-implicit",viewBox:n,value:r}):(0,i.isValidElement)(r)?r.type===sn?(0,i.cloneElement)(r,{key:"label-implicit",viewBox:n}):a().createElement(sn,{key:"label-implicit",content:r,viewBox:n}):V()(r)?a().createElement(sn,{key:"label-implicit",content:r,viewBox:n}):Y()(r)?a().createElement(sn,l7({viewBox:n},r,{key:"label-implicit"})):null:null)].concat(function(t){if(Array.isArray(t))return l4(t)}(l)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(l)||function(t,e){if(t){if("string"==typeof t)return l4(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l4(t,e)}}(l)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var si=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},sa=r(69691),sc=r.n(sa),su=r(47212),sl=r.n(su),ss=function(t){return null};ss.displayName="Cell";var sf=r(5359),sp=r.n(sf);function sh(t){return(sh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var sd=["valueAccessor"],sy=["data","dataKey","clockWise","id","textBreakAll"];function sv(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sm(){return(sm=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sb(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sb(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sh(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sh(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sh(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sb(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sx(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var sO=function(t){return Array.isArray(t.value)?sp()(t.value):t.value};function sw(t){var e=t.valueAccessor,r=void 0===e?sO:e,n=sx(t,sd),o=n.data,i=n.dataKey,c=n.clockWise,u=n.id,l=n.textBreakAll,s=sx(n,sy);return o&&o.length?a().createElement(tz,{className:"recharts-label-list"},o.map(function(t,e){var n=X()(i)?r(t,e):u4(t&&t.payload,i),o=X()(u)?{}:{id:"".concat(u,"-").concat(e)};return a().createElement(sn,sm({},tg(t,!0),s,o,{parentViewBox:t.parentViewBox,value:n,textBreakAll:l,viewBox:sn.parseViewBox(X()(c)?t:sg(sg({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}sw.displayName="LabelList",sw.renderCallByParent=function(t,e){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var o=td(t.children,sw).map(function(t,r){return(0,i.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return n?[(r=t.label)?!0===r?a().createElement(sw,{key:"labelList-implicit",data:e}):a().isValidElement(r)||V()(r)?a().createElement(sw,{key:"labelList-implicit",data:e,content:r}):Y()(r)?a().createElement(sw,sm({data:e},r,{key:"labelList-implicit"})):null:null].concat(function(t){if(Array.isArray(t))return sv(t)}(o)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return sv(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sv(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var sj=r(38404),sS=r.n(sj),sP=r(98451),sA=r.n(sP);function sE(t){return(sE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sk(){return(sk=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sM(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function s_(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sT(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s_(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sE(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s_(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sC=function(t,e,r,n,o){var i,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+o)+"L ".concat(t+r-a/2-n,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},sD={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},sN=function(t){var e,r=sT(sT({},sD),t),n=(0,i.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,i.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return sM(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sM(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=o[0],u=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.upperWidth,p=r.lowerWidth,h=r.height,d=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||h!==+h||0===f&&0===p||0===h)return null;var g=(0,O.A)("recharts-trapezoid",d);return b?a().createElement(np,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:h,x:l,y:s},to:{upperWidth:f,lowerWidth:p,height:h,x:l,y:s},duration:v,animationEasing:y,isActive:b},function(t){var e=t.upperWidth,o=t.lowerWidth,i=t.height,u=t.x,l=t.y;return a().createElement(np,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:y},a().createElement("path",sk({},tg(r,!0),{className:g,d:sC(u,l,e,o,i),ref:n})))}):a().createElement("g",null,a().createElement("path",sk({},tg(r,!0),{className:g,d:sC(l,s,f,p,h)})))};function sI(t){return(sI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sB(){return(sB=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sL(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sR(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sL(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sI(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sI(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sL(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sz=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,l=c*(a?1:-1)+n,s=Math.asin(c/l)/lZ,f=u?o:o+i*s;return{center:lJ(e,r,l,f),circleTangency:lJ(e,r,n,f),lineTangency:lJ(e,r,l*Math.cos(s*lZ),u?o-i*s:o),theta:s}},sU=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,a=t.endAngle,c=C(a-i)*Math.min(Math.abs(a-i),359.999),u=i+c,l=lJ(e,r,o,i),s=lJ(e,r,o,u),f="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(i>u),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var p=lJ(e,r,n,i),h=lJ(e,r,n,u);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(i<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(e,",").concat(r," Z");return f},sq=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,l=t.endAngle,s=C(l-u),f=sz({cx:e,cy:r,radius:o,angle:u,sign:s,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,h=f.lineTangency,d=f.theta,y=sz({cx:e,cy:r,radius:o,angle:l,sign:-s,cornerRadius:i,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=c?Math.abs(u-l):Math.abs(u-l)-d-b;if(g<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):sU({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:u,endAngle:l});var x="M ".concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var O=sz({cx:e,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),w=O.circleTangency,j=O.lineTangency,S=O.theta,P=sz({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),A=P.circleTangency,E=P.lineTangency,k=P.theta,M=c?Math.abs(u-l):Math.abs(u-l)-S-k;if(M<0&&0===i)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(A.x,",").concat(A.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(s>0),",").concat(w.x,",").concat(w.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x},sF={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},s$=function(t){var e,r=sR(sR({},sF),t),n=r.cx,o=r.cy,i=r.innerRadius,c=r.outerRadius,u=r.cornerRadius,l=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,h=r.className;if(c<i||f===p)return null;var d=(0,O.A)("recharts-sector",h),y=c-i,v=R(u,y,0,!0);return e=v>0&&360>Math.abs(f-p)?sq({cx:n,cy:o,innerRadius:i,outerRadius:c,cornerRadius:Math.min(v,y/2),forceCornerRadius:l,cornerIsExternal:s,startAngle:f,endAngle:p}):sU({cx:n,cy:o,innerRadius:i,outerRadius:c,startAngle:f,endAngle:p}),a().createElement("path",sB({},tg(r,!0),{className:d,d:e,role:"img"}))},sW=["option","shapeType","propTransformer","activeClassName","isActive"];function sX(t){return(sX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sV(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sH(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sX(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sG(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return a().createElement(nO,r);case"trapezoid":return a().createElement(sN,r);case"sector":return a().createElement(s$,r);case"symbols":if("symbols"===e)return a().createElement(e$,r);break;default:return null}}function sY(t){var e,r=t.option,n=t.shapeType,o=t.propTransformer,c=t.activeClassName,u=t.isActive,l=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,sW);if((0,i.isValidElement)(r))e=(0,i.cloneElement)(r,sV(sV({},l),(0,i.isValidElement)(r)?r.props:r));else if(V()(r))e=r(l);else if(sS()(r)&&!sA()(r)){var s=(void 0===o?function(t,e){return sV(sV({},e),t)}:o)(r,l);e=a().createElement(sG,{shapeType:n,elementProps:s})}else e=a().createElement(sG,{shapeType:n,elementProps:l});return u?a().createElement(tz,{className:void 0===c?"recharts-active-shape":c},e):e}function sK(t,e){return null!=e&&"trapezoids"in t.props}function sZ(t,e){return null!=e&&"sectors"in t.props}function sJ(t,e){return null!=e&&"points"in t.props}function sQ(t,e){var r,n,o=t.x===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function s0(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function s1(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}var s2=["x","y"];function s5(t){return(s5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s3(){return(s3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s4(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s6(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s4(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=s5(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s5(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s4(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function s8(t,e){var r=t.x,n=t.y,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,s2),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||o.height),10),u=parseInt("".concat(e.width||o.width),10);return s6(s6(s6(s6(s6({},e),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function s7(t){return a().createElement(sY,s3({shapeType:"rectangle",propTransformer:s8,activeClassName:"recharts-active-bar"},t))}var s9=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o="number"==typeof r;return o?t(r,n):(o||tD(!1),e)}},ft=["value","background"];function fe(t){return(fe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fr(){return(fr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fo(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fn(Object(r),!0).forEach(function(e){fl(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fn(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fi(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fs(n.key),n)}}function fa(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fa=function(){return!!t})()}function fc(t){return(fc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fu(t,e){return(fu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fl(t,e,r){return(e=fs(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fs(t){var e=function(t,e){if("object"!=fe(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fe(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fe(e)?e:e+""}var ff=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=fc(e),fl(t=function(t,e){if(e&&("object"===fe(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,fa()?Reflect.construct(e,r||[],fc(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),fl(t,"id",L("recharts-bar-")),fl(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),fl(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fu(t,e)}(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,o=r.dataKey,i=r.activeIndex,c=r.activeBar,u=tg(this.props,!1);return t&&t.map(function(t,r){var l=r===i,s=fo(fo(fo({},u),t),{},{isActive:l,option:l?c:n,index:r,dataKey:o,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return a().createElement(tz,fr({className:"recharts-bar-rectangle"},to(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value)}),a().createElement(s7,s))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,o=e.isAnimationActive,i=e.animationBegin,c=e.animationDuration,u=e.animationEasing,l=e.animationId,s=this.state.prevData;return a().createElement(np,{begin:i,duration:c,isActive:o,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var o=e.t,i=r.map(function(t,e){var r=s&&s[e];if(r){var i=q(r.x,t.x),a=q(r.y,t.y),c=q(r.width,t.width),u=q(r.height,t.height);return fo(fo({},t),{},{x:i(o),y:a(o),width:c(o),height:u(o)})}if("horizontal"===n){var l=q(0,t.height)(o);return fo(fo({},t),{},{y:t.y+t.height-l,height:l})}var f=q(0,t.width)(o);return fo(fo({},t),{},{width:f})});return a().createElement(tz,null,t.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!c2()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,o=e.activeIndex,i=tg(this.props.background,!1);return r.map(function(e,r){e.value;var c=e.background,u=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,ft);if(!c)return null;var l=fo(fo(fo(fo(fo({},u),{},{fill:"#eee"},c),i),to(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return a().createElement(s7,fr({key:"background-bar-".concat(r),option:t.props.background,isActive:r===o},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,i=r.yAxis,c=r.layout,u=td(r.children,uG);if(!u)return null;var l="vertical"===c?n[0].height/2:n[0].width/2,s=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:u4(t,e)}};return a().createElement(tz,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:c,offset:l,dataPointFormatter:s})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,o=t.xAxis,i=t.yAxis,c=t.left,u=t.top,l=t.width,s=t.height,f=t.isAnimationActive,p=t.background,h=t.id;if(e||!r||!r.length)return null;var d=this.state.isAnimationFinished,y=(0,O.A)("recharts-bar",n),v=o&&o.allowDataOverflow,m=i&&i.allowDataOverflow,b=v||m,g=X()(h)?this.id:h;return a().createElement(tz,{className:y},v||m?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(g)},a().createElement("rect",{x:v?c:c-l/2,y:m?u:u-s/2,width:v?l:2*l,height:m?s:2*s}))):null,a().createElement(tz,{className:"recharts-bar-rectangles",clipPath:b?"url(#clipPath-".concat(g,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(b,g),(!f||d)&&sw.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&fi(n.prototype,e),r&&fi(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function fp(t){return(fp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fh(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fm(n.key),n)}}function fd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fy(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fd(Object(r),!0).forEach(function(e){fv(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fd(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fv(t,e,r){return(e=fm(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fm(t){var e=function(t,e){if("object"!=fp(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fp(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fp(e)?e:e+""}fl(ff,"displayName","Bar"),fl(ff,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!t8.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),fl(ff,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,h=lp(n,r);if(!h)return null;var d=e.layout,y=r.type.defaultProps,v=void 0!==y?fo(fo({},y),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===d?a:i,O=l?x.scale.domain():null,w=lx({numericAxis:x}),j=td(b,ss),S=f.map(function(t,e){l?f=lh(l[s+e],O):Array.isArray(f=u4(t,m))||(f=[w,f]);var n=s9(g,ff.defaultProps.minPointSize)(f[1],e);if("horizontal"===d){var f,p,y,v,b,x,S,P=[a.scale(f[0]),a.scale(f[1])],A=P[0],E=P[1];p=lg({axis:i,ticks:c,bandSize:o,offset:h.offset,entry:t,index:e}),y=null!==(S=null!=E?E:A)&&void 0!==S?S:void 0,v=h.size;var k=A-E;if(b=Number.isNaN(k)?0:k,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var M=C(b||n)*(Math.abs(n)-Math.abs(b));y-=M,b+=M}}else{var _=[i.scale(f[0]),i.scale(f[1])],T=_[0],D=_[1];if(p=T,y=lg({axis:a,ticks:u,bandSize:o,offset:h.offset,entry:t,index:e}),v=D-T,b=h.size,x={x:i.x,y:y,width:i.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var N=C(v||n)*(Math.abs(n)-Math.abs(v));v+=N}}return fo(fo(fo({},t),{},{x:p,y:y,width:v,height:b,value:l?f:f[1],payload:t,background:x},j&&j[e]&&j[e].props),{},{tooltipPayload:[lk(r,t)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return fo({data:S,layout:d},p)});var fb=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},fg=function(){var t,e;function r(t){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,r),this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&fh(r.prototype,t),e&&fh(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();fv(fg,"EPS",1e-4);var fx=function(t){var e=Object.keys(t).reduce(function(e,r){return fy(fy({},e),{},fv({},r,fg.create(t[r])))},{});return fy(fy({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return sc()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return sl()(t,function(t,r){return e[r].isInRange(t)})}})},fO=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/e);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o))};function fw(){return(fw=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fj(t){return(fj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fS(Object(r),!0).forEach(function(e){fM(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fS(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fA(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fA=function(){return!!t})()}function fE(t){return(fE=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fk(t,e){return(fk=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fM(t,e,r){return(e=f_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f_(t){var e=function(t,e){if("object"!=fj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fj(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fj(e)?e:e+""}var fT=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=fx({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return si(t,"discard")&&!i.isInRange(a)?null:a},fC=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=fE(t),function(t,e){if(e&&("object"===fj(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,fA()?Reflect.construct(t,e||[],fE(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fk(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,o=t.r,i=t.alwaysShow,c=t.clipPathId,u=I(e),l=I(n);if($(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!l)return null;var s=fT(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,d=h.shape,y=h.className,v=fP(fP({clipPath:si(this.props,"hidden")?"url(#".concat(c,")"):void 0},tg(this.props,!0)),{},{cx:f,cy:p});return a().createElement(tz,{className:(0,O.A)("recharts-reference-dot",y)},r.renderDot(d,v),sn.renderCallByParent(this.props,{x:f-o,y:p-o,width:2*o,height:2*o}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,f_(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);fM(fC,"displayName","ReferenceDot"),fM(fC,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fM(fC,"renderDot",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):V()(t)?t(e):a().createElement(rr,fw({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var fD=r(67367),fN=r.n(fD),fI=r(22964),fB=r.n(fI),fL=r(86451),fR=r.n(fL)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),fz=(0,i.createContext)(void 0),fU=(0,i.createContext)(void 0),fq=(0,i.createContext)(void 0),fF=(0,i.createContext)({}),f$=(0,i.createContext)(void 0),fW=(0,i.createContext)(0),fX=(0,i.createContext)(0),fH=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,o=e.offset,i=t.clipPathId,c=t.children,u=t.width,l=t.height,s=fR(o);return a().createElement(fz.Provider,{value:r},a().createElement(fU.Provider,{value:n},a().createElement(fF.Provider,{value:o},a().createElement(fq.Provider,{value:s},a().createElement(f$.Provider,{value:i},a().createElement(fW.Provider,{value:l},a().createElement(fX.Provider,{value:u},c)))))))},fV=function(t){var e=(0,i.useContext)(fz);null==e&&tD(!1);var r=e[t];return null==r&&tD(!1),r},fG=function(){var t=(0,i.useContext)(fU);return fB()(t,function(t){return sl()(t.domain,Number.isFinite)})||z(t)},fY=function(t){var e=(0,i.useContext)(fU);null==e&&tD(!1);var r=e[t];return null==r&&tD(!1),r},fK=function(){return(0,i.useContext)(fX)},fZ=function(){return(0,i.useContext)(fW)};function fJ(t){return(fJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fQ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fQ=function(){return!!t})()}function f0(t){return(f0=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f1(t,e){return(f1=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function f2(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f5(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f2(Object(r),!0).forEach(function(e){f3(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f2(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function f3(t,e,r){return(e=f4(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f4(t){var e=function(t,e){if("object"!=fJ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fJ(e)?e:e+""}function f6(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f8(){return(f8=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var f7=function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):V()(t)?t(e):a().createElement("line",f8({},e,{className:"recharts-reference-line-line"}))},f9=function(t,e,r,n,o,i,a,c,u){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=u.y,d=t.y.apply(h,{position:i});if(si(u,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:i});if(si(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:i})});return si(u,"discard")&&fN()(g,function(e){return!t.isInRange(e)})?null:g}return null};function pt(t){var e,r=t.x,n=t.y,o=t.segment,c=t.xAxisId,u=t.yAxisId,l=t.shape,s=t.className,f=t.alwaysShow,p=(0,i.useContext)(f$),h=fV(c),d=fY(u),y=(0,i.useContext)(fq);if(!p||!y)return null;$(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var v=f9(fx({x:h.scale,y:d.scale}),I(r),I(n),o&&2===o.length,y,t.position,h.orientation,d.orientation,t);if(!v)return null;var m=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return f6(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f6(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),b=m[0],g=b.x,x=b.y,w=m[1],j=w.x,S=w.y,P=f5(f5({clipPath:si(t,"hidden")?"url(#".concat(p,")"):void 0},tg(t,!0)),{},{x1:g,y1:x,x2:j,y2:S});return a().createElement(tz,{className:(0,O.A)("recharts-reference-line",s)},f7(l,P),sn.renderCallByParent(t,fb({x:(e={x1:g,y1:x,x2:j,y2:S}).x1,y:e.y1},{x:e.x2,y:e.y2})))}var pe=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=f0(t),function(t,e){if(e&&("object"===fJ(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,fQ()?Reflect.construct(t,e||[],f0(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&f1(t,e)}(r,t),e=[{key:"render",value:function(){return a().createElement(pt,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,f4(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function pr(){return(pr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pn(t){return(pn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function po(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pi(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?po(Object(r),!0).forEach(function(e){pl(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):po(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}f3(pe,"displayName","ReferenceLine"),f3(pe,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function pa(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(pa=function(){return!!t})()}function pc(t){return(pc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function pu(t,e){return(pu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pl(t,e,r){return(e=ps(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ps(t){var e=function(t,e){if("object"!=pn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pn(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pn(e)?e:e+""}var pf=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=fx({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!si(o,"discard")||f.isInRange(p)&&f.isInRange(h)?fb(p,h):null},pp=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=pc(t),function(t,e){if(e&&("object"===pn(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,pa()?Reflect.construct(t,e||[],pc(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&pu(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,o=t.y1,i=t.y2,c=t.className,u=t.alwaysShow,l=t.clipPathId;$(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=I(e),f=I(n),p=I(o),h=I(i),d=this.props.shape;if(!s&&!f&&!p&&!h&&!d)return null;var y=pf(s,f,p,h,this.props);if(!y&&!d)return null;var v=si(this.props,"hidden")?"url(#".concat(l,")"):void 0;return a().createElement(tz,{className:(0,O.A)("recharts-reference-area",c)},r.renderRect(d,pi(pi({clipPath:v},tg(this.props,!0)),y)),sn.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ps(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function ph(t){return function(t){if(Array.isArray(t))return pd(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return pd(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pd(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pd(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}pl(pp,"displayName","ReferenceArea"),pl(pp,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),pl(pp,"renderRect",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):V()(t)?t(e):a().createElement(nO,pr({},e,{className:"recharts-reference-area-rect"}))});var py=function(t,e,r,n,o){var i=td(t,pe),a=td(t,fC),c=[].concat(ph(i),ph(a)),u=td(t,pp),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&si(e.props,"extendDomain")&&N(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&si(e.props,"extendDomain")&&N(e.props[p])&&N(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return N(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},pv=r(11117),pm=new(r.n(pv)()),pb="recharts.syncMouseEvents";function pg(t){return(pg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function px(t,e,r){return(e=pO(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pO(t){var e=function(t,e){if("object"!=pg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pg(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pg(e)?e:e+""}var pw=function(){var t,e;return t=function t(){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,t),px(this,"activeIndex",0),px(this,"coordinateList",[]),px(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!=n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,u=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pO(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function pj(){}function pS(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function pP(t){this._context=t}function pA(t){this._context=t}function pE(t){this._context=t}pP.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:pS(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:pS(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pA.prototype={areaStart:pj,areaEnd:pj,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:pS(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pE.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:pS(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class pk{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function pM(t){this._context=t}function p_(t){this._context=t}function pT(t){return new p_(t)}pM.prototype={areaStart:pj,areaEnd:pj,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function pC(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function pD(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function pN(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,c=(i-n)/3;t._context.bezierCurveTo(n+c,o+c*e,i-c,a-c*r,i,a)}function pI(t){this._context=t}function pB(t){this._context=new pL(t)}function pL(t){this._context=t}function pR(t){this._context=t}function pz(t){var e,r,n=t.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[n-1]=(t[n]+o[n-1])/2;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function pU(t,e){this._context=t,this._t=e}function pq(t){return t[0]}function pF(t){return t[1]}function p$(t,e){var r=ek(!0),n=null,o=pT,i=null,a=eN(c);function c(c){var u,l,s,f=(c=cB(c)).length,p=!1;for(null==n&&(i=o(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,u,c),+e(l,u,c));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?pq:ek(t),e="function"==typeof e?e:void 0===e?pF:ek(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:ek(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:ek(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:ek(!!t),c):r},c.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),c):o},c.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),c):n},c}function pW(t,e,r){var n=null,o=ek(!0),i=null,a=pT,c=null,u=eN(l);function l(l){var s,f,p,h,d,y=(l=cB(l)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(c=a(d=u())),s=0;s<=y;++s){if(!(s<y&&o(h=l[s],s,l))===v){if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return c=null,d+""||null}function s(){return p$().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?pq:ek(+t),e="function"==typeof e?e:void 0===e?ek(0):ek(+e),r="function"==typeof r?r:void 0===r?pF:ek(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:ek(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:ek(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:ek(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:ek(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:ek(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:ek(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:ek(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(c=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=c=null:c=a(i=t),l):i},l}function pX(t){return(pX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pH(){return(pH=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pV(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=pX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pX(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}p_.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},pI.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:pN(this,this._t0,pD(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,pN(this,pD(this,r=pC(this,t,e)),r);break;default:pN(this,this._t0,r=pC(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(pB.prototype=Object.create(pI.prototype)).point=function(t,e){pI.prototype.point.call(this,e,t)},pL.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},pR.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r){if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=pz(t),o=pz(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},pU.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var pY={curveBasisClosed:function(t){return new pA(t)},curveBasisOpen:function(t){return new pE(t)},curveBasis:function(t){return new pP(t)},curveBumpX:function(t){return new pk(t,!0)},curveBumpY:function(t){return new pk(t,!1)},curveLinearClosed:function(t){return new pM(t)},curveLinear:pT,curveMonotoneX:function(t){return new pI(t)},curveMonotoneY:function(t){return new pB(t)},curveNatural:function(t){return new pR(t)},curveStep:function(t){return new pU(t,.5)},curveStepAfter:function(t){return new pU(t,1)},curveStepBefore:function(t){return new pU(t,0)}},pK=function(t){return t.x===+t.x&&t.y===+t.y},pZ=function(t){return t.x},pJ=function(t){return t.y},pQ=function(t,e){if(V()(t))return t;var r="curve".concat(ep()(t));return("curveMonotone"===r||"curveBump"===r)&&e?pY["".concat(r).concat("vertical"===e?"Y":"X")]:pY[r]||pT},p0=function(t){var e,r=t.type,n=t.points,o=void 0===n?[]:n,i=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,l=pQ(void 0===r?"linear":r,a),s=u?o.filter(function(t){return pK(t)}):o;if(Array.isArray(i)){var f=u?i.filter(function(t){return pK(t)}):i,p=s.map(function(t,e){return pG(pG({},t),{},{base:f[e]})});return(e="vertical"===a?pW().y(pJ).x1(pZ).x0(function(t){return t.base.x}):pW().x(pZ).y1(pJ).y0(function(t){return t.base.y})).defined(pK).curve(l),e(p)}return(e="vertical"===a&&N(i)?pW().y(pJ).x1(pZ).x0(i):N(i)?pW().x(pZ).y1(pJ).y0(i):p$().x(pZ).y(pJ)).defined(pK).curve(l),e(s)},p1=function(t){var e=t.className,r=t.points,n=t.path,o=t.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?p0(t):n;return a().createElement("path",pH({},tg(t,!1),tn(t),{className:(0,O.A)("recharts-curve",e),d:i,ref:o}))};function p2(t){return(p2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var p5=["x","y","top","left","width","height","className"];function p3(){return(p3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p4(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var p6=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,o=void 0===n?0:n,i=t.top,c=void 0===i?0:i,u=t.left,l=void 0===u?0:u,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,d=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p4(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=p2(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p2(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p4(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:o,top:c,left:l,width:f,height:h},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,p5));return N(r)&&N(o)&&N(f)&&N(h)&&N(c)&&N(l)?a().createElement("path",p3({},tg(y,!0),{className:(0,O.A)("recharts-cross",d),d:"M".concat(r,",").concat(c,"v").concat(h,"M").concat(l,",").concat(o,"h").concat(f)})):null};function p8(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[lJ(e,r,n,o),lJ(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}function p7(t){return(p7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p9(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ht(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p9(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=p7(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p7(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p9(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function he(t){var e,r,n,o,a=t.element,c=t.tooltipEventType,u=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,h=t.tooltipAxisBandSize,d=t.layout,y=t.chartName,v=null!==(r=a.props.cursor)&&void 0!==r?r:null===(n=a.type.defaultProps)||void 0===n?void 0:n.cursor;if(!a||!v||!u||!l||"ScatterChart"!==y&&"axis"!==c)return null;var m=p1;if("ScatterChart"===y)o=l,m=p6;else if("BarChart"===y)e=h/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===d?l.x-e:f.left+.5,y:"horizontal"===d?f.top+.5:l.y-e,width:"horizontal"===d?h:f.width-1,height:"horizontal"===d?f.height-1:h},m=nO;else if("radial"===d){var b=p8(l),g=b.cx,x=b.cy,w=b.radius;o={cx:g,cy:x,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:w,outerRadius:w},m=s$}else o={points:function(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return p8(e);var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=lJ(c,u,l,f),h=lJ(c,u,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}(d,l,f)},m=p1;var j=ht(ht(ht(ht({stroke:"#ccc",pointerEvents:"none"},f),o),tg(v,!1)),{},{payload:s,payloadIndex:p,className:(0,O.A)("recharts-tooltip-cursor",v.className)});return(0,i.isValidElement)(v)?(0,i.cloneElement)(v,j):(0,i.createElement)(m,j)}var hr=["item"],hn=["children","className","width","height","style","compact","title","desc"];function ho(t){return(ho="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hi(){return(hi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ha(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||hp(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hc(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function hu(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hu=function(){return!!t})()}function hl(t){return(hl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hs(t,e){return(hs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hf(t){return function(t){if(Array.isArray(t))return hh(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||hp(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hp(t,e){if(t){if("string"==typeof t)return hh(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hh(t,e)}}function hh(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hy(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hd(Object(r),!0).forEach(function(e){hv(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hd(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hv(t,e,r){return(e=hm(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hm(t){var e=function(t,e){if("object"!=ho(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ho(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ho(e)?e:e+""}var hb={xAxis:["bottom","top"],yAxis:["left","right"]},hg={width:"100%",height:"100%"},hx={x:0,y:0};function hO(t){return t}var hw=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return hy(hy(hy({},n),lJ(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return hy(hy(hy({},n),lJ(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return hx},hj=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(hf(t),hf(r)):t},[]);return i.length>0?i:t&&t.length&&N(n)&&N(o)?t.slice(n,o+1):[]};function hS(t){return"number"===t?[0,"auto"]:void 0}var hP=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=hj(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var u,l,s=null!==(u=c.props.data)&&void 0!==u?u:e;return(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),l=i.dataKey&&!i.allowDuplicatedCategory?F(void 0===s?a:s,i.dataKey,n):s&&s[r]||a[r])?[].concat(hf(o),[lk(c,l)]):o},[])},hA=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=u8(i,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=hP(t,e,l,s),p=hw(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},hE=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=li(l,o);return r.reduce(function(e,r){var h=void 0!==r.type.defaultProps?hy(hy({},r.type.defaultProps),r.props):r.props,d=h.type,y=h.dataKey,v=h.allowDataOverflow,m=h.allowDuplicatedCategory,b=h.scale,g=h.ticks,x=h.includeHidden,O=h[i];if(e[O])return e;var w=hj(t.data,{graphicalItems:n.filter(function(t){var e;return(i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i])===O}),dataStartIndex:c,dataEndIndex:u}),j=w.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&N(n)&&N(o))return!0}return!1})(h.domain,v,d)&&(A=lP(h.domain,null,v),p&&("number"===d||"auto"!==b)&&(k=u6(w,y,"category")));var S=hS(d);if(!A||0===A.length){var P,A,E,k,M,_=null!==(M=h.domain)&&void 0!==M?M:S;if(y){if(A=u6(w,y,d),"category"===d&&p){var T=U(A);m&&T?(E=A,A=t_()(0,j)):m||(A=lE(_,A,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(hf(t),[e])},[]))}else if("category"===d)A=m?A.filter(function(t){return""!==t&&!X()(t)}):lE(_,A,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||X()(e)?t:[].concat(hf(t),[e])},[]);else if("number"===d){var C=ln(w,n.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===O&&(x||!o)}),y,o,l);C&&(A=C)}p&&("number"===d||"auto"!==b)&&(k=u6(w,y,"category"))}else A=p?t_()(0,j):a&&a[O]&&a[O].hasStack&&"number"===d?"expand"===f?[0,1]:lw(a[O].stackGroups,c,u):lo(w,n.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===O&&(x||!r)}),d,l,!0);"number"===d?(A=py(s,A,O,o,g),_&&(A=lP(_,A,v))):"category"===d&&_&&A.every(function(t){return _.indexOf(t)>=0})&&(A=_)}return hy(hy({},e),{},hv({},O,hy(hy({},h),{},{axisType:o,domain:A,categoricalDomain:k,duplicateDomain:E,originalDomain:null!==(P=h.domain)&&void 0!==P?P:S,isCategorical:p,layout:l})))},{})},hk=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=hj(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,h=li(l,o),d=-1;return r.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?hy(hy({},e.type.defaultProps),e.props):e.props)[i],m=hS("number");return t[v]?t:(d++,y=h?t_()(0,p):a&&a[v]&&a[v].hasStack?py(s,y=lw(a[v].stackGroups,c,u),v,o):py(s,y=lP(m,lo(f,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===v&&!o}),"number",l),n.defaultProps.allowDataOverflow),v,o),hy(hy({},t),{},hv({},v,hy(hy({axisType:o},n.defaultProps),{},{hide:!0,orientation:M()(hb,"".concat(o,".").concat(d%2),null),domain:y,originalDomain:m,isCategorical:h,layout:l}))))},{})},hM=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=td(l,o),p={};return f&&f.length?p=hE(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):i&&i.length&&(p=hk(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},h_=function(t){var e=z(t),r=lc(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:tC()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:lA(e,r)}},hT=function(t){var e=t.children,r=t.defaultShowTooltip,n=ty(e,lV),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},hC=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},hD=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,f=r.margin||{},p=ty(s,lV),h=ty(s,rt),d=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:hy(hy({},t),{},hv({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:hy(hy({},t),{},hv({},n,M()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=hy(hy({},y),d),m=v.bottom;p&&(v.bottom+=p.props.height||lV.defaultProps.height),h&&e&&(v=le(v,n,r,e));var b=u-v.left-v.right,g=l-v.top-v.bottom;return hy(hy({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},hN=["type","layout","connectNulls","ref"],hI=["key"];function hB(t){return(hB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hL(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function hR(){return(hR=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hz(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hU(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hz(Object(r),!0).forEach(function(e){hV(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hz(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hq(t){return function(t){if(Array.isArray(t))return hF(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return hF(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hF(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hF(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h$(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hG(n.key),n)}}function hW(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hW=function(){return!!t})()}function hX(t){return(hX=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hH(t,e){return(hH=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hV(t,e,r){return(e=hG(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hG(t){var e=function(t,e){if("object"!=hB(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hB(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hB(e)?e:e+""}var hY=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=hX(e),hV(t=function(t,e){if(e&&("object"===hB(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,hW()?Reflect.construct(e,r||[],hX(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!0,totalLength:0}),hV(t,"generateSimpleStrokeDasharray",function(t,e){return"".concat(e,"px ").concat(t-e,"px")}),hV(t,"getStrokeDasharray",function(e,r,o){var i=o.reduce(function(t,e){return t+e});if(!i)return t.generateSimpleStrokeDasharray(r,e);for(var a=Math.floor(e/i),c=e%i,u=r-e,l=[],s=0,f=0;s<o.length;f+=o[s],++s)if(f+o[s]>c){l=[].concat(hq(o.slice(0,s)),[c-f]);break}var p=l.length%2==0?[0,u]:[u];return[].concat(hq(n.repeat(o,a)),hq(l),p).map(function(t){return"".concat(t,"px")}).join(", ")}),hV(t,"id",L("recharts-line-")),hV(t,"pathRef",function(e){t.mainCurve=e}),hV(t,"handleAnimationEnd",function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()}),hV(t,"handleAnimationStart",function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&hH(t,e)}(n,t),e=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(t){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.points,o=r.xAxis,i=r.yAxis,c=r.layout,u=td(r.children,uG);if(!u)return null;var l=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:u4(t.payload,e)}};return a().createElement(tz,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:c,dataPointFormatter:l})}))}},{key:"renderDots",value:function(t,e,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var o=this.props,i=o.dot,c=o.points,u=o.dataKey,l=tg(this.props,!1),s=tg(i,!0),f=c.map(function(t,e){var r=hU(hU(hU({key:"dot-".concat(e),r:3},l),s),{},{value:t.value,dataKey:u,cx:t.x,cy:t.y,index:e,payload:t.payload});return n.renderDotItem(i,r)}),p={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(r,")"):null};return a().createElement(tz,hR({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(t,e,r,n){var o=this.props,i=o.type,c=o.layout,u=o.connectNulls,l=hU(hU(hU({},tg((o.ref,hL(o,hN)),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(r,")"):null,points:t},n),{},{type:i,layout:c,connectNulls:u});return a().createElement(p1,hR({},l,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var r=this,n=this.props,o=n.points,i=n.strokeDasharray,c=n.isAnimationActive,u=n.animationBegin,l=n.animationDuration,s=n.animationEasing,f=n.animationId,p=n.animateNewValues,h=n.width,d=n.height,y=this.state,v=y.prevPoints,m=y.totalLength;return a().createElement(np,{begin:u,duration:l,isActive:c,easing:s,from:{t:0},to:{t:1},key:"line-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var a,c=n.t;if(v){var u=v.length/o.length,l=o.map(function(t,e){var r=Math.floor(e*u);if(v[r]){var n=v[r],o=q(n.x,t.x),i=q(n.y,t.y);return hU(hU({},t),{},{x:o(c),y:i(c)})}if(p){var a=q(2*h,t.x),l=q(d/2,t.y);return hU(hU({},t),{},{x:a(c),y:l(c)})}return hU(hU({},t),{},{x:t.x,y:t.y})});return r.renderCurveStatically(l,t,e)}var s=q(0,m)(c);if(i){var f="".concat(i).split(/[,\s]+/gim).map(function(t){return parseFloat(t)});a=r.getStrokeDasharray(s,m,f)}else a=r.generateSimpleStrokeDasharray(m,s);return r.renderCurveStatically(o,t,e,{strokeDasharray:a})})}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,o=r.isAnimationActive,i=this.state,a=i.prevPoints,c=i.totalLength;return o&&n&&n.length&&(!a&&c>0||!c2()(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,o=e.points,i=e.className,c=e.xAxis,u=e.yAxis,l=e.top,s=e.left,f=e.width,p=e.height,h=e.isAnimationActive,d=e.id;if(r||!o||!o.length)return null;var y=this.state.isAnimationFinished,v=1===o.length,m=(0,O.A)("recharts-line",i),b=c&&c.allowDataOverflow,g=u&&u.allowDataOverflow,x=b||g,w=X()(d)?this.id:d,j=null!==(t=tg(n,!1))&&void 0!==t?t:{r:3,strokeWidth:2},S=j.r,P=j.strokeWidth,A=(n&&"object"===tu(n)&&"clipDot"in n?n:{}).clipDot,E=void 0===A||A,k=2*(void 0===S?3:S)+(void 0===P?2:P);return a().createElement(tz,{className:m},b||g?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(w)},a().createElement("rect",{x:b?s:s-f/2,y:g?l:l-p/2,width:b?f:2*f,height:g?p:2*p})),!E&&a().createElement("clipPath",{id:"clipPath-dots-".concat(w)},a().createElement("rect",{x:s-k/2,y:l-k/2,width:f+k,height:p+k}))):null,!v&&this.renderCurve(x,w),this.renderErrorBar(x,w),(v||n)&&this.renderDots(x,E,w),(!h||y)&&sw.renderCallByParent(this.props,o))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!=0?[].concat(hq(t),[0]):t,n=[],o=0;o<e;++o)n=[].concat(hq(n),hq(r));return n}},{key:"renderDotItem",value:function(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(V()(t))r=t(e);else{var n=e.key,o=hL(e,hI),i=(0,O.A)("recharts-line-dot","boolean"!=typeof t?t.className:"");r=a().createElement(rr,hR({key:n},o,{className:i}))}return r}}],e&&h$(n.prototype,e),r&&h$(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function hK(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e){if(void 0!==r&&!0!==r(t[o]))return;n.push(t[o])}return n}function hZ(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function hJ(t){return(hJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h0(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hQ(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=hJ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hJ(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h1(t,e,r){var n,o,i,a,c,u=t.tick,l=t.ticks,s=t.viewBox,f=t.minTickGap,p=t.orientation,h=t.interval,d=t.tickFormatter,y=t.unit,v=t.angle;if(!l||!l.length||!u)return[];if(N(h)||t8.isSsr)return hK(l,("number"==typeof h&&N(h)?h:0)+1);var m=[],b="top"===p||"bottom"===p?"width":"height",g=y&&"width"===b?nR(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},x=function(t,n){var o,i=V()(d)?d(t.value,n):t.value;return"width"===b?fO({width:(o=nR(i,{fontSize:e,letterSpacing:r})).width+g.width,height:o.height+g.height},v):nR(i,{fontSize:e,letterSpacing:r})[b]},O=l.length>=2?C(l[1].coordinate-l[0].coordinate):1,w=(n="width"===b,o=s.x,i=s.y,a=s.width,c=s.height,1===O?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i});return"equidistantPreserveStart"===h?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c;s<=a.length;)if(i=function(){var e,i=null==n?void 0:n[l];if(void 0===i)return{v:hK(n,s)};var a=l,p=function(){return void 0===e&&(e=r(i,a)),e},h=i.coordinate,d=0===l||hZ(t,h,p,f,u);d||(l=0,f=c,s+=1),d&&(f=h+t*(p()/2+o),l+=s)}())return i.v;return[]}(O,w,x,l,f):("preserveStart"===h||"preserveStartEnd"===h?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(i){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=h0(h0({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),hZ(t,s.tickCoord,function(){return f},u,l)&&(l=s.tickCoord-t*(f/2+o),a[c-1]=h0(h0({},s),{},{isShow:!0}))}for(var h=i?c-1:c,d=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var s=t*(i.coordinate-t*c()/2-u);a[e]=i=h0(h0({},i),{},{tickCoord:s<0?i.coordinate-s*t:i.coordinate})}else a[e]=i=h0(h0({},i),{},{tickCoord:i.coordinate});hZ(t,i.tickCoord,c,u,l)&&(u=i.tickCoord+t*(c()/2+o),a[e]=h0(h0({},i),{},{isShow:!0}))},y=0;y<h;y++)d(y);return a}(O,w,x,l,f,"preserveStartEnd"===h):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,l=function(e){var n,l=i[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);i[e]=l=h0(h0({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else i[e]=l=h0(h0({},l),{},{tickCoord:l.coordinate});hZ(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+o),i[e]=h0(h0({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return i}(O,w,x,l,f)).filter(function(t){return t.isShow})}hV(hY,"displayName","Line"),hV(hY,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!t8.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),hV(hY,"getComposedData",function(t){var e=t.props,r=t.xAxis,n=t.yAxis,o=t.xAxisTicks,i=t.yAxisTicks,a=t.dataKey,c=t.bandSize,u=t.displayedData,l=t.offset,s=e.layout;return hU({points:u.map(function(t,e){var u=u4(t,a);return"horizontal"===s?{x:lb({axis:r,ticks:o,bandSize:c,entry:t,index:e}),y:X()(u)?null:n.scale(u),value:u,payload:t}:{x:X()(u)?null:r.scale(u),y:lb({axis:n,ticks:i,bandSize:c,entry:t,index:e}),value:u,payload:t}}),layout:s},l)});var h2=["viewBox"],h5=["viewBox"],h3=["ticks"];function h4(t){return(h4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h6(){return(h6=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function h8(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h7(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h8(Object(r),!0).forEach(function(e){di(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h8(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h9(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function dt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,da(n.key),n)}}function de(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(de=function(){return!!t})()}function dr(t){return(dr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dn(t,e){return(dn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function di(t,e,r){return(e=da(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function da(t){var e=function(t,e){if("object"!=h4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h4(e)?e:e+""}var dc=function(t){var e,r;function n(t){var e,r,o;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[t],r=dr(r),(e=function(t,e){if(e&&("object"===h4(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,de()?Reflect.construct(r,o||[],dr(this).constructor):r.apply(this,o))).state={fontSize:"",letterSpacing:""},e}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&dn(t,e)}(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=h9(t,h2),o=this.props,i=o.viewBox,a=h9(o,h5);return!Z(r,i)||!Z(n,a)||!Z(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,d=c.mirror,y=c.tickMargin,v=d?-1:1,m=t.tickSize||h,b=N(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+ +!d*f)-v*m)-v*y,i=b;break;case"left":n=o=t.coordinate,i=(e=(r=u+ +!d*s)-v*m)-v*y,a=b;break;case"right":n=o=t.coordinate,i=(e=(r=u+ +d*s)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(o=l+ +d*f)+v*m)+v*y,i=b}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.orientation,c=t.mirror,u=t.axisLine,l=h7(h7(h7({},tg(this.props,!1)),tg(u,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var s=+("top"===i&&!c||"bottom"===i&&c);l=h7(h7({},l),{},{x1:e,y1:r+s*o,x2:e+n,y2:r+s*o})}else{var f=+("left"===i&&!c||"right"===i&&c);l=h7(h7({},l),{},{x1:e+f*n,y1:r,x2:e+f*n,y2:r+o})}return a().createElement("line",h6({},l,{className:(0,O.A)("recharts-cartesian-axis-line",M()(u,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var o=this,i=this.props,c=i.tickLine,u=i.stroke,l=i.tick,s=i.tickFormatter,f=i.unit,p=h1(h7(h7({},this.props),{},{ticks:t}),e,r),h=this.getTickTextAnchor(),d=this.getTickVerticalAnchor(),y=tg(this.props,!1),v=tg(l,!1),m=h7(h7({},y),{},{fill:"none"},tg(c,!1)),b=p.map(function(t,e){var r=o.getTickLineCoord(t),i=r.line,b=r.tick,g=h7(h7(h7(h7({textAnchor:h,verticalAnchor:d},y),{},{stroke:"none",fill:u},v),b),{},{index:e,payload:t,visibleTicksCount:p.length,tickFormatter:s});return a().createElement(tz,h6({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},to(o.props,t,e)),c&&a().createElement("line",h6({},m,i,{className:(0,O.A)("recharts-cartesian-axis-tick-line",M()(c,"className"))})),l&&n.renderTickItem(l,g,"".concat(V()(s)?s(t.value,e):t.value).concat(f||"")))});return a().createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,o=e.height,i=e.ticksGenerator,c=e.className;if(e.hide)return null;var u=this.props,l=u.ticks,s=h9(u,h3),f=l;return(V()(i)&&(f=i(l&&l.length>0?this.props:s)),n<=0||o<=0||!f||!f.length)?null:a().createElement(tz,{className:(0,O.A)("recharts-cartesian-axis",c),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),sn.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return a().isValidElement(t)?a().cloneElement(t,e):V()(t)?t(e):a().createElement(oe,h6({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&dt(n.prototype,e),r&&dt(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);function du(t){return(du="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}di(dc,"displayName","CartesianAxis"),di(dc,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function dl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dl=function(){return!!t})()}function ds(t){return(ds=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function df(t,e){return(df=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dp(t,e,r){return(e=dh(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dh(t){var e=function(t,e){if("object"!=du(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=du(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==du(e)?e:e+""}function dd(){return(dd=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dy(t){var e=t.xAxisId,r=fK(),n=fZ(),o=fV(e);return null==o?null:a().createElement(dc,dd({},o,{className:(0,O.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return lc(t,!0)}}))}var dv=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=ds(t),function(t,e){if(e&&("object"===du(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,dl()?Reflect.construct(t,e||[],ds(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&df(t,e)}(r,t),e=[{key:"render",value:function(){return a().createElement(dy,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dh(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function dm(t){return(dm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}dp(dv,"displayName","XAxis"),dp(dv,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function db(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(db=function(){return!!t})()}function dg(t){return(dg=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dx(t,e){return(dx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dO(t,e,r){return(e=dw(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dw(t){var e=function(t,e){if("object"!=dm(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dm(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dm(e)?e:e+""}function dj(){return(dj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var dS=function(t){var e=t.yAxisId,r=fK(),n=fZ(),o=fY(e);return null==o?null:a().createElement(dc,dj({},o,{className:(0,O.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return lc(t,!0)}}))},dP=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=dg(t),function(t,e){if(e&&("object"===dm(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,db()?Reflect.construct(t,e||[],dg(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&dx(t,e)}(r,t),e=[{key:"render",value:function(){return a().createElement(dS,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dw(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);dO(dP,"displayName","YAxis"),dO(dP,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var dA=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,o=void 0===n?"axis":n,c=t.validateTooltipEventTypes,u=void 0===c?["axis"]:c,l=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,p=t.defaultProps,h=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,u=t.barSize,s=t.layout,f=t.barGap,p=t.barCategoryGap,h=t.maxBarSize,d=hC(s),y=d.numericAxisName,v=d.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=ts(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,d){var g=hj(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),x=void 0!==r.type.defaultProps?hy(hy({},r.type.defaultProps),r.props):r.props,O=x.dataKey,w=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(v,"Id")],P=l.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||tD(!1);var i=n[o];return hy(hy({},t),{},hv(hv({},r.axisType,i),"".concat(r.axisType,"Ticks"),lc(i)))},{}),A=P[v],E=P["".concat(v,"Ticks")],k=n&&n[j]&&n[j].hasStack&&lO(r,n[j].stackGroups),M=ts(r.type).indexOf("Bar")>=0,_=lA(A,E),T=[],C=m&&u9({barSize:u,stackGroups:n,totalSize:"xAxis"===v?P[v].width:"yAxis"===v?P[v].height:void 0});if(M){var D,N,I=X()(w)?h:w,B=null!==(D=null!==(N=lA(A,E,!0))&&void 0!==N?N:I)&&void 0!==D?D:0;T=lt({barGap:f,barCategoryGap:p,bandSize:B!==_?B:_,sizeList:C[S],maxBarSize:I}),B!==_&&(T=T.map(function(t){return hy(hy({},t),{},{position:hy(hy({},t.position),{},{offset:t.position.offset-B/2})})}))}var L=r&&r.type&&r.type.getComposedData;L&&b.push({props:hy(hy({},L(hy(hy({},P),{},{displayedData:g,props:t,dataKey:O,item:r,bandSize:_,barPosition:T,offset:o,stackedData:k,layout:s,dataStartIndex:a,dataEndIndex:c}))),{},hv(hv(hv({key:r.key||"item-".concat(d)},y,P[y]),v,P[v]),"animationId",i)),childIndex:th(t.children).indexOf(r),item:r})}),b},d=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!tv({props:o}))return null;var u=o.children,s=o.layout,p=o.stackOffset,d=o.data,y=o.reverseStackOrder,v=hC(s),m=v.numericAxisName,b=v.cateAxisName,g=td(u,r),x=lv(d,g,"".concat(m,"Id"),"".concat(b,"Id"),p,y),O=l.reduce(function(t,e){var r="".concat(e.axisType,"Map");return hy(hy({},t),{},hv({},r,hM(o,hy(hy({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),w=hD(hy(hy({},O),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(O).forEach(function(t){O[t]=f(o,O[t],w,t.replace("Map",""),e)});var j=h_(O["".concat(b,"Map")]),S=h(o,hy(hy({},O),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:x,offset:w}));return hy(hy({formattedGraphicalItems:S,graphicalItems:g,offset:w,stackGroups:x},j),O)},y=function(t){var r;function n(t){var r,o,c,u,l;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),u=n,l=[t],u=hl(u),hv(c=function(t,e){if(e&&("object"===ho(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,hu()?Reflect.construct(u,l||[],hl(this).constructor):u.apply(this,l)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),hv(c,"accessibilityManager",new pw),hv(c,"handleLegendBBoxUpdate",function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;c.setState(hy({legendBBox:t},d({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:o},hy(hy({},c.state),{},{legendBBox:t}))))}}),hv(c,"handleReceiveSyncEvent",function(t,e,r){c.props.syncId===t&&(r!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(e)}),hv(c,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return hy({dataStartIndex:e,dataEndIndex:r},d({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),hv(c,"handleMouseEnter",function(t){var e=c.getMouseInfo(t);if(e){var r=hy(hy({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;V()(n)&&n(r,t)}}),hv(c,"triggeredAfterMouseMove",function(t){var e=c.getMouseInfo(t),r=e?hy(hy({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;V()(n)&&n(r,t)}),hv(c,"handleItemMouseEnter",function(t){c.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),hv(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),hv(c,"handleMouseMove",function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)}),hv(c,"handleMouseLeave",function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;V()(r)&&r(e,t)}),hv(c,"handleOuterEvent",function(t){var e,r,n=tj(t),o=M()(c.props,"".concat(n));n&&V()(o)&&o(null!==(e=/.*touch.*/i.test(n)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))&&void 0!==e?e:{},t)}),hv(c,"handleClick",function(t){var e=c.getMouseInfo(t);if(e){var r=hy(hy({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;V()(n)&&n(r,t)}}),hv(c,"handleMouseDown",function(t){var e=c.props.onMouseDown;V()(e)&&e(c.getMouseInfo(t),t)}),hv(c,"handleMouseUp",function(t){var e=c.props.onMouseUp;V()(e)&&e(c.getMouseInfo(t),t)}),hv(c,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),hv(c,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])}),hv(c,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])}),hv(c,"handleDoubleClick",function(t){var e=c.props.onDoubleClick;V()(e)&&e(c.getMouseInfo(t),t)}),hv(c,"handleContextMenu",function(t){var e=c.props.onContextMenu;V()(e)&&e(c.getMouseInfo(t),t)}),hv(c,"triggerSyncEvent",function(t){void 0!==c.props.syncId&&pm.emit(pb,c.props.syncId,t,c.eventEmitterSymbol)}),hv(c,"applySyncEvent",function(t){var e=c.props,r=e.layout,n=e.syncMethod,o=c.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(hy({dataStartIndex:i,dataEndIndex:a},d({props:c.props,dataStartIndex:i,dataEndIndex:a,updateId:o},c.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=c.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var y=0;y<h.length;y++)if(h[y].value===t.activeLabel){s=y;break}}var v=hy(hy({},p),{},{x:p.left,y:p.top}),m=Math.min(u,v.x+v.width),b=Math.min(l,v.y+v.height),g=h[s]&&h[s].value,x=hP(c.state,c.props.data,s),O=h[s]?{x:"horizontal"===r?h[s].coordinate:m,y:"horizontal"===r?b:h[s].coordinate}:hx;c.setState(hy(hy({},t),{},{activeLabel:g,activeCoordinate:O,activePayload:x,activeTooltipIndex:s}))}else c.setState(t)}),hv(c,"renderCursor",function(t){var r,n=c.state,o=n.isTooltipActive,i=n.activeCoordinate,u=n.activePayload,l=n.offset,s=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=c.getTooltipEventType(),h=null!==(r=t.props.active)&&void 0!==r?r:o,d=c.props.layout,y=t.key||"_recharts-cursor";return a().createElement(he,{key:y,activeCoordinate:i,activePayload:u,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),hv(c,"renderPolarAxis",function(t,e,r){var n=M()(t,"type.axisType"),o=M()(c.state,"".concat(n,"Map")),a=t.type.defaultProps,u=void 0!==a?hy(hy({},a),t.props):t.props,l=o&&o[u["".concat(n,"Id")]];return(0,i.cloneElement)(t,hy(hy({},l),{},{className:(0,O.A)(n,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:lc(l,!0)}))}),hv(c,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,o=e.polarRadius,a=c.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=z(u),f=z(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,i.cloneElement)(t,{polarAngles:Array.isArray(n)?n:lc(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(o)?o:lc(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),hv(c,"renderLegend",function(){var t=c.state.formattedGraphicalItems,e=c.props,r=e.children,n=e.width,o=e.height,a=c.props.margin||{},u=uJ({children:r,formattedGraphicalItems:t,legendWidth:n-(a.left||0)-(a.right||0),legendContent:s});if(!u)return null;var l=u.item,f=hc(u,hr);return(0,i.cloneElement)(l,hy(hy({},f),{},{chartWidth:n,chartHeight:o,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),hv(c,"renderTooltip",function(){var t,e=c.props,r=e.children,n=e.accessibilityLayer,o=ty(r,es);if(!o)return null;var a=c.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!==(t=o.props.active)&&void 0!==t?t:u;return(0,i.cloneElement)(o,{viewBox:hy(hy({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:n})}),hv(c,"renderBrush",function(t){var e=c.props,r=e.margin,n=e.data,o=c.state,a=o.offset,u=o.dataStartIndex,l=o.dataEndIndex,s=o.updateId;return(0,i.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:ll(c.handleBrushChange,t.props.onChange),data:n,x:N(t.props.x)?t.props.x:a.left,y:N(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:N(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),hv(c,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=c.clipPathId,o=c.state,a=o.xAxisMap,u=o.yAxisMap,l=o.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,i.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:u[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:n})}),hv(c,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?hy(hy({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=hy(hy({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:u7(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},tg(s,!1)),tn(s));return c.push(n.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(i))),o?c.push(n.renderActiveDot(s,hy(hy({},f),{},{cx:o.x,cy:o.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c}),hv(c,"renderGraphicChild",function(t,e,r){var n=c.filterFormatItem(t,e,r);if(!n)return null;var o=c.getTooltipEventType(),a=c.state,u=a.isTooltipActive,l=a.tooltipAxis,s=a.activeTooltipIndex,f=a.activeLabel,p=ty(c.props.children,es),h=n.props,d=h.points,y=h.isRange,v=h.baseLine,m=void 0!==n.item.type.defaultProps?hy(hy({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,x=m.activeBar,O=m.activeShape,w=!!(!g&&u&&p&&(b||x||O)),j={};"axis"!==o&&p&&"click"===p.props.trigger?j={onClick:ll(c.handleItemMouseEnter,t.props.onClick)}:"axis"!==o&&(j={onMouseLeave:ll(c.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:ll(c.handleItemMouseEnter,t.props.onMouseEnter)});var S=(0,i.cloneElement)(t,hy(hy({},n.props),j));if(w){if(s>=0){if(l.dataKey&&!l.allowDuplicatedCategory){var P="function"==typeof l.dataKey?function(t){return"function"==typeof l.dataKey?l.dataKey(t.payload):null}:"payload.".concat(l.dataKey.toString());E=F(d,P,f),k=y&&v&&F(v,P,f)}else E=null==d?void 0:d[s],k=y&&v&&v[s];if(O||x){var A=void 0!==t.props.activeIndex?t.props.activeIndex:s;return[(0,i.cloneElement)(t,hy(hy(hy({},n.props),j),{},{activeIndex:A})),null,null]}if(!X()(E))return[S].concat(hf(c.renderActivePoints({item:n,activePoint:E,basePoint:k,childIndex:s,isRange:y})))}else{var E,k,M,_=(null!==(M=c.getItemByXY(c.state.activeCoordinate))&&void 0!==M?M:{graphicalItem:S}).graphicalItem,T=_.item,C=void 0===T?t:T,D=_.childIndex,N=hy(hy(hy({},n.props),j),{},{activeIndex:D});return[(0,i.cloneElement)(C,N),null,null]}}return y?[S,null,null]:[S,null]}),hv(c,"renderCustomized",function(t,e,r){return(0,i.cloneElement)(t,hy(hy({key:"recharts-customized-".concat(r)},c.props),c.state))}),hv(c,"renderMap",{CartesianGrid:{handler:hO,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:hO},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:hO},YAxis:{handler:hO},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!==(r=t.id)&&void 0!==r?r:L("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=j()(c.triggeredAfterMouseMove,null!==(o=t.throttleDelay)&&void 0!==o?o:1e3/60),c.state={},c}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&hs(t,e)}(n,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=ty(e,es);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=hP(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=hy(hy({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){tx([ty(t.children,es)],[ty(this.props.children,es)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=ty(this.props.children,es);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return u.indexOf(e)>=0?e:o}return o}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap;if("axis"!==this.getTooltipEventType()&&u&&l){var s=z(u).scale,f=z(l).scale,p=s&&s.invert?s.invert(o.chartX):null,h=f&&f.invert?f.invert(o.chartY):null;return hy(hy({},o),{},{xValue:p,yValue:h})}var d=hA(this.state,this.props.data,this.props.layout,a);return d?hy(hy({},o),d):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;return u&&l?l2({x:o,y:i},z(u)):null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=ty(t,es),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),hy(hy({},tn(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){pm.on(pb,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){pm.removeListener(pb,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===ts(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,o=e.height,i=e.width;return a().createElement("defs",null,a().createElement("clipPath",{id:t},a().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=ha(e,2),n=r[0],o=r[1];return hy(hy({},t),{},hv({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=ha(e,2),n=r[0],o=r[1];return hy(hy({},t),{},hv({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?hy(hy({},u.type.defaultProps),u.props):u.props,s=ts(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return ng(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return l2(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(sK(a,n)||sZ(a,n)||sJ(a,n)){var h=function(t){var e,r,n,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,c=(sK(i,o)?e="trapezoids":sZ(i,o)?e="sectors":sJ(i,o)&&(e="points"),e),u=sK(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:sZ(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:sJ(i,o)?o.payload:{},l=a.filter(function(t,e){var r=c2()(u,t),n=i.props[c].filter(function(t){var e;return(sK(i,o)?e=sQ:sZ(i,o)?e=s0:sJ(i,o)&&(e=s1),e)(t,o)}),a=i.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:hy(hy({},a),{},{childIndex:d}),payload:sJ(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!tv(this))return null;var n=this.props,o=n.children,i=n.className,c=n.width,u=n.height,l=n.style,s=n.compact,f=n.title,p=n.desc,h=tg(hc(n,hn),!1);if(s)return a().createElement(fH,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement(tB,hi({},h,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),tw(o,this.renderMap)));this.props.accessibilityLayer&&(h.tabIndex=null!==(t=this.props.tabIndex)&&void 0!==t?t:0,h.role=null!==(e=this.props.role)&&void 0!==e?e:"application",h.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},h.onFocus=function(){r.accessibilityManager.focus()});var d=this.parseEventsOfWrapper();return a().createElement(fH,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement("div",hi({className:(0,O.A)("recharts-wrapper",i),style:hy({position:"relative",cursor:"default",width:c,height:u},l)},d,{ref:function(t){r.container=t}}),a().createElement(tB,hi({},h,{width:c,height:u,title:f,desc:p,style:hg}),this.renderClipPath(),tw(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hm(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);hv(y,"displayName",e),hv(y,"defaultProps",hy({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),hv(y,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,c=t.layout,u=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=hT(t);return hy(hy(hy({},p),{},{updateId:0},d(hy(hy({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!Z(l,e.prevMargin)){var h=hT(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=hy(hy({},hA(e,n,c)),{},{updateId:e.updateId+1}),m=hy(hy(hy({},h),y),v);return hy(hy(hy({},m),d(hy({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(!tx(o,e.prevChildren)){var b,g,x,O,w=ty(o,lV),j=w&&null!==(b=null===(g=w.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:s,S=w&&null!==(x=null===(O=w.props)||void 0===O?void 0:O.endIndex)&&void 0!==x?x:f,P=X()(n)||j!==s||S!==f?e.updateId+1:e.updateId;return hy(hy({updateId:P},d(hy(hy({props:t},e),{},{updateId:P,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),hv(y,"renderActiveDot",function(t,e,r){var n;return n=(0,i.isValidElement)(t)?(0,i.cloneElement)(t,e):V()(t)?t(e):a().createElement(rr,e),a().createElement(tz,{className:"recharts-active-dot",key:r},n)});var v=(0,i.forwardRef)(function(t,e){return a().createElement(y,hi({},t,{ref:e}))});return v.displayName=y.displayName,v}({chartName:"LineChart",GraphicalChild:hY,axisComponents:[{axisType:"xAxis",AxisComp:dv},{axisType:"yAxis",AxisComp:dP}],formatAxisMap:function(t,e,r,n,o){var i=t.width,a=t.height,c=t.layout,u=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!ty(u,ff);return l.reduce(function(i,a){var u,l,p,h,d,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,O=y.reversed,w="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,P=y.categoricalDomain.sort();if(P.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(P[e-1]||0),S))}),Number.isFinite(S)){var A=S/j,E="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(u=A*E/2),"no-gap"===y.padding){var k=R(t.barCategoryGap,A*E),M=A*E/2;u=M-k-(M-k)/E*k}}}l="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:y.range,O&&(l=[l[1],l[0]]);var _=ls(y,o,f),T=_.scale,C=_.realScaleType;T.domain(m).range(l),lf(T);var D=lm(T,fy(fy({},y),{},{realScaleType:C}));"xAxis"===n?(d="top"===v&&!x||"bottom"===v&&x,p=r.left,h=s[w]-d*y.height):"yAxis"===n&&(d="left"===v&&!x||"right"===v&&x,p=s[w]-d*y.width,h=r.top);var N=fy(fy(fy({},y),D),{},{realScaleType:C,x:p,y:h,scale:T,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return N.bandSize=lA(N,D),y.hide||"xAxis"!==n?y.hide||(s[w]+=(d?-1:1)*N.width):s[w]+=(d?-1:1)*N.height,fy(fy({},i),{},fv({},a,N))},{})}}),dE=["x1","y1","x2","y2","key"],dk=["offset"];function dM(t){return(dM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d_(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dT(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d_(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=dM(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dM(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d_(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dC(){return(dC=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dD(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var dN=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,n=t.x,o=t.y,i=t.width,c=t.height,u=t.ry;return a().createElement("rect",{x:n,y:o,ry:u,width:i,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function dI(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(V()(t))r=t(e);else{var n=e.x1,o=e.y1,i=e.x2,c=e.y2,u=e.key,l=tg(dD(e,dE),!1),s=(l.offset,dD(l,dk));r=a().createElement("line",dC({},s,{x1:n,y1:o,x2:i,y2:c,fill:"none",key:u}))}return r}function dB(t){var e=t.x,r=t.width,n=t.horizontal,o=void 0===n||n,i=t.horizontalPoints;if(!o||!i||!i.length)return null;var c=i.map(function(n,i){return dI(o,dT(dT({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(i),index:i}))});return a().createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function dL(t){var e=t.y,r=t.height,n=t.vertical,o=void 0===n||n,i=t.verticalPoints;if(!o||!i||!i.length)return null;var c=i.map(function(n,i){return dI(o,dT(dT({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(i),index:i}))});return a().createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function dR(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,o=t.y,i=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,u){var l=s[u+1]?s[u+1]-t:o+c-t;if(l<=0)return null;var f=u%e.length;return a().createElement("rect",{key:"react-".concat(u),y:t,x:n,height:l,width:i,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function dz(t){var e=t.vertical,r=t.verticalFill,n=t.fillOpacity,o=t.x,i=t.y,c=t.width,u=t.height,l=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var s=l.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,e){var l=s[e+1]?s[e+1]-t:o+c-t;if(l<=0)return null;var f=e%r.length;return a().createElement("rect",{key:"react-".concat(e),x:t,y:i,width:l,height:u,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var dU=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return la(h1(dT(dT(dT({},dc.defaultProps),r),{},{ticks:lc(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},dq=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return la(h1(dT(dT(dT({},dc.defaultProps),r),{},{ticks:lc(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},dF={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function d$(t){var e,r,n,o,c,u,l=fK(),s=fZ(),f=(0,i.useContext)(fF),p=dT(dT({},t),{},{stroke:null!==(e=t.stroke)&&void 0!==e?e:dF.stroke,fill:null!==(r=t.fill)&&void 0!==r?r:dF.fill,horizontal:null!==(n=t.horizontal)&&void 0!==n?n:dF.horizontal,horizontalFill:null!==(o=t.horizontalFill)&&void 0!==o?o:dF.horizontalFill,vertical:null!==(c=t.vertical)&&void 0!==c?c:dF.vertical,verticalFill:null!==(u=t.verticalFill)&&void 0!==u?u:dF.verticalFill,x:N(t.x)?t.x:f.left,y:N(t.y)?t.y:f.top,width:N(t.width)?t.width:f.width,height:N(t.height)?t.height:f.height}),h=p.x,d=p.y,y=p.width,v=p.height,m=p.syncWithTicks,b=p.horizontalValues,g=p.verticalValues,x=z((0,i.useContext)(fz)),O=fG();if(!N(y)||y<=0||!N(v)||v<=0||!N(h)||h!==+h||!N(d)||d!==+d)return null;var w=p.verticalCoordinatesGenerator||dU,j=p.horizontalCoordinatesGenerator||dq,S=p.horizontalPoints,P=p.verticalPoints;if((!S||!S.length)&&V()(j)){var A=b&&b.length,E=j({yAxis:O?dT(dT({},O),{},{ticks:A?b:O.ticks}):void 0,width:l,height:s,offset:f},!!A||m);$(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(dM(E),"]")),Array.isArray(E)&&(S=E)}if((!P||!P.length)&&V()(w)){var k=g&&g.length,M=w({xAxis:x?dT(dT({},x),{},{ticks:k?g:x.ticks}):void 0,width:l,height:s,offset:f},!!k||m);$(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(dM(M),"]")),Array.isArray(M)&&(P=M)}return a().createElement("g",{className:"recharts-cartesian-grid"},a().createElement(dN,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),a().createElement(dB,dC({},p,{offset:f,horizontalPoints:S,xAxis:x,yAxis:O})),a().createElement(dL,dC({},p,{offset:f,verticalPoints:P,xAxis:x,yAxis:O})),a().createElement(dR,dC({},p,{horizontalPoints:S})),a().createElement(dz,dC({},p,{verticalPoints:P})))}d$.displayName="CartesianGrid";var dW=r(43010);let dX=(t,e)=>{let r=t.filter(t=>"SELL"===t.orderType&&void 0!==t.realizedProfitLossCrypto2),n=r.reduce((t,e)=>t+(e.realizedProfitLossCrypto2||0),0),o=r.reduce((t,e)=>t+(e.realizedProfitLossCrypto1||0),0),i=r.filter(t=>(t.realizedProfitLossCrypto2||0)>0).length,a=r.length>0?i/r.length*100:0,c=t.length,u=t.filter(t=>"BUY"===t.orderType).length,l=r.length>0?n/r.length:0,s=r.length>0?o/r.length:0;return{totalProfitLossCrypto1:parseFloat(o.toFixed(e.numDigits)),totalProfitLossCrypto2:parseFloat(n.toFixed(e.numDigits)),winRate:parseFloat(a.toFixed(2)),totalTradesExecuted:c,buyTrades:u,sellTrades:r.length,avgProfitPerTradeCrypto2:parseFloat(l.toFixed(e.numDigits)),avgProfitPerTradeCrypto1:parseFloat(s.toFixed(e.numDigits))}},dH=(t,e)=>{let r=t.filter(t=>"SELL"===t.orderType&&void 0!==t.realizedProfitLossCrypto2),n=0;return r.map((t,e)=>(n+=t.realizedProfitLossCrypto2||0,{date:(0,dW.GP)(new Date(t.timestamp),"MMM dd HH:mm"),pnl:parseFloat(n.toFixed(4)),trade:e+1}))};function dV(){let{orderHistory:t,config:e,getDisplayOrders:r}=(0,h.U)(),[n,a]=(0,i.useState)([]),[c,u]=(0,i.useState)("current"),[v,O]=(0,i.useState)([]),[w,j]=(0,i.useState)(e);d.C.getInstance();let S=(0,i.useMemo)(()=>dX(v,w),[v,w]),P=(0,i.useMemo)(()=>dH(v,w.crypto2),[v,w.crypto2]),A=(0,i.useMemo)(()=>"current"!==c?"0.0000":r().reduce((t,e)=>"Full"===e.status&&void 0!==e.incomeCrypto2?t+e.incomeCrypto2:t,0).toFixed(w.numDigits),[r,w.numDigits,c]),E="current"===c?{name:"Current Session",pair:`${e.crypto1}/${e.crypto2}`,isActive:!0}:n.find(t=>t.id===c),k=(0,i.useMemo)(()=>[{title:`Total Realized P/L (${w.crypto1})`,value:S.totalProfitLossCrypto1,icon:(0,o.jsx)(y.A,{className:"h-6 w-6 text-primary"}),description:"Sum of profits from sell trades in Crypto1",isProfit:S.totalProfitLossCrypto1>=0},{title:`Total Realized P/L (${w.crypto2})`,value:S.totalProfitLossCrypto2,icon:(0,o.jsx)(y.A,{className:"h-6 w-6 text-primary"}),description:"Sum of profits from sell trades in Crypto2",isProfit:S.totalProfitLossCrypto2>=0},{title:"Win Rate",value:`${S.winRate}%`,icon:(0,o.jsx)(m,{className:"h-6 w-6 text-primary"}),description:"Profitable sell trades / Total sell trades",isProfit:S.winRate>=50},{title:"Total Trades",value:S.totalTradesExecuted,icon:(0,o.jsx)(b,{className:"h-6 w-6 text-primary"}),description:`${S.buyTrades} buys, ${S.sellTrades} sells`,isProfit:!0},{title:`Avg Profit/Trade (${w.crypto2})`,value:S.avgProfitPerTradeCrypto2,icon:(0,o.jsx)(g,{className:"h-6 w-6 text-primary"}),description:"Average profit per sell trade",isProfit:S.avgProfitPerTradeCrypto2>=0},{title:`Current Unrealized P/L (${w.crypto2})`,value:A,icon:(0,o.jsx)(x.A,{className:"h-6 w-6 text-primary"}),description:"Unrealized profit/loss from active positions",isProfit:parseFloat(A)>=0,isCurrentOnly:!0}],[S,w,A]);return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)(l.Zp,{className:"border-2 border-border",children:[(0,o.jsxs)(l.aR,{children:[(0,o.jsx)(l.ZB,{className:"text-xl font-bold text-primary",children:"Session Analytics"}),(0,o.jsx)(l.BT,{children:"View trading analytics for current and past sessions."})]}),(0,o.jsxs)(l.Wu,{className:"space-y-4",children:[(0,o.jsx)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center",children:(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Select Session:"}),(0,o.jsxs)(s.l6,{value:c,onValueChange:u,children:[(0,o.jsx)(s.bq,{className:"w-full sm:w-[300px]",children:(0,o.jsx)(s.yv,{placeholder:"Select a session"})}),(0,o.jsxs)(s.gC,{children:[(0,o.jsx)(s.eb,{value:"current",children:(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(f.E,{variant:"default",className:"text-xs",children:"Current"}),(0,o.jsxs)("span",{children:["Current Session (",e.crypto1,"/",e.crypto2,")"]})]})}),n.length>0&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(p.w,{className:"my-1"}),n.map(t=>(0,o.jsx)(s.eb,{value:t.id,children:(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(f.E,{variant:"secondary",className:"text-xs",children:"Past"}),(0,o.jsxs)("span",{children:[t.name," (",t.pair,")"]})]})},t.id))]})]})]})]})}),E&&(0,o.jsx)("div",{className:"bg-muted/50 rounded-lg p-4",children:(0,o.jsxs)("div",{className:"flex items-center gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"font-medium",children:E.name}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:E.pair})]}),E.isActive&&(0,o.jsx)(f.E,{variant:"default",className:"text-xs",children:"Active"})]})})]})]}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:k.map((t,e)=>t.isCurrentOnly&&"current"!==c?null:(0,o.jsxs)(l.Zp,{className:"border-2 border-border",children:[(0,o.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,o.jsx)(l.ZB,{className:"text-sm font-medium",children:t.title}),t.icon]}),(0,o.jsxs)(l.Wu,{children:[(0,o.jsx)("div",{className:`text-2xl font-bold ${"number"==typeof t.value?t.isProfit?"text-green-600":"text-red-600":"text-foreground"}`,children:"number"==typeof t.value?t.value.toFixed(w.numDigits):t.value}),(0,o.jsx)("p",{className:"text-xs text-muted-foreground",children:t.description})]})]},e))}),(0,o.jsxs)(l.Zp,{className:"border-2 border-border",children:[(0,o.jsxs)(l.aR,{children:[(0,o.jsxs)(l.ZB,{className:"text-xl font-bold text-primary",children:["Cumulative Profit/Loss Over Time (",w.crypto2,")"]}),(0,o.jsxs)(l.BT,{children:["Chart visualization of trading performance for ",E?.name||"selected session","."]})]}),(0,o.jsx)(l.Wu,{className:"h-80",children:P.length>0?(0,o.jsx)(tk,{width:"100%",height:"100%",children:(0,o.jsxs)(dA,{data:P,margin:{top:5,right:20,left:-25,bottom:5},children:[(0,o.jsx)(d$,{strokeDasharray:"3 3",stroke:"hsl(var(--border))"}),(0,o.jsx)(dv,{dataKey:"date",stroke:"hsl(var(--muted-foreground))",fontSize:12,tickLine:!1,axisLine:!1}),(0,o.jsx)(dP,{stroke:"hsl(var(--muted-foreground))",fontSize:12,tickLine:!1,axisLine:!1,tickFormatter:t=>`${t.toFixed(2)}`}),(0,o.jsx)(es,{content:({active:t,payload:e,label:r})=>t&&e&&e.length?(0,o.jsxs)("div",{className:"bg-card border border-border rounded-lg p-3 shadow-lg",children:[(0,o.jsx)("p",{className:"text-sm font-medium",children:`Date: ${r}`}),(0,o.jsxs)("p",{className:"text-sm",children:[(0,o.jsx)("span",{className:"text-muted-foreground",children:"P/L: "}),(0,o.jsxs)("span",{className:e[0].value>=0?"text-green-600":"text-red-600",children:[e[0].value," ",w.crypto2]})]}),(0,o.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Trade #",e[0].payload.trade]})]}):null}),(0,o.jsx)(hY,{type:"monotone",dataKey:"pnl",stroke:"hsl(var(--primary))",strokeWidth:2,dot:{fill:"hsl(var(--primary))",strokeWidth:2,r:4},activeDot:{r:6,stroke:"hsl(var(--primary))",strokeWidth:2}})]})}):(0,o.jsx)("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)(b,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,o.jsx)("p",{children:"No sell trades recorded yet for this session."}),(0,o.jsx)("p",{className:"text-xs",children:"Chart will appear after first profitable trade."})]})})})]})]})}function dG(){return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsx)(c.A,{}),(0,o.jsx)(u.A,{}),(0,o.jsx)(dV,{})]})}},38404:(t,e,r)=>{var n=r(29395),o=r(65932),i=r(27467),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,l=c.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==l}},38428:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},39672:(t,e,r)=>{var n=r(58141);t.exports=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},39774:t=>{t.exports=function(t){return t!=t}},40491:(t,e,r)=>{var n=r(1707);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},40542:t=>{t.exports=Array.isArray},41011:(t,e,r)=>{var n=r(41353);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},41132:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},41157:(t,e,r)=>{var n=r(91928);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},41353:t=>{t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},41547:(t,e,r)=>{var n=r(61548),o=r(90851);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},41693:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},42082:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},42205:(t,e,r)=>{var n=r(41693),o=r(85450);t.exports=function t(e,r,i,a,c){var u=-1,l=e.length;for(i||(i=o),c||(c=[]);++u<l;){var s=e[u];r>0&&i(s)?r>1?t(s,r-1,i,a,c):n(c,s):a||(c[c.length]=s)}return c}},42403:(t,e,r)=>{var n=r(80195);t.exports=function(t){return null==t?"":n(t)}},43378:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},44708:t=>{"use strict";t.exports=require("node:https")},45058:(t,e,r)=>{var n=r(42082),o=r(8852),i=r(67619),a=r(46436);t.exports=function(t){return i(t)?n(a(t)):o(t)}},45603:(t,e,r)=>{var n=r(20540),o=r(55048);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},45803:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},46063:t=>{t.exports=function(t,e){return t<e}},46229:(t,e,r)=>{var n=r(48169),o=r(66354),i=r(11424);t.exports=function(t,e){return i(o(t,e,n),t+"")}},46328:(t,e,r)=>{var n=r(95746),o=r(89185),i=r(16854);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},46436:(t,e,r)=>{var n=r(49227),o=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-o?"-0":e}},47212:(t,e,r)=>{var n=r(87270),o=r(30316),i=r(22),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},47282:(t,e,r)=>{t=r.nmd(t);var n=r(10663),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,c=function(){try{var t=i&&i.require&&i.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},47603:(t,e,r)=>{var n=r(14675),o=r(91928),i=r(48169);t.exports=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i},48169:t=>{t.exports=function(t){return t}},48385:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+e+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+u+c+")*",s=RegExp(n+"(?="+n+")|"+("(?:"+[o+r+"?",r,i,a,"["+e+"]"].join("|"))+")"+(u+c+l),"g");t.exports=function(t){return t.match(s)||[]}},49227:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},51449:(t,e,r)=>{var n=r(85745),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g;t.exports=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e})},52599:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},52823:(t,e,r)=>{var n=r(85406),o=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!o&&o in t}},52931:(t,e,r)=>{var n=r(77834),o=r(89605),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},54379:t=>{"use strict";t.exports=require("node:path")},54765:(t,e,r)=>{var n=r(67554),o=r(32269);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},55048:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},55659:(t,e,r)=>{Promise.resolve().then(r.bind(r,4080))},56506:(t,e,r)=>{var n=r(32269);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,c=Object(r);(e?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},56801:t=>{"use strict";t.exports=require("import-in-the-middle")},57075:t=>{"use strict";t.exports=require("node:stream")},57202:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},57797:(t,e,r)=>{var n=r(67009);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},57975:t=>{"use strict";t.exports=require("node:util")},58141:(t,e,r)=>{t.exports=r(41547)(Object,"create")},58276:t=>{t.exports=function(t,e){return t.has(e)}},58744:(t,e,r)=>{var n=r(57797);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},59467:(t,e,r)=>{var n=r(35142),o=r(35163),i=r(40542),a=r(38428),c=r(69619),u=r(46436);t.exports=function(t,e,r){e=n(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=u(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(i(t)||o(t))}},59774:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},61320:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).has(t)}},61548:(t,e,r)=>{var n=r(5231),o=r(52823),i=r(55048),a=r(12290),c=/^\[object .+?Constructor\]$/,u=Object.prototype,l=Function.prototype.toString,s=u.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?f:c).test(a(t))}},61837:(t,e,r)=>{var n=r(21367),o=r(22),i=r(54765),a=r(40542);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(t,e,r)=>{var n=r(29395),o=r(40542),i=r(27467);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},63979:(t,e,r)=>{var n=r(52599),o=r(6330),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;t.exports=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return i.call(t,e)})}:o},65662:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},65727:(t,e,r)=>{var n=r(81957);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,c=i.length,u=r.length;++o<c;){var l=n(i[o],a[o]);if(l){if(o>=u)return l;return l*("desc"==r[o]?-1:1)}}return t.index-e.index}},65932:(t,e,r)=>{t.exports=r(65662)(Object.getPrototypeOf,Object)},65984:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++o];if(!1===r(i[u],u,i))break}return e}}},66354:(t,e,r)=>{var n=r(85244),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,c=o(i.length-e,0),u=Array(c);++a<c;)u[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(u),n(t,this,l)}}},66400:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},66713:(t,e,r)=>{var n=r(3105),o=r(34117),i=r(48385);t.exports=function(t){return o(t)?i(t):n(t)}},66837:(t,e,r)=>{var n=r(58141);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(t,e,r)=>{var n=r(27669),o=r(28837),i=r(94388),a=r(35800),c=r(58744);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},67009:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},67200:(t,e,r)=>{var n=r(66930),o=r(37575),i=r(75411),a=r(34746),c=r(25118),u=r(30854);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=c,l.prototype.set=u,t.exports=l},67367:(t,e,r)=>{var n=r(99525),o=r(22),i=r(75847),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},67554:(t,e,r)=>{var n=r(99114);t.exports=r(56506)(n)},67619:(t,e,r)=>{var n=r(40542),o=r(49227),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},69433:(t,e,r)=>{t.exports=r(5566)("toUpperCase")},69619:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}},69691:(t,e,r)=>{var n=r(41157),o=r(99114),i=r(22);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},70151:(t,e,r)=>{var n=r(85718);t.exports=function(){return n.Date.now()}},70222:(t,e,r)=>{var n=r(79474),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[c]=r:delete t[c]),o}},71960:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},71967:(t,e,r)=>{var n=r(15871);t.exports=function(t,e){return n(t,e)}},73024:t=>{"use strict";t.exports=require("node:fs")},73136:t=>{"use strict";t.exports=require("node:url")},73496:t=>{"use strict";t.exports=require("http2")},73566:t=>{"use strict";t.exports=require("worker_threads")},74075:t=>{"use strict";t.exports=require("zlib")},74610:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return -1}},74998:t=>{"use strict";t.exports=require("perf_hooks")},75254:(t,e,r)=>{var n=r(78418),o=r(93311),i=r(41132);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},75411:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},75847:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},77030:t=>{"use strict";t.exports=require("node:net")},77598:t=>{"use strict";t.exports=require("node:crypto")},77822:(t,e,r)=>{var n=r(93490);t.exports=function(t){return n(t)&&t!=+t}},77834:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},78418:(t,e,r)=>{var n=r(67200),o=r(15871);t.exports=function(t,e,r,i){var a=r.length,c=a,u=!i;if(null==t)return!c;for(t=Object(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<c;){var s=(l=r[a])[0],f=t[s],p=l[1];if(u&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(i)var d=i(f,p,s,t,e,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},79428:t=>{"use strict";t.exports=require("buffer")},79474:(t,e,r)=>{t.exports=r(85718).Symbol},79551:t=>{"use strict";t.exports=require("url")},79646:t=>{"use strict";t.exports=require("child_process")},79748:t=>{"use strict";t.exports=require("fs/promises")},80195:(t,e,r)=>{var n=r(79474),o=r(21367),i=r(40542),a=r(49227),c=1/0,u=n?n.prototype:void 0,l=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return l?l.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},80329:(t,e,r)=>{t=r.nmd(t);var n=r(85718),o=r(1944),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,c=a&&a.exports===i?n.Buffer:void 0,u=c?c.isBuffer:void 0;t.exports=u||o},80458:(t,e,r)=>{var n=r(29395),o=r(69619),i=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},80704:(t,e,r)=>{var n=r(96678);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},81488:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},81630:t=>{"use strict";t.exports=require("http")},81957:(t,e,r)=>{var n=r(49227);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),c=void 0!==e,u=null===e,l=e==e,s=n(e);if(!u&&!s&&!a&&t>e||a&&c&&l&&!u&&!s||o&&c&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||u&&r&&i||!c&&i||!l)return -1}return 0}},82038:(t,e,r)=>{var n=r(57202),o=r(35163),i=r(40542),a=r(80329),c=r(38428),u=r(10090),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),s=!r&&o(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&u(t),h=r||s||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)(e||l.call(t,v))&&!(h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&d.push(v);return d}},84031:(t,e,r)=>{"use strict";var n=r(34452);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},84261:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=+!!e,e}},84297:t=>{"use strict";t.exports=require("async_hooks")},84482:(t,e,r)=>{var n=r(28977);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},84713:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},85244:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},85406:(t,e,r)=>{t.exports=r(85718)["__core-js_shared__"]},85450:(t,e,r)=>{var n=r(79474),o=r(35163),i=r(40542),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},85718:(t,e,r)=>{var n=r(10663),o="object"==typeof self&&self&&self.Object===Object&&self;t.exports=n||o||Function("return this")()},85745:(t,e,r)=>{var n=r(86451);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},85938:(t,e,r)=>{var n=r(42205),o=r(17518),i=r(46229),a=r(7383);t.exports=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])})},86451:(t,e,r)=>{var n=r(95746);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},86564:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>s,routeModule:()=>p,tree:()=>l});var n=r(65239),o=r(48088),i=r(88170),a=r.n(i),c=r(30893),u={};for(let t in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(u[t]=()=>c[t]);r.d(e,u);let l={children:["",{children:["dashboard",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4080)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\analytics\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,63144)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,s=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\analytics\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/dashboard/analytics/page",pathname:"/dashboard/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},87270:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},87321:(t,e,r)=>{var n=r(98798),o=r(7383),i=r(28977);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},87506:(t,e,r)=>{var n=r(66837),o=r(84261),i=r(89492),a=r(90200),c=r(39672);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},87955:(t,e,r)=>{t.exports=r(84031)()},89167:(t,e,r)=>{t.exports=r(41547)(r(85718),"DataView")},89185:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},89492:(t,e,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},89605:(t,e,r)=>{t.exports=r(65662)(Object.keys,Object)},89624:t=>{t.exports=function(t){return function(e){return t(e)}}},90200:(t,e,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},90453:(t,e,r)=>{var n=r(2984),o=r(99180),i=r(48169);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},90851:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},91290:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return -1}},91645:t=>{"use strict";t.exports=require("net")},91928:(t,e,r)=>{var n=r(41547);t.exports=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},92662:(t,e,r)=>{var n=r(46328),o=r(80704),i=r(71960),a=r(58276),c=r(95308),u=r(2408);t.exports=function(t,e,r){var l=-1,s=o,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=i;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,s=a,d=new n}else d=e?[]:h;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue e;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},93311:(t,e,r)=>{var n=r(34883),o=r(7651);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},93490:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},94388:(t,e,r)=>{var n=r(57797);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},94735:t=>{"use strict";t.exports=require("events")},95308:(t,e,r)=>{var n=r(34772),o=r(36959),i=r(2408);t.exports=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o},95746:(t,e,r)=>{var n=r(15909),o=r(29205),i=r(29508),a=r(61320),c=r(19976);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},96678:(t,e,r)=>{var n=r(91290),o=r(39774),i=r(74610);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},97668:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case i:case c:case a:case p:case h:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case d:case u:return t;default:return e}}case o:return e}}}(t)===i}},98451:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},98798:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,c=r(e((n-t)/(o||1)),0),u=Array(c);c--;)u[i?c:++a]=t,t+=o;return u}},99114:(t,e,r)=>{var n=r(12344),o=r(7651);t.exports=function(t,e){return t&&n(t,e,o)}},99180:t=>{t.exports=function(t,e){return t>e}},99525:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}}};var e=require("../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[585,118,759,112,124,241],()=>r(86564));module.exports=n})();