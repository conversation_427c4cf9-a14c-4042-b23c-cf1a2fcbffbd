"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-node_modules_next_dist_client_components_react-dev-overlay_ui_components_errors_call-stack_c-dc969f58"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js ***!
  \******************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    CALL_STACK_STYLES: function() {\n        return CALL_STACK_STYLES;\n    },\n    CallStack: function() {\n        return CallStack;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _callstackframe = __webpack_require__(/*! ../../call-stack-frame/call-stack-frame */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/call-stack-frame/call-stack-frame.js\");\nfunction CallStack(param) {\n    let { frames, dialogResizerRef } = param;\n    const initialDialogHeight = (0, _react.useRef)(NaN);\n    const [isIgnoreListOpen, setIsIgnoreListOpen] = (0, _react.useState)(false);\n    const ignoredFramesTally = (0, _react.useMemo)(()=>{\n        return frames.reduce((tally, frame)=>tally + (frame.ignored ? 1 : 0), 0);\n    }, [\n        frames\n    ]);\n    function onToggleIgnoreList() {\n        const dialog = dialogResizerRef == null ? void 0 : dialogResizerRef.current;\n        if (!dialog) {\n            return;\n        }\n        const { height: currentHeight } = dialog == null ? void 0 : dialog.getBoundingClientRect();\n        if (!initialDialogHeight.current) {\n            initialDialogHeight.current = currentHeight;\n        }\n        if (isIgnoreListOpen) {\n            function onTransitionEnd() {\n                dialog.removeEventListener('transitionend', onTransitionEnd);\n                setIsIgnoreListOpen(false);\n            }\n            dialog.style.height = \"\" + initialDialogHeight.current + \"px\";\n            dialog.addEventListener('transitionend', onTransitionEnd);\n        } else {\n            setIsIgnoreListOpen(true);\n        }\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        className: \"error-overlay-call-stack-container\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                className: \"error-overlay-call-stack-header\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        className: \"error-overlay-call-stack-title\",\n                        children: [\n                            \"Call Stack\",\n                            ' ',\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                className: \"error-overlay-call-stack-count\",\n                                children: frames.length\n                            })\n                        ]\n                    }),\n                    ignoredFramesTally > 0 && /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"button\", {\n                        \"data-expand-ignore-button\": isIgnoreListOpen,\n                        className: \"error-overlay-call-stack-ignored-list-toggle-button\",\n                        onClick: onToggleIgnoreList,\n                        children: [\n                            (isIgnoreListOpen ? 'Hide' : 'Show') + \" \" + ignoredFramesTally + \" ignore-listed frame(s)\",\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(ChevronUpDown, {})\n                        ]\n                    })\n                ]\n            }),\n            frames.map((frame, frameIndex)=>{\n                return !frame.ignored || isIgnoreListOpen ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_callstackframe.CallStackFrame, {\n                    frame: frame\n                }, frameIndex) : null;\n            })\n        ]\n    });\n}\n_c = CallStack;\nfunction ChevronUpDown() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M8.70722 2.39641C8.3167 2.00588 7.68353 2.00588 7.29301 2.39641L4.46978 5.21963L3.93945 5.74996L5.00011 6.81062L5.53044 6.28029L8.00011 3.81062L10.4698 6.28029L11.0001 6.81062L12.0608 5.74996L11.5304 5.21963L8.70722 2.39641ZM5.53044 9.71963L5.00011 9.1893L3.93945 10.25L4.46978 10.7803L7.29301 13.6035C7.68353 13.994 8.3167 13.994 8.70722 13.6035L11.5304 10.7803L12.0608 10.25L11.0001 9.1893L10.4698 9.71963L8.00011 12.1893L5.53044 9.71963Z\",\n            fill: \"currentColor\"\n        })\n    });\n}\n_c1 = ChevronUpDown;\nconst CALL_STACK_STYLES = \"\\n  .error-overlay-call-stack-container {\\n    position: relative;\\n    margin-top: 8px;\\n  }\\n\\n  .error-overlay-call-stack-header {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    min-height: var(--size-28);\\n    padding: 8px 8px 12px 4px;\\n    width: 100%;\\n  }\\n\\n  .error-overlay-call-stack-title {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    gap: 8px;\\n\\n    margin: 0;\\n\\n    color: var(--color-gray-1000);\\n    font-size: var(--size-16);\\n    font-weight: 500;\\n  }\\n\\n  .error-overlay-call-stack-count {\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n\\n    width: var(--size-20);\\n    height: var(--size-20);\\n    gap: 4px;\\n\\n    color: var(--color-gray-1000);\\n    text-align: center;\\n    font-size: var(--size-11);\\n    font-weight: 500;\\n    line-height: var(--size-16);\\n\\n    border-radius: var(--rounded-full);\\n    background: var(--color-gray-300);\\n  }\\n\\n  .error-overlay-call-stack-ignored-list-toggle-button {\\n    all: unset;\\n    display: flex;\\n    align-items: center;\\n    gap: 6px;\\n    color: var(--color-gray-900);\\n    font-size: var(--size-14);\\n    line-height: var(--size-20);\\n    border-radius: 6px;\\n    padding: 4px 6px;\\n    margin-right: -6px;\\n    transition: background 150ms ease;\\n\\n    &:hover {\\n      background: var(--color-gray-100);\\n    }\\n\\n    &:focus {\\n      outline: var(--focus-ring);\\n    }\\n\\n    svg {\\n      width: var(--size-16);\\n      height: var(--size-16);\\n    }\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=call-stack.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"CallStack\");\n$RefreshReg$(_c1, \"ChevronUpDown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js ***!
  \******************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    CALL_STACK_STYLES: function() {\n        return CALL_STACK_STYLES;\n    },\n    CallStack: function() {\n        return CallStack;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _callstackframe = __webpack_require__(/*! ../../call-stack-frame/call-stack-frame */ \"(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/call-stack-frame/call-stack-frame.js\");\nfunction CallStack(param) {\n    let { frames, dialogResizerRef } = param;\n    const initialDialogHeight = (0, _react.useRef)(NaN);\n    const [isIgnoreListOpen, setIsIgnoreListOpen] = (0, _react.useState)(false);\n    const ignoredFramesTally = (0, _react.useMemo)(()=>{\n        return frames.reduce((tally, frame)=>tally + (frame.ignored ? 1 : 0), 0);\n    }, [\n        frames\n    ]);\n    function onToggleIgnoreList() {\n        const dialog = dialogResizerRef == null ? void 0 : dialogResizerRef.current;\n        if (!dialog) {\n            return;\n        }\n        const { height: currentHeight } = dialog == null ? void 0 : dialog.getBoundingClientRect();\n        if (!initialDialogHeight.current) {\n            initialDialogHeight.current = currentHeight;\n        }\n        if (isIgnoreListOpen) {\n            function onTransitionEnd() {\n                dialog.removeEventListener('transitionend', onTransitionEnd);\n                setIsIgnoreListOpen(false);\n            }\n            dialog.style.height = \"\" + initialDialogHeight.current + \"px\";\n            dialog.addEventListener('transitionend', onTransitionEnd);\n        } else {\n            setIsIgnoreListOpen(true);\n        }\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        className: \"error-overlay-call-stack-container\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                className: \"error-overlay-call-stack-header\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        className: \"error-overlay-call-stack-title\",\n                        children: [\n                            \"Call Stack\",\n                            ' ',\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                className: \"error-overlay-call-stack-count\",\n                                children: frames.length\n                            })\n                        ]\n                    }),\n                    ignoredFramesTally > 0 && /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"button\", {\n                        \"data-expand-ignore-button\": isIgnoreListOpen,\n                        className: \"error-overlay-call-stack-ignored-list-toggle-button\",\n                        onClick: onToggleIgnoreList,\n                        children: [\n                            (isIgnoreListOpen ? 'Hide' : 'Show') + \" \" + ignoredFramesTally + \" ignore-listed frame(s)\",\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(ChevronUpDown, {})\n                        ]\n                    })\n                ]\n            }),\n            frames.map((frame, frameIndex)=>{\n                return !frame.ignored || isIgnoreListOpen ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_callstackframe.CallStackFrame, {\n                    frame: frame\n                }, frameIndex) : null;\n            })\n        ]\n    });\n}\n_c = CallStack;\nfunction ChevronUpDown() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M8.70722 2.39641C8.3167 2.00588 7.68353 2.00588 7.29301 2.39641L4.46978 5.21963L3.93945 5.74996L5.00011 6.81062L5.53044 6.28029L8.00011 3.81062L10.4698 6.28029L11.0001 6.81062L12.0608 5.74996L11.5304 5.21963L8.70722 2.39641ZM5.53044 9.71963L5.00011 9.1893L3.93945 10.25L4.46978 10.7803L7.29301 13.6035C7.68353 13.994 8.3167 13.994 8.70722 13.6035L11.5304 10.7803L12.0608 10.25L11.0001 9.1893L10.4698 9.71963L8.00011 12.1893L5.53044 9.71963Z\",\n            fill: \"currentColor\"\n        })\n    });\n}\n_c1 = ChevronUpDown;\nconst CALL_STACK_STYLES = \"\\n  .error-overlay-call-stack-container {\\n    position: relative;\\n    margin-top: 8px;\\n  }\\n\\n  .error-overlay-call-stack-header {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    min-height: var(--size-28);\\n    padding: 8px 8px 12px 4px;\\n    width: 100%;\\n  }\\n\\n  .error-overlay-call-stack-title {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    gap: 8px;\\n\\n    margin: 0;\\n\\n    color: var(--color-gray-1000);\\n    font-size: var(--size-16);\\n    font-weight: 500;\\n  }\\n\\n  .error-overlay-call-stack-count {\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n\\n    width: var(--size-20);\\n    height: var(--size-20);\\n    gap: 4px;\\n\\n    color: var(--color-gray-1000);\\n    text-align: center;\\n    font-size: var(--size-11);\\n    font-weight: 500;\\n    line-height: var(--size-16);\\n\\n    border-radius: var(--rounded-full);\\n    background: var(--color-gray-300);\\n  }\\n\\n  .error-overlay-call-stack-ignored-list-toggle-button {\\n    all: unset;\\n    display: flex;\\n    align-items: center;\\n    gap: 6px;\\n    color: var(--color-gray-900);\\n    font-size: var(--size-14);\\n    line-height: var(--size-20);\\n    border-radius: 6px;\\n    padding: 4px 6px;\\n    margin-right: -6px;\\n    transition: background 150ms ease;\\n\\n    &:hover {\\n      background: var(--color-gray-100);\\n    }\\n\\n    &:focus {\\n      outline: var(--focus-ring);\\n    }\\n\\n    svg {\\n      width: var(--size-16);\\n      height: var(--size-16);\\n    }\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=call-stack.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"CallStack\");\n$RefreshReg$(_c1, \"ChevronUpDown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js\n"));

/***/ })

}]);