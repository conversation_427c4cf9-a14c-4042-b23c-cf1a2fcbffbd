"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx":
/*!********************************************************!*\
  !*** ./src/components/layout/TradingConfigSidebar.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TradingConfigSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _contexts_AIContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AIContext */ \"(app-pages-browser)/./src/contexts/AIContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/TargetPriceModal */ \"(app-pages-browser)/./src/components/modals/TargetPriceModal.tsx\");\n/* harmony import */ var _components_modals_AlarmConfigModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/AlarmConfigModal */ \"(app-pages-browser)/./src/components/modals/AlarmConfigModal.tsx\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var _components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/crypto-input */ \"(app-pages-browser)/./src/components/ui/crypto-input.tsx\");\n/* harmony import */ var _components_ui_backend_status__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/backend-status */ \"(app-pages-browser)/./src/components/ui/backend-status.tsx\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power-off.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\nfunction TradingConfigSidebar() {\n    _s();\n    // Safely get trading context with error handling\n    let tradingContext;\n    try {\n        tradingContext = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    } catch (error) {\n        console.error('Trading context not available:', error);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n            className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"Trading context not available. Please refresh the page.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this);\n    }\n    const { config, dispatch, botSystemStatus, appSettings, setTargetPrices: contextSetTargetPrices } = tradingContext;\n    const isBotActive = botSystemStatus === 'Running';\n    const isBotWarmingUp = botSystemStatus === 'WarmingUp';\n    // Safely get AI context\n    let aiContext;\n    try {\n        aiContext = (0,_contexts_AIContext__WEBPACK_IMPORTED_MODULE_3__.useAIContext)();\n    } catch (error) {\n        console.warn('AI context not available:', error);\n        aiContext = {\n            suggestion: null,\n            isLoading: false,\n            error: null,\n            getTradingModeSuggestion: ()=>Promise.resolve()\n        };\n    }\n    const { suggestion: aiSuggestion, isLoading: aiLoading, error: aiError, getTradingModeSuggestion } = aiContext;\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_17__.useToast)();\n    const [isTargetModalOpen, setIsTargetModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAlarmModalOpen, setIsAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // AI Suggestion form state\n    const [riskTolerance, setRiskTolerance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"medium\");\n    const [preferredCryptos, setPreferredCryptos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [investmentGoals, setInvestmentGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        let val;\n        if (type === 'checkbox') {\n            val = checked;\n        } else if (type === 'number') {\n            // Handle empty string or invalid numbers gracefully\n            if (value === '' || value === null || value === undefined) {\n                val = 0;\n            } else {\n                const parsed = parseFloat(value);\n                val = isNaN(parsed) ? 0 : parsed;\n            }\n        } else {\n            val = value;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: val\n            }\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: value\n            }\n        });\n        if (name === 'crypto1') {\n            // Reset crypto2 if new crypto1 doesn't support current crypto2\n            const validQuotes = _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_QUOTES_SIMPLE[value] || DEFAULT_QUOTE_CURRENCIES || [\n                \"USDT\",\n                \"USDC\",\n                \"BTC\"\n            ];\n            if (!config.crypto2 || !Array.isArray(validQuotes) || !validQuotes.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: validQuotes[0] || 'USDT'\n                    }\n                }); // Ensure crypto2 gets a valid default\n            }\n        }\n    };\n    const handleCrypto1Selection = (crypto)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto1: crypto\n            }\n        });\n        // Reset crypto2 to first allowed crypto2 when crypto1 changes\n        const allowedCrypto2 = _lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO2 || [\n            \"USDT\",\n            \"USDC\",\n            \"DAI\"\n        ];\n        if (!allowedCrypto2.includes(config.crypto2)) {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    crypto2: allowedCrypto2[0]\n                }\n            });\n        }\n    };\n    const handleCrypto2Selection = (crypto)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto2: crypto\n            }\n        });\n    };\n    const handleTradingModeChange = (checked)=>{\n        const newMode = checked ? \"StablecoinSwap\" : \"SimpleSpot\";\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                tradingMode: newMode\n            }\n        });\n    };\n    const handleIncomeSplitChange = (cryptoKey, value)=>{\n        let percent = parseFloat(value);\n        if (isNaN(percent)) percent = 0;\n        if (percent < 0) percent = 0;\n        if (percent > 100) percent = 100;\n        if (cryptoKey === 'incomeSplitCrypto1Percent') {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto1Percent: percent,\n                    incomeSplitCrypto2Percent: 100 - percent\n                }\n            });\n        } else {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto2Percent: percent,\n                    incomeSplitCrypto1Percent: 100 - percent\n                }\n            });\n        }\n    };\n    const handleAISuggestion = async ()=>{\n        if (!riskTolerance || !preferredCryptos || !investmentGoals) {\n            toast({\n                title: \"AI Suggestion Error\",\n                description: \"Please fill all AI suggestion fields.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        await getTradingModeSuggestion({\n            riskTolerance,\n            preferredCryptocurrencies: preferredCryptos,\n            investmentGoals\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingConfigSidebar.useEffect\": ()=>{\n            if (aiSuggestion) {\n                toast({\n                    title: \"AI Suggestion Received\",\n                    description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Mode:\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    aiSuggestion.suggestedMode\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Reason:\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    aiSuggestion.reason\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                size: \"sm\",\n                                className: \"mt-2 btn-neo\",\n                                onClick: {\n                                    \"TradingConfigSidebar.useEffect\": ()=>dispatch({\n                                            type: 'SET_CONFIG',\n                                            payload: {\n                                                tradingMode: aiSuggestion.suggestedMode === 'Simple Spot' ? 'SimpleSpot' : 'StablecoinSwap'\n                                            }\n                                        })\n                                }[\"TradingConfigSidebar.useEffect\"],\n                                children: \"Apply Suggestion\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    duration: Infinity\n                });\n            }\n            if (aiError) {\n                toast({\n                    title: \"AI Suggestion Error\",\n                    description: aiError,\n                    variant: \"destructive\",\n                    duration: Infinity\n                });\n            }\n        }\n    }[\"TradingConfigSidebar.useEffect\"], [\n        aiSuggestion,\n        aiError,\n        toast,\n        dispatch\n    ]);\n    // Use static crypto lists\n    const crypto1Options = _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS || [];\n    const crypto2Options = config.tradingMode === \"SimpleSpot\" ? _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_QUOTES_SIMPLE[config.crypto1] || DEFAULT_QUOTE_CURRENCIES || [\n        \"USDT\",\n        \"USDC\",\n        \"BTC\"\n    ] : (_lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS || []).filter((c)=>c !== config.crypto1); // Ensure Crypto2 is not same as Crypto1 in Swap mode\n    // Debug log to see what's actually loaded\n    console.log('🔍 DEBUG: AVAILABLE_CRYPTOS length:', _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS.length);\n    console.log('🔍 DEBUG: crypto1Options length:', crypto1Options.length);\n    console.log('🔍 DEBUG: First 20 cryptos:', _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS.slice(0, 20));\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO1:', _lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO1);\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO2:', _lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO2);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-sidebar-primary\",\n                        children: \"Trading Configuration\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_backend_status__WEBPACK_IMPORTED_MODULE_16__.BackendStatus, {\n                        className: \"text-xs\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_10__.ScrollArea, {\n                className: \"flex-1 pr-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Mode\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__.Checkbox, {\n                                                    id: \"enableStablecoinSwap\",\n                                                    checked: config.tradingMode === \"StablecoinSwap\",\n                                                    onCheckedChange: handleTradingModeChange\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"enableStablecoinSwap\",\n                                                    className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n                                                    children: \"Enable Stablecoin Swap Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"Current mode: \",\n                                                config.tradingMode === \"SimpleSpot\" ? \"Simple Spot\" : \"Stablecoin Swap\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this),\n                                        config.tradingMode === \"StablecoinSwap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"preferredStablecoin\",\n                                                    children: \"Preferred Stablecoin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                    name: \"preferredStablecoin\",\n                                                    value: config.preferredStablecoin,\n                                                    onValueChange: (val)=>handleSelectChange(\"preferredStablecoin\", val),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                            id: \"preferredStablecoin\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                placeholder: \"Select stablecoin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                            className: \"max-h-[300px] overflow-y-auto\",\n                                                            children: _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_STABLECOINS && _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_STABLECOINS.map((sc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: sc,\n                                                                    children: sc\n                                                                }, sc, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 81\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 18\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 87\n                                            }, this),\n                                            \" AI Mode Suggestion\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"riskTolerance\",\n                                                    children: \"Risk Tolerance\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                    value: riskTolerance,\n                                                    onValueChange: setRiskTolerance,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                            id: \"riskTolerance\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                placeholder: \"Select risk tolerance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 53\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: \"low\",\n                                                                    children: \"Low\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: \"medium\",\n                                                                    children: \"Medium\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: \"high\",\n                                                                    children: \"High\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"preferredCryptos\",\n                                                    children: \"Preferred Cryptocurrencies (comma-separated)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"preferredCryptos\",\n                                                    value: preferredCryptos,\n                                                    onChange: (e)=>setPreferredCryptos(e.target.value),\n                                                    placeholder: \"e.g., BTC, ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"investmentGoals\",\n                                                    children: \"Investment Goals\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"investmentGoals\",\n                                                    value: investmentGoals,\n                                                    onChange: (e)=>setInvestmentGoals(e.target.value),\n                                                    placeholder: \"e.g., Long term, Short term profit\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleAISuggestion,\n                                            disabled: aiLoading,\n                                            className: \"w-full btn-neo\",\n                                            children: [\n                                                aiLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 31\n                                                }, this),\n                                                \"Get AI Suggestion\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Pair\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_15__.CryptoInput, {\n                                            label: \"Crypto 1 (Base)\",\n                                            value: config.crypto1,\n                                            allowedCryptos: config.tradingMode === \"StablecoinSwap\" ? _lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ] : _lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ],\n                                            onValidCrypto: handleCrypto1Selection,\n                                            placeholder: \"e.g., BTC, ETH, SOL\",\n                                            description: \"Enter the base cryptocurrency symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_15__.CryptoInput, {\n                                            label: config.tradingMode === \"StablecoinSwap\" ? \"Crypto 2\" : \"Crypto 2 (Stablecoin)\",\n                                            value: config.crypto2,\n                                            allowedCryptos: config.tradingMode === \"StablecoinSwap\" ? _lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ] : _lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO2 || [\n                                                \"USDC\",\n                                                \"DAI\",\n                                                \"TUSD\",\n                                                \"FDUSD\",\n                                                \"USDT\",\n                                                \"EUR\"\n                                            ],\n                                            onValidCrypto: handleCrypto2Selection,\n                                            placeholder: config.tradingMode === \"StablecoinSwap\" ? \"e.g., BTC, ETH, SOL\" : \"e.g., USDT, USDC, DAI\",\n                                            description: config.tradingMode === \"StablecoinSwap\" ? \"Enter the second cryptocurrency symbol\" : \"Enter the quote/stablecoin symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Parameters\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        [\n                                            {\n                                                name: \"baseBid\",\n                                                label: \"Base Bid (Crypto 2)\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            },\n                                            {\n                                                name: \"multiplier\",\n                                                label: \"Multiplier\",\n                                                type: \"number\",\n                                                step: \"0.001\"\n                                            },\n                                            {\n                                                name: \"numDigits\",\n                                                label: \"Display Digits\",\n                                                type: \"number\",\n                                                step: \"1\"\n                                            },\n                                            {\n                                                name: \"slippagePercent\",\n                                                label: \"Slippage %\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            }\n                                        ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: field.name,\n                                                        children: field.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: field.name,\n                                                        name: field.name,\n                                                        type: field.type,\n                                                        value: config[field.name],\n                                                        onChange: handleInputChange,\n                                                        step: field.step,\n                                                        min: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    children: \"Couple Income % Split (must sum to 100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto1Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto1,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"incomeSplitCrypto1Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto1Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto1Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto2Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto2,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"incomeSplitCrypto2Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto2Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto2Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {\n                        className: \"mb-4 bg-sidebar-border\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_18__.cn)(\"text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border\", isBotActive ? \"bg-green-600 text-primary-foreground\" : \"bg-muted text-muted-foreground\"),\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 129\n                                    }, this),\n                                    \"Bot Status: \",\n                                    isBotActive ? 'Running' : isBotWarmingUp ? 'Warming Up' : 'Stopped'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setIsTargetModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Target Prices\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setIsAlarmModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Alarm\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>{\n                                    if (isBotActive) {\n                                        dispatch({\n                                            type: 'SYSTEM_STOP_BOT'\n                                        });\n                                    } else {\n                                        dispatch({\n                                            type: 'SYSTEM_START_BOT_INITIATE'\n                                        });\n                                    }\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_18__.cn)('w-full btn-neo', isBotActive || isBotWarmingUp ? 'bg-destructive hover:bg-destructive/90' : 'bg-green-600 hover:bg-green-600/90'),\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 129\n                                    }, this),\n                                    isBotActive ? 'Stop Bot' : isBotWarmingUp ? 'Warming Up...' : 'Start Bot'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>{\n                                    dispatch({\n                                        type: 'SYSTEM_RESET_BOT'\n                                    });\n                                    toast({\n                                        title: \"Bot Reset\",\n                                        description: \"Trading bot has been reset to fresh state. All positions cleared.\",\n                                        duration: 3000\n                                    });\n                                },\n                                variant: \"outline\",\n                                className: \"w-full btn-outline-neo\",\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reset Bot\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_12__.TargetPriceModal, {\n                isOpen: isTargetModalOpen,\n                onClose: ()=>setIsTargetModalOpen(false),\n                onSetTargetPrices: contextSetTargetPrices\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_AlarmConfigModal__WEBPACK_IMPORTED_MODULE_13__.AlarmConfigModal, {\n                isOpen: isAlarmModalOpen,\n                onClose: ()=>setIsAlarmModalOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 378,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n_s(TradingConfigSidebar, \"VLL9LNLRLp1B1k6hkOPIBw7LnUM=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_17__.useToast\n    ];\n});\n_c = TradingConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TradingConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx\n"));

/***/ })

});