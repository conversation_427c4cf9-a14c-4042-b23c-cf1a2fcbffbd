"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPanelPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell-ring.js\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _components_admin_SessionManager__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/admin/SessionManager */ \"(app-pages-browser)/./src/components/admin/SessionManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Force the correct stablecoins to ensure all 6 are available\nconst FORCE_STABLECOINS = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n\n\n\n\nfunction AdminPanelPage() {\n    _s();\n    const { appSettings, dispatch, botSystemStatus } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_10__.useTradingContext)();\n    const [localSettings, setLocalSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(appSettings);\n    const [apiKey, setApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('iVIOLOnigRM31Qzm4UoLYsJo4QYIsd1XeXKztnwHfcijpWiAaWQKRsmx3NO7LrLA');\n    const [apiSecret, setApiSecret] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('jzAnpgIFFv3Ypdhf4jEXljjbkBpfJE5W2aN0zrtypmD3RAjoh2vdQXMr66LOv5fp');\n    const [showApiKey, setShowApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showApiSecret, setShowApiSecret] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [telegramToken, setTelegramToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [telegramChatId, setTelegramChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanelPage.useEffect\": ()=>{\n            setLocalSettings(appSettings);\n        }\n    }[\"AdminPanelPage.useEffect\"], [\n        appSettings\n    ]);\n    const handleSettingsChange = (key, value)=>{\n        setLocalSettings((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const handleSaveAppSettings = ()=>{\n        dispatch({\n            type: 'SET_APP_SETTINGS',\n            payload: localSettings\n        });\n        toast({\n            title: \"App Settings Saved\",\n            description: \"Global application settings have been updated.\"\n        });\n    };\n    const handleSaveApiKeys = async ()=>{\n        try {\n            localStorage.setItem('binance_api_key', apiKey);\n            localStorage.setItem('binance_api_secret', apiSecret);\n            console.log(\"API Keys Saved:\", {\n                apiKey: apiKey.substring(0, 10) + '...',\n                apiSecret: apiSecret.substring(0, 10) + '...'\n            });\n            toast({\n                title: \"API Keys Saved\",\n                description: \"Binance API keys have been saved securely.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save API keys.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleTestApiConnection = async ()=>{\n        try {\n            const response = await fetch('https://api.binance.com/api/v3/ping');\n            if (response.ok) {\n                toast({\n                    title: \"API Connection Test\",\n                    description: \"Successfully connected to Binance API!\"\n                });\n            } else {\n                toast({\n                    title: \"Connection Failed\",\n                    description: \"Unable to connect to Binance API.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            toast({\n                title: \"Connection Error\",\n                description: \"Network error while testing API connection.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleSaveTelegramConfig = ()=>{\n        try {\n            localStorage.setItem('telegram_bot_token', telegramToken);\n            localStorage.setItem('telegram_chat_id', telegramChatId);\n            console.log(\"Telegram Config Saved:\", {\n                telegramToken: telegramToken.substring(0, 10) + '...',\n                telegramChatId\n            });\n            toast({\n                title: \"Telegram Config Saved\",\n                description: \"Telegram settings have been saved successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save Telegram configuration.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleTestTelegram = async ()=>{\n        if (!telegramToken || !telegramChatId) {\n            toast({\n                title: \"Missing Configuration\",\n                description: \"Please enter both Telegram bot token and chat ID.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    chat_id: telegramChatId,\n                    text: '🤖 Test message from Pluto Trading Bot! Your Telegram integration is working correctly.'\n                })\n            });\n            if (response.ok) {\n                toast({\n                    title: \"Telegram Test Successful\",\n                    description: \"Test message sent successfully!\"\n                });\n            } else {\n                toast({\n                    title: \"Telegram Test Failed\",\n                    description: \"Failed to send test message. Check your token and chat ID.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            toast({\n                title: \"Telegram Error\",\n                description: \"Network error while testing Telegram integration.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const adminTabs = [\n        {\n            value: \"systemTools\",\n            label: \"System Tools\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 58\n            }, this)\n        },\n        {\n            value: \"appSettings\",\n            label: \"App Settings\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 58\n            }, this)\n        },\n        {\n            value: \"apiKeys\",\n            label: \"Exchange API Keys\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 59\n            }, this)\n        },\n        {\n            value: \"telegram\",\n            label: \"Telegram Integration\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 63\n            }, this)\n        },\n        {\n            value: \"sessionManager\",\n            label: \"Session Manager\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 64\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n            className: \"border-2 border-border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                    className: \"flex flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                    className: \"text-3xl font-bold text-primary\",\n                                    children: \"Admin Panel\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                    children: \"Manage global settings and tools for Pluto Trading Bot.\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>router.push('/dashboard'),\n                            className: \"btn-outline-neo\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                \"Return to Dashboard\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                        defaultValue: \"systemTools\",\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_12__.ScrollArea, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                    className: \"bg-card border-border border-2 p-1\",\n                                    children: adminTabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: tab.value,\n                                            className: \"px-4 py-2 text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    tab.icon,\n                                                    \" \",\n                                                    tab.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 22\n                                            }, this)\n                                        }, tab.value, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 20\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"systemTools\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                        className: \"bg-card-foreground/5 border-border border-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                    children: \"System Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 31\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Database Editor, Clean Duplicates, Export/Import, Backup/Restore, Diagnostics - (Placeholders for future implementation).\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"DB Editor Clicked\"\n                                                                    }),\n                                                                children: \"View Database (Read-Only)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Export Orders Clicked\"\n                                                                    }),\n                                                                children: \"Export Orders to Excel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Export History Clicked\"\n                                                                    }),\n                                                                children: \"Export History to Excel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Backup DB Clicked\"\n                                                                    }),\n                                                                children: \"Backup Database\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Restore DB Clicked\"\n                                                                    }),\n                                                                disabled: true,\n                                                                children: \"Restore Database\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Diagnostics Clicked\"\n                                                                    }),\n                                                                children: \"Run System Diagnostics\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"appSettings\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    className: \"bg-card-foreground/5 border-border border-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                children: \"Application Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 29\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"preferredStablecoin\",\n                                                            children: \"Preferred Stablecoin (for Swap Mode)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                            value: localSettings.preferredStablecoin,\n                                                            onValueChange: (val)=>handleSettingsChange('preferredStablecoin', val),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                    id: \"preferredStablecoin\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                        placeholder: \"Select Stablecoin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 63\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                    children: FORCE_STABLECOINS.map((sc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                            value: sc,\n                                                                            children: sc\n                                                                        }, sc, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 173,\n                                                                            columnNumber: 54\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"priceUpdateIntervalMs\",\n                                                            children: \"Price Update Interval (ms)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"priceUpdateIntervalMs\",\n                                                            type: \"number\",\n                                                            value: localSettings.priceUpdateIntervalMs || 1000,\n                                                            onChange: (e)=>handleSettingsChange('priceUpdateIntervalMs', parseInt(e.target.value) || 1000)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    onClick: handleSaveAppSettings,\n                                                    className: \"btn-neo\",\n                                                    children: \"Save App Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"apiKeys\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    className: \"bg-card-foreground/5 border-border border-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                children: \"Exchange API Keys (Binance)\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 29\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Configure your Binance API keys for real trading. Keys are stored securely in browser storage.\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"apiKey\",\n                                                            children: \"API Key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"apiKey\",\n                                                                    type: showApiKey ? \"text\" : \"password\",\n                                                                    value: apiKey,\n                                                                    onChange: (e)=>setApiKey(e.target.value),\n                                                                    placeholder: \"Enter your Binance API key\",\n                                                                    className: \"pr-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                    onClick: ()=>setShowApiKey(!showApiKey),\n                                                                    children: showApiKey ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 214,\n                                                                        columnNumber: 39\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 214,\n                                                                        columnNumber: 72\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"apiSecret\",\n                                                            children: \"API Secret\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"apiSecret\",\n                                                                    type: showApiSecret ? \"text\" : \"password\",\n                                                                    value: apiSecret,\n                                                                    onChange: (e)=>setApiSecret(e.target.value),\n                                                                    placeholder: \"Enter your Binance API secret\",\n                                                                    className: \"pr-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                    onClick: ()=>setShowApiSecret(!showApiSecret),\n                                                                    children: showApiSecret ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 42\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 75\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: handleSaveApiKeys,\n                                                            className: \"btn-neo\",\n                                                            children: \"Save API Keys\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: handleTestApiConnection,\n                                                            variant: \"outline\",\n                                                            className: \"btn-outline-neo\",\n                                                            children: \"Test Connection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"telegram\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                            className: \"bg-card-foreground/5 border-border border-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                            children: \"Telegram Configuration\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                                            children: \"Configure Telegram bot for real-time trading notifications.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"telegramToken\",\n                                                                    children: \"Telegram Bot Token\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"telegramToken\",\n                                                                    type: \"password\",\n                                                                    value: telegramToken,\n                                                                    onChange: (e)=>setTelegramToken(e.target.value),\n                                                                    placeholder: \"Enter your Telegram bot token\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"telegramChatId\",\n                                                                    children: \"Telegram Chat ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"telegramChatId\",\n                                                                    value: telegramChatId,\n                                                                    onChange: (e)=>setTelegramChatId(e.target.value),\n                                                                    placeholder: \"Enter your Telegram chat ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                    id: \"notifyOnOrder\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"notifyOnOrder\",\n                                                                    children: \"Notify on Order Execution\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                    id: \"notifyOnErrors\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"notifyOnErrors\",\n                                                                    children: \"Notify on Errors\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    onClick: handleSaveTelegramConfig,\n                                                                    className: \"btn-neo\",\n                                                                    children: \"Save Telegram Config\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    onClick: handleTestTelegram,\n                                                                    variant: \"outline\",\n                                                                    className: \"btn-outline-neo\",\n                                                                    children: \"Test Telegram\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                            className: \"bg-card-foreground/5 border-border border-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                        children: \"Setup Guide\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-yellow-500 mb-3\",\n                                                                    children: \"Step 1: Create a Telegram Bot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                    className: \"text-sm space-y-1 list-decimal list-inside text-muted-foreground\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Open Telegram and search for \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                    className: \"bg-muted px-1 rounded\",\n                                                                                    children: \"@BotFather\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 304,\n                                                                                    columnNumber: 58\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Send \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                    className: \"bg-muted px-1 rounded\",\n                                                                                    children: \"/newbot\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 305,\n                                                                                    columnNumber: 34\n                                                                                }, this),\n                                                                                \" command\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: 'Choose a name for your bot (e.g., \"My Trading Bot\")'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: 'Choose a username ending with \"bot\" (e.g., \"mytradingbot\")'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Copy the bot token provided by BotFather\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 308,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-yellow-500 mb-3\",\n                                                                    children: \"Step 2: Get Your Chat ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                    className: \"text-sm space-y-1 list-decimal list-inside text-muted-foreground\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Start a chat with your new bot\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 316,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Send any message to the bot\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                \"Visit: \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                    className: \"bg-muted px-1 rounded text-xs\",\n                                                                                    children: \"https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 318,\n                                                                                    columnNumber: 36\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 318,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: [\n                                                                                'Look for \"chat\":',\n                                                                                \"{\",\n                                                                                'id\": in the response'\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 319,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Copy the chat ID number\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 320,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-yellow-500 mb-3\",\n                                                                    children: \"Step 3: Configure Bot\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"text-sm space-y-1 list-disc list-inside text-muted-foreground\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: 'Paste the bot token in the \"Telegram Bot Token\" field'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: 'Paste the chat ID in the \"Telegram Chat ID\" field'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Choose your notification preferences\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 330,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: 'Click \"Save Telegram Config\"'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 331,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: 'Test the connection with \"Test Telegram\"'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-md p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-yellow-500\",\n                                                                        children: \"\\uD83D\\uDCA1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                className: \"font-semibold text-yellow-600 mb-1\",\n                                                                                children: \"Pro Tip:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 341,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-muted-foreground\",\n                                                                                children: \"Keep your bot token secure and never share it publicly. You can regenerate it anytime via BotFather if needed.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 342,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"sessionManager\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SessionManager__WEBPACK_IMPORTED_MODULE_13__.SessionManager, {}, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPanelPage, \"tbnDcI/g4v3KjLPgVB/4mL2wCfY=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_10__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminPanelPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPanelPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});